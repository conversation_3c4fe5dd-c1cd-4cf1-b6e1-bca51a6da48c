import calendar
import datetime
import json
import logging
import os
import traceback

import requests
from openpyxl.reader.excel import load_workbook

from saian_api import settings
from saian_api.celery import celery_app
from saian_api.device.models import DeviceAttribute
from saian_api.device.views import DeviceAttributeViewV5
from saian_api.dimension.models import DimensionAttribute, PpvConfig
from saian_api.project.models import Project
from saian_api.report.models import DlTask, ManualRecord
from saian_api.scheduledtask.utils import set_global_db
from saian_api.utils.inthttpapi import DOMAIN
from saian_api.utils.inthttpapi.base import BaseAPI
from saian_api.utils.tools import ec_type_to_unit
from saian_api.utils.utils import AuthUtils

def get_SysRunStatus(dt: datetime.datetime):
    mr = ManualRecord.objects.filter(data_time__year=dt.year, data_time__month=dt.month, data_time__day=dt.day,
                                     use_for=20, name__endswith="系统总控").last()
    if mr is None:
        return None

    timerange = []
    data = json.loads(mr.data)
    date = dt.date()

    for k, v in data.items():
        if k != '本地':
            timerange += v

    result = []
    for r in timerange:
        [start_time, end_time] = r
        hour = int(start_time[:2])
        minute = int(start_time[2:4])
        second = int(start_time[4:6])
        start_time = datetime.time(hour, minute, second)
        start = datetime.datetime.combine(date, start_time)

        hour = int(end_time[:2])
        minute = int(end_time[2:4])
        second = int(end_time[4:6])
        end_time = datetime.time(hour, minute, second)
        end = datetime.datetime.combine(date, end_time)

        result.append([start, end])

    return result


@celery_app.task(name='saian_api.report.tasks.sys_ec_report', max_retries=3)
def sys_ec_report(project_id, time_type, from_at, till_at, cs_status_id, dl_task_id):
    set_global_db(project_id)

    project = Project.objects.get(pk=project_id)
    dt = datetime.datetime.strptime(from_at, '%Y%m%d%H%M%S')
    cs_status_da = DeviceAttribute.objects.get(pk=cs_status_id)
    dl_task = DlTask.objects.get(pk=dl_task_id)

    random_id = AuthUtils.generate_random_comm_char(9)

    if time_type == 'di':
        title = f'{project.name}空调节能{dt.month}月{dt.day}日数据统计表'
        dl_task.status = 20
        dl_task.name = title
        dl_task.save()

        try:
            template_path = f'saian_api/report/templates/daily_template_{project_id}.xltx'
            file_path = os.path.join(settings.BASE_DIR, template_path)
            wb = load_workbook(file_path)
            wb.template = False
            ws = wb.active
            ws['A1'] = title

            # 找出非本地运行的时间段
            not_local_run = get_SysRunStatus(dt)
            if not_local_run is None:
                not_local_run = []
                stats, count = DeviceAttributeViewV5.query_stats(None, cs_status_da, from_at, till_at)

                time_start = dt.replace(hour=0, minute=0, second=0, microsecond=0)
                time_end = None
                local_index = str(stats['options'].split(',').index('本地'))
                for stat in stats['values']:
                    if stat['value'] != local_index:
                        if time_start is None:
                            time_start = stat['time']
                        else:
                            time_end = stat['time']
                    else:
                        if time_start is not None and time_end is not None:
                            not_local_run.append([time_start, time_end])
                        time_start = None
                        time_end = None
                if time_start is not None and time_end is not None:
                    not_local_run.append([time_start, time_end])

                if len(not_local_run):
                    day_begin = not_local_run[0][0]
                    day_begin = day_begin.replace(hour=0, minute=0, second=0, microsecond=0)
                    not_local_run[0][0] = day_begin
                    day_end = not_local_run[-1][1]
                    day_end = day_end.replace(hour=23, minute=59, second=59)
                    not_local_run[-1][1] = day_end

            for column in ws.iter_cols(min_col=2, max_col=ws.max_column):
                da = None

                # 在这里对列进行操作
                for cell in column:
                    if cell.row == 2 and isinstance(cell.value, int):
                        da = DimensionAttribute.objects.get(pk=cell.value)
                        cell.value = f'{da.name}（{ec_type_to_unit(da.ec_type)}）'

                    if cell.value == 0:
                        ec_cost = None

                        row_time = ws[f'A{cell.row}'].value
                        if not isinstance(row_time, datetime.time):
                            continue

                        hb_time = row_time.replace(minute=0, second=0)
                        b_dt = datetime.datetime.combine(dt.date(), hb_time)
                        e_dt = b_dt + datetime.timedelta(hours=1)

                        hour_from = b_dt
                        hour_till = e_dt

                        calc_cost = False
                        for idx, run_range in enumerate(not_local_run):
                            if b_dt < run_range[0] and e_dt <= run_range[0]:
                                ec_cost = 0
                                break
                            if b_dt < run_range[0] < e_dt < run_range[1]:
                                hour_from = run_range[0]
                                calc_cost = True
                                break
                            if b_dt < run_range[0] and e_dt > run_range[1]:
                                hour_from = run_range[0]
                                hour_till = run_range[1]
                                calc_cost = True
                                break
                            if b_dt >= run_range[0] and e_dt <= run_range[1]:
                                calc_cost = True
                                break
                            if run_range[0] <= b_dt < run_range[1] < e_dt:
                                hour_till = run_range[1]
                                calc_cost = True
                                break
                            if b_dt >= run_range[1] and idx == len(not_local_run) - 1:
                                ec_cost = 0

                        if calc_cost:
                            ec_cost = DimensionAttribute.objects.calc_hourly_stats(da, hour_from, hour_till)
                        else:
                            ec_cost = 0

                        cell.value = ec_cost

            path = f'drf-assets/exports/{title}__{random_id}.xlsx'
            wb.save(filename=path)
        except Exception as e:
            dl_task.status = 40
            dl_task.save()
            error_message = traceback.format_exc()
            logging.error(f'导出{title}出错，error: {e.__str__()}, {error_message}')

            return

    else:
        title = f'{project.name}空调节能{dt.month}月费用计算表'
        dl_task.status = 20
        dl_task.name = title
        dl_task.save()

        try:
            template_path = f'saian_api/report/templates/monthly_template_{project_id}.xltx'
            file_path = os.path.join(settings.BASE_DIR, template_path)
            wb = load_workbook(file_path)
            wb.template = False
            ws = wb.active
            dim_attr = DimensionAttribute.objects.get(pk=ws['A1'].value)
            ws['A1'] = title

            # 读取电价和节能率
            data_time = datetime.datetime(dt.year, dt.month, 1, 0, 0, 0)
            mr = ManualRecord.objects.filter(data_time=data_time, use_for=20, name=f'{dt.year}年{dt.month:02}月用电配置').last()
            if mr is not None:
                config = json.loads(mr.data)
                ws['C2'] = float(config.get('sharp', 2.13))
                ws['E2'] = float(config.get('peak', 1.7))
                ws['G2'] = float(config.get('flat', 1))
                ws['I2'] = float(config.get('valley', 0.38))
                ws['K2'] = float(config.get('es_ratio', 20)) / 100

            # 查询峰平谷尖区间
            ppv_configs = list(PpvConfig.objects.filter(ec_type=10).order_by('begin_at'))

            for row_idx, row in enumerate(ws.iter_rows(min_row=5, min_col=2, max_row=ws.max_row)):
                day_row = ws[f'A{row_idx + 5}'].value
                if not day_row or '日' not in day_row:
                    continue

                ppv_ec = {10: [], 20: [], 30: [], 40: []}

                day = int(day_row.replace('日', ''))

                monthrange = calendar.monthrange(dt.year, dt.month)[1]
                if day > monthrange:
                    break

                day_begin = f'{dt.year}{dt.month:02}{day:02}000000'
                begin = datetime.datetime.strptime(day_begin, '%Y%m%d%H%M%S')
                end = begin + datetime.timedelta(days=1)
                day_end = datetime.datetime.strftime(end, '%Y%m%d%H%M%S')
                # day_end = f'{dt.year}{dt.month:02}{day + 1:02}000000'

                # 找出非本地运行的时间段
                not_local_run = get_SysRunStatus(begin)
                if not_local_run is None:
                    not_local_run = []
                    stats, count = DeviceAttributeViewV5.query_stats(None, cs_status_da, day_begin, day_end)

                    time_start = begin
                    time_end = None
                    local_index = str(stats['options'].split(',').index('本地'))
                    for stat in stats['values']:
                        if stat['value'] != local_index:
                            if time_start is None:
                                time_start = stat['time']
                            else:
                                time_end = stat['time']
                        else:
                            if time_start is not None and time_end is not None:
                                not_local_run.append([time_start, time_end])
                            time_start = None
                            time_end = None
                    if time_start is not None and time_end is not None:
                        not_local_run.append([time_start, time_end])

                    if len(not_local_run):
                        day_begin = not_local_run[0][0]
                        day_begin = day_begin.replace(hour=0, minute=0, second=0, microsecond=0)
                        not_local_run[0][0] = day_begin
                        day_end = not_local_run[-1][1]
                        day_end = day_end.replace(hour=23, minute=59, second=59)
                        not_local_run[-1][1] = day_end

                # not_local_run = [[datetime.datetime(2024, 4, day, 11, 3, 0),
                #                   datetime.datetime(2024, 4, day, 11, 5, 0)],
                #                  [datetime.datetime(2024, 4, day, 11, 7, 0),
                #                   datetime.datetime(2024, 4, day, 23, 50, 0)]]

                for idx, run_range in enumerate(not_local_run):
                    hour_begin = run_range[0]
                    while hour_begin < run_range[1]:
                        # 根据项目配置的ppv_configs，求出hour_begin的所属时段
                        prev_ppv = 30
                        for ppv_idx, config in enumerate(ppv_configs):
                            if hour_begin.time() < config.begin_at:
                                break
                            prev_ppv = config.ppv_type
                            if ppv_idx == len(ppv_configs) - 1:
                                break

                        hour_till = (hour_begin + datetime.timedelta(hours=1)).replace(minute=0, second=0)
                        if hour_till > run_range[1]:
                            hour_till = run_range[1]
                            ec_cost = DimensionAttribute.objects.calc_hourly_stats(dim_attr, hour_begin, hour_till)
                            hour_begin = run_range[1]
                        else:
                            ec_cost = DimensionAttribute.objects.calc_hourly_stats(dim_attr, hour_begin, hour_till)
                            hour_begin = hour_till

                        ppv_ec[prev_ppv].append(ec_cost)

                # 尖
                s_ec = sum(ppv_ec[40])
                ws[f'B{row_idx + 5}'] = s_ec
                # 峰
                p_ec = sum(ppv_ec[10])
                ws[f'D{row_idx + 5}'] = p_ec
                # 平
                f_ec = sum(ppv_ec[20])
                ws[f'F{row_idx + 5}'] = f_ec
                # 谷
                v_ec = sum(ppv_ec[30])
                ws[f'H{row_idx + 5}'] = v_ec

                path = f'drf-assets/exports/{title}__{random_id}.xlsx'
                wb.save(filename=path)

            ws['A36'] = f'{dt.year}年{dt.month}月节能效益金额（元）'
            path = f'drf-assets/exports/{title}__{random_id}.xlsx'
            wb.save(filename=path)
        except Exception as e:
            dl_task.status = 40
            dl_task.save()
            error_message = traceback.format_exc()
            logging.error(f'导出{title}出错, error: {e.__str__()}, {error_message}')
            return

    # 将工作簿对象保存到内存中的二进制数据
    with open(os.path.join(settings.BASE_DIR, path), 'rb') as file:
        res = f'saianapi/intapi/drf_assets'
        headers = BaseAPI.admin_intapi_header(res)
        headers['project'] = str(project_id)
        r = requests.post(f'{DOMAIN}/{res}', headers=headers, files={'file': file}, data={'name': f'{title}__{random_id}.xlsx'})

        if r.status_code != 200:
            dl_task.status = 40
            dl_task.save()
            logging.error(f'failed to send drf assets: {title}, status code: {r.status_code}, {r.text}')
        else:
            dl_task.status = 30
            dl_task.file_path = path
            dl_task.save()
