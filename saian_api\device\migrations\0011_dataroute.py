# Generated by Django 3.2.8 on 2023-03-30 18:04

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('device', '0010_devicectrllog_action_log'),
    ]

    operations = [
        migrations.CreateModel(
            name='DataRoute',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('src_mac', models.Char<PERSON>ield(max_length=100)),
                ('src_idf', models.Char<PERSON>ield(max_length=255)),
                ('dest_mac', models.Char<PERSON>ield(max_length=100)),
                ('dest_idf', models.Char<PERSON>ield(max_length=255)),
                ('enabled', models.<PERSON><PERSON>an<PERSON>ield(default=False)),
            ],
            options={
                'db_table': 'data_routes',
            },
        ),
    ]
