"""
  统计用户在线时长
"""
from django.core.management.base import BaseCommand
from saian_api.user.models import UserLog, WebUser
from saian_api.scheduledtask.utils import get_projects, set_global_db

import datetime
import traceback


class Command(BaseCommand):
    help = '统计用户在线时长'

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目列表
        projects = get_projects(self, options)

        hour_ago = datetime.datetime.now() - datetime.timedelta(hours=1)
        last_hour_begin = hour_ago.replace(minute=0, second=0, microsecond=0)
        last_hour_end = last_hour_begin + datetime.timedelta(hours=1)

        for project_id in projects:
            try:
                set_global_db(project_id)

                user_logs = (UserLog.objects.filter(created_at__range=(last_hour_begin, last_hour_end)).values('web_user_id', 'created_at')
                             .order_by('web_user_id', 'created_at'))

                current_user = None
                log_times = {}
                user_seconds = {}
                for log in user_logs:
                    web_user_id = log['web_user_id']
                    if web_user_id not in log_times:
                        log_times[web_user_id] = []
                        user_seconds[web_user_id] = 0
                    log_times[web_user_id].append(log['created_at'])

                for web_user_id, times in log_times.items():
                    for session in self.split_list_by_difference(times):
                        user_seconds[web_user_id] += (session[-1] - session[0]).total_seconds()

                for web_user_id, seconds in user_seconds.items():
                    try:
                        web_user = WebUser.objects.get(pk=web_user_id)
                        online_minutes = round(seconds / 60, 2)
                        web_user.online_time += online_minutes
                        web_user.online_time = round(web_user.online_time, 2)
                        web_user.save(update_fields=['online_time'])
                    except WebUser.DoesNotExist:
                        pass

            except Exception as e:
                self.stderr.write(f"统计用户在线时长出错，project_id: {project_id}, err: {e.__str__()}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
                continue

    @classmethod
    def split_list_by_difference(cls, numbers, max_difference=15):
        result = []
        current_sublist = [numbers[0]]

        for num in numbers[1:]:
            if (num - current_sublist[-1]).total_seconds() > max_difference:
                result.append(current_sublist)
                current_sublist = [num]
            else:
                current_sublist.append(num)

        result.append(current_sublist)
        return result
