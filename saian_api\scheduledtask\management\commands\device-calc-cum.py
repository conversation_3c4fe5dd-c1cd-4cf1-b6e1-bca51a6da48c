"""
    设备累计值参数统计
"""
import datetime
import json
import logging
import re
import traceback
from collections import defaultdict

from django.core import exceptions
from django.core.management import BaseCommand, CommandError
from django.db.models import Q

from saian_api.devdefine.models import AttributePrototype, DevicePrototypeRatio, DevicePrototype
from saian_api.device.models import Device, ParamRecord, DeviceRuntime, DeviceEvent, DeviceAttribute
from saian_api.notifi.models import Notification
from saian_api.project.models import Project
from saian_api.report.models import ReportConfigurer
from saian_api.scheduledtask.utils import get_projects, set_global_db
from saian_api.utils.db.Redis import RedisHelper
from saian_api.utils.tools import is_number

def execute_formula_by_current(device, formula):
    matches = re.findall('\{\{\w+\}\}', formula)
    new_str = formula
    for m in matches:
        attr_id = m[2:len(m) - 2]
        try:
            ap = AttributePrototype.objects.get(pk=attr_id)
            value = device.get_value_by_idf(ap.identifier)
            new_str = new_str.replace(m, str(value))
        except exceptions.ObjectDoesNotExist:
            continue

    try:
        result = eval(new_str)
    except:
        logging.error('Exception in ParamRecord.execute_formula_by_current(), formula = %s' % new_str)
        result = None

    return result

class Command(BaseCommand):
    help = "设备累计值参数统计"

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目ID列表
        projects = get_projects(self, options)
        # 项目倒序，先统计新项目
        projects.reverse()

        # 统计任务开始时的时间。
        now = datetime.datetime.now()

        for project_id in projects:
            try:
                begin_at = datetime.datetime.now()
                # 报错信息
                error_info = f'设备累计值参数统计, {begin_at}, 项目: {project_id} \n'
                errors = []
                # 设置全局数据库
                set_global_db(project_id)
                project = Project.objects.get(pk=project_id)

                self.stdout.write(f"设备累计值参数统计开始: {project.name}", ending='\n')

                # 项目存在的设备类型
                prj_dp_ids = Device.objects.values_list('device_prototype_id', flat=True).distinct()

                # 报表中已配置的设备类型
                report_cfgs = list(ReportConfigurer.objects.filter(target_type__model='DevicePrototype', target_id__in=prj_dp_ids))
                dp_ids = [rc.target_id for rc in report_cfgs]

                # 所有相关设备
                devices = Device.objects.filter(device_prototype_id__in=dp_ids, project_id__isnull=False)
                devices_map = defaultdict(list)
                for d in devices:
                    devices_map[d.device_prototype_id].append(d)

                # 查找所有相关属性类型
                ap_names = [f'attribute_prototype:{rc.target_id}_{rc.identifier}' for rc in report_cfgs]
                aps = [ap for ap in AttributePrototype.objects.query_by_names(project_id, ap_names) if ap.is_cum]

                aps_map = defaultdict(list)
                for ap in aps:
                    aps_map[(ap.device_prototype_id, ap.identifier)].append(ap)

                # 查找设备属性
                device_ids = [device.id for device in devices]
                ap_ids = [ap.id for ap in aps]
                das = DeviceAttribute.objects.filter(device_id__in=device_ids, attribute_prototype_id__in=ap_ids)
                das_map = {(da.device_id, da.attribute_prototype_id): da for da in das}

                runtime_ratios = DevicePrototypeRatio.objects.filter(params__isnull=False).exclude(params='')

                for cfg in report_cfgs:
                    cfg_devices = devices_map.get(cfg.target_id, [])
                    new_prs = []

                    for device in cfg_devices:
                        # 先处理设备的运行时间统计
                        self.process_runtime(device, cfg.identifier, runtime_ratios)
                        #
                        created_at = now.replace(minute=5, second=0, microsecond=0)
                        device_aps = aps_map.get((cfg.target_id, cfg.identifier), [])

                        for device_ap in device_aps:
                            da = das_map.get((device.id, device_ap.id), None)
                            pr, error_msg = self.process_param_record(device, device_ap, da, now, created_at)
                            if error_msg:
                                errors.append(error_msg)
                            if pr:
                                new_prs.append(pr)

                    ParamRecord.objects.bulk_create(new_prs, batch_size=1000)

                if len(errors):
                    errors = [error_info] + errors
                    Notification.objects.send_debug_email_with_attachment(project.name, "\n".join(errors))
                self.stdout.write(f"设备累计值参数统计任务完成: {project.name}, 耗时: {(datetime.datetime.now() - begin_at).seconds} 秒。",
                                  ending='\n')

            except CommandError:
                self.stderr.write(f"运行'设备累计值参数统计'任务失败，项目ID：{project_id}，命令参数不合法！", ending='\n')
                continue
            except Exception as e:
                self.stderr.write(f"运行'设备累计值参数统计'任务失败，项目ID：{project_id}, {e.__str__()}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
                continue

    @classmethod
    def process_param_record(cls, device, ap, da, now, created_at):
        try:
            if ap.formula:
                result = execute_formula_by_current(device, ap.formula)
            else:
                # da = DeviceAttribute.objects.query_object_by_ap(device, ap)
                result = da.value if da else None
                # result = device.get_value_by_idf(ap.identifier)
                if result is not None and not is_number(result) and result != '':
                    message = (f'项目: {device.project_id}, 设备{device.id}-{device.nick_name}, 参数 '
                               f'{ap.identifier}, 当前值非数值: {result}')
                    Notification.objects.send_debug_mail(device, message)
                    result = ap.default_value if ap.default_value else 0
            if result is not None:
                name = f'param_record_{now.strftime("%Y%m%d%H")}:{device.id}_{ap.identifier}'
                pr = ParamRecord.objects.filter(device_id=device.id, identifier=ap.identifier,
                                                created_at=created_at).last()

                if not RedisHelper.exists(device.project_id, name) and pr is None:
                    pr = ParamRecord(
                        device_id=device.id,
                        mac=device.mac,
                        identifier=ap.identifier,
                        value=result,
                        created_at=created_at,
                    )
                    # pr.created_at = created_at
                    # pr.save()
                    RedisHelper.set_value(device.project_id, name, result, True, 7600)
                    return pr, None
                else:
                    if str(result) != str(pr.value):
                        logging.info(f'\n{device.mac} ,{created_at}, {ap.identifier}, old: {pr.value}, new: {result}')
                        pass

            return None, None

        except Exception as e:
            msg = f'设备累计值参数统计出错! device: {device.id}-{device.nick_name}, error: {e.__str__()} \n'
            logging.error(msg)
            return None, msg


    @staticmethod
    def generate_runtime(last_runtime: DeviceRuntime, params):
        now = datetime.datetime.now()
        # 设置当前计算时间为 0 分
        now = now.replace(minute=0, second=0, microsecond=0)
        created_at = now.strftime('%Y-%m-%d %H:00:00')
        seconds = (now - last_runtime.created_at).seconds if now > last_runtime.created_at else 0
        ratio = DevicePrototypeRatio.objects.get_match_ratio(params, json.loads(last_runtime.new_statuses), last_runtime.sub_idx)

        # 如果最近一条运行时间记录在五分钟之内，则不用自动创建记录
        if seconds > 300 and ratio != 0 and not DeviceRuntime.objects.filter(created_at=created_at, prefix=last_runtime.prefix,
                                                                             sub_idx=last_runtime.sub_idx, device_id=last_runtime.device_id).exists():
            runtime_seconds = round(seconds * ratio)
            runtime = DeviceRuntime.objects.create(
                device=last_runtime.device,
                mac=last_runtime.mac,
                nick_name=last_runtime.nick_name,
                sub_idx=last_runtime.sub_idx,
                prefix=last_runtime.prefix,
                old_statuses=last_runtime.new_statuses,
                new_statuses=last_runtime.new_statuses,
                run_time=runtime_seconds,
            )
            runtime.created_at = created_at
            runtime.save()
            DeviceRuntime.objects.update_device_runtime(last_runtime.device, last_runtime.sub_idx, last_runtime.prefix, runtime.run_time)

    @staticmethod
    def get_value_by_time(device, identifier, now):
        """获取某个时间点的累计类型参数值，当定时任务没有执行成功后，用于数据补全"""
        pr = ParamRecord.objects.filter(device_id=device.id, identifier=identifier,
                                        created_at__lt=now.strftime('%Y-%m-%d %H:00:00')).order_by('-created_at').first()
        if pr is not None:
            return pr.value
        return None

    @classmethod
    def process_runtime(cls, device, identifier, runtime_ratios):
        try:
            if 'SoftRuntime' in identifier:
                sub_dp_ids = DevicePrototype.objects.filter(parent_id=device.device_prototype_id).values_list('id', flat=True)
                dprs = runtime_ratios.filter(Q(device_prototype_id=device.device_prototype_id) |
                                             Q(device_prototype_id__in=sub_dp_ids))
                for dp_ratio in dprs:
                    # dp_ratio = runtime_ratios.get(device_prototype_id=device.device_prototype_id)
                    params = json.loads(dp_ratio.params)
                    if isinstance(params, list):
                        dp = DevicePrototype.objects.get(pk=dp_ratio.device_prototype_id)
                        # device_runtimes = DeviceRuntime.objects.filter(device=device, prefix=dp.prefix).order_by('created_at')
                        # last_runtime = device_runtimes.last()
                        last_runtime = DeviceRuntime.objects.filter(device=device, prefix=dp.prefix).order_by('-created_at').first()
                        if last_runtime is not None:
                            # 是否有子设备
                            if last_runtime.sub_idx:
                                idx_list = DeviceRuntime.objects.filter(device=device, prefix=dp.prefix).values_list('sub_idx', flat=True).distinct()
                                # idx_list = list(set(device_runtimes.values_list('sub_idx', flat=True)))
                                for idx in idx_list:
                                    last_idx_runtime = DeviceRuntime.objects.filter(device=device, prefix=dp.prefix, sub_idx=idx
                                                                                    ).order_by('-created_at').first()
                                    # last_idx_runtime = device_runtimes.filter(sub_idx=idx).last()
                                    cls.generate_runtime(last_idx_runtime, params)

                            else:
                                cls.generate_runtime(last_runtime, params)
        except Exception as e:
            # 捕捉所有异常，不影响后续操作
            logging.error(f"运行'设备累计值参数统计'任务, 设备的运行时间统计失败，项目ID：{device.project_id}, {e.__str__()}")
