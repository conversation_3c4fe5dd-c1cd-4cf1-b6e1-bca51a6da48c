import json

import requests
from django.contrib.contenttypes.models import ContentType
from django.db.models import Q
from rest_framework import viewsets, status
from rest_framework.response import Response

from saian_api.devdefine.serializers import (
    DeviceTypeSerializer,
    DevicePrototypeSerializer,
    AttributePrototypeSerializer,
    AttributeTypeSerializer,
    AttributeInLinkSerializer,
    WebDeviceAttributeSerializer, AttributeTypeSerializerV5
)
from saian_api.device.models import Device
from saian_api.utils.sy_jsonrenderer import SyJ<PERSON><PERSON>ender
from .models import DeviceType, DevicePrototype, AttributeType, AttributePrototype
from ..group.models import GroupDevice, GroupAction
from ..terminal.models import TerminalAttribute, Terminal
from ..utils.db.Convert import Convert
from ..utils.httpapi import DOMAIN

# Create your views here.
class DeviceTypeViewSet(viewsets.ModelViewSet):
    queryset = DeviceType.objects.filter(parent=None).order_by('id')
    serializer_class = DeviceTypeSerializer
    renderer_classes = (SyJSONRender,)

    def list(self, request, *args, **kwargs):
        dts = super().list(request, *args, **kwargs).data

        dt_fields = []
        for dt in dts['results']:
            dt_obj = DeviceType.objects.get(pk=dt['id'])
            device_count = Device.objects.filter(
                project_id=request.user['project_id'],
                device_type_id__in=dt_obj.children.values_list('id', flat=True)
            ).count()

            tmp_fields = {
                'id': dt_obj.id,
                'icon': dt_obj.icon,
                'uni_name': dt_obj.uni_name,
                'name': dt_obj.name,
                'created_at': dt_obj.created_at,
                'device_count': device_count
            }

            sub_types_fields = []

            for sub_type in dt_obj.filter_sub_types(request.user['project_id']):
                sub_type_fields = {
                    'id': sub_type.id,
                    'icon': sub_type.icon,
                    'uni_name': sub_type.uni_name,
                    'name': sub_type.name,
                    'created_at': sub_type.created_at
                }
                sub_types_fields.append(sub_type_fields)

            tmp_fields['sub_types'] = sub_types_fields

            dt_fields.append(tmp_fields)

        res_data = {
            'device_types': dt_fields
        }

        return Response(res_data)

    def retrieve(self, request, *args, **kwargs):
        dt = super().retrieve(request, *args, **kwargs).data

        dt_obj = DeviceType.objects.get(pk=dt['id'])

        sub_types = []
        for sub_type in dt_obj.filter_sub_types(request.user['project_id']):
            sub_type_fields = {
                'id': sub_type.id,
                'icon': sub_type.icon,
                'name': sub_type.name,
                'uni_name': sub_type.uni_name,
                'created_at': sub_type.created_at
            }

            sub_types.append(sub_type_fields)

        dt_fields = {
            'id': dt['id'],
            'icon': dt['icon'],
            'name': dt['name'],
            'uni_name': dt['uni_name'],
            'created_at': dt['created_at'],
            'sub_types': sub_types
        }

        res_data = {
            'device_type': dt_fields
        }

        return Response(res_data)


class DevicePrototypeViewSet(viewsets.ModelViewSet):
    serializer_class = DevicePrototypeSerializer
    renderer_classes = (SyJSONRender,)
    ordering = ("-created_at",)

    def get_queryset(self):
        # v1 - device，v5 - terminal
        if 'v5' in self.request.path:
            dp_ids = Terminal.objects.filter(show_en=True).values_list('device_prototype_id', flat=True)
        else:
            dp_ids = Device.objects.filter(project_id=self.request.user['project_id']).values_list('device_prototype_id', flat=True)
        queryset = DevicePrototype.objects.filter(id__in=dp_ids).order_by('device_type_id')

        dt_id = self.request.query_params.get('dt_id', None)
        if dt_id is not None:
            queryset = queryset.filter(device_type_id=dt_id)

        search = self.request.query_params.get('search', None)
        if search is not None:
            queryset = queryset.filter(Q(name__icontains=search) | Q(uni_name__icontains=search))

        return queryset.order_by(Convert('name', 'gbk'))

    def list(self, request, *args, **kwargs):
        dps = super().list(request, *args, **kwargs).data

        res_data = {
            'count': dps['count'],
            'device_prototypes': dps['results']
        }

        return Response(res_data)

class AttributeTypeViewV5(viewsets.ModelViewSet):
    serializer_class = AttributeTypeSerializerV5

    def get_queryset(self):
        queryset = AttributeType.objects.filter(hidden=False)

        dp_id = self.request.query_params.get('dp_id', None)
        if dp_id is not None:
            queryset = queryset.filter(device_prototype_id=dp_id)

        search = self.request.query_params.get('search', None)
        if search is not None:
            queryset = queryset.filter(name__icontains=search)

        return queryset.order_by('seq')

    def list(self, request, *args, **kwargs):
        data = super().list(request, *args, **kwargs).data

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'attribute_types': data['results']
            },
            'total': data['count']
        })

class AttributeTypeViewSet(viewsets.ModelViewSet):
    serializer_class = AttributeTypeSerializer
    renderer_classes = (SyJSONRender,)
    ordering = ("seq",)

    def get_queryset(self):
        queryset = AttributeType.objects.filter(hidden=False)

        device_id = self.request.query_params.get('device_id', None)
        in_add = self.request.query_params.get('in_add', None)
        dp_id = self.request.query_params.get('dp_id', None)

        if device_id is not None:
            device = Device.objects.get(pk=device_id)
            queryset = AttributeType.objects.filter(
                hidden=False,
                device_prototype_id=device.device_prototype_id
            )
            if in_add is not None:
                queryset = queryset.filter(in_add=in_add)

        if dp_id is not None:
            queryset = AttributeType.objects.filter(hidden=False, device_prototype_id=dp_id)

        return queryset

    def list(self, request, *args, **kwargs):
        at = super().list(request, *args, **kwargs).data

        res_data = {
            'count': at['count'],
            'attribute_types': at['results']
        }

        return Response(res_data)


class AttributePrototypeViewSet(viewsets.ModelViewSet):
    serializer_class = AttributePrototypeSerializer
    renderer_classes = (SyJSONRender,)
    ordering = ("seq",)

    def get_queryset(self):
        queryset = AttributePrototype.objects.filter(hidden=False)
        dp_ids = self.request.query_params.get('dp_ids', None)
        dt = self.request.query_params.get('dt', None)
        mac = self.request.query_params.get('mac', None)
        device_id = self.request.query_params.get('device_id', None)
        read_only = self.request.query_params.get('read_only', None)
        group_id = self.request.query_params.get('group_id', None)
        search = self.request.query_params.get('search', None)
        exclude_ids = self.request.query_params.get('exclude_ids', None)

        # # 终端分组，查找终端相关的 attribute_prototypes
        # if group_id is not None:
        #     terminal_type_id = ContentType.objects.get(model='terminal').id
        #     gts = GroupDevice.objects.filter(content_type_id=terminal_type_id, group_id=group_id)
        #     terminal_ids = [gt.object_id for gt in gts]
        #     ta_aps = TerminalAttribute.objects.filter(terminal_id__in=terminal_ids).values_list('attribute_prototype_id', flat=True)
        #     queryset = AttributePrototype.objects.filter(id__in=ta_aps, hidden=False)

        if dp_ids is not None:
            queryset = queryset.filter(device_prototype_id__in=json.loads(dp_ids))

        if dt is not None and dt != '':
            queryset = queryset.filter(data_type__in=dt.split(','))

        if mac is not None:
            device = Device.objects.get(mac=mac)
            queryset = queryset.filter(device_prototype_id=device.device_prototype_id)

        if device_id is not None:
            device = Device.objects.get(pk=device_id)
            queryset = queryset.filter(device_prototype_id=device.device_prototype_id)

        if read_only is not None:
            queryset = queryset.filter(read_only=read_only)

        if search is not None:
            queryset = queryset.filter(Q(identifier__icontains=search) | Q(name__icontains=search))

        if exclude_ids is not None:
            queryset = queryset.exclude(id__in=exclude_ids.split(','))

        return queryset

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()

        group_id = request.query_params.get('group_id')
        group_action_id = request.query_params.get('ga_id')
        total_selected = None
        if group_action_id is not None or group_id is not None:
            # 有 ga_id，则取分组操作对应的分组，否则取 group_id
            group_action = GroupAction.objects.filter(pk=group_action_id).last()
            if group_action is not None:
                group_id = group_action.group_id

            group_objects = GroupDevice.objects.filter(group_id=group_id)
            if group_objects.exists():
                first_object = group_objects[0].content_object
                if isinstance(first_object, Device):
                    # 设备分组，每个成员的da都是一样的，只需取第一个设备的da
                    # object_attr = first_object.deviceattribute_set.all().values_list('attribute_prototype_id', flat=True)
                    object_attr = AttributePrototype.objects.filter(device_prototype_id=first_object.device_prototype_id
                                                                    ).values_list('id', flat=True)
                    queryset = queryset.filter(id__in=object_attr)
                    # 分组操作选中的ap, 如果 group_action 不为 None，则把选择的排在前面
                    if group_action is not None:
                        ga_attrs = queryset.filter(id__in=group_action.actionattribute_set.all()
                                                   .filter(content_type=ContentType.objects.get_for_model(AttributePrototype))
                                                   .values_list('object_id', flat=True))
                        total_selected = ga_attrs.count()

                        unselect = queryset.exclude(id__in=ga_attrs.values_list('id', flat=True))
                        queryset = ga_attrs.union(unselect)

                elif isinstance(first_object, Terminal):
                    # 终端分组，ta 可能都不一样，需要取所有终端的ta
                    group_terminals = group_objects.filter(content_type=ContentType.objects.get_for_model(Terminal)).values_list('object_id',
                                                                                                                                 flat=True)
                    ta_aps = TerminalAttribute.objects.filter(terminal_id__in=group_terminals).values_list('attribute_prototype_id', flat=True)
                    queryset = queryset.filter(id__in=ta_aps)
                    # 分组操作选中的ap，如果 group_action 不为 None，则把选择的排在前面
                    if group_action is not None:
                        ga_attrs = queryset.filter(id__in=group_action.actionattribute_set.all()
                                                   .filter(content_type=ContentType.objects.get_for_model(AttributePrototype))
                                                   .values_list('object_id', flat=True))
                        total_selected = ga_attrs.count()

                        unselect = queryset.exclude(id__in=ga_attrs.values_list('id', flat=True))
                        queryset = ga_attrs.union(unselect)

        # ap = super().list(request, *args, **kwargs).data
        count = queryset.count()
        per_page = request.query_params.get('per_page', None)
        page = request.query_params.get('page', '1')
        if per_page is not None and per_page.isdigit() and page.isdigit():
            per_page = int(per_page)
            page = int(page)
            begin = (page - 1) * per_page
            end = page * per_page
            queryset = queryset[begin: end]
        # queryset = self.paginate_queryset(queryset)
        ap = self.serializer_class(queryset, many=True).data

        res_data = {
            'count': count,
            'attribute_prototypes': ap,
            'total_selected': total_selected
        }

        return Response(res_data)


class AttributeInLinkViewSet(viewsets.GenericViewSet):
    renderer_classes = (SyJSONRender,)

    def get_queryset(self):
        serializer = AttributeInLinkSerializer(data=self.request.data)
        serializer.is_valid(raise_exception=True)

        # device_type_id没用到
        dt_id = self.request.query_params.get('device_type_id', None)
        dp_id = self.request.query_params.get('device_prototype_id', None)

        queryset = AttributePrototype.objects.filter(hidden=False)

        if dp_id is not None:
            queryset = queryset.filter(device_prototype_id=dp_id)

        return queryset

    def list(self, request):
        queryset = self.get_queryset()
        aps = self.paginate_queryset(queryset)

        results = []

        for attr in aps:
            ap_fields = {
                'id': attr.id,
                'name': attr.name,
                'identifier': attr.identifier,
                'data_type': attr.data_type,
                'in_crement': attr.in_crement,
                'min_value': attr.min_value,
                'max_value': attr.max_value,
                'options': attr.options,
                'default_value': attr.default_value,
                'unit': attr.unit,
                'pre_cision': attr.pre_cision,
                'remark': attr.remark,
                'value': attr.default_value
            }

            results.append(ap_fields)

        res_data = {
            'count': queryset.count(),
            'attributes': results
        }

        return Response(res_data)


# 根据设备id获取配置分类
class DeviceAttributeTypeViewSet(viewsets.ModelViewSet):
    renderer_classes = (SyJSONRender,)
    serializer_class = AttributeTypeSerializer

    def get_queryset(self):
        pk = self.kwargs['pk']
        protoid = Device.objects.get(pk=pk).device_prototype_id
        return AttributeType.objects.filter(hidden=False, device_prototype_id=protoid)

    def list(self, request, *args, **kwargs):
        try:
            serializer = AttributeTypeSerializer(self.get_queryset(), many=True, context={'read_only': True})
            # results = super().list(request, *args, **kwargs).data.get('results')
            return Response({'attribute_types': serializer.data})

        # 找不到设备，则向旧接口请求数据
        except Device.DoesNotExist:
            pk = self.kwargs['pk']
            url = f'{DOMAIN}/saianapi/v1/devices/{pk}/attribute_types?in_add=1'
            headers = {'Authorization': self.request.headers.get('Authorization')}
            r = requests.get(url, headers=headers)
            if r.status_code == 200:
                return Response(data=r.json().get('data'))
            else:
                return Response(status=r.status_code, data={'status': r.status_code})


# 根据配置分类获取设备配置项
class WebDeviceAttributeViewSet(viewsets.ModelViewSet):
    renderer_classes = (SyJSONRender,)
    serializer_class = WebDeviceAttributeSerializer

    def get_queryset(self):
        pk = self.kwargs['pk']
        device_id = self.request.query_params.get('device_id')
        device_protoid = Device.objects.get(pk=device_id).device_prototype_id
        return AttributePrototype.objects.filter(attribute_type_id=pk, device_prototype_id=device_protoid, read_only=False).order_by('id')

    def list(self, request, *args, **kwargs):
        results = super().list(request, *args, **kwargs).data.get('results')
        attribute_type_id = kwargs.get('pk')
        device_id = request.query_params.get('device_id')
        device_nick_name = Device.objects.get(pk=device_id).nick_name
        return Response({
            'device': {'id': device_id, 'nick_name': device_nick_name},
            'device_attributes': results,
            'id': attribute_type_id,
            'name': results[0].get('name') if len(results) else ''
        })
