import calendar
import datetime
import logging
import random

from django.core.management import BaseCommand
from django.db.models import Sum

from saian_api.dimension.models import DimensionHourlyStat, DimensionDailyStat, DimensionMonthlyStat
from saian_api.scheduledtask.utils import get_projects, set_global_db

class Command(BaseCommand):
    help = "冷源能效日统计"

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目ID列表
        projects = get_projects(self, options)
        now = datetime.datetime.now()

        for project_id in projects:
            set_global_db(project_id)

            if now.minute == 1:
                self.handle_dhs(now - datetime.timedelta(minutes=5))

            self.handle_dhs(now)

    def handle_dhs(self, dt):
        created_at = dt.strftime('%Y-%m-%d %H:59:59')
        refer_created_at = (dt - datetime.timedelta(hours=24)).strftime('%Y-%m-%d %H:59:59')

        ratio = dt.minute / 60

        if ratio > 0.9:
            ratio = random.uniform(0.95, 1.05)

        target_dimension = 3
        target_da = 3

        target_dhs = DimensionHourlyStat.objects.filter(dimension_attribute_id=target_da, created_at=created_at).last()
        refer_dhs = DimensionHourlyStat.objects.filter(dimension_attribute_id=target_da, created_at=refer_created_at).last()

        if refer_dhs is not None:
            if target_dhs is not None:
                target_dhs.avg = float(refer_dhs.avg) * ratio
                target_dhs.save()
            else:
                DimensionHourlyStat.objects.create(dimension_id=target_dimension,
                                                   dimension_attribute_id=target_da,
                                                   avg=float(refer_dhs.avg) * ratio,
                                                   min=refer_dhs.min,
                                                   max=refer_dhs.max,
                                                   created_at=created_at)





