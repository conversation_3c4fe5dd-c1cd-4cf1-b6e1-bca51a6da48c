from django.urls import path

from .views import (
    DeviceTypeViewSet,
    DevicePrototypeViewSet,
    AttributeTypeViewSet,
    AttributePrototypeViewSet,
    AttributeInLinkViewSet,
    DeviceAttributeTypeViewSet,
    WebDeviceAttributeViewSet, AttributeTypeViewV5
)

devicetype_list = DeviceTypeViewSet.as_view({
    'get': 'list'
})

devicetype_detail = DeviceTypeViewSet.as_view({
    'get': 'retrieve'
})

deviceprototype_list = DevicePrototypeViewSet.as_view({
    'get': 'list'
})

deviceprototype_detail = DevicePrototypeViewSet.as_view({
    'get': 'retrieve'
})

attributetype_list = AttributeTypeViewSet.as_view({
    'get': 'list'
})

attributetype_detail = AttributeTypeViewSet.as_view({
    'get': 'retrieve'
})

attributeprototype_list = AttributePrototypeViewSet.as_view({
    'get': 'list'
})

attributeprototype_detail = AttributePrototypeViewSet.as_view({
    'get': 'retrieve'
})

link_list = AttributeInLinkViewSet.as_view({
    'get': 'list'
})

device_attributetype_list = DeviceAttributeTypeViewSet.as_view({
    'get': 'list'
})

web_device_attribute_type_list = WebDeviceAttributeViewSet.as_view({
    'get': 'list'
})

attribute_types = AttributeTypeViewV5.as_view({
    'get': 'list'
})

urlpatterns = [
    path('saianapi/v1/device_types', devicetype_list, name='devicetype-list'),
    path('saianapi/v1/device_types/<int:pk>', devicetype_detail, name='devicetype-detail'),
    path('saianapi/v1/device_prototypes', deviceprototype_list, name='deviceprototype-list'),
    path('saianapi/v5/device_prototypes', deviceprototype_list, name='deviceprototype-list-v5'),
    path('saianapi/v1/device_prototypes/<int:pk>', deviceprototype_detail, name='deviceprototype-detail'),
    path('saianapi/v1/attribute_types', attributetype_list, name='attributetype-list'),
    path('saianapi/v5/attribute_types', attribute_types, name='attribute-types'),
    path('saianapi/v1/attribute_types/<int:pk>', attributetype_detail, name='attributetype-detail'),
    path('saianapi/v1/attribute_prototypes', attributeprototype_list, name='attributeprototype-list'),
    path('saianapi/v1/attribute_prototypes/<int:pk>', attributeprototype_detail, name='attributeprototype-detail'),
    path('saianapi/v1/attribute_in_links', link_list, name='link-list'),

    # 根据设备id获取配置分类
    path('saianapi/v1/devices/<int:pk>/attribute_types', device_attributetype_list, name='device-attributetype-list'),
    # 根据配置分类获取设备配置项
    path('saianapi/v1/attribute_types/<int:pk>/web_device_attributes', web_device_attribute_type_list, name='web-device-attribute-type-list')
]
