import datetime
import logging
import traceback
from collections import defaultdict

from django.core.management import CommandError, BaseCommand

from saian_api.device.models import Device, DaSnapshot
from saian_api.scheduledtask.utils import set_global_db, get_projects
from saian_api.utils.db.Redis import RedisHelper


class Command(BaseCommand):
    help = "定时快照任务"

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目ID列表
        projects = get_projects(self, options)
        now = datetime.datetime.now()
        snapped_at = now.replace(second=0, microsecond=0)
        for project_id in projects:
            try:
                # 设置全局数据库
                set_global_db(project_id)

                minute_ago = now - datetime.timedelta(minutes=1)
                minute_ago = minute_ago.replace(second=0, microsecond=0)

                ap_cache = {}
                # 离线设备，已解除绑定设备不做处理
                devices = list(Device.objects.filter(online=True, project_id__isnull=False, updated_at__gte=minute_ago))
                device_ids = [device.id for device in devices]
                # 查询所有相关的ap_id
                for dp_id in set(device.device_prototype_id for device in devices):
                    seg = now.minute
                    if seg == 0:
                        seg = 60
                    ap_cache[dp_id] = RedisHelper.get_members(project_id, f'recently_updated_{seg - 1:02}:{dp_id}', True)

                # 查询所有设备的已有最新快照
                redis_das = defaultdict(list)
                snap_names = []
                da_names = []

                # 值发生变化，需要新创建的快照
                new_snapshots = []

                # 从缓存查询设备参数
                for device in devices:
                    dp_id = device.device_prototype_id
                    ap_ids = ap_cache.get(dp_id, None)
                    if ap_ids:
                        da_names += [f'device_attribute:{device.id}_{ap_id}' for ap_id in ap_ids]

                # 从Redis查询所有da
                cache_das_raw = RedisHelper.get_multi_value(project_id, da_names, False)
                # cache_das = filter(lambda x: x is not None and x.updated_at >= minute_ago, cache_das_raw)
                cache_das = [x for x in cache_das_raw if x and x.updated_at >= minute_ago]

                # cache_das = sorted(cache_das, key=lambda obj: obj.device_id)
                for da in cache_das:
                    redis_das[da.device_id].append(da)
                    snap_names.append(f'snap:{da.device_id}_{da.attribute_prototype_id}')

                redis_das = dict(redis_das)

                # 从缓存查询快照
                snap_das = DaSnapshot.objects.query_object_by_names(project_id, snap_names)

                # 处理每个设备的参数
                for device in devices:
                    if device.id not in redis_das:
                        continue
                    results = self.process_device(redis_das[device.id], snap_das, snapped_at)
                    new_snapshots += results

                DaSnapshot.objects.bulk_create(new_snapshots)

                # 查询上面创建的快照
                snap_aps = set(snap.attribute_prototype_id for snap in new_snapshots)
                create_snapshots = list(DaSnapshot.objects.filter(device_id__in=device_ids,
                                                                  attribute_prototype_id__in=snap_aps,
                                                                  snapped_at__gte=minute_ago))

                # 保存快照到缓存
                redis_snapshot = {}
                for ds in create_snapshots:
                    name = f'snap:{ds.device_id}_{ds.attribute_prototype_id}'
                    redis_snapshot[name] = ds.value
                RedisHelper.set_multi_value(project_id, redis_snapshot, True, ex=3600)

                logging.info(f'项目: {project_id}, dt: {datetime.datetime.now()}, new_snapshots: {len(new_snapshots)}')

            except CommandError:
                self.stderr.write(f"运行'冷源能效小时统计'任务失败，项目ID：{project_id}，命令参数不合法！", ending='\n')
                continue
            except Exception as e:
                self.stderr.write(f"运行'冷源能效小时统计'任务失败，项目ID：{project_id}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
                continue

    @classmethod
    def process_device(cls, redis_das, snap_das, snapped_at):
        """处理单个设备的所有参数"""
        new_snapshots = []

        for da in redis_das:
            key = f"{da.device_id}_{da.attribute_prototype_id}"
            snap = snap_das.get(key, None)
            if not snap or str(snap) != str(da.value):
                new_snapshots.append(DaSnapshot(device_id=da.device_id,
                                                attribute_prototype_id=da.attribute_prototype_id,
                                                value=da.value,
                                                snapped_at=snapped_at))

        return new_snapshots
