import logging
import threading

from django.utils.deprecation import MiddlewareMixin


local = threading.local()
class RequestLogFilter(logging.Filter):
    """
        日志过滤器，将当前请求线程的 request 信息保存到日志的 record 上下文
        record 带有 formatter 需要的信息。
    """
    def filter(self, record):
        record.project = getattr(local, 'project', "none")
        record.path = getattr(local, 'path', "none")
        record.identity = getattr(local, 'identity', "none")
        record.method = getattr(local, 'method', "none")
        record.data = getattr(local, 'data', 'none')
        return True

class RequestLogMiddleware(MiddlewareMixin):
    """
        将 request 的信息记录在当前的请求线程上。
    """
    def process_request(self, request):
        authorization = request.headers.get("Authorization", None)
        identity = None
        project_id = None

        if authorization is not None and 'project=' in authorization:
            try:
                project_id = authorization.split("=")[-1]
                identity = authorization.split(",")[1].split('=')[-1]
            except Exception as e:
                logging.error(f"request log middleware error: {e.__str__()}")
                pass
        else:
            project_id = request.headers.get('project')

        local.path = request.path
        local.project = project_id
        local.identity = identity
        local.data = str(request.body)
        local.method = request.method
