from django.urls import include, path

from .views import DatapointsViewSet, DeviceCtrlsViewSet, DeviceViewSet

datapoints = DatapointsViewSet.as_view({
    'get': 'list'
})

device_ctrls = DeviceCtrlsViewSet.as_view({
    'post': 'create'
})

devices = DeviceViewSet.as_view({
    'get': 'list'
})


urlpatterns = [
    path('entapi/v1/devices/<int:device_id>/datapoints', datapoints, name='datapoints'),
    path('entapi/v1/device_ctrls', device_ctrls, name='device_ctrls'),
    path('entapi/v1/devices', devices, name='devices'),
]