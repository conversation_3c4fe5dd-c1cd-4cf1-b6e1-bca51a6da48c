# Generated by Django 3.2.8 on 2021-12-30 15:16

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='DeviceType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('icon', models.CharField(max_length=255, null=True)),
                ('uni_name', models.CharField(max_length=255)),
                ('is_default', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('parent', models.ForeignKey(blank=True, db_column='pid', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='devdefine.devicetype')),
            ],
            options={
                'db_table': 'device_types',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DevicePrototype',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('m_name', models.CharField(max_length=255, null=True)),
                ('uni_name', models.CharField(max_length=255)),
                ('skip_config', models.BooleanField(default=False)),
                ('content', models.TextField(null=True)),
                ('web_content', models.TextField(null=True)),
                ('prefix', models.CharField(max_length=255, null=True)),
                ('dashboard_attres', models.TextField(null=True)),
                ('detail_attres', models.TextField(null=True)),
                ('icons', models.CharField(max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('device_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='devdefine.devicetype')),
                ('parent', models.ForeignKey(blank=True, db_column='pid', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='devdefine.deviceprototype')),
            ],
            options={
                'db_table': 'device_prototypes',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='AttributeType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('seq', models.IntegerField()),
                ('hidden', models.BooleanField(default=False)),
                ('uni_name', models.CharField(max_length=255)),
                ('in_add', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('device_prototype', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='devdefine.deviceprototype')),
            ],
            options={
                'db_table': 'attribute_types',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='AttributePrototype',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('identifier', models.CharField(max_length=255)),
                ('remark', models.CharField(max_length=255, null=True)),
                ('read_only', models.BooleanField(default=True)),
                ('data_type', models.IntegerField()),
                ('pre_cision', models.CharField(max_length=255, null=True)),
                ('in_crement', models.CharField(max_length=255, null=True)),
                ('min_value', models.CharField(max_length=255, null=True)),
                ('max_value', models.CharField(max_length=255, null=True)),
                ('options', models.TextField(null=True)),
                ('length', models.IntegerField()),
                ('icon', models.CharField(max_length=255, null=True)),
                ('unit', models.CharField(max_length=255, null=True)),
                ('default_value', models.CharField(max_length=255, null=True)),
                ('seq', models.IntegerField()),
                ('send_immediate', models.BooleanField(default=False)),
                ('is_key', models.BooleanField(default=False)),
                ('can_debug', models.BooleanField(default=False)),
                ('hidden', models.BooleanField(default=False)),
                ('in_add', models.BooleanField(default=False)),
                ('show_in_list', models.BooleanField(default=False)),
                ('formula', models.CharField(max_length=255, null=True)),
                ('is_cum', models.BooleanField(default=False)),
                ('label', models.CharField(max_length=255)),
                ('do_export', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('attribute_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='devdefine.attributetype')),
                ('device_prototype', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='devdefine.deviceprototype')),
            ],
            options={
                'db_table': 'attribute_prototypes',
                'ordering': ['-created_at'],
            },
        ),
    ]
