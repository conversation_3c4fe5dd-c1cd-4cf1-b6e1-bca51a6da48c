import datetime
import logging
import traceback

from django.core.management import CommandError
from django.core.management.base import BaseCommand
from django.db.models import Sum
from datetime import timedelta
import numpy as np

from saian_api.devdefine.models import AttributePrototype
from saian_api.device.models import Device, <PERSON>ceAttribute, ParamRecord, DeviceEvent
from saian_api.issue.models import DeviceIssue
from saian_api.message.models import Message
from saian_api.notifi.models import Notification
from saian_api.report.models import DeviceHourlyStat
from saian_api.scheduledtask.utils import get_projects, set_global_db
from saian_api.terminal.models import Terminal


class Command(BaseCommand):
    help = '每小时检测分体空调能耗异常（均值+标准差），每分钟检查水表用水异常'
    now = datetime.datetime.now()

    def add_arguments(self, parser):
        parser.add_argument(
            '--k', type=float, default=2.0,
            help='阈值系数（默认 2.0，即 均值 + 2×标准差）'
        )

    @classmethod
    def get_last_not_resolve_alarm(cls, device_id, name):
        return DeviceIssue.objects.filter(device_id=device_id, issue_type=20, is_solved=False, name=name).first()

    def handle(self, *args, **options):
        project_id = 91
        k = options['k']

        try:
            set_global_db(project_id)
            logging.info(f'now: {self.now}, minute: {self.now.minute}')
            if self.now.minute == 10:
                self.check_split_ac(k)
            self.check_all_water_meters()

        except CommandError:
            self.stderr.write(
                f"运行'能耗异常检测'任务失败，项目ID：{project_id}，命令参数不合法！\n"
            )
        except Exception:
            self.stderr.write(
                f"运行'能耗异常检测'任务失败，项目ID：{project_id}\n"
            )
            self.stderr.write(traceback.format_exc(), ending='')

    @classmethod
    def check_split_ac(cls, k):
        end_time = cls.now.replace(minute=0, second=0, microsecond=0)
        start_time = end_time - timedelta(hours=1)

        issue_name = f"空调能耗过高"

        updated_devices = []
        updated_issue = []

        devices = Device.objects.filter(
            device_prototype_id=489,  # 分体空调
            online=True
        )
        device_map = {device.id: device for device in devices}
        device_ids = list(device_map.keys())

        energy_records = DeviceHourlyStat.objects.filter(
            device_id__in=device_ids,
            identifier='power_cons',
            created_at__gte=start_time,
            created_at__lt=end_time,
            avg__gt=0
        ).values('device_id').annotate(total_energy=Sum('avg'))

        skip_calc = False

        if not energy_records:
            logging.warning(f'[项目 91] 无能耗数据')
            skip_calc = True

        energy_values = [r['total_energy'] for r in energy_records if r['total_energy'] is not None]

        if not energy_values:
            logging.warning(f'[项目 91] 无有效能耗数据')
            skip_calc = True

        if len(energy_values) < 3:
            logging.warning(f'[项目 91] 数据点不足，跳过检测')
            skip_calc = True

        if skip_calc:
            # 数据不足，解除所有报警

            for alarm in DeviceIssue.objects.filter(device_id__in=device_ids, name=issue_name, is_solved=False, issue_type=20):
                alarm.updated_at = cls.now
                alarm.is_solved = True
                alarm.save()

                if not DeviceIssue.objects.filter(device_id=alarm.device_id, is_solved=False, issue_type=20).exists():
                    device = device_map.get(alarm.device_id)
                    device.in_alarm = False
                    device.save()

                    Terminal.objects.filter(device_id=device.id, in_alarm=True).update(in_alarm=False)

            Device.objects.bulk_update(updated_devices, ['in_alarm'])
            DeviceIssue.objects.bulk_update(updated_issue, ['created_at', 'updated_at', 'display_name', 'is_solved'])

            return

        # 均值 & 标准差
        mean_val = np.mean(energy_values)
        std_val = np.std(energy_values)

        if std_val == 0:
            logging.warning(f'[项目 91] 标准差为 0，跳过检测')
            return

        threshold = mean_val + k * std_val

        # 找出超过阈值的设备
        abnormal_devices = [
            r for r in energy_records if r['total_energy'] > threshold
        ]
        abnormal_device_ids = []

        # 创建或更新"空调能耗过高"的报警
        for record in abnormal_devices:
            device_id = record['device_id']
            energy = record['total_energy']

            abnormal_device_ids.append(device_id)

            device = device_map.get(device_id)
            last_issue = cls.get_last_not_resolve_alarm(device_id, issue_name)
            display_name = f"分体空调能耗过高，能耗值: {energy:.2f} kWh，阈值: {threshold:.2f} kWh"
            if not last_issue:
                last_issue = DeviceIssue.objects.create(
                    project_id=device.project_id,
                    device_id=device_id,
                    name=issue_name,
                    display_name=display_name,
                    is_solved=False,
                    issue_type=20,
                    device_type_id=device.device_type_id
                )
                last_issue.created_at = start_time.strftime('%Y-%m-%d %H:59:59')
                last_issue.save()

                # 添加报警消息
                Message.objects.alarm(device, display_name)
                data = {
                    'content': f'设备报警: 设备【{device.id}-{device.nick_name}】报警: {display_name}',
                    'device_id': device.id,
                    'device_nickname': device.nick_name,
                    'glossary': display_name
                }
                Notification.objects.notify(9, device, data)
                # 更新终端状态
                Terminal.objects.update_by_issue_add(20, device, display_name)
            else:
                last_issue.updated_at = cls.now
                last_issue.created_at = start_time.strftime('%Y-%m-%d %H:59:59')
                last_issue.display_name = display_name
                updated_issue.append(last_issue)

            device.in_alarm = True
            updated_devices.append(device)
            logging.warning(
                f'[项目 91] 设备 {device_id}-{device.nick_name} 异常，能耗 {energy:.2f} kWh，阈值 {threshold:.2f} kWh'
            )

        # 消除正常设备的"空调能耗过高"报警
        normal_device_ids = list(set(device_ids) - set(abnormal_device_ids))
        for alarm in DeviceIssue.objects.filter(device_id__in=normal_device_ids, name=issue_name, is_solved=False, issue_type=20):
            alarm.updated_at = cls.now
            alarm.is_solved = True
            alarm.save()

            if not DeviceIssue.objects.filter(device_id=alarm.device_id, is_solved=False, issue_type=20).exists():
                device = device_map.get(alarm.device_id)
                device.in_alarm = False
                device.save()

                Terminal.objects.filter(device_id=device.id, in_alarm=True).update(in_alarm=False)

        Device.objects.bulk_update(updated_devices, ['in_alarm'])
        DeviceIssue.objects.bulk_update(updated_issue, ['created_at', 'updated_at', 'display_name', 'is_solved'])

        logging.warning(f'[项目 91] 能耗异常检测完成')

    @classmethod
    def get_water_step(cls, records):
        """
        根据历史记录自动推断水表平均增长, 一般每个水表每次用水量增长都是同一个数。例如"01楼水表"每次增长0.1, "审计局饭堂水表"每次增长1.0
        records: [(timestamp, value), ...] 已按时间排序
        return: step
        """
        deltas = []
        for i in range(1, len(records)):
            try:
                diff = float(records[i][1]) - float(records[i - 1][1])
                if diff > 0:
                    deltas.append(diff)
            except ValueError:
                pass
        if not deltas:
            return None
        # 使用中位数
        step = np.median(deltas)
        return step

    @classmethod
    def detect_water_meter_anomalies(cls, device_id, records, tol=0.25, large_factor=2):
        """
        检测水表最新一次上报是否异常：
        - 连续小步长用水（可能忘记关水，漏水）
        - 单次大量用水
        device_id: 水表ID
        records: [(timestamp, value), ...] 已按时间排序
        tol: 步长容差
        large_factor: 超过步长多少倍判定单次大量用水
        return: (alerts, continuous_minutes)
        """
        alerts = []

        if len(records) < 2:
            return alerts

        step = cls.get_water_step(records)
        if not step:
            return alerts

        # 最新一次差分
        ts, val = records[-1]
        prev_val = records[-2][1]
        diff = float(val) - float(prev_val)
        print(f'device: {device_id}, step: {step}, diff: {diff}')

        continuous_count = 0
        if diff > 0 and abs(diff - step) <= tol:
            # 从最新往前数连续的标准步长
            continuous_count = 1
            for i in range(len(records) - 2, 0, -1):
                d = float(records[i][1]) - float(records[i - 1][1])
                if d != 0 and abs(d - step) <= tol:
                    continuous_count += 1
                else:
                    break

            if continuous_count >= 3:
                alerts.append((ts, f"水表连续{continuous_count * 5}分钟用水"))

        elif diff > step * large_factor:
            alerts.append((ts, f"水表五分钟内大量用水"))

        return alerts

    @classmethod
    def check_all_water_meters(cls):
        dp_id = 535
        identifier = 'Meter_Water_Cons'
        ap = AttributePrototype.objects.get(device_prototype_id=dp_id, identifier=identifier)
        devices = Device.objects.filter(device_prototype_id=dp_id)
        device_ids = [device.id for device in devices]
        one_minute_ago = cls.now - datetime.timedelta(minutes=1)
        # recently_update_devices = list(DeviceAttribute.objects.filter(device_id__in=device_ids,
        #                                                               attribute_prototype_id=ap.id,
        #                                                               updated_at__gte=one_minute_ago).values_list('device_id', flat=True))
        # alarm_devices = [device.id for device in devices if device.in_alarm]
        device_map = {device.id: device for device in devices}

        issue_name = '水表用水异常'

        for device_id in [device.id for device in devices if device.updated_at >= one_minute_ago]:
            device = device_map.get(device_id)
            records = list(ParamRecord.objects.filter(device_id=device_id, identifier=identifier
                                                      ).order_by("created_at").values_list("created_at", "value"))
            device_alarms = cls.detect_water_meter_anomalies(device_id, records)
            if device_alarms:
                logging.warning(f'水表: {device.id}-{device.nick_name}用水异常, alarms: {device_alarms}')
                display_name = '，'.join([alarm[1] for alarm in device_alarms])
                last_issue = cls.get_last_not_resolve_alarm(device_id, issue_name)

                if not last_issue:
                    DeviceIssue.objects.create(
                        project_id=device.project_id,
                        device_id=device_id,
                        name=issue_name,
                        display_name=display_name,
                        is_solved=False,
                        issue_type=20,
                        device_type_id=device.device_type_id
                    )

                    # 添加报警消息
                    Message.objects.alarm(device, display_name)
                    data = {
                        'content': f'设备报警: 设备【{device.id}-{device.nick_name}】报警: {display_name}',
                        'device_id': device.id,
                        'device_nickname': device.nick_name,
                        'glossary': display_name
                    }
                    Notification.objects.notify(9, device, data)
                    # 更新终端状态
                    Terminal.objects.update_by_issue_add(20, device, display_name)

                else:
                    last_issue.updated_at = cls.now
                    last_issue.display_name = display_name
                    last_issue.save()

                device.in_alarm = True
                device.save()

            elif device.in_alarm:
                for alarm in DeviceIssue.objects.filter(device_id=device.id, name=issue_name, is_solved=False, issue_type=20):
                    alarm.updated_at = cls.now
                    alarm.is_solved = True
                    alarm.save()

                    if not DeviceIssue.objects.filter(device_id=device.id, is_solved=False, issue_type=20).exists():
                        device.in_alarm = False
                        device.save()

                        Terminal.objects.filter(device_id=device.id, in_alarm=True).update(in_alarm=False)
