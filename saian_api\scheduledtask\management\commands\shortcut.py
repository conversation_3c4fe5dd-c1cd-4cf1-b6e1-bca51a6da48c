"""
新建快捷操作表
"""
import traceback

from django.core.management import BaseCommand, CommandError

from saian_api.group.models import Shortcut, GroupAction
from saian_api.linkage.models import LinkageRule
from saian_api.project.models import Project
from saian_api.scheduledtask.utils import get_projects, set_global_db


class Command(BaseCommand):
    help = "添加快捷操作任务"

    # def add_arguments(self, parser):
    #     parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目列表
        # projects = get_projects(self, options)

        # for project_id in projects:
        project_id = 66
        try:
            set_global_db(project_id)
            project = Project.objects.get(pk=project_id)
            # shortcuts = Shortcut.objects.all()
            # delete_count = shortcuts.count()
            # if delete_count:
            #     shortcuts.delete()
            #     self.stdout.write(f'\t清除快捷操作{delete_count}条。', ending='\n')
            self.stdout.write(f"新增快捷操作: {project.name}", ending='\n')
            gas = GroupAction.objects.all()
            for ga in gas:
                if not Shortcut.objects.filter(name=ga.name, op_type=10, op_id=ga.id).exists():
                    Shortcut.objects.create(name=ga.name, op_type=10, op_id=ga.id, created_at=ga.created_at, updated_at=ga.updated_at)
            self.stdout.write(f'\t从分组操作新增快捷操作{gas.count()}条。', ending='\n')

            linkage_rules = LinkageRule.objects.all()
            for linkage_rule in linkage_rules:
                if not Shortcut.objects.filter(name=linkage_rule.name, op_type=20, op_id=linkage_rule.id).exists():
                    Shortcut.objects.create(name=linkage_rule.name, op_type=20, op_id=linkage_rule.id, remark=linkage_rule.remark,
                                            created_at=linkage_rule.created_at, updated_at=linkage_rule.updated_at)
            self.stdout.write(f'\t从联动操作新增快捷操作{linkage_rules.count()}条。', ending='\n')

        except CommandError:
            self.stderr.write(f"运行'添加快捷操作'任务失败，项目ID：{project_id}，命令参数不合法！", ending='\n')
            # continue
        except Exception as e:
            self.stderr.write(f"运行'添加快捷操作'任务失败，项目ID：{project_id}", ending='\n')
            self.stderr.write(traceback.format_exc(), ending='')
            # continue
