import base64
import hmac
import logging
import os
import datetime
from urllib.parse import quote
import calendar
from functools import reduce
import requests
from django.conf import settings
from django.contrib.auth.hashers import check_password, make_password
from django.db import transaction
from django.db.models import Q, Count
from django.shortcuts import get_object_or_404
from rest_framework import viewsets, status, exceptions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView

from saian_api.device.models import Device, DeviceAttribute
from saian_api.notifi.models import VerifiedCode
from saian_api.project.models import Project, WebMenu
from saian_api.user.models import WebRole, UserDevice, UserLog, UserProject, UserStat, PageUrl
from saian_api.user.serializers import WebUserInfoSerializer, WebMenuSerializer, WebRoleSerializer, UserLogSerializer
from saian_api.user.serializers import WebUserSerializer, UserSearchSerializer, UserDeviceSerializer
from saian_api.utils.httpapi.user import UserApi
# Create your views here.
from saian_api.utils.legacy_auth import <PERSON><PERSON><PERSON>
from saian_api.utils.sy_jsonrenderer import ResponseUtils, SyJSONRender
from saian_api.utils.utils import AuthUtils
from .models import WebUser, UserSearches
from ..devdefine.models import AttributePrototype
from ..dimension.models import DimensionUser, Dimension
from ..utils.db.Redis import RedisHelper
from ..utils.httpapi.image import ImageAPI
from ..utils.intapi_auth import IntapiAuth
from ..utils.inthttpapi import DOMAIN
from ..utils.inthttpapi.base import BaseAPI
from ..utils.tools import is_number, fill_element


class WebUserViewSet(viewsets.ModelViewSet):
    serializer_class = WebUserSerializer
    renderer_classes = (SyJSONRender,)

    def get_queryset(self):
        user_ids = list(UserProject.objects.values_list('web_user_id', flat=True))
        queryset = WebUser.objects.filter(id__in=user_ids, status=10).order_by('-id')

        search = self.request.query_params.get('search', None)

        if search is not None:
            queryset = queryset.filter(Q(username__icontains=search) | Q(name__icontains=search))

        return queryset

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        serializer = self.serializer_class(self.paginate_queryset(queryset), many=True)

        results = []

        for item in serializer.data:
            web_user = dict(item)
            user = WebUser.objects.get_executor_info(request, web_user['id'])

            web_user['mini_id'] = user.get('mini_userid', web_user['id'])
            web_user['mobile'] = user.get('mobile', None)
            web_user['mini_username'] = user.get('mini_username', web_user['name'])

            avatar = web_user.get('avatar')
            web_roles = web_user.get('web_roles')

            mini_id = web_user.get('mini_id', None)
            web_id = web_user.get('id', None)

            user = {
                'id': web_id,
                'mini_id': mini_id if mini_id else web_id,
                'username': web_user.get('username'),
                'name': web_user.get('name'),
                'mini_username': web_user.get('mini_username'),
                'mobile': web_user.get('mobile'),
                'email': web_user.get('email'),
                'status': web_user.get('status'),
                'avatar': avatar,
                'permissions': web_user.get('permissions'),
                'is_super': web_user.get('is_super'),
                'created_at': web_user.get('created_at'),
                'updated_at': web_user.get('updated_at'),
            }

            if avatar and avatar.isdigit():
                user['avatar'] = ImageAPI.get_url(request, avatar, 'originals')

            if web_roles and len(web_roles):
                role_id = web_roles[0] if len(web_roles) == 1 else web_roles[-1]
                web_role = WebRole.objects.get(pk=role_id)
                user['user_role'] = {
                    'id': web_role.id,
                    'name': web_role.name
                }
                user['roles'] = [web_role.en_name]
            else:
                user['user_role'] = {}

            results.append(user)

        return Response({
            'users': results,
            'count': queryset.count()
        })

    # 由超级管理员创建用户
    def create(self, request, *args, **kwargs):
        user = WebUser.objects.get(pk=request.user['id'])
        project_id = request.user.get('project_id')
        # 是否管理员角色
        is_admin = user.web_roles.filter(en_name='admin').exists() or user.is_super
        if is_admin:
            with transaction.atomic(using=f'prj{project_id}db'):
                data = request.data
                # 检查账号名或者手机号码是否已被使用
                new_username = data.get('username', None)
                new_mobile = data.get('mobile', None)
                if not new_mobile and not new_username:
                    return Response(status=status.HTTP_400_BAD_REQUEST)
                valid = WebUser.objects.valid_create(new_username, new_mobile)
                if not valid:
                    return Response({'message': '账户名或手机号已存在！', 'status': 40018})

                is_local_deployment = settings.LOCAL_DEPLOYMENT
                if is_local_deployment:
                    # 默认激活用户
                    data['status'] = 10
                    # 处理用户角色 role => web_roles
                    data['web_roles'] = [data['role']]
                    data['last_login'] = datetime.datetime.now()
                    # 生成 ec_token
                    raw_token = AuthUtils.generate_random_char(32)
                    base64_token = str(base64.b64encode(raw_token.encode('ascii')), 'ascii')
                    data['ec_token'] = base64_token[:-2]
                    # 生成 unionid
                    raw_token = AuthUtils.generate_random_char(32)
                    base64_token = str(base64.b64encode(raw_token.encode('ascii')), 'ascii')
                    data['unionid'] = base64_token[:-2]

                    serializer = WebUserSerializer(data=data)
                    if serializer.is_valid():
                        new_user = serializer.save(password=make_password(str(request.data['password'])))
                        # 用户绑定项目
                        # UserProject(web_user=new_user, project_id=request.user['project_id']).save()
                        # 构建 auth
                        appid = 'saee54d4ab'
                        et = int((datetime.datetime.now() + datetime.timedelta(seconds=120)).timestamp())
                        res = 'ecapi/intapi/ec_users'

                        access_key = os.environ[appid]
                        key = base64.b64decode(access_key)

                        org = appid + '&' + str(et) + '&' + res
                        sign_b = hmac.new(key=key, msg=org.encode(), digestmod='sha1')
                        # 生成签名
                        sign = quote(base64.b64encode(sign_b.digest()).decode(), safe='')

                        int_auth = f'appid={appid}&et={et}&res={res}&sign={sign}'

                        headers = {
                            "Content-Type": "application/json",
                            "Authorization": int_auth
                        }
                        data = {
                            'name': new_user.name,
                            'username': new_user.username,
                            'password': request.data['password'],
                            'unionid': new_user.unionid,
                            'project_id': project_id
                        }
                        # 将新用户同步至能耗平台
                        r = requests.post('http://192.168.3.145:8088/ecapi/intapi/ec_users', headers=headers, data=data)
                        if r.status_code != 200:
                            raise exceptions.AuthenticationFailed('Query user info failed!')
                        return Response(ResponseUtils.custom_response_format(serializer.data, 'user', True))

                    else:
                        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)
                else:
                    res = 'saianadmin/intapi/web_users'
                    headers = BaseAPI.admin_intapi_header(res)
                    r = requests.post(f'{DOMAIN}/{res}', json={**request.data, 'project_id': project_id}, headers=headers)
                    if r.status_code == 200:
                        return Response({
                            'status': status.HTTP_200_OK,
                            'data': r.json().get('data')
                        })
                    else:
                        return Response(status=r.status_code, data=None)
        else:
            return Response(status=status.HTTP_403_FORBIDDEN)

    def update(self, request, *args, **kwargs):
        web_user = get_object_or_404(WebUser, pk=kwargs.get('pk'))

        project_id = request.user['project_id']
        user = WebUser.objects.get(pk=request.user['id'])
        # 是否管理员角色
        is_admin = user.web_roles.filter(en_name='admin').exists() or user.is_super

        # 管理员或者自己可以编辑用户信息
        if is_admin or user.id == web_user.id:
            # 处理提交上来的内容
            data = request.data
            # 检查账号名或者手机号码是否已被使用
            new_username = data.get('username', None)
            if new_username and new_username != web_user.username:
                valid = WebUser.objects.valid_update(new_username, None, web_user.id)
                if not valid:
                    return Response({'message': '用户名已存在！', 'status': 40018})
            new_mobile = data.get('mobile', None)
            if new_mobile and new_mobile != web_user.mobile:
                valid = WebUser.objects.valid_update(None, new_mobile, web_user.id)
                if not valid:
                    return Response({'message': '手机号已存在！', 'status': 40018})

            res = f'saianadmin/intapi/web_users/{web_user.id}'
            url = f'{DOMAIN}/{res}'
            headers = BaseAPI.admin_intapi_header(res)
            r = requests.put(url, json={**request.data, 'project_id': project_id}, headers=headers)
            if r.status_code == 200:
                web_role_id = data.get('web_role_id', None)
                if web_role_id is not None:
                    web_role = WebRole.objects.get(pk=web_role_id)
                    if web_user.web_roles.filter(pk=web_role.id).exists():
                        return Response({
                            'status': status.HTTP_200_OK,
                            'data': None
                        })
                    web_user.web_roles.clear()
                    web_user.web_roles.add(web_role)

            serializer = WebUserInfoSerializer(web_user, data, partial=True, context={'request': request})

            # 绑定单位维度
            dimension_ids = request.data.get('dimension_ids', None)
            if dimension_ids is not None:
                with transaction.atomic(using=f"prj{project_id}db"):
                    current_dimension_ids = list(DimensionUser.objects.filter(web_user_id=web_user.id).values_list('dimension_id', flat=True))
                    new_dimension = set(dimension_ids) - set(current_dimension_ids)
                    del_dimension = set(current_dimension_ids) - set(dimension_ids)
                    for dimension_id in new_dimension:
                        DimensionUser.objects.create(
                            dimension_id=dimension_id,
                            web_user_id=web_user.id
                        )
                    DimensionUser.objects.filter(web_user_id=web_user.id, dimension_id__in=del_dimension).delete()

            return Response(status=r.status_code, data={
                'status': r.status_code,
                'data': {
                    'user': serializer.data if serializer.is_valid(raise_exception=False) else None
                }
            })
        else:
            return Response(status=status.HTTP_403_FORBIDDEN)

    def retrieve(self, request, *args, **kwargs):
        obj = self.get_object()
        serializer = WebUserInfoSerializer(obj)
        return Response({
            'status': status.HTTP_200_OK,
            'data': serializer.data
        })

    def destroy(self, request, *args, **kwargs):
        # 要删除的目标用户
        web_user = get_object_or_404(WebUser, pk=kwargs.get('pk'))
        project_id = request.user['project_id']

        # 操作员
        user = WebUser.objects.get(pk=request.user['id'])
        # 是否管理员角色
        is_admin = user.web_roles.filter(en_name='admin').exists() or user.is_super

        if not is_admin:
            return Response(status=status.HTTP_403_FORBIDDEN)
        else:
            res = f'saianadmin/intapi/web_user_project'
            url = f'{DOMAIN}/{res}'
            headers = BaseAPI.admin_intapi_header(res)
            r = requests.delete(url, json={'web_user_id': web_user.id, 'project_id': project_id}, headers=headers)
            if r.status_code == 200:
                UserProject.objects.filter(web_user_id=web_user.id, project_id=project_id).delete()
                return Response({'status': 200, 'data': None})
            else:
                return Response(status=status.HTTP_500_INTERNAL_SERVER_ERROR)



class WebUserDetail(APIView):
    # authentication_classes = (LegacyAuth,)
    serializer_class = WebUserInfoSerializer

    def get(self, request, pk, format=None):

        is_local_deployment = settings.LOCAL_DEPLOYMENT

        if is_local_deployment:
            user = get_object_or_404(WebUser, id=pk)
            serializer = WebUserInfoSerializer(user)
            return Response(ResponseUtils.format(serializer.data))
        else:
            user = UserApi.myInfo(request.headers['Authorization'], request.user['project_id'])
            return Response(user)

    def put(self, request, pk):
        """ 更新 web 用户信息 """

        is_local_deployment = settings.LOCAL_DEPLOYMENT

        web_user = get_object_or_404(WebUser, pk=pk)

        # 处理提交上来的内容
        data = request.data
        data['status'] = web_user.status if data.get('status', None) is None else data['status']
        data['last_login'] = web_user.last_login
        data['web_roles'] = data['role'] if isinstance(data['role'], list) else [data['role']]
        if data.get('role', None) is not None:
            pass
        else:
            # data['web_roles'] = web_user.web_roles
            pass
        if data.get('password', None) is not None:
            data['password'] = make_password(data['password'])

        serializer = WebUserSerializer(instance=web_user, data=request.data)
        if serializer.is_valid():
            serializer.save()

            return Response(ResponseUtils.custom_response_format(serializer.data))
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class MyInfoView(APIView):

    def get(self, request):
        # is_local_deployment = settings.LOCAL_DEPLOYMENT

        # if is_local_deployment:
        # me = get_object_or_404(WebUser, pk=request.user['id'])
        try:
            me = WebUser.objects.get(pk=request.user['id'])
            serializer = WebUserInfoSerializer(me, context={'request': request})
            data = serializer.data

            user = WebUser.objects.get_executor_info(request, me.id)
            data['mini_username'] = user.get('mini_username', None)
            data['mini_userid'] = user.get('mini_userid', None)

            # 用户角色
            if me.web_roles.exists():

                # 处理用户角色对应的菜单
                last_role = WebRole.objects.get(pk=data['user_role']['id'])
                web_menus = last_role.web_menus.order_by('seq', 'id')
                web_menus_data = WebMenuSerializer(web_menus, many=True).data
                # 把第一级菜单的 "parent" 设置为 -1，然后进行排序，把第一级菜单排在前面
                for menu in web_menus_data:
                    if not menu.get('parent'):
                        menu['parent'] = -1
                web_menus = [dict(menu) for menu in web_menus_data]
                web_menus.sort(key=lambda x: x['id'] if x['parent'] >= 0 else x['parent'])
                menus = []
                for menu in web_menus:
                    if menu.get('enabled', False):
                        if menu.get('parent') > 0:
                            parent_menu = list(filter(lambda x: x.get('id') == menu.get('parent'), menus))
                            if len(parent_menu) == 1:
                                parent_menu[0]['children'].append({
                                    'id': menu.get('id'),
                                    'name': menu.get('name'),
                                    'uni_name': menu.get('uni_name'),
                                    'seq': menu.get('seq'),
                                    'is_cus': menu.get('is_cus'),
                                    'page_content': fill_element(menu.get('page_content'))
                                })
                            elif len(parent_menu) == 0:
                                p_menu = WebMenu.objects.get(pk=menu.get('parent'))
                                menus.append({
                                    'id': p_menu.id,
                                    'name': p_menu.name,
                                    'uni_name': p_menu.uni_name,
                                    'seq': p_menu.seq,
                                    'is_cus': p_menu.is_cus,
                                    'children': [{
                                        'id': menu.get('id'),
                                        'name': menu.get('name'),
                                        'uni_name': menu.get('uni_name'),
                                        'seq': menu.get('seq'),
                                        'is_cus': menu.get('is_cus'),
                                        'page_content': fill_element(menu.get('page_content'))
                                    }]
                                })
                        else:
                            menus.append({
                                'id': menu.get('id'),
                                'name': menu.get('name'),
                                'uni_name': menu.get('uni_name'),
                                'seq': menu.get('seq'),
                                'is_cus': menu.get('is_cus'),
                                'page_content': fill_element(menu.get('page_content')),
                                'children': []
                            })
                data['menus'] = menus
            return Response(ResponseUtils.custom_response_format(data, 'user', True))
        except WebUser.DoesNotExist:
            # else:
            user = UserApi.myInfo(request.headers['Authorization'], request.user['project_id'])
            return Response(user)

    def put(self, request):
        is_local_deployment = settings.LOCAL_DEPLOYMENT

        if is_local_deployment:
            password = request.data.get('password', None)
            avatar = request.data.get('avatar', None)
            email = request.data.get('email', None)
            mobile = request.data.get('mobile', None)
            # sms = request.data.get('captcha', None)
            name = request.data.get('name', None)

            web_user = WebUser.objects.get(pk=request.user['id'])

            if password is not None:
                # if sms is None:
                #     return Response(status=status.HTTP_400_BAD_REQUEST)
                #
                # verified_code = VerifiedCode.objects.is_valid_code(mobile, sms)
                # if not verified_code:
                #     return Response({
                #         'status': 40003,
                #         'message': '验证码已失效'
                #     })
                # verified_code.used = True
                # verified_code.save()
                web_user.password = make_password(password)

            if avatar is not None:
                web_user.avatar = avatar
            if email is not None:
                web_user.email = email
            if mobile is not None:
                web_user.mobile = mobile
            if name is not None:
                web_user.name = name

            web_user.save()
            serializer = WebUserInfoSerializer(web_user)
            return Response({'status': 200, 'data': serializer.data})
        else:
            data = request.data
            web_user = WebUser.objects.get(pk=request.user.get('id'))

            UserApi.update_profile(request)
            if "password" in data and "captcha" in data:
                data.pop('password')
                data.pop('captcha')
            data['id'] = request.user.get('id')
            serializer = WebUserInfoSerializer(web_user, data=data, partial=True, context={'request': request})
            serializer.is_valid(raise_exception=True)
            serializer.save()

            return Response({
                'status': status.HTTP_200_OK,
                'data': serializer.data
            })

# 用户鉴权，.0.83 服务器接受请求，再去请求旧框架的 web_users/my?prjid= 接口
class LegacyAuthView(APIView):

    def get(self, request):
        return Response(UserApi.myInfo(request.headers['Authorization'], request.user['project_id']))


class WebUserSession(APIView):
    """
        web 用户登陆，只适用于本地部署项目
    """
    authentication_classes = []

    def post(self, request):
        # 检查是否本地部署项目
        is_local_deployment = settings.LOCAL_DEPLOYMENT
        if not is_local_deployment:
            return Response(status=status.HTTP_400_BAD_REQUEST)
        mobile = request.data.get('mobile', None)
        username = request.data.get('username', None)
        password = request.data.get('password', None)
        captcha = request.data.get('code', None)
        sms = request.data.get('sms', None)

        if not mobile and not username:
            return Response(status=status.HTTP_400_BAD_REQUEST)

        if mobile:
            web_user = WebUser.objects.filter(mobile=mobile)
        else:
            web_user = WebUser.objects.filter(username=username)
        # 检查用户是否存在
        if not web_user.exists():
            return Response({
                'status': 40002,
                'message': '用户未注册'
            })

        # 通过账号密码登陆
        if sms is None:
            # TODO 本地部署删除登陆验证码
            # if not password or not captcha:
            if not password:
                return Response(status=status.HTTP_400_BAD_REQUEST)

            user = web_user.last()

            # # 检查验证码是否正确
            # if captcha.upper() != user.captcha:
            #     return Response({
            #         'status': 40003,
            #         'message': '验证码已失效'
            #     })
            # 检查密码
            if check_password(password, user.password):
                raw_token = AuthUtils.generate_random_char(64)
                # 更新用户 token 和 最后登陆时间
                base64_token = base64.b64encode(raw_token.encode('ascii'))
                # 移除 base64_token 的 b'' 前缀
                user.authentication_token = str(base64_token, 'ascii')
                user.last_login = datetime.datetime.now()
                # 成功后旧 token 就失效了
                user.save()
                serializer = WebUserSerializer(user)
                # 获取项目信息
                project = Project.objects.all().last()
                data = serializer.data
                data['projects'] = [{
                    'id': project.id,
                    'name': project.name,
                    'image': ImageAPI.get_url(request, project.images),
                    'enable_ec': project.enable_ec
                }]
                return Response(ResponseUtils.custom_response_format(data, 'user', True))
            else:
                return Response({
                    'status': 40100,
                    'message': '账号或密码错误！'
                })

        # 通过短信验证码登陆
        else:
            verified_code = VerifiedCode.objects.is_valid_code(mobile, sms)

            if not verified_code:
                return Response({
                    'status': 40003,
                    'message': '验证码已失效'
                })

            # 通过验证
            verified_code.used = True
            verified_code.save()

            user = web_user.last()
            raw_token = AuthUtils.generate_random_char(64)
            # 更新用户 token 和 最后登陆时间
            base64_token = base64.b64encode(raw_token.encode('ascii'))
            # 移除 base64_token 的 b'' 前缀
            user.authentication_token = str(base64_token, 'ascii')
            user.last_login = datetime.datetime.now()
            # 成功后旧 token 就失效了
            user.save()
            serializer = WebUserSerializer(user)
            # 获取项目信息
            project = Project.objects.all().last()
            data = serializer.data
            data['projects'] = [{
                'id': project.id,
                'name': project.name,
                'image': ImageAPI.get_url(request, project.images),
                'enable_ec': project.enable_ec
            }]
            return Response(ResponseUtils.custom_response_format(data, 'user', True))

    def delete(self, request):
        auth = LegacyAuth()
        current_user = auth.authenticate(request)
        # 检查是否本地部署项目
        is_local_deployment = settings.LOCAL_DEPLOYMENT
        if not is_local_deployment:
            return Response(status=status.HTTP_400_BAD_REQUEST)
        # 用户登出，删除用户表中的 authentication_token
        web_user = WebUser.objects.get(pk=current_user[0].get('id'))
        web_user.authentication_token = None
        web_user.save()
        return Response({
            'status': 200,
            'message': '用户登出'
        })


class WebUserResetPassword(APIView):
    """
        本地部署项目用户重设密码
    """
    authentication_classes = []

    def post(self, request):
        # 检查是否本地部署项目
        is_local_deployment = settings.LOCAL_DEPLOYMENT
        if not is_local_deployment:
            return Response(status=status.HTTP_400_BAD_REQUEST)
        mobile = request.data.get('mobile', None)
        password = request.data.get('password', None)
        captcha = request.data.get('captcha', None)

        if not mobile or not password or not captcha:
            return Response(status=status.HTTP_400_BAD_REQUEST)

        verified_code = VerifiedCode.objects.filter(target=mobile, verified_code=captcha, used=False).order_by(
            'created_at').last()
        if verified_code:
            now = datetime.datetime.now()
            period_seconds = (now - verified_code.created_at).seconds
            # 验证码是否超时
            if period_seconds > 300:
                return Response({
                    'status': 40003,
                    'message': '验证码已失效'
                })
            # 检查账号是否存在
            user = WebUser.objects.filter(mobile=mobile)
            if not user.exists():
                return Response({
                    'status': 40002,
                    'message': '用户未注册'
                })
            # 通过验证，更新用户密码
            user = user.last()
            new_password = make_password(password)
            user.password = new_password
            user.save()
            # 更新使用过的验证码
            verified_code.used = True
            verified_code.save()
            return Response({
                'status': status.HTTP_200_OK
            })
        return Response({
            'status': 40003,
            'message': '验证码已失效'
        })


# 获取所有角色
class WebRoleView(APIView):
    def get(self, request):
        web_roles = WebRole.objects.filter(project_id=request.user['project_id']).order_by('id')
        serializer = WebRoleSerializer(web_roles, many=True)
        return Response(ResponseUtils.custom_response_format(serializer.data, 'web_roles', True))

    def post(self, request):
        data = request.data
        data['project'] = request.user['project_id']
        data['web_menus'] = data['menu_ids']
        data.pop('menu_ids')
        serializer = WebRoleSerializer(data=data)

        if serializer.is_valid():
            serializer.save()
            return Response(ResponseUtils.custom_response_format(serializer.data, 'web_role', True))
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class WebRoleDetailView(APIView):
    def get(self, request, pk):
        web_role = get_object_or_404(WebRole, pk=pk, project_id=request.user['project_id'])
        serializer = WebRoleSerializer(web_role)
        data = serializer.data
        data.pop('project')
        return Response(ResponseUtils.custom_response_format(data, 'web_role', True))

    def put(self, request, pk):
        web_role = get_object_or_404(WebRole, pk=pk, project_id=request.user['project_id'])

        data = request.data
        data['project'] = web_role.project_id
        if 'menu_ids' in data:
            data['web_menus'] = data['menu_ids']
            data.pop('menu_ids')
        serializer = WebRoleSerializer(instance=web_role, data=data, partial=True)

        if serializer.is_valid():
            serializer.save()
            data = serializer.data
            data.pop('project')
            return Response(ResponseUtils.custom_response_format(data, 'web_role', True))
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk):
        web_role = get_object_or_404(WebRole, pk=pk, project_id=request.user['project_id'])
        web_role.delete()
        return Response({'status': 200, 'data': None})


# 获取用户的角色
class WebUserRoleView(APIView):
    def get(self, request, pk=None):
        user_id = request.user['id'] if pk is None else pk
        web_user = get_object_or_404(WebUser, pk=user_id)
        serializer = WebRoleSerializer(web_user.web_roles, many=True)

        return Response(ResponseUtils.custom_response_format(serializer.data, 'user_roles', True))


# 账号密码登陆图形验证码
class LoginCaptchaView(APIView):
    authentication_classes = []

    def post(self, request):
        # 验证登陆的手机号码
        mobile = request.data.get('mobile', None)
        username = request.data.get('username', None)
        if mobile is None and username is None:
            return Response({})
        try:
            if mobile is not None:
                user = WebUser.objects.get(mobile=mobile)
            else:
                user = WebUser.objects.get(username=username)
            # captcha 是列表。第一位是验证码文本，第二位是图片的base64
            captcha = AuthUtils.generate_captcha()
            user.captcha = captcha[0]
            user.save()
            return Response(ResponseUtils.custom_response_format({'captcha': captcha[1]}))

        except WebUser.DoesNotExist:
            return Response({})


class UserDeviceStatViewSet(viewsets.ViewSet):
    def list(self, request):
        devices = Device.objects.filter(project_id=request.user['project_id'])
        web_user = WebUser.objects.get(pk=request.user['id'])
        total = web_user.userdevice_set.filter(device__project_id__isnull=False).count()

        # total_online = web_user.userdevice_set.filter(device__in=devices.filter(online=True)).count()
        total_online = web_user.userdevice_set.filter(device__online=True, device__project_id__isnull=False).count()
        total_offline = total - total_online

        # total_infault = web_user.userdevice_set.filter(device__in=devices.filter(online=True, in_fault=True)).count()
        # total_inalarm = web_user.userdevice_set.filter(device__in=devices.filter(online=True, in_alarm=True)).count()
        total_infault = web_user.userdevice_set.filter(device__online=True, device__in_fault=True, device__project_id__isnull=False).count()
        total_inalarm = web_user.userdevice_set.filter(device__online=True, device__in_alarm=True, device__project_id__isnull=False).count()

        total_open = web_user.userdevice_set.filter(device__sw_on=True, device__project_id__isnull=False).count()
        total_close = web_user.userdevice_set.filter(device__sw_on=False, device__project_id__isnull=False).count()
        res_data = {
            'status': status.HTTP_200_OK,
            'data': {
                'my_devices': {
                    'total': total,
                    'total_online': total_online,
                    'total_offline': total_offline,
                    'total_in_fault': total_infault,
                    'total_in_alarm': total_inalarm,
                    'total_open': total_open,
                    'total_close': total_close
                }
            }
        }

        return Response(res_data)


class EcLoginView(APIView):
    authentication_classes = []

    def post(self, request):
        ec_token = request.data.get('ec_token', None)
        if ec_token is None:
            return Response({
                "status": 40402,
                "error": "找不到用户！"
            })

        user = WebUser.objects.filter(ec_token=ec_token)
        if (not user.exists()):
            return Response({
                "status": 40402,
                "error": "找不到用户！"
            })
        user = user.last()
        serializer = WebUserInfoSerializer(user)
        data = serializer.data
        project = user.project_set.last()
        data['projects'] = [{
            'id': project.id,
            'name': project.name,
            'image': project.images
        }]

        # 刷新 ec_token
        raw_token = AuthUtils.generate_random_char(32)
        base64_token = str(base64.b64encode(raw_token.encode('ascii')), 'ascii')
        user.ec_token = base64_token[:-2]
        user.save()

        return Response(ResponseUtils.custom_response_format(data, 'user', True))


class UserSearchesView(viewsets.ModelViewSet):
    serializer_class = UserSearchSerializer
    renderer_classes = (SyJSONRender,)

    def get_queryset(self):
        return UserSearches.objects.filter(
            web_user_id=self.request.user['id']).order_by('id')

    def list(self, request, *args, **kwargs):
        result = super(UserSearchesView, self).list(
            request, *args, **kwargs).data
        return Response({
            'searches': result['results']
        })

    def destroy(self, request, *args, **kwargs):
        if 'pk' in kwargs:
            search = UserSearches.objects.filter(pk=kwargs['pk'])
        else:
            search = UserSearches.objects.filter(web_user_id=self.request.user['id'])
        search.delete()
        return Response()


class UserDeviceViewSet(viewsets.ModelViewSet):
    serializer_class = UserDeviceSerializer
    renderer_classes = (SyJSONRender,)

    def get_queryset(self):
        return UserDevice.objects.filter(
            web_user_id=self.request.user['id']).order_by('id')

    def list(self, request, *args, **kwargs):
        user_devices = self.get_queryset()
        result = []
        for user_device in user_devices:
            device = Device.objects.get(pk=user_device.device_id)
            device_prototype = device.device_prototype

            nickname_da = None
            if user_device.unit_idx:
                nickname_ap = AttributePrototype.objects.filter(device_prototype_id=device.device_prototype_id)
                nickname_ap = nickname_ap.filter(Q(identifier__contains=user_device.unit_idx) & Q(identifier__contains='NickName'))
                if user_device.unit_prefix:
                    nickname_ap = nickname_ap.filter(identifier__contains=user_device.unit_prefix)
                if nickname_ap.exists():
                    # nickname_da = DeviceAttribute.objects.get_by_idf(device, nickname_ap.last().identifier)
                    # nickname_da = DeviceAttribute.objects.get_by_ap(device, nickname_ap.last())
                    nickname_da = DeviceAttribute.objects.query_object_by_ap(device, nickname_ap.last())

            result.append({
                'created_at': user_device.created_at,
                'device': {
                    'custz_detail': True if device.device_prototype.content is not None else False,
                    'device_prototype': {
                        'id': device_prototype.id,
                        'name': device_prototype.name,
                        'uni_name': device_prototype.uni_name
                    },
                    'id': device.id,
                    'idx': user_device.unit_idx,
                    'in_alarm': device.in_alarm,
                    'in_fault': device.in_fault,
                    'is_sub': True if user_device.unit_prefix is not None else False,
                    'key_attributes': device.key_attributes(),
                    'dashboard_attres': device.dashboard_attres(),
                    'nick_name': device.nick_name if nickname_da is None else nickname_da.value,
                    'online': device.online,
                    'prefix': user_device.unit_prefix,
                    'status': device.status,
                    'sw_on': device.sw_on,
                },
                'id': device.id
            })
        res_data = {
            'user_devices': result,
            'count': user_devices.count()
        }

        return Response(res_data)

    def create(self, request, *args, **kwargs):
        device_id = request.data.get('device_id', None)
        serializer = UserDeviceSerializer(data=request.data)
        # serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user_device = UserDevice.objects.create(
            device_id=device_id,
            web_user_id=self.request.user['id']
        )
        res_data = {
            'user_device': {
                'id': user_device.id
            }
        }

        return Response(res_data)

    def destroy(self, request, *args, **kwargs):
        user_device = UserDevice.objects.filter(
            web_user_id=self.request.user['id'], pk=kwargs['pk'])
        user_device.delete()

        return Response()


class UserLogView(viewsets.ModelViewSet):
    serializer_class = UserLogSerializer
    renderer_classes = (SyJSONRender,)

    @action(detail=True, methods=['get'])
    def get_authenticators(self):
        if 'intapi' in self.request.path:
            return [IntapiAuth(), ]
        return [LegacyAuth(), ]

    def get_queryset(self):
        queryset = UserLog.objects.all()

        user_id = self.request.query_params.get('user_id', None)
        if user_id is not None:
            queryset = queryset.filter(web_user_id=user_id)

        http_method = self.request.query_params.get('http_method', None)
        if http_method is not None:
            queryset = queryset.filter(http_method__iexact=http_method)

        from_at = self.request.query_params.get('from', None)
        till_at = self.request.query_params.get('till', None)
        if from_at is not None and till_at is not None:
            from_at = datetime.datetime.strptime(from_at, '%Y%m%d%H%M%S')
            till_at = datetime.datetime.strptime(till_at, '%Y%m%d%H%M%S')
            queryset = queryset.filter(created_at__range=(from_at, till_at))

        search = self.request.query_params.get('search', None)
        if search is not None:
            queryset = queryset.filter(Q(data__icontains=search) | Q(api_url__icontains=search))

        return queryset.order_by('-id')

    def list(self, request, *args, **kwargs):
        data = super(UserLogView, self).list(request, *args, **kwargs).data
        user_logs = data['results']
        count = data['count']
        user_cache = {}
        for user_log in user_logs:
            web_user_id = user_log['web_user']
            info = user_cache.get(web_user_id, None)
            if info is None:
                info = WebUser.objects.get_executor_info(request=request, web_user_id=web_user_id)
                user_cache[web_user_id] = info
            user_log['user_name'] = info['name']
            user_log['mini_username'] = info['mini_username']

        return Response({
            'status': status.HTTP_200_OK,
            'user_logs': user_logs,
            'count': count
        })

    @classmethod
    def get_ip(cls, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]  # 所以这里是真实的ip
        else:
            ip = request.META.get('REMOTE_ADDR')  # 这里获得代理ip
        return ip
    
    def create(self, request, *args, **kwargs):
        api_url = request.data.get('api_url', None)
        if api_url is None:
            raise exceptions.ValidationError(detail='api_url is required.')
        
        http_method = request.data.get('http_method', 'GET')

        data = request.data.get('data', '')

        user = WebUser.objects.get(pk=request.user['id'])
        
        UserLog.objects.create(
            web_user_id = user.id,
            user_name = user.username,
            user_mobile = user.mobile,
            api_url = api_url,
            http_method = http_method,
            data = data,
            browser=request.META.get('HTTP_USER_AGENT')[:255],
            ip_address=self.get_ip(request)
        )

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

class UserManualViewSet(viewsets.GenericViewSet):
    authentication_classes = []

    def list(self, request):
        content = '<html>\n<head>\n\t<title></title>\n\t<style type=\"text/css\">\n\t</style>\n</head>\n<body style=\"padding: 10px 10px 10px 10px;\">\n<p style=\"text-align: center;\"><span style=\"font-size:20px;\">AirConTap小程序服务和隐私协议</span></p>\n\n<p style=\"text-align: justify;\"><span style=\"font-size:16px;\">&nbsp; &nbsp; &nbsp; &nbsp;欢迎使用&ldquo;AirConTap小程序&rdquo;，为使用&ldquo;AirConTap小程序&rdquo;软件（以下简称&ldquo;本软件&rdquo;）及服务，你应当阅读并遵守《AirConTap小程序服务和隐私协议》（以下简称&ldquo;本协议&rdquo;）。</span></p>\n\n<p style=\"text-align: justify;\">&nbsp;</p>\n\n<p style=\"text-align: justify;\"><span style=\"font-size:20px;\">一、总则</span></p>\n\n<p style=\"text-align: justify;\">1. &ldquo;AirConTap小程序&rdquo;的所有权和运营权归广州市塞安物联网科技有限公司（以下简称&ldquo;本公司&rdquo;）所有。</p>\n\n<p style=\"text-align: justify;\">2. 用户在注册之前，应当仔细阅读本协议，并同意遵守本协议后方可成为注册用户。一旦注册成功，则用户与本公司之间自动形成协议关系，用户应当受本协议的约束。用户在使用特殊的服务或产品时，应当同意接受相关协议后方能使用。</p>\n\n<p style=\"text-align: justify;\">3. 本协议则可由本公司随时更新，用户应当及时关注并同意本公司不承担通知义务。本软件的通知、公告、声明或其它类似内容是本协议的一部分。</p>\n\n<p style=\"text-align: justify;\">&nbsp;</p>\n\n<p style=\"text-align: justify;\"><span style=\"font-size:20px;\">二、服务内容</span></p>\n\n<p style=\"text-align: justify;\">1. 本软件的具体内容根据项目的实际情况收集并提供。</p>\n\n<p style=\"text-align: justify;\">2. 本软件仅提供相关的网络服务，除此之外与相关网络服务有关的设备(如个人电脑、手机、及其他与接入互联网或移动网有关的装置)及所需的费用(如为接入互联网而支付的电话费及上网费、为使用移动网而支付的手机费)均应由用户自行负担。</p>\n\n<p style=\"text-align: justify;\">3. 本公司非常重视对未成年人个人信息的保护，若您是18周岁以下的未成年人，在使用本软件的服务前，应事先取得您家长或法定监护人的书面同意。</p>\n\n<p style=\"text-align: justify;\">&nbsp;</p>\n\n<p style=\"text-align: justify;\"><span style=\"font-size:20px;\">三、用户帐号</span></p>\n\n<p style=\"text-align: justify;\">1. 经本软件注册系统完成注册程序并通过身份认证的用户即成为正式用户，可以获得本软件默认用户级别所应享有的权限。</p>\n\n<p style=\"text-align: justify;\">2. 用户需按照注册要求提供相关的资料进行注册。用户有义务保证密码和帐号的安全，用户利用该密码和帐号所进行的一切活动引起的任何损失或损害，由用户自行承担全部责任，本公司不承担任何责任。如用户发现帐号遭到未授权的使用或发生其他任何安全问题，应立即修改帐号密码并妥善保管，如有必要，请通知本公司。因黑客行为或用户的保管疏忽导致帐号非法使用，本公司不承担任何责任。</p>\n\n<p style=\"text-align: justify;\">3. 如用户使用手机号码或微信账号作为注册凭证，由于这两种凭据本身会被运营商及微信官方回收和重新使用，本软件有权根据具体情况进行回收，回收后，用户将不能使用该凭据作为凭证进行登录，用户可以使用其他凭证绑定账号，号码回收后，可以继续使用该凭证进行登录等操作。</p>\n\n<p style=\"text-align: justify;\">&nbsp;</p>\n\n<p style=\"text-align: justify;\"><span style=\"font-size:20px;\">四、使用规则</span></p>\n\n<p style=\"text-align: justify;\">1. 遵守中华人民共和国相关法律法规，包括但不限于《中华人民共和国计算机信息系统安全保护条例》、《计算机软件保护条例》、《最高人民法院关于审理涉及计算机网络著作权纠纷案件适用法律若干问题的解释(法释[2004]1号)》、《全国人大常委会关于维护互联网安全的决定》、《互联网电子公告服务管理规定》、《互联网新闻信息服务管理规定》、《互联网著作权行政保护办法》和《信息网络传播权保护条例》等有关计算机互联网规定和知识产权的法律和法规、实施办法。</p>\n\n<p style=\"text-align: justify;\">2. 用户对其自行发表、上传或传送的内容负全部责任，所有用户不得在软件任何页面发布、转载、传送含有下列内容之一的信息，否则本公司有权自行处理并不通知用户：</p>\n\n<p style=\"text-align: justify;\">(1)违反宪法确定的基本原则的；</p>\n\n<p style=\"text-align: justify;\">(2)危害国家安全，泄漏国家机密，颠覆国家政权，破坏国家统一的；</p>\n\n<p style=\"text-align: justify;\">(3)损害国家荣誉和利益的；</p>\n\n<p style=\"text-align: justify;\">(4)煽动民族仇恨、民族歧视，破坏民族团结的；</p>\n\n<p style=\"text-align: justify;\">(5)破坏国家宗教政策，宣扬邪教和封建迷信的；</p>\n\n<p style=\"text-align: justify;\">(6)散布谣言，扰乱社会秩序，破坏社会稳定的；</p>\n\n<p style=\"text-align: justify;\">(7)散布淫秽、色情、赌博、暴力、恐怖或者教唆犯罪的；</p>\n\n<p style=\"text-align: justify;\">(8)侮辱或者诽谤他人，侵害他人合法权益的；</p>\n\n<p style=\"text-align: justify;\">(9)煽动非法集会、结社、游行、示威、聚众扰乱社会秩序的；</p>\n\n<p style=\"text-align: justify;\">(10)以非法民间组织名义活动的；</p>\n\n<p style=\"text-align: justify;\">(11)含有法律、行政法规禁止的其他内容的。</p>\n\n<p style=\"text-align: justify;\">3. 用户承诺对其发表或者上传于本软件的所有信息(即属于《中华人民共和国著作权法》规定的作品，包括但不限于文字、图片、音乐、电影、表演和录音录像制品和电脑程序等)均享有完整的知识产权，或者已经得到相关权利人的合法授权；如用户违反本条规定造成本公司被第三人索赔的，用户应全额补偿本公司一切费用(包括但不限于各种赔偿费、诉讼代理费及为此支出的其它合理费用)；</p>\n\n<p style=\"text-align: justify;\">4. 当第三方认为用户发表或者上传于本软件的信息侵犯其权利，并根据《信息网络传播权保护条例》或者相关法律规定向本公司发送权利通知书时，用户同意本公司可以自行判断决定删除涉嫌侵权信息，除非用户提交书面证据材料排除侵权的可能性，本公司将不会自动恢复上述删除的信息；</p>\n\n<p style=\"text-align: justify;\">(1)不得为任何非法目的而使用本软件；</p>\n\n<p style=\"text-align: justify;\">(2)遵守所有与网络服务有关的网络协议、规定和程序；</p>\n\n<p style=\"text-align: justify;\">(3)不得利用本软件进行任何可能对互联网的正常运转造成不利影响的行为；</p>\n\n<p style=\"text-align: justify;\">(4)不得利用本软件进行任何不利于本公司的行为。</p>\n\n<p style=\"text-align: justify;\">5. 如用户在使用网络服务时违反上述任何规定，本公司有权要求用户改正或直接采取一切必要的措施(包括但不限于删除用户张贴的内容、暂停或终止用户使用服务的权利)以减轻用户不当行为而造成的影响。</p>\n\n<p style=\"text-align: justify;\">&nbsp;</p>\n\n<p style=\"text-align: justify;\"><span style=\"font-size:20px;\">五、隐私保护</span></p>\n\n<p style=\"text-align: justify;\">1. 本公司不对外公开或向第三方提供单个用户的注册资料及用户在使用网络服务时存储在本软件的非公开内容，但下列情况除外：</p>\n\n<p style=\"text-align: justify;\">(1)事先获得用户的明确授权；</p>\n\n<p style=\"text-align: justify;\">(2)根据有关的法律法规要求；</p>\n\n<p style=\"text-align: justify;\">(3)按照相关政府主管部门的要求。</p>\n\n<p style=\"text-align: justify;\">2. 本公司可能会与第三方合作向用户提供相关的网络服务，在此情况下，如该第三方同意承担与本公司同等的保护用户隐私的责任，则本公司有权将用户的注册资料等提供给该第三方。</p>\n\n<p style=\"text-align: justify;\">3. 在不透露单个用户隐私资料的前提下，本公司有权对整个用户数据库进行分析并对用户数据库进行商业上的利用。</p>\n\n<p style=\"text-align: justify;\">&nbsp;</p>\n\n<p style=\"text-align: justify;\"><span style=\"font-size:20px;\">六、版权声明</span></p>\n\n<p style=\"text-align: justify;\">1. 本软件的文字、图片、音频、视频等版权均归本公司享有，未经本公司许可，不得任意转载或应用于商业使用。&nbsp;</p>\n\n<p style=\"text-align: justify;\">2. 恶意转载本软件内容的，本公司保留将其诉诸法律的权利。</p>\n\n<p style=\"text-align: justify;\">&nbsp;</p>\n\n<p style=\"text-align: justify;\">七、责任声明</p>\n\n<p style=\"text-align: justify;\">1. 用户明确同意其使用本软件服务所存在的风险及一切后果将完全由用户本人承担，本公司对此不承担任何责任。</p>\n\n<p style=\"text-align: justify;\">2. 本软件无法保证网络服务一定能满足用户的要求，也不保证网络服务的及时性、安全性、准确性。</p>\n\n<p style=\"text-align: justify;\">3. 本软件不保证为方便用户而设置的外部链接的准确性和完整性，同时，对于该等外部链接指向的不由本软件实际控制的任何网页上的内容，本公司不承担任何责任。</p>\n\n<p style=\"text-align: justify;\">4. 对于因不可抗力或本公司不能控制的原因造成的网络服务中断或其它缺陷，本公司不承担任何责任，但将尽力减少因此而给用户造成的损失和影响。</p>\n\n<p style=\"text-align: justify;\">5. 对未成年人没有受到家长书面同意的情况下，使用本软件服务所造成的损失或意外，一切后果由会员或其监护人承担，本公司对此不承担任何责任，但将尽力给会员提供协助。</p>\n\n<p style=\"text-align: justify;\">&nbsp;</p>\n\n<p style=\"text-align: justify;\"><span style=\"font-size:20px;\">八、附则</span></p>\n\n<p style=\"text-align: justify;\">1. 本协议的订立、执行和解释及争议的解决均应适用中华人民共和国法律。</p>\n\n<p style=\"text-align: justify;\">2. 如本协议中的任何条款无论因何种原因完全或部分无效或不具有执行力，本协议的其余条款仍应有效并且有约束力。</p>\n\n<p style=\"text-align: justify;\">3. 本协议解释权及修订权归本公司所有。</p>\n\n<p style=\"text-align: justify;\">&nbsp;</p>\n\n<p style=\"text-align: justify;\">&nbsp;</p>\n\n<p style=\"text-align: justify;\">&nbsp;</p>\n\n<p style=\"text-align: justify;\">&nbsp;</p>\n</body>\n</html>\n'
        res_data = {
            'status': status.HTTP_200_OK,
            'data': {
                'content': content
            }
        }

        return Response(res_data)

class ActiveUserView(viewsets.ModelViewSet):

    def list(self, request, *args, **kwargs):
        # 排名用户个数
        qty = request.query_params.get('qty', 12)
        now = datetime.datetime.now()
        project_id = request.user['project_id']

        yesterday_begin = datetime.datetime.combine(now, datetime.time.min)

        user_logs = UserLog.objects.filter(created_at__gte=yesterday_begin)
        now = datetime.datetime.now()
        user_activity = list(user_logs.values('web_user_id').annotate(total_logs=Count('web_user_id')).order_by('-total_logs'))
        user_ids = [activity['web_user_id'] for activity in user_activity if activity.get('web_user_id', None)]

        # 员工不展示
        staff_ids = [1, 20, 21, 23, 25, 27, 30, 34, 37, 66, 71, 73, 74, 86, 98, 110, 121, 200, 213]
        web_users = list(WebUser.objects.filter(id__in=user_ids).exclude(id__in=staff_ids))

        results = []
        for ua in user_activity:
            web_user = next(filter(lambda x: x.id == ua['web_user_id'], web_users), None)
            if web_user is not None:
                ua['id'] = web_user.id
                ua['name'] = web_user.name
                ua['avatar'] = web_user.avatar
                ua['last_login'] = web_user.last_login
                ua['last_active'] = UserLog.objects.filter(web_user_id=web_user.id).order_by('-created_at').first().created_at
                ua['online_time'] = web_user.online_time
                if web_user.unionid:
                    mini_user = WebUser.objects.get_mini_user(web_user.unionid, project_id)
                    ua['mini_id'] = mini_user['id']
                    ua['mini_username'] = mini_user['mini_username']
                    ua['avatar'] = mini_user['avatar']
                    # 隐藏本公司用户(user_type == 10)
                    # user_type = mini_user.get('user_type', 0)
                    # if not (user_type and is_number(user_type) and int(user_type) == 10):
                    results.append(ua)
                else:
                    if is_number(web_user.avatar) or web_user.avatar is None:
                        ua['avatar'] = ImageAPI.get_url(request, web_user.avatar, 'thumb')
                    else:
                        ua['avatar'] = web_user.avatar
                    results.append(ua)

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'users': results
            },
            'total': len(results)
        })

class UserStatView(viewsets.ModelViewSet):

    def list(self, request, *args, **kwargs):

        dt = request.query_params.get('dt', None)
        dt = datetime.datetime.now() - datetime.timedelta(days=1) if dt is None else datetime.datetime.strptime(dt, '%Y%m%d%H%M%S')

        web_user_id = self.request.user['id']

        period = request.query_params.get('period', 20)
        cur_time = dt.strftime('%Y-%m-%d 00:00:00')
        if period == '10':
            cur_time = dt.strftime('%Y-%m-%d %H:00:00')
        elif period == '30':
            cur_time = (dt - datetime.timedelta(days=dt.weekday())).strftime('%Y-%m-%d 00:00:00')
        elif period == '40':
            cur_time = dt.strftime('%Y-%m-01 00:00:00')
        elif period == '50':
            cur_time = dt.strftime('%Y-01-01 00:00:00')

        lst_time = (dt - datetime.timedelta(days=1)).strftime('%Y-%m-%d 00:00:00')

        lstwk_time = (dt - datetime.timedelta(days=dt.weekday() + 7)).strftime('%Y-%m-%d 00:00:00')

        year = dt.year - 1 if dt.month == 1 else dt.year
        month = 12 if dt.month == 1 else dt.month - 1
        lstmon_time = datetime.datetime.strptime('%s-%s-01 00:00:00' % (year, month), '%Y-%m-%d %H:%M:%S')

        cur_user_stats_list = UserStat.objects.filter(web_user_id=web_user_id, target_dt=cur_time, periods=period)
        cur_user_stats = None
        if len(cur_user_stats_list) > 0:
            cur_user_stats = cur_user_stats_list[0]

        lst_user_stats_list = UserStat.objects.filter(web_user_id=web_user_id, target_dt=lst_time, periods=20)
        lst_user_stats = None
        if len(lst_user_stats_list) > 0 and (period == '10' or period == '20'):
            lst_user_stats = lst_user_stats_list[0]

        lstwk_user_stats_list = UserStat.objects.filter(web_user_id=web_user_id, target_dt=lstwk_time, periods=30)
        lstwk_user_stats = None
        if len(lstwk_user_stats_list) > 0 and (period == '10' or period == '20' or period == '30'):
            lstwk_user_stats = lstwk_user_stats_list[0]

        lstmon_user_stats_list = UserStat.objects.filter(web_user_id=web_user_id, target_dt=lstmon_time, periods=40)
        lstmon_user_stats = None
        if len(lstmon_user_stats_list) > 0 and (period == '10' or period == '20' or period == '30' or period == '40'):
            lstmon_user_stats = lstmon_user_stats_list[0]

        # 活跃度排名
        ranking = {
            # 当期排名
            "cur": cur_user_stats.ranking if cur_user_stats else "--",
            # 昨天排名
            "lst": lst_user_stats.ranking if lst_user_stats else "--",
            # 上周排名
            "lstwk": lstwk_user_stats.ranking if lstwk_user_stats else "--",
            # 上月排名
            "lstmon": lstmon_user_stats.ranking if lstmon_user_stats else "--"
        }

        # 打开平台次数
        acc_times = {
            # 当期次数
            "cur": cur_user_stats.acc_times if cur_user_stats else '--',
            # 相对上期的上升或下降的百分比
            "lst": round((cur_user_stats.acc_times - lst_user_stats.acc_times) / lst_user_stats.acc_times * 100, 2) if cur_user_stats and lst_user_stats and lst_user_stats.acc_times else "--",
            # 相对于上个星期上升或下降的百分比
            "lstwk": round((cur_user_stats.acc_times - lstwk_user_stats.acc_times) / lstwk_user_stats.acc_times * 100, 2) if cur_user_stats and lstwk_user_stats and lstwk_user_stats.acc_times else "--",
            # 相对于上个月上升或下降的百分比
            "lstmon": round((cur_user_stats.acc_times - lstmon_user_stats.acc_times) / lstmon_user_stats.acc_times * 100, 2) if cur_user_stats and lstmon_user_stats and lstmon_user_stats.acc_times else "--"
        }

        # 在线时长
        online_time = {
            # 当期在线时长（分钟）
            "cur": round(cur_user_stats.online_time, 2) if cur_user_stats else '--',
            # 相对上期的上升或下降的百分比
            "lst": round((cur_user_stats.online_time - lst_user_stats.online_time) / lst_user_stats.online_time * 100, 2) if cur_user_stats and lst_user_stats and lst_user_stats.online_time else "--",
            # 相对于上个星期上升或下降的百分比
            "lstwk": round((cur_user_stats.online_time - lstwk_user_stats.online_time) / lstwk_user_stats.online_time * 100, 2) if cur_user_stats and lstwk_user_stats and lstwk_user_stats.online_time else "--",
            # 相对于上个月上升或下降的百分比
            "lstmon": round((cur_user_stats.online_time - lstmon_user_stats.online_time) / lstmon_user_stats.online_time * 100, 2) if cur_user_stats and lstmon_user_stats and lstmon_user_stats.online_time else "--"
        }

        # 访问页面数
        acc_pages = {
            # 当期访问的页面数
            "cur": cur_user_stats.acc_pages if cur_user_stats else '--',
            # 相对上期的上升或下降的百分比
            "lst": round((cur_user_stats.acc_pages - lst_user_stats.acc_pages) / lst_user_stats.acc_pages * 100, 2) if cur_user_stats and lst_user_stats and lst_user_stats.acc_pages else "--",
            # 相对于上个星期上升或下降的百分比
            "lstwk": round((cur_user_stats.acc_pages - lstwk_user_stats.acc_pages) / lstwk_user_stats.acc_pages * 100, 2) if cur_user_stats and lstwk_user_stats and lstwk_user_stats.acc_pages else "--",
            # 相对于上个月上升或下降的百分比
            "lstmon": round((cur_user_stats.acc_pages - lstmon_user_stats.acc_pages) / lstmon_user_stats.acc_pages * 100, 2) if cur_user_stats and lstmon_user_stats and lstmon_user_stats.acc_pages else "--"
        }

        return Response({
            "status": status.HTTP_200_OK,
            "data": {
                "my_stats": {
                    "ranking": ranking,
                    "acc_times": acc_times,
                    "online_time": online_time,
                    "acc_pages": acc_pages
                }
            }
        })

class UserDimensionView(viewsets.ModelViewSet):
    """用户维度"""
    def get_queryset(self):
        queryset = DimensionUser.objects.all()

        user_id = self.request.query_params.get('user_id', None)
        if user_id is None:
            raise exceptions.ValidationError(detail={'detail': 'user_id is required!'})

        queryset = queryset.filter(web_user_id=user_id)

        return queryset.order_by('dimension_id')

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()

        dimension_ids = queryset.values_list('dimension_id', flat=True)

        result = []

        for d_id in dimension_ids:
            dimension = Dimension.objects.get(pk=d_id)

            result.append({
                "id": dimension.id,
                "name": dimension.name,
            })

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'user_dimensions': result,
            }
        })

    def create(self, request, *args, **kwargs):
        project_id = request.user.get('project_id')
        user_id = request.data.get('user_id', None)
        if user_id is None:
            raise exceptions.ValidationError(detail={'detail': 'user_id is required!'})
        web_user = get_object_or_404(WebUser, pk=user_id)

        user = WebUser.objects.get(pk=request.user['id'])
        # 是否管理员角色
        is_admin = user.web_roles.filter(en_name='admin').exists() or user.is_super

        # 管理员或者自己可以编辑用户信息
        if is_admin or user.id == web_user.id:

            dimension_ids = request.data.get('dimension_ids', None)
            if dimension_ids is None:
                raise exceptions.ValidationError(detail={'detail': 'dimension_ids is required!'})
            if isinstance(dimension_ids, int):
                dimension_ids = [dimension_ids]

            current_dimension_ids = list(DimensionUser.objects.filter(web_user_id=user_id).values_list('dimension_id', flat=True))
            new_dimension = set(dimension_ids) - set(current_dimension_ids)
            del_dimension = set(current_dimension_ids) - set(dimension_ids)

            with transaction.atomic(using=f"prj{project_id}db"):
                for dimension_id in new_dimension:
                    DimensionUser.objects.create(
                        dimension_id=dimension_id,
                        web_user_id=user_id
                    )

                DimensionUser.objects.filter(web_user_id=user_id, dimension_id__in=del_dimension).delete()

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })
