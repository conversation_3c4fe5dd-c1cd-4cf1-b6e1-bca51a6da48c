import logging

from django.core.exceptions import ObjectDoesNotExist
from rest_framework import exceptions
from rest_framework import serializers

from saian_api.device.models import Device
from saian_api.project.models import Project
from saian_api.utils.httpapi.image import ImageAPI
from .models import DeviceIssue, IssueWhitelist, CheckingRecord


class DeviceIssueSerializer(serializers.ModelSerializer):
    project_id = serializers.PrimaryKeyRelatedField(queryset=Project.objects.all())
    device_id = serializers.PrimaryKeyRelatedField(queryset=Device.objects.all())

    class Meta:
        model = DeviceIssue
        fields = ['id', 'eventid', 'name', 'display_name', 'is_solved', 'issue_type',
                  'device_type_id', 'created_at', 'updated_at', 'device_id', 'project_id']

    def to_representation(self, instance):
        ret = super().to_representation(instance)

        ret['device'] = {
            'id': instance.device.id,
            'nick_name': instance.device.nick_name,
            'mac': instance.device.mac,
            'wudid': instance.device.wudid,
            'type_name': instance.device.device_type.name,
            'prototype_uniname': instance.device.device_prototype.uni_name
        }

        view = self.context['view']
        if view.action == 'retrieve' or view.action == 'partial_update':
            checking_records = CheckingRecord.objects.filter(device_issue=instance)
            temp_crs = []
            try:
                for cr in checking_records:
                    temp_cr = {
                        'id': cr.id,
                        'remark': cr.remark,
                        'device_status': cr.device_status,
                        'created_at': cr.created_at,
                        'images': ImageAPI.get_urls(self.context['request'], image_id=cr.images),
                        'user': {
                            'id': cr.user.id,
                            'name': cr.user.name,
                            'avatar_url': ImageAPI.get_url(self.context['request'], image_id=cr.user.avatar)
                        }
                    }

                    temp_crs.append(temp_cr)
            except ObjectDoesNotExist as e:
                logging.warning(e.with_traceback)

            ret['checking_records'] = temp_crs

        ret.pop('device_type_id')
        ret.pop('project_id')
        ret.pop('device_id')

        return ret

class IssueWhitelistSerializer(serializers.ModelSerializer):
    device_id = serializers.IntegerField()

    class Meta:
        model = IssueWhitelist
        fields = ['id', 'issue_name', 'days', 'device_id', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']

    def to_representation(self, instance):
        ret = super().to_representation(instance)

        ret['left_days'] = instance.left_days
        ret['device'] = {
            'id': instance.device.id,
            'nick_name': instance.device.nick_name
        }
        dp = instance.device.device_prototype
        ret['device_prototype'] = {
            'id': dp.id,
            'name': dp.name
        }

        return ret

class CheckingRecordModelSerializer(serializers.ModelSerializer):
    device_issue = serializers.PrimaryKeyRelatedField(queryset=DeviceIssue.objects.all())

    class Meta:
        model = CheckingRecord
        fields = ['id', 'user_id', 'images', 'remark', 'device_status', 'device_issue', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']

class CheckingRecordSerializer(serializers.Serializer):
    issue_id = serializers.IntegerField()
    images = serializers.CharField(min_length=1, max_length=220, required=False)
    remark = serializers.CharField(min_length=1, max_length=200, required=False)
    is_checked = serializers.BooleanField(required=False)

    def create(self, validated_data):
        try:
            issue = DeviceIssue.objects.get(pk=validated_data['issue_id'])
            serializer = CheckingRecordModelSerializer(data={
                'device_issue': validated_data['issue_id'],
                'user_id': validated_data['user_id'],
                'images': validated_data['images'],
                'device_status': issue.device.status,
                'remark': validated_data['remark']
            })

            if serializer.is_valid(raise_exception=True):
                cr = serializer.save()
        except ObjectDoesNotExist:
            raise exceptions.ValidationError('Device issue not exist!')

        return cr

    def to_representation(self, instance):
        ret = {
            'id': instance.id,
            'remark': instance.remark,
            'images': ImageAPI.get_urls(self.context['request'], image_id=instance.images),
            'created_at': instance.created_at
        }

        return ret
