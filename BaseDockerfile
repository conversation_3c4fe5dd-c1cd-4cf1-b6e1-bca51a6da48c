# drf基础镜像构建 (saian-api-base)
# v0.5: 添加依赖 numpy
# v0.6: 添加依赖 cryptography
# v0.8: 添加依赖 chinesecalendar 中国节假日支持至 2025
# 构建命令 docker build -f BaseDockerfile -t swr.cn-south-1.myhuaweicloud.com/cloud-gz/saian-api-base:v0.8 ./

FROM swr.cn-south-1.myhuaweicloud.com/cloud-gz/saian-api-base:v0.6

# 设置环境变量
ENV PYTHONUNBUFFERED 1

#RUN mkdir /saian

# 设置工作目录
WORKDIR /saian

RUN pip install --no-cache-dir chinesecalendar -i https://mirrors.aliyun.com/pypi/simple/ \
   && pip cache purge

#COPY ./requirements.txt ./

#RUN apt update \
#    && apt install -y vim \
#    && apt clean \
#    && pip install --no-cache-dir -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/ \
#    && pip cache purge
