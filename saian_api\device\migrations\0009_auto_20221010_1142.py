# Generated by Django 3.2.8 on 2022-10-10 11:42

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('device', '0008_devicelimit'),
    ]

    operations = [
        migrations.CreateModel(
            name='DeviceRuntime',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('mac', models.<PERSON>r<PERSON>ield(max_length=100)),
                ('nick_name', models.Char<PERSON>ield(max_length=100)),
                ('sub_idx', models.IntegerField(null=True)),
                ('old_statuses', models.CharField(blank=True, max_length=255, null=True)),
                ('new_statuses', models.CharField(max_length=255)),
                ('run_time', models.IntegerField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('update_at', models.DateTime<PERSON>ield(auto_now=True)),
                ('device', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='device.device')),
            ],
            options={
                'db_table': 'device_runtime',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='deviceruntime',
            index=models.Index(fields=['device_id'], name='device_runt_device__59e352_idx'),
        ),
        migrations.AddIndex(
            model_name='deviceruntime',
            index=models.Index(fields=['mac'], name='device_runt_mac_7b4a97_idx'),
        ),
        migrations.AddIndex(
            model_name='deviceruntime',
            index=models.Index(fields=['device_id', 'created_at'], name='device_runt_device__46f3cc_idx'),
        ),
        migrations.AddIndex(
            model_name='deviceruntime',
            index=models.Index(fields=['mac', 'created_at'], name='device_runt_mac_c95478_idx'),
        ),
    ]
