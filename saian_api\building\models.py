import json
from json import J<PERSON><PERSON>ecodeError

from django.contrib.contenttypes.models import ContentType
from django.db import models, transaction, IntegrityError
from rest_framework import exceptions

from saian_api.project.models import Project
from saian_api.terminal.models import Terminal

# Create your models here.
class Building(models.Model):
    """ 建筑 """
    # 所属项目
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    # 楼层编号
    building_no = models.IntegerField(null=True, default=None)
    # 建筑图片
    image = models.CharField(max_length=255, blank=True, null=True)
    # 名称
    name = models.CharField(max_length=255)
    # 地址
    address = models.CharField(max_length=255, blank=True, null=True)
    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'buildings'
        ordering = ['building_no']
        indexes = [
            models.Index(fields=['id', 'project_id']),
        ]

class Floor(models.Model):
    """ 楼层 """
    # 所属建筑
    building = models.ForeignKey(Building, on_delete=models.CASCADE)
    # 楼层编号
    floor_no = models.IntegerField(null=True)
    # 楼层图片
    image = models.CharField(max_length=255, blank=True, null=True)
    # 名称
    name = models.CharField(max_length=255, blank=True, null=True)
    # 楼层缩略图
    thumb = models.CharField(max_length=255, blank=True, null=True)
    # 楼层平面图组件数据，json格式
    coords_arr = models.TextField(blank=True, null=True)
    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'floors'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['id', 'building_id']),
        ]

    # 校验楼层的元素
    # 如果有房间则绑定设备与房间的关系
    # @transaction.atomic()
    def update_with_covers(self, request):
        from saian_api.device.models import Device, RoomDevice

        try:
            project_id = request.user.get('project_id')
            with transaction.atomic(using=f"prj{project_id}db"):
                covers = json.loads(request.data['coords_arr'])
                for cover_t in enumerate(covers):
                    cover = cover_t[1]
                    # if cover['room'] is not None and cover['room'] != '':
                    if cover.get('room', None) is not None and cover['room'] != '':
                        room = ActiveRoom.objects.filter(floor_id=self.id, name=cover['room']).last()
                        device = Device.objects.filter(mac=cover['mac']).last()
                        if room is not None and device is not None:
                            device_model = ContentType.objects.get_for_model(Device)
                            rd_not_exist = RoomDevice.objects.filter(active_room_id=room.id, object_id=device.id, content_type=device_model).exists()
                            if not rd_not_exist:
                                RoomDevice.objects.create(active_room_id=room.id, object_id=device.id, content_type=device_model)
        except JSONDecodeError:
            raise exceptions.APIException('解析coords_arr出错，请检查coords_arr是否符合要求！')
        except IntegrityError:
            print('校验楼层的元素出错！')

class RoomManager(models.Manager):
    """
      房间管理器
    """
    def manager_test(self):
        return "manager_test"

class ActiveRoom(models.Model):
    """ 房间 """
    # 所属建筑
    building = models.ForeignKey(Building, on_delete=models.CASCADE)
    # 所属楼层
    floor = models.ForeignKey(Floor, on_delete=models.CASCADE)
    # 房间编号
    active_room_no = models.IntegerField(null=True, default=None)
    # 图片
    image = models.CharField(max_length=255, blank=True, null=True)
    # 名称
    name = models.CharField(max_length=255, blank=True, null=True)
    # 温度报警下限
    min_temp = models.IntegerField(null=True)
    # 温度报警上限
    max_temp = models.IntegerField(null=True)
    # 湿度报警下限
    min_humidity = models.IntegerField(null=True)
    # 湿度报警上限
    max_humidity = models.IntegerField(null=True)
    # tvoc浓度报警下限
    min_tvoc = models.IntegerField(null=True)
    # tvoc浓度报警上限
    max_tvoc = models.IntegerField(null=True)
    # co2浓度报警下限
    min_co2 = models.IntegerField(null=True)
    # co2浓度报警上限
    max_co2 = models.IntegerField(null=True)
    # pm25浓度报警下限
    min_pm25 = models.IntegerField(null=True)
    # pm25浓度报警上限
    max_pm25 = models.IntegerField(null=True)
    # 数据是否在datav显示
    show_in_datav = models.BooleanField(default=False)
    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'active_rooms'
        ordering = ['active_room_no']
        indexes = [
            models.Index(fields=['id', 'building_id']),
            models.Index(fields=['id', 'floor_id']),
        ]

    objects = RoomManager()

    def stats(self):
        """
          根据房间和设备的绑定关系，统计房间当前设备数
          total - 总数，total_run - 运行数，total_stop - 停止数，total_fault - 故障数
        """
        from saian_api.device.models import RoomDevice
        from saian_api.device.models import Device

        room_members = RoomDevice.objects.filter(active_room_id=self.id)
        total = room_members.count()
        total_run = 0
        total_stop = 0
        total_fault = 0
        if room_members.exists():
            member_model = room_members.first().content_type
            member_from = None

            if member_model.name == 'device':
                member_from = Device.objects
            elif member_model.name == 'terminal':
                member_from = Terminal.objects

            if member_from is not None:
                members = member_from.filter(id__in=room_members.values_list('object_id', flat=True))
                total = members.count()
                total_run = members.filter(sw_on__exact=True).count()
                total_stop = members.filter(sw_on__exact=False).count()
                total_fault = members.filter(in_fault__exact=True).count()

        result = {
            'total': total,
            'total_run': total_run,
            'total_stop': total_stop,
            'total_fault': total_fault
        }

        return result

    def location(self):
        """ 房间位置 """
        return f'{self.building.name}{self.floor.name}{self.name}'

class RoomParam(models.Model):
    """
      房间参数记录表，主要用于报表统计，以提高报表统计效率
    """
    # 所属房间
    active_room = models.ForeignKey(ActiveRoom, on_delete=models.CASCADE)
    # room_no
    room_no = models.CharField(max_length=255, db_index=True)
    # 参数标识
    identifier = models.CharField(max_length=255)
    # 参数值
    value = models.CharField(max_length=255)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'room_params'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['room_no', 'identifier']),
            models.Index(fields=['active_room_id', 'identifier']),
            models.Index(fields=['room_no', 'created_at']),
            models.Index(fields=['active_room_id', 'created_at']),
            models.Index(fields=['active_room_id', 'identifier', 'created_at']),
            models.Index(fields=['room_no', 'identifier', 'created_at'])
        ]
