"""
    项目天气周统计
"""
import datetime
import traceback

from django.core.management import BaseCommand, CommandError

from saian_api.dashboard.models import ProjectWeather
from saian_api.dashboard.tasks.tasks import computeWeatherData
from saian_api.scheduledtask.utils import get_projects, set_global_db

class Command(BaseCommand):
    help = "项目周天气"

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目ID列表
        projects = get_projects(self, options)
        now = datetime.datetime.now()
        # now = datetime.datetime.strptime('20240514150500', '%Y%m%d%H%M%S')

        # 每周一运行
        if now.isoweekday() != 1:
            return

        begin = (now - datetime.timedelta(days=7)).replace(hour=0, minute=0, second=0, microsecond=0)
        end = (begin + datetime.timedelta(days=7)) - datetime.timedelta(seconds=1)

        for project_id in projects:
            try:
                # 设置全局数据库
                set_global_db(project_id)

                weathers = ProjectWeather.objects.filter(created_at__range=[begin, end], type='di', project_id=project_id)
                weather_weekly = computeWeatherData(weathers, project_id, 'wk', end)
                record = ProjectWeather.objects.filter(type='wk', created_at=end, project_id=project_id).last()
                if record is None:
                    weather_weekly.save()
                else:
                    weather_weekly.id = record.id
                    # weather_weekly.save()

            except CommandError:
                self.stderr.write(f"运行'项目天气日统计'任务失败，项目ID：{project_id}, 命令参数不合法！", ending='\n')
                continue
            except Exception as e:
                self.stderr.write(f"运行'项目天气日统计'任务失败，项目ID：{project_id}，error: {e.__str__()}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
                continue
