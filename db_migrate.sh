#!/bin/bash

# 正式环境数据库迁移
 PROJECTS=(1 4 6 7 8 9 10 11 12 13 14 15 16 17 18 19 20 21 22 23 24 25 26 27 28 29 30 31 32 33 34 35 36 37 38 40 42 43 44 46 47 48 49 50 58 66 67 68 69 70 71 72 73 74 75 76 77 78 79 80 81 82 83 84 85 86 87 88 89 90 91 92 93 94 95 96 97 98 99 100 101 102 103 104 105 106 107 108 109 110)
#PROJECTS=(66)

python manage.py makemigrations &&
  for prj in "${PROJECTS[@]}"; do
    echo "开始迁移项目数据库：prj${prj}db"
    python manage.py migrate --database="prj${prj}db"
  done
