import datetime
import json
import logging
import pickle
import re
from itertools import chain

from django.core.exceptions import MultipleObjectsReturned, ObjectDoesNotExist
from django.db import models
from django.db.models import Q
from django.db.models.base import ModelState
from rest_framework import exceptions

from saian_api.utils.db.Redis import RedisHelper
from saian_api.utils.utils import AuthUtils

# Create your models here.
"""
  设备分类
"""


class DeviceType(models.Model):
    # 父分类id
    parent = models.ForeignKey('self', blank=True, null=True, related_name='children', db_column='pid', on_delete=models.CASCADE)
    # 名字
    name = models.CharField(max_length=255)
    # icon
    icon = models.CharField(max_length=255, null=True)
    # 唯一名称
    uni_name = models.CharField(max_length=255)
    # 是否默认分类
    is_default = models.BooleanField(default=False)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'device_types'
        ordering = ['-created_at']

    # 获取所有的子分类，默认的和项目拥有的
    def filter_sub_types(self, project_id):
        from saian_api.device.models import Device

        default_ids = DeviceType.objects.filter(is_default=True).values_list('id', flat=True)
        other_ids = Device.objects.filter(project_id=project_id).values_list('device_type_id', flat=True)
        sub_ids = list(chain(default_ids, other_ids))
        sub_types = DeviceType.objects.filter(id__in=sub_ids, parent=self)

        return sub_types

    def run_stats(self):
        from saian_api.device.models import Device
        devices = Device.objects.filter(device_type_id=self.id, project__isnull=False)
        return {
            'device_type': self.name,
            'fault_total': devices.filter(in_fault=True).count(),
            'offline_total': devices.filter(online=False).count(),
            'run_total': devices.filter(online=True, sw_on=True).count(),
            'stop_total': devices.filter(online=True, sw_on=False).count(),
            'total': devices.count()
        }

    def device_stats(self):
        from saian_api.device.models import Device
        devices = Device.objects.filter(device_type_id=self.id, project__isnull=False)
        on_devices = devices.filter(online=True).count()
        off_devices = devices.filter(online=False).count()

        result = [{
            'on_value': on_devices,
            'name': self.name
        }, {
            'off_value': off_devices,
            'name': self.name
        }]

        # if on_devices:
        # if off_devices:

        return result

    def issue_stats(self):
        from saian_api.issue.models import DeviceIssue

        now = datetime.datetime.now()
        from_7_day = (now - datetime.timedelta(days=7)).strftime('%Y-%m-%d 00:00:00')
        return {
            'device_type': self.name,
            'uni_name': self.uni_name,
            'total': DeviceIssue.objects.filter(
                created_at__range=[from_7_day, now],
                issue_type=10,
                device_type_id=self.id
            ).count()
        }


class DevicePrototypeManager(models.Manager):
    def ap_values(self, attres, da, ap=None):
        from saian_api.utils.tools import is_number

        if ap is None:
            ap = AttributePrototype.objects.get(pk=da.attribute_prototype_id)
        attres['id'] = ap.id
        attres['value'] = da.value
        if ap.data_type == 20:
            if ap.identifier == 'RSSI':
                attres['value'] = round(int(da.value, 16)) if is_number(da.value) else ''
            else:
                attres['value'] = round(float(da.value), 2) if is_number(da.value) else ''
        attres['name'] = ap.label
        attres['identifier'] = ap.identifier
        attres['data_type'] = ap.data_type
        attres['pre_cision'] = ap.pre_cision
        attres['in_crement'] = ap.in_crement
        attres['min_value'] = ap.min_value
        attres['max_value'] = ap.max_value
        attres['options'] = ap.options
        attres['unit'] = ap.unit
        attres['default_value'] = ap.default_value
        attres['remark'] = ap.remark
        attres['read_only'] = ap.read_only
        attres['background_color'] = '#F7F8FA'

        if 'FaultType' in attres['identifier'] and (attres['value'] != '无故障' and attres['value'] != '0'):
            attres['background_color'] = '#E0C2BF'

        # 故障状态值不等于无故障时标红
        if 'FaultStatus' in attres['identifier'] and (attres['value'] != '无故障' and attres['value'] != '0'):
            attres['background_color'] = '#E0C2BF'

        # 报警类型不等于无报警时标红
        if 'AlarmType' in attres['identifier'] and (attres['value'] != '无报警' and attres['value'] != '0'):
            attres['background_color'] = '#F8EECF'

        # 报警状态值不等于无报警时标红
        if 'AlarmStatus' in attres['identifier'] and (attres['value'] != '无报警' and attres['value'] != '0'):
            attres['background_color'] = '#F8EECF'

        if 'RunStatus' in attres['identifier']:
            if isinstance(attres['value'], str) and ('停止' not in attres['value'] and '关' not in attres['value'] and '禁用' not in attres['value']):
                attres['background_color'] = '#CEF7EA'
            else:
                attres['background_color'] = '#BCD8F0'

        # 设备状态值背景变化：离线时标红，包含“禁用”时深灰，包含“蓄冷”时绿色，包含“蓄热”时橙色，
        if '_Status' in attres['identifier']:
            if '离线' in attres['value']:
                attres['background_color'] = '#E0C2BF'
            elif '运行' in attres['value']:
                attres['background_color'] = '#CEF7EA'
            elif '禁用' in attres['value']:
                attres['background_color'] = '#D9DBE3'
            elif '蓄冷' in attres['value']:
                attres['background_color'] = '#CEF7EA'
            elif '蓄热' in attres['value']:
                attres['background_color'] = '#F8EECF'
            # 电表类型，除在线外全部标红
            if 'Meter' in attres['identifier'] and '电表' in ap.name and attres['value'] != '在线':
                attres['background_color'] = '#E0C2BF'


        # 水流开关值为闭合时标绿
        if '水流开关' in ap.name and attres['value'] == '闭合':
            attres['background_color'] = '#CEF7EA'

    # 根据设备类型的content或web content配置解析并添加相应设备的参数和值

    def parse_content(self, device, content, prefix=None, idx=None, das=None, aps=None):
        jsons = None
        if content is not None and content != '':
            try:
                jsons = json.loads(content)
                for item in jsons:
                    components = item.get('components', None)
                    if components is not None:
                        for comp in components:
                            if 'attr' in comp and comp['attr'] is not None:
                                self.content_attr(device, comp, prefix=prefix, idx=idx, das=das, aps=aps)

                            if 'attres' in comp and comp['attres'] is not None:
                                self.content_attres(device, comp, prefix=prefix, idx=idx, das=das, aps=aps)

                            if 'components' in comp and comp['components'] is not None:
                                comp['components'] = self.parse_content(device, json.dumps(comp['components']), prefix=prefix, idx=idx, das=das,
                                                                        aps=aps)

                            if 'tabs' in comp and comp['tabs'] is not None:
                                comp["tabs"] = self.parse_content(device, json.dumps(comp['tabs']), prefix=prefix, idx=idx, das=das, aps=aps)

                            if 'branches' in comp and comp['branches'] is not None:
                                comp["branches"] = self.parse_content(device, json.dumps(comp['branches']), prefix=prefix, idx=idx, das=das, aps=aps)

                    if 'attr' in item and item['attr'] is not None:
                        self.content_attr(device, item, prefix=prefix, idx=idx, das=das, aps=aps)

                    if 'attres' in item and item['attres'] is not None:
                        self.content_attres(device, item, prefix=prefix, idx=idx, das=das, aps=aps)

                    if 'tabs' in item and item['tabs'] is not None:
                        item["tabs"] = self.parse_content(device, json.dumps(item['tabs']), prefix=prefix, idx=idx, das=das, aps=aps)

                    if 'branches' in item and item['branches'] is not None:
                        item["branches"] = self.parse_content(device, json.dumps(item['branches']), prefix=prefix, idx=idx, das=das, aps=aps)

            except json.JSONDecodeError:
                logging.warning('Device prototype parse content exception, device id = %s', str(device.id))

        return jsons

    # 解析界面配置的单参数
    def content_attr(self, device, comp, prefix=None, idx=None, das=None, aps=None):
        from saian_api.device.models import DeviceAttribute
        try:
            name = None
            label = None
            da = None
            idf = None
            ap = None
            # 配置的内容
            confs = {}
            if prefix is not None and idx is not None:
                if comp['attr'].get(f'{prefix}_{idx}', None) is not None:
                    idf = comp['attr'][f'{prefix}_{idx}']['idf']
                    confs = comp['attr'][f'{prefix}_{idx}']
                    if 'name' in comp['attr'][f'{prefix}_{idx}']:
                        name = comp['attr'][f'{prefix}_{idx}']['name']
                    if 'label' in comp['attr'][f'{prefix}_{idx}']:
                        label = comp['attr'][f'{prefix}_{idx}']['label']
                    # da = device.get_attr_by_name(idf)
            else:
                if 'attr' in comp and comp['attr'].get('default', None) is not None:
                    idf = comp['attr']['default']['idf']
                    confs = comp['attr']['default']
                    if 'name' in comp['attr']['default']:
                        name = comp['attr']['default']['name']
                    if 'label' in comp['attr']['default']:
                        label = comp['attr']['default']['label']
                    # da = device.get_attr_by_name(idf)

            if idf is not None:
                if das is not None and aps is not None:
                    ap_list = list(filter(lambda x: x.identifier == idf, aps))
                    if len(ap_list):
                        ap = ap_list[0]
                        da_list = list(filter(lambda x: x.attribute_prototype_id == ap.id, das))
                        if len(da_list):
                            da = da_list[0]
                if da is None:
                    da = device.get_attr_by_name(idf)

            if da is not None:
                tmp_attr = {}
                self.ap_values(tmp_attr, da, ap=ap)

                # 如果配置json配置了name，则覆盖属性的name
                if name is not None:
                    tmp_attr['name'] = name

                # 如果配置json配置了label，则增加属性的label
                if label is not None:
                    tmp_attr['label'] = label

                # 返回配置的内容
                for k, v in confs.items():
                    if k not in tmp_attr:
                        tmp_attr[k] = v

                # 温控器v2酒店版，特殊功能为湿度控制时，运行模式的值默认显示为除湿
                if device.device_prototype_id == 223 and tmp_attr['identifier'] == 'FCURunningMode' and DeviceAttribute.objects.query_object_by_idf(device, 'SpecialFunc').value == '湿度控制':
                    tmp_attr['value'] = '除湿'

                comp['attr'] = tmp_attr
        except ValueError:
            raise exceptions.APIException('Parse content exception!')

    # 解析多个参数
    def content_attres(self, device, comp, prefix=None, idx=None, das=None, aps=None):
        try:
            if 'attres' in comp and comp['attres'] is not None:
                attres = []

                for ja in comp['attres']:
                    name = None
                    da = None
                    idf = None
                    ap = None
                    # 配置的内容
                    confs = {}
                    if prefix is not None and idx is not None:
                        prefx_idx = ''.join([prefix, '_', str(idx)])
                        if ja[prefx_idx] is not None and 'idf' in ja[prefx_idx]:
                            idf = ja[prefx_idx]['idf']
                            confs = ja[prefx_idx]
                            if das is not None and aps is not None:
                                ap_list = list(filter(lambda x: x.identifier == idf, aps))
                                if len(ap_list):
                                    ap = ap_list[0]
                                    da_list = list(filter(lambda x: x.attribute_prototype_id == ap.id, das))
                                    if len(da_list):
                                        da = da_list[0]

                            if da is None:
                                da = device.get_attr_by_name(idf)

                        if 'name' in ja[prefx_idx]:
                            name = ja[prefx_idx]['name']
                    else:
                        if ja.get('default', None) is not None:
                            if 'idf' in ja['default']:
                                idf = ja['default']['idf']
                                confs = ja['default']
                                if das is not None and aps is not None:
                                    ap_list = list(filter(lambda x: x.identifier == idf, aps))
                                    if len(ap_list):
                                        ap = ap_list[0]
                                        da_list = list(filter(lambda x: x.attribute_prototype_id == ap.id, das))
                                        if len(da_list):
                                            da = da_list[0]
                                if da is None:
                                    da = device.get_attr_by_name(ja['default']['idf'])

                            if 'name' in ja['default']:
                                name = ja['default']['name']

                    if da is not None:
                        tmp_attr = {}
                        self.ap_values(tmp_attr, da, ap=ap)

                        # 如果配置json配置了name，则覆盖属性的name
                        if name is not None:
                            tmp_attr['name'] = name

                        # 返回配置的内容
                        for k, v in confs.items():
                            if k not in tmp_attr:
                                if k == 'subIdf':
                                    da = device.get_attr_by_name(v)
                                    if da is not None:
                                        sub_tmp_attr = {}
                                        sub_ap = AttributePrototype.objects.query_by_idf(device, v)
                                        self.ap_values(sub_tmp_attr, da, ap=sub_ap)
                                        tmp_attr['subIdf'] = sub_tmp_attr
                            else:
                                tmp_attr[k] = v

                        attres.append(tmp_attr)

                # 改为attr方便前端处理
                comp['attr'] = attres
                comp.pop('attres')
        except ValueError:
            raise exceptions.APIException('Parse content exception!')

    # web content
    def web_content(self, device, prefix=None, idx=None):
        """获取设备(终端)的设备类型 web_content 配置"""
        from saian_api.device.models import DeviceAttribute

        web_content = device.device_prototype.web_content
        if prefix is not None and idx is not None:
            # 如果找不到子设备类型，则使用父设备类型的 content。这是为了适配旧项目的设备类型(一些旧设备类型没有子设备类型)
            sub_dp = device.sub_dp(prefix)
            web_content = sub_dp.web_content if sub_dp else web_content
        if web_content is not None:
            idf_list = re.findall(r'"idf":\s?"(\w+)"', web_content)
            target_aps = list(AttributePrototype.objects.filter(device_prototype_id=device.device_prototype_id, identifier__in=idf_list))
            ap_ids = [ap.id for ap in target_aps]
            # target_das = list(DeviceAttribute.objects.filter(device_id=device.id, attribute_prototype_id__in=ap_ids))
            target_das = DeviceAttribute.objects.query_object_list(device, ap_ids)

            return self.parse_content(device, web_content, prefix, idx, das=target_das, aps=target_aps)
        else:
            return []

    # content
    def content(self, device, prefix=None, idx=None):
        """获取设备(终端)的设备类型 content 配置"""
        from saian_api.device.models import DeviceAttribute

        content = device.device_prototype.content
        if prefix is not None and idx is not None:
            # 如果找不到子设备类型，则使用父设备类型的 content。这是为了适配旧项目的设备类型(一些旧设备类型没有子设备类型)
            sub_dp = device.sub_dp(prefix)
            content = sub_dp.content if sub_dp is not None else content
        if content is not None:
            idf_list = re.findall(r'"idf":\s?"(\w+)"', content)
            target_aps = list(AttributePrototype.objects.filter(device_prototype_id=device.device_prototype_id, identifier__in=idf_list))
            ap_ids = [ap.id for ap in target_aps]
            # target_das = list(DeviceAttribute.objects.filter(device_id=device.id, attribute_prototype_id__in=ap_ids))
            target_das = DeviceAttribute.objects.query_object_list(device, ap_ids)

            return self.parse_content(device, content, prefix, idx, das=target_das, aps=target_aps)
        else:
            return []


"""
  设备类型
"""


class DevicePrototype(models.Model):
    # 所属分类
    device_type = models.ForeignKey(DeviceType, on_delete=models.CASCADE)
    # 名称
    name = models.CharField(max_length=255)
    # 模块名称
    m_name = models.CharField(max_length=255, null=True)
    # 唯一名称
    uni_name = models.CharField(max_length=255)
    # 是否跳过初始配置（小程序扫码添加设备时）
    skip_config = models.BooleanField(default=False)
    # 小程序设备详情界面配置数据，json格式
    content = models.TextField(null=True)
    # web设备详情界面配置数据，json格式
    web_content = models.TextField(null=True)
    # 父设备类型
    parent = models.ForeignKey('self', blank=True, null=True, db_column='pid', related_name='children', on_delete=models.CASCADE)
    # 设备类型前缀
    prefix = models.CharField(max_length=255, null=True)
    # 设备列表的参数列表配置
    dashboard_attres = models.TextField(null=True)
    # 设备详情的参数列表配置
    detail_attres = models.TextField(null=True)
    # 设备icon
    icons = models.CharField(max_length=255, null=True)
    # 上行数据的解析代码
    up_parser = models.TextField(null=True)
    # 下行数据的解析代码
    down_parser = models.TextField(null=True)
    # 判断该类型下的终端是否超时，单位秒，默认 7200 秒（即2个小时）
    timeout_set = models.IntegerField(default=7200, null=True)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'device_prototypes'
        ordering = ['-created_at']

    objects = DevicePrototypeManager()

    # 解析设备上报数据
    def parse_up_data(self, data=None, mac=None, db=None):
        from saian_api.scheduledtask.utils import set_global_db

        try:
            if db is not None:
                set_global_db(db)
            if self.up_parser is None:
                return None
            exec(self.up_parser, None, locals())
        except Exception as e:
            logging.warning(f"解析设备上报数据出错，{e.__str__()}, mac: {mac}, data: {data}")
            # raise exceptions.ValidationError(detail={'detail': e.__str__()})
        data = locals().get('result')

        return data

    # 解析设备下发数据
    def parse_down_data(self, data=None, mac=None, addr=None, iu=None, db=None, dev_id=None):
        from saian_api.scheduledtask.utils import set_global_db

        try:
            if db is not None:
                set_global_db(db)
            if self.down_parser is None:
                return None
            exec(self.down_parser, None, locals())
        except Exception as e:
            logging.warning(f"解析设备下发数据出错，{e.__str__()}, mac: {mac}, data: {data}")
            # raise exceptions.ValidationError(detail={'detail': e.__str__()})
        data = locals().get('result')
        return data

    @classmethod
    def get_random_number(cls, length=10):
        """生成随机数字"""
        return AuthUtils.generate_random_code(length=length)

    def parse_up_attrs(self, mac, *identifiers):
        """将参数通过函数 parse_up_data 解析，得到新的值，并更新到设备"""
        from saian_api.device.models import DeviceAttribute, Device
        from saian_api.terminal.models import TerminalAttribute

        device = Device.objects.get(mac=mac)

        data = {}
        for idf in identifiers:
            da = DeviceAttribute.objects.query_object_by_idf(device, idf)
            if da:
                data[idf] = da.value

        parsed_data = self.parse_up_data(data, mac)

        for k, v in parsed_data.items():
            da = DeviceAttribute.objects.query_object_by_idf(device, k)
            if da:
                da.value = v
                DeviceAttribute.objects.save_to_redis(device, da)
                TerminalAttribute.objects.update_with_da(da)

    @staticmethod
    def set_value(mac, identifier, value):
        """直接更新参数值"""
        from saian_api.device.models import DeviceAttribute, Device
        from saian_api.terminal.models import TerminalAttribute

        device = Device.objects.get(mac=mac)
        da = DeviceAttribute.objects.get_by_idf(device, identifier)
        da.value = value
        DeviceAttribute.objects.save_to_redis(device, da)
        TerminalAttribute.objects.update_with_da(da)

    @staticmethod
    def set_device(mac, key, value, glossary='', idx=None, prefix=None):
        """设备以及设备终端"""
        valid_keys = ['sw_on', 'in_alarm', 'in_fault']
        if key not in valid_keys:
            logging.error(f'invalid set device key: {key}')
            return

        from saian_api.device.models import Device
        from saian_api.terminal.models import Terminal
        from saian_api.issue.models import DeviceIssue
        try:
            device = Device.objects.get(mac=mac)
            # 故障报警已经在 DeviceIssue 的方法中处理了，这里只需要更新设备和终端的开关状态
            if key == 'sw_on':

                if idx is None:
                    setattr(device, key, value)
                    device.save(update_fields='sw_on')
                    # terminal = Terminal.objects.get_terminal_of_device(device)
                    Terminal.objects.bulk_update_by_device(device, key, value)
                else:
                    terminal = Terminal.objects.get_sub_terminal_of_device(device, idx, prefix)
                    if terminal is not None:
                        setattr(terminal, key, value)
                        terminal.save()

            if key == 'in_alarm':
                DeviceIssue.objects.add_alarm(device, glossary) if value else DeviceIssue.objects.recover_alarm(device, glossary)
            if key == 'in_fault':
                DeviceIssue.objects.add_fault(device, glossary) if value else DeviceIssue.objects.recover_fault(device, glossary.split(':')[0] + ':')

        except Device.DoesNotExist:
            logging.error(f'function set_device: Device-{mac} Not Found! ')

    @staticmethod
    def get_value(mac, idf):
        from saian_api.device.models import Device
        from saian_api.device.models import DeviceAttribute
        value = None

        try:
            device = Device.objects.filter(mac=mac, project_id__isnull=False).last()
            if device is not None:
                da = DeviceAttribute.objects.query_object_by_idf(device, idf)
                value = da.value if da is not None else None
        except Device.DoesNotExist:
            logging.error(f'function get_value: Device-{mac} Not Found! ')
        return value

    @classmethod
    def get_terminal(cls, mac, idx, prefix):
        from saian_api.terminal.models import Terminal
        return Terminal.objects.filter(device__mac=mac, idx=idx, prefix=prefix).last()

    @classmethod
    def get_last_not_solved_issue_by_glossary(cls, mac, glossary):
        from saian_api.issue.models import DeviceIssue
        from saian_api.device.models import Device

        return DeviceIssue.objects.last_not_solved_issue_by_glossary(20, device=Device.objects.get(mac=mac), glossary=glossary)

    @staticmethod
    def get_options(mac, idf):
        from saian_api.device.models import Device
        from saian_api.device.models import DeviceAttribute
        options = None
        try:
            ap = AttributePrototype.objects.get_by_idf(Device.objects.filter(mac=mac).order_by('created_at').last(), idf)
            if ap is not None:
                if ap.data_type == 30:
                    options = ap.options.split(',')
        except Device.DoesNotExist:
            logging.error(f'function get_options: Device-{mac} Not Found! ')
        return options

    @staticmethod
    def set_terminal(mac, key, value, prefix=None, idx=None):
        valid_keys = ['sw_on', 'online', 'in_alarm', 'in_fault']
        if key in valid_keys:
            from saian_api.terminal.models import Terminal

            terminals = Terminal.objects.filter(device__mac=mac, prefix=prefix, idx=idx)
            for terminal in terminals:
                setattr(terminal, key, value)
                terminal.save()


"""
  设备属性分类
"""


class AttributeType(models.Model):
    # 所属设备类型
    device_prototype = models.ForeignKey(DevicePrototype, on_delete=models.CASCADE)
    # 名字
    name = models.CharField(max_length=255)
    # 排序，正序排
    seq = models.IntegerField()
    # 是否隐藏（小程序的设置）
    hidden = models.BooleanField(default=False)
    # 唯一名称
    uni_name = models.CharField(max_length=255)
    # 是否在扫码添加中显示
    in_add = models.BooleanField(default=True)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'attribute_types'
        ordering = ['seq']


class AttributePrototypeManager(models.Manager):
    @classmethod
    def query_by_names(cls, project_id, names: list):
        results = RedisHelper.get_multi_value(project_id, names, False)

        not_cache_names = []
        not_cache_aps = []
        aps = []

        for idx, result in enumerate(results):
            if not result:
                not_cache_names.append(names[idx])
            else:
                aps.append(result)

        if len(not_cache_names):
            ap_query = Q()
            for name in not_cache_names:
                _, postfix = name.split(':', 1)
                dp_id, identifier = postfix.split('_', 1)
                ap_query |= Q(device_prototype_id=dp_id, identifier=identifier)

            not_cache_aps = list(AttributePrototype.objects.filter(ap_query))
            if len(not_cache_aps):
                values = {}
                for ap in not_cache_aps:
                    name = f'attribute_prototype:{ap.device_prototype_id}_{ap.identifier}'
                    values[name] = ap

                RedisHelper.set_multi_value(project_id, values, False)

        return aps + not_cache_aps

    @classmethod
    def query_by_idfs(cls, device, idfs):

        dp_id = device.device_prototype_id
        names = [f'attribute_prototype:{dp_id}_{idf}' for idf in idfs]
        results = RedisHelper.get_multi_value(device.project_id, names, False)

        not_cache_idfs = []
        aps = []

        for idx, result in enumerate(results):
            if not result:
                not_cache_idfs.append(idfs[idx])
            else:
                aps.append(result)

        not_cache_aps = list(AttributePrototype.objects.filter(device_prototype_id=dp_id, identifier__in=not_cache_idfs))
        if len(not_cache_aps):
            values = {}
            for ap in not_cache_aps:
                name = f'attribute_prototype:{dp_id}_{ap.identifier}'
                values[name] = ap

            RedisHelper.set_multi_value(device.project_id, values, False)

        return aps + not_cache_aps

    def query_by_idf(self, device, idf):
        name = f'attribute_prototype:{device.device_prototype_id}_{idf}'
        ap = RedisHelper.get_value(device.project_id, name, False)

        if ap is None:
            ap = self.get_by_idf(device, idf)

        return ap

    def get_by_idf(self, device, idf, db=None):
        try:
            if db is not None:
                attr = self.using(db).get(device_prototype_id=device.device_prototype_id, identifier=idf)
            else:
                attr = self.get(device_prototype_id=device.device_prototype_id, identifier=idf)
        except ObjectDoesNotExist:
            # logging.warning(f'Attribute Prototype not found, dp_id: {device.device_prototype_id}, identifier: {idf}')
            attr = None
        except MultipleObjectsReturned:
            # logging.warning(f'Attribute Prototype MultipleObjectsReturned, dp_id: {device.device_prototype_id}, identifier: {idf}')
            if db is not None:
                attr = self.using(db).filter(device_prototype_id=device.device_prototype_id, identifier=idf).order_by('created_at').last()
            else:
                attr = self.filter(device_prototype_id=device.device_prototype_id, identifier=idf).order_by('created_at').last()
        return attr

    @staticmethod
    def affect_by_device_limit(device_limits, device, ap, fields):
        from saian_api.device.models import DeviceAttribute, Device
        from saian_api.terminal.models import TerminalAttribute, Terminal
        for limit in device_limits:
            limit_aps = limit.attribute_prototypes.replace(' ', '').split(',')
            if str(ap.id) in limit_aps:
                run_mode_aps = AttributePrototype.objects.filter(label__contains='运行模式', device_prototype_id=device.device_prototype_id)
                if run_mode_aps.exists():
                    run_mode_ap = None
                    if run_mode_aps.count() == 1:
                        run_mode_ap = run_mode_aps.first()
                    else:
                        # 找出 idx
                        idx = re.findall(r'\d+', ap.identifier)
                        if len(idx):
                            idx = idx[0]
                            run_mode_aps = run_mode_aps.filter(identifier__contains=f'_{idx}_')
                            if run_mode_aps.exists():
                                run_mode_ap = run_mode_aps.last()

                    if run_mode_ap is not None:
                        # run_mode = DeviceAttribute.objects.get_by_idf(device, run_mode_ap.identifier)
                        # run_mode = DeviceAttribute.objects.get_by_ap(device, run_mode_ap)
                        run_mode = DeviceAttribute.objects.query_object_by_ap(device, run_mode_ap)
                        if run_mode is not None:
                            if run_mode.value == '制暖':
                                fields['max_value'] = limit.up_value
                            elif run_mode.value == '制冷':
                                fields['min_value'] = limit.low_value

    def update_name_by_nickname(cls, start, old_prefix, new_prefix):
        # 修改昵称时同步修改对应ap的名字
        # ps：不用同步管理平台的相应数据到项目数据库，避免修改后被默认值覆盖
        # start标识开头，old_prefix旧昵称，new_prefix新昵称
        aps_to_update = AttributePrototype.objects.filter(
            identifier__startswith=f'{start}', name__startswith=f'{old_prefix}').exclude(identifier__endswith='_NickName')

        for ap in aps_to_update:
            ap.name = new_prefix + ap.name[len(old_prefix):]  # 替换旧前缀为新前缀

        AttributePrototype.objects.bulk_update(aps_to_update, ['name'])


"""
  设备属性类型
"""


class AttributePrototype(models.Model):
    # 所属设备类型
    device_prototype = models.ForeignKey(DevicePrototype, on_delete=models.CASCADE)
    # 所属设备属性分类
    attribute_type = models.ForeignKey(AttributeType, on_delete=models.CASCADE)
    # 名称
    name = models.CharField(max_length=255)
    # 标识
    identifier = models.CharField(max_length=255)
    # 备注
    remark = models.CharField(max_length=255, null=True)
    # 是否只读
    read_only = models.BooleanField(default=True)
    # 数据类型，10-布尔，20-数值，30-枚举，40-扩展，80-字符串
    data_type = models.IntegerField()
    # 精度，当data_type=20时有意义
    pre_cision = models.CharField(max_length=255, null=True)
    # 步进，当data_type=20时有意义
    in_crement = models.CharField(max_length=255, null=True)
    # 最小值，当data_type=20时有意义
    min_value = models.CharField(max_length=255, null=True)
    # 最大值，当data_type=20时有意义
    max_value = models.CharField(max_length=255, null=True)
    # 选项，当data_type=30时有意义
    options = models.TextField(null=True)
    # 长度
    length = models.IntegerField()
    # icon
    icon = models.CharField(max_length=255, null=True)
    # 单位
    unit = models.CharField(max_length=255, null=True)
    # 默认值
    default_value = models.CharField(max_length=255, null=True)
    # 排序，升序
    seq = models.IntegerField()
    # 下发指令时，是否立即发送
    send_immediate = models.BooleanField(default=False)
    # 是否为关键参数，即在设备列表或设备详情时返回
    is_key = models.BooleanField(default=False)
    # 是否可以调试
    can_debug = models.BooleanField(default=False)
    # 是否隐藏，即不返回到前端
    hidden = models.BooleanField(default=False)
    # 是否在添加设备时可配置
    in_add = models.BooleanField(default=False)
    # 是否在设备列表返回
    show_in_list = models.BooleanField(default=False)
    # 参值的计算公式
    formula = models.CharField(max_length=255, null=True)
    # 是否是累计参数，比如：用电量
    is_cum = models.BooleanField(default=False)
    # 界面显示的名字
    label = models.CharField(max_length=255)
    # 是否可导出（运行情况报表）
    do_export = models.BooleanField(default=False)
    # 是否下发到设备
    do_send = models.BooleanField(default=True)
    # 第三方平台请求时是否返回该属性
    en_entapi = models.BooleanField(default=False)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'attribute_prototypes'
        ordering = ['-created_at']

    objects = AttributePrototypeManager()

    def __getstate__(self):
        """自定义对象序列化时的状态"""
        state = self.__dict__.copy()  # 获取对象的所有属性
        result = {}
        for k, v in state.items():
            if k == '_state':
                state_adding = v.adding
                state_db = v.db
                v = {
                    "adding": state_adding,
                    "db": state_db
                }

            if k.endswith('at') and isinstance(v, datetime.datetime):
                v = v.strftime('%Y-%m-%d %H:%M:%S')
            result[k] = v
        return result

    def __setstate__(self, state):
        """自定义反序列化时的状态"""
        for k, v in state.items():
            if k.endswith('at') and isinstance(v, str):
                try:
                    state[k] = datetime.datetime.strptime(v, '%Y-%m-%d %H:%M:%S')
                except ValueError:
                    pass  # 跳过无法转换的字段
            if k == '_state' and isinstance(v, dict):
                state_adding = v.get('adding', False)
                state_db = v.get('db', None)
                model_state = ModelState()
                model_state.adding = state_adding
                model_state.db = state_db
                model_state.fields_cache = {}
                state[k] = model_state
        self.__dict__.update(state)


# 设备协议配置，用于解析设备协议
class DeviceProtocol(models.Model):
    # 所属设备类型
    device_prototype = models.ForeignKey(DevicePrototype, null=True, blank=True, on_delete=models.CASCADE)

    # 协议类型，10-mewtocol, 20-modbus, 30-lwm2m, 40-syense lwm2m, 50-mqtt
    p_type = models.IntegerField(null=True)

    name = models.CharField(null=True, max_length=255)

    # 参数标识
    identifier = models.CharField(null=True, max_length=255)

    # 参数标识
    addr = models.CharField(null=True, max_length=255)

    # 数据类型, 10 - 十进制/整型，20 - bit，30 - BCD，40 - 浮点, 50-字符串，60-Char（Modbus）
    data_type = models.IntegerField(null=True)

    # 数据长度，10 - 1字，20 - Bit，30 - 双字，40-4字（8字节）, 50-7字节（用于syense 运行时间解析），60-1位（Modbus）
    data_len = models.IntegerField(null=True)

    # 是否使用
    in_use = models.BooleanField(default=True)

    # 倍数
    devider = models.IntegerField(default=1)

    # 只读
    ro = models.BooleanField()

    # slaveid
    slaveid = models.IntegerField(null=True)

    # func_code
    func_code = models.CharField(null=True, max_length=100)

    # objid
    objid = models.CharField(null=True, max_length=100)

    # insid
    insid = models.CharField(null=True, max_length=100)

    # resid
    resid = models.CharField(null=True, max_length=100)

    # position
    position = models.CharField(null=True, max_length=100)

    # prefix
    prefix = models.CharField(null=True, max_length=100)

    # suffix
    suffix = models.CharField(null=True, max_length=100)

    # 读取的时间周期，10-1分钟，20-3分钟，30-5分钟，40-10分钟，50-15分钟，60-30分钟
    read_time = models.IntegerField(null=True)

    topic = models.CharField(null=True, max_length=255)
    topic_set = models.CharField(null=True, max_length=255)

    # 按位解析的数据定义，json格式
    sp_defs = models.TextField(null=True)
    model_no = models.CharField(null=True, max_length=255)
    w_code = models.CharField(null=True, max_length=100)
    w_addr = models.CharField(null=True, max_length=100)

    class Meta:
        db_table = 'device_protocols'


class AddrSegment(models.Model):
    device_prototype = models.ForeignKey(DevicePrototype, on_delete=models.CASCADE)
    start_addr = models.CharField(max_length=255, null=True)
    end_addr = models.CharField(max_length=255, null=True)
    # 读取周期，单位：秒
    interval = models.IntegerField()

    class Meta:
        db_table = 'addr_segments'


class DevicePrototypeRatioManager(models.Manager):
    @staticmethod
    def get_match_ratio(params: list, statuses: dict, idx):
        """从配置中找到符合设备目前状态的运行时间比率"""
        import copy
        idx_params = []

        if idx is not None and idx != '0':
            for param in params:
                temp = {}
                for k, v in param.items():
                    temp[k.replace('_XXX', f'_{idx}')] = v
                idx_params.append(temp)
        else:
            idx_params = copy.deepcopy(params)

        for item in idx_params:
            ratio = item.pop('Ratio')
            if item == statuses:
                return ratio
            else:
                match = True
                for k, v in item.items():
                    if statuses and isinstance(v, str) and (v.startswith('>') or v.startswith('<')):
                        value = statuses.get(k, None)
                        eval_str = f'float(value) {v}'
                        try:
                            if value is None or not eval(eval_str):
                                match = False
                                break
                        except Exception as e:
                            logging.error(f'get_match_ratio 出错, eval: {eval_str}, ERROR: {e.__str__()}')
                    else:
                        match = False

                if match:
                    return ratio

        return 0


class DevicePrototypeRatio(models.Model):
    # 设备类型
    device_prototype = models.OneToOneField(DevicePrototype, on_delete=models.CASCADE, unique=True)
    # 配置参数
    params = models.CharField(max_length=2000, blank=True, null=True)
    # 创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'dp_ratios'

    objects = DevicePrototypeRatioManager()
