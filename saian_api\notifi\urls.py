from django.urls import path

from .views import VerifiedCodeViewSet, LoginVerifiedCodeView, NotifyConfigView, NotifyLimitView, NotificationView

verified_codes = VerifiedCodeViewSet.as_view({
    'post': 'create'
})

notify_configs = NotifyConfigView.as_view({
    'get': 'list',
    'post': 'create'
})
notify_config = NotifyConfigView.as_view({
    'put': 'partial_update',
    'delete': 'destroy',
    'get': 'retrieve'
})

notify_limits = NotifyLimitView.as_view({
    'get': 'list',
    'post': 'create'
})
notify_limit = NotifyLimitView.as_view({
    'put': 'partial_update',
    'delete': 'destroy',
    'get': 'retrieve'
})

notifications = NotificationView.as_view({
    'get': 'list'
})

urlpatterns = [
    path('saianapi/v1/verified_codes', verified_codes, name='verified-codes'),
    path('saianapi/v1/local_verified_codes', LoginVerifiedCodeView.as_view()),
    path('saianapi/v5/notify_configs', notify_configs, name='notify-configs'),
    path('saianapi/v5/notify_configs/<int:pk>', notify_config, name='notify-config'),
    path('saianapi/v5/notify_limits', notify_limits, name='notify-limits'),
    path('saianapi/v5/notify_limits/<int:pk>', notify_limit, name='notify-limit'),
    path('saianapi/v5/notify_histories', notifications, name='notifications'),
]
