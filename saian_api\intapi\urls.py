from django.urls import path

from saian_api.dashboard.views import WebDashboardChartView
from saian_api.issue.views import IssueStatViewSet

from .views import (
    DeviceEventViewSet,
    AttributeTypeViewSet,
    DeviceTypeViewSet,
    DevicePrototypeViewSet,
    AttributePrototypeViewSet,
    WebUserViewSet,
    WebRoleViewSet,
    UserProjectViewSet,
    UserDeviceViewSet,
    DeviceViewSet,
    EcMeterViewSet,
    ProjectWebMenuViewSet,
    ProjectChartViewSet,
    WebChartViewSet,
    BuildingViewSet,
    FloorViewSet,
    ActiveRoomViewSet,
    WebRegionViewSet,
    WebMenuViewSet,
    ReportConfigurerViewSet,
    WebUserRoleViewSet,
    DeviceAttributeViewSet,
    WebRoleMenuViewSet,
    DeviceProtocolViewSet,
    MessageViewSet,
    EcUserViewSet,
    SnpVarViewSet,
    ParserTrialView,
    DeviceIssueView,
    DevicePrototypeRatioViewSet,
    TerminalView,
    SySimView,
    ProjectSettingView, RedisDataView, DrfAssetsView,
    UserStatView, SnapshotView, DaSnapshotHistoryView,
    BulkUpdateAttribute, DeviceHistoryDataExportView
)
from .views import ProjectViewSet
from ..device.urls import data_routes, data_route_detail
from ..device.views import DeviceLogsViewSet, DeviceLimitViewSet
from ..dimension.views import DimensionAttributeEcItem, DimensionEcRankingView, DimensionEcStats, DimensionEcTypeView, DimensionEtQoqStatsView, DimensionQoqStatsView, DimensionTerminalView, DimensionAttributeView, DimensionView, DimensionTypeView, EcTotalStatsView
from ..terminal.views import TerminalAttributeView, TerminalStatsView
from ..terminal.views import TerminalView as TerminalInitView
from ..user.views import UserLogView

# project
project_viewset_list = ProjectViewSet.as_view({
    'post': 'create'
})
project_viewset_detail = ProjectViewSet.as_view({
    'put': 'partial_update'
})

# project_chart
project_chart_list = ProjectChartViewSet.as_view({
    'post': 'create'
})
project_chart_detail = ProjectChartViewSet.as_view({
    'put': 'partial_update'
})

# web_chart
web_chart_list = WebChartViewSet.as_view({
    'post': 'create',
    'get': 'list'
})
web_chart_detail = WebChartViewSet.as_view({
    'put': 'partial_update',
    'delete': 'destroy'
})

# project_web_menu
project_web_menu_list = ProjectWebMenuViewSet.as_view({
    'post': 'create',
    'get': 'list'
})
project_web_menu_detail = ProjectWebMenuViewSet.as_view({
    'delete': 'destroy'
})

device_event_list = DeviceEventViewSet.as_view({
    'post': 'create',
    'get': 'list'
})
device_event_detail = DeviceEventViewSet.as_view({
    'get': 'retrieve'
})

# device_type
device_type_list = DeviceTypeViewSet.as_view({
    'post': 'create',
    'get': 'list'
})
device_type_detail = DeviceTypeViewSet.as_view({
    'put': 'partial_update',
    'delete': 'destroy',
    'get': 'retrieve'
})

# device_prototype
device_prototype_list = DevicePrototypeViewSet.as_view({
    'post': 'create',
    'get': 'list'
})
device_prototype_detail = DevicePrototypeViewSet.as_view({
    'put': 'partial_update',
    'delete': 'destroy',
    'get': 'retrieve'
})

# attribute_type
attribute_type_list = AttributeTypeViewSet.as_view({
    'post': 'create',
    'get': 'list'
})
attribute_type_detail = AttributeTypeViewSet.as_view({
    'put': 'partial_update',
    'delete': 'destroy',
    'get': 'retrieve'
})

# attribute_prototype
attribute_prototype_list = AttributePrototypeViewSet.as_view({
    'post': 'create',
    'get': 'list'
})
attribute_prototype_detail = AttributePrototypeViewSet.as_view({
    'put': 'partial_update',
    'delete': 'destroy',
    'get': 'retrieve'
})

# web_role
web_role_list = WebRoleViewSet.as_view({
    'post': 'create',
    'get': 'list'
})
web_role_detail = WebRoleViewSet.as_view({
    'put': 'partial_update'
})

# web_user
web_user_list = WebUserViewSet.as_view({
    'post': 'create',
    'get': 'list'
})
web_user_detail = WebUserViewSet.as_view({
    'put': 'partial_update',
    'get': 'retrieve'
})

# user_project
user_project_list = UserProjectViewSet.as_view({
    'post': 'create'
})
user_project_detail = UserProjectViewSet.as_view({
    'put': 'partial_update',
    'delete': 'destroy'
})

# user_device
user_device_list = UserDeviceViewSet.as_view({
    'post': 'create'
})
user_device_detail = UserDeviceViewSet.as_view({
    'delete': 'destroy'
})

# ec_meter
ec_meter_list = EcMeterViewSet.as_view({
    'post': 'create'
})
ec_meter_detail = EcMeterViewSet.as_view({
    'put': 'partial_update',
    'delete': 'destroy'
})

# device
device_list = DeviceViewSet.as_view({
    'post': 'create',
    'get': 'list'
})
device_detail = DeviceViewSet.as_view({
    'get': 'retrieve',
    'put': 'partial_update',
    'delete': 'destroy'
})

# building
building_list = BuildingViewSet.as_view({
    'post': 'create'
})
building_detail = BuildingViewSet.as_view({
    'put': 'partial_update'
})

# floor
floor_list = FloorViewSet.as_view({
    'post': 'create'
})
floor_detail = FloorViewSet.as_view({
    'put': 'partial_update'
})

# active_room
active_room_list = ActiveRoomViewSet.as_view({
    'post': 'create'
})
active_room_detail = ActiveRoomViewSet.as_view({
    'put': 'partial_update'
})

# web_region
web_region_list = WebRegionViewSet.as_view({
    'post': 'create'
})
web_region_detail = WebRegionViewSet.as_view({
    'put': 'partial_update'
})

# web_menu
web_menu_list = WebMenuViewSet.as_view({
    'post': 'create',
    'get': 'list'
})
web_menu_detail = WebMenuViewSet.as_view({
    'put': 'partial_update',
    'delete': 'destroy'
})

# report_configurer
report_configurer_list = ReportConfigurerViewSet.as_view({
    'post': 'create',
    'get': 'list'
})
report_configurer_detail = ReportConfigurerViewSet.as_view({
    'put': 'partial_update',
    'delete': 'destroy'
})

web_user_role = WebUserRoleViewSet.as_view({
    'post': 'create'
})

web_role_menu = WebRoleMenuViewSet.as_view({
    'post': 'create'
})

device_attribute_list = DeviceAttributeViewSet.as_view({
    'post': 'create',
    'delete': 'destroy',
    'get': 'list'
})
device_attribute_detail = DeviceAttributeViewSet.as_view({
    'put': 'partial_update',
    'delete': 'destroy',
    'get': 'retrieve'
})

device_protocols = DeviceProtocolViewSet.as_view({
    'get': 'list'
})

ec_user_list = EcUserViewSet.as_view({
    'post': 'create'
})

message_list = MessageViewSet.as_view({
    'get': 'list',
    'post': 'create'
})
message_detail = MessageViewSet.as_view({
    'delete': 'destroy'
})

snp_vars = SnpVarViewSet.as_view({
    'post': 'create'
})

snp_var = SnpVarViewSet.as_view({
    'delete': 'destroy'
})

parser_trial = ParserTrialView.as_view({
    'post': 'create'
})

device_logs = DeviceLogsViewSet.as_view({
    'get': 'list'
})
device_log = DeviceLogsViewSet.as_view({
    'get': 'retrieve'
})

device_issues = DeviceIssueView.as_view({
    'get': 'list'
})
device_issue = DeviceIssueView.as_view({
    'get': 'retrieve'
})

device_limits = DeviceLimitViewSet.as_view({
    'get': 'list',
    'post': 'create'
})
device_limit = DeviceLimitViewSet.as_view({
    'put': 'partial_update',
    'get': 'retrieve',
    'delete': 'destroy'
})

dp_ratios_list = DevicePrototypeRatioViewSet.as_view({
    'get': 'list',
    'post': 'create'
})
dp_ratios_detail = DevicePrototypeRatioViewSet.as_view({
    'get': 'retrieve',
    'put': 'partial_update',
    'delete': 'destroy'
})

terminal_attributes = TerminalAttributeView.as_view({
    'get': 'list',
    'post': 'create'
})

terminal_attribute_detail = TerminalAttributeView.as_view({
    'put': 'partial_update',
    'delete': 'destroy'
})

terminal_list = TerminalView.as_view({
    'get': 'list',
    'post': 'create'
})
terminal_detail = TerminalView.as_view({
    'put': 'partial_update',
    'delete': 'destroy'
})

terminal_init = TerminalInitView.as_view({
    'post': 'create'
})

terminals_stats = TerminalStatsView.as_view({
    'get': 'list'
})


user_logs = UserLogView.as_view({
    'get': 'list'
})
# =========== Dimensions ===================
dimensions = DimensionView.as_view({
    'get': 'list',
    'post': 'create'
})
dimension = DimensionView.as_view({
    'get': 'retrieve',
    'put': 'partial_update',
    'delete': 'destroy'
})

dimension_attributes = DimensionAttributeView.as_view({
    'get': 'list',
    'post': 'create'
})
dimension_attribute = DimensionAttributeView.as_view({
    'get': 'retrieve',
    'put': 'partial_update',
    'delete': 'destroy'
})

dimension_terminals = DimensionTerminalView.as_view({
    'post': 'create',
    'get': 'list'
})
dimension_terminal = DimensionTerminalView.as_view({
    'delete': 'destroy'
})
dimension_types = DimensionTypeView.as_view({
    'get': 'list'
})
# ===========================================

# sim
sy_sims = SySimView.as_view({
    'get': 'list',
    'post': 'create',
})
sy_sim_detail = SySimView.as_view({
    'get': 'retrieve',
    'put': 'partial_update',
    'delete': 'destroy'
})

# project settings
project_settings = ProjectSettingView.as_view({
    'put': 'update'
})

# Redis Relate
redis_data = RedisDataView.as_view({
    'get': 'retrieve',
    'put': 'update',
    'delete': 'destroy'
})

# Drf Assets
drf_assets = DrfAssetsView.as_view({
    'post': 'create'
})

# 用户状态
user_stats = UserStatView.as_view({
    'get': 'list'
})

# 设备参数快照
snapshots = SnapshotView.as_view({
    'get': 'list'
})
snapshot_histories = DaSnapshotHistoryView.as_view({
    'get': 'list'
})

dashboards = WebDashboardChartView.as_view({
    'get': 'list'
})

run_stats = WebDashboardChartView.as_view({
    'get': 'list'
})
issue_stats = IssueStatViewSet.as_view({
    'get': 'list'
})


dimension_ec_items = DimensionAttributeEcItem.as_view({
    'get': 'list'
})

dimension_ec_types = DimensionEcTypeView.as_view({
    'get': 'list'
})

ec_summaries = DimensionEcStats.as_view({
    'get': 'list'
})

# 能耗排名
ec_rankings = DimensionEcRankingView.as_view({
    'get': 'list'
})
# 环比分析
qoq_stats = DimensionQoqStatsView.as_view({
    'get': 'list'
})
# 分项环比分析
et_qoq_stats = DimensionEtQoqStatsView.as_view({
    'get': 'list'
})

# 能耗汇总统计
ec_total_stats = EcTotalStatsView.as_view({
    'get': 'list'
})


bulkupdate_attributes = BulkUpdateAttribute.as_view({
    'post': 'create'
})

device_history_data_export = DeviceHistoryDataExportView.as_view({
    'post': 'create'
})

urlpatterns = [
    path('saianapi/intapi/projects', project_viewset_list, name='project-list'),
    path('saianapi/intapi/projects/<int:pk>', project_viewset_detail, name='project-detail'),

    path('saianapi/intapi/project_web_menus', project_web_menu_list, name='project_web_menu_list'),
    path('saianapi/intapi/project_web_menus/<int:pk>', project_web_menu_detail, name='project_web_menu_detail'),

    path('saianapi/intapi/web_menus', web_menu_list, name="web_menu_list"),
    path('saianapi/intapi/web_menus/<int:pk>', web_menu_detail, name="web_menu_detail"),

    path('saianapi/intapi/project_charts', project_chart_list, name="project_chart_list"),
    path('saianapi/intapi/project_charts/<int:pk>', project_chart_detail, name="project_chart_detail"),

    path('saianapi/intapi/web_charts', web_chart_list, name="web_chart_list"),
    path('saianapi/intapi/web_charts/<int:pk>', web_chart_detail, name="web_chart_detail"),

    path('saianapi/intapi/device_events', device_event_list, name='deviceevent-list'),
    path('saianapi/intapi/device_events/<int:pk>', device_event_detail, name='deviceevent-detail'),

    path('saianapi/intapi/device_types', device_type_list, name='device_type_list'),
    path('saianapi/intapi/device_types/<int:pk>', device_type_detail, name='device_type_detail'),

    path('saianapi/intapi/device_prototypes', device_prototype_list, name='device_prototype_list'),
    path('saianapi/intapi/device_prototypes/<int:pk>', device_prototype_detail, name='device_prototype_detail'),

    path('saianapi/intapi/attribute_types', attribute_type_list, name='attribute_type_list'),
    path('saianapi/intapi/attribute_types/<int:pk>', attribute_type_detail, name='attribute_type_detail'),

    path('saianapi/intapi/attribute_prototypes', attribute_prototype_list, name='attribute_prototype_list'),
    path('saianapi/intapi/attribute_prototypes/<int:pk>', attribute_prototype_detail, name='attribute_prototype_detail'),

    path('saianapi/intapi/web_roles', web_role_list, name="web_role_list"),
    path('saianapi/intapi/web_roles/<int:pk>', web_role_detail, name="web_role_detail"),

    path('saianapi/intapi/web_users', web_user_list, name="web_user_list"),
    path('saianapi/intapi/web_users/<int:pk>', web_user_detail, name="web_user_detail"),

    path('saianapi/intapi/web_user_projects', user_project_list, name="user_project_list"),
    path('saianapi/intapi/web_user_projects/<int:pk>', user_project_detail, name="user_project_detail"),

    path('saianapi/intapi/user_devices', user_device_list, name="user_device_list"),
    path('saianapi/intapi/user_devices/<int:pk>', user_device_detail, name="user_device_detail"),

    path('saianapi/intapi/ec_meters', ec_meter_list, name="ec_meter_list"),
    path('saianapi/intapi/ec_meters/<int:pk>', ec_meter_detail, name="ec_meter_detail"),

    path('saianapi/intapi/devices', device_list, name="device_list"),
    path('saianapi/intapi/devices/<int:pk>', device_detail, name="device_detail"),

    path('saianapi/intapi/buildings', building_list, name="building_list"),
    path('saianapi/intapi/buildings/<int:pk>', building_detail, name="building_detail"),

    path('saianapi/intapi/floors', floor_list, name="floor_list"),
    path('saianapi/intapi/floors/<int:pk>', floor_detail, name="floor_detail"),

    path('saianapi/intapi/active_rooms', active_room_list, name="active_room_list"),
    path('saianapi/intapi/active_rooms/<int:pk>', active_room_detail, name="active_room_detail"),

    path('saianapi/intapi/admin_regions', web_region_list, name="web_region_list"),
    path('saianapi/intapi/admin_regions/<int:pk>', web_region_detail, name="web_region_detail"),

    path('saianapi/intapi/report_configurers', report_configurer_list, name="report_configurer_list"),
    path('saianapi/intapi/report_configurers/<int:pk>', report_configurer_detail, name="report_configurer_detail"),

    path('saianapi/intapi/web_user_roles', web_user_role, name="web_user_role_list"),

    path('saianapi/intapi/web_role_menus', web_role_menu, name="web_role_menu_list"),

    path('saianapi/intapi/device_attributes', device_attribute_list, name="device_attribute_list"),
    path('saianapi/intapi/device_attributes/<int:pk>', device_attribute_detail, name="device_attribute_detail"),

    # 为了兼容旧接口url
    path('saianapi/intapi/device_protocols', device_protocols, name="device_protocols"),

    # 本地部署项目，项目平台与能耗平台同步用户
    path('saianapi/intapi/ec_users', ec_user_list, name="ec_user_list"),

    # 消息中心
    path('saianapi/intapi/messages', message_list, name="message_list"),
    path('saianapi/intapi/messages/<str:uid>', message_detail, name="message_detail"),

    # 同步联动系统变量
    path('saianapi/intapi/snp_vars', snp_vars, name="snp_vars"),
    path('saianapi/intapi/snp_vars/<int:pk>', snp_var, name="snp_var"),

    # 调试执行解析代码。不再使用。
    path('saianapi/intapi/execute_parser', parser_trial, name="parse_trial"),

    # 查询设备日志，包含设备上报和下发
    path('saianapi/intapi/device_logs', device_logs, name='device_logs'),
    path('saianapi/intapi/device_logs/<int:pk>', device_log, name='device_log'),

    # 设备故障报警
    path('saianapi/intapi/device_issues', device_issues, name='device_issues'),
    path('saianapi/intapi/device_issues/<int:pk>', device_issue, name='device_issue'),

    path('saianapi/intapi/device_limits', device_limits, name='device_limits'),
    path('saianapi/intapi/device_limits/<int:pk>', device_limit, name='device_limit'),

    # 设备类型系数
    path('saianapi/intapi/dp_ratios', dp_ratios_list, name='device-ratios-list'),
    path('saianapi/intapi/dp_ratios/<int:pk>', dp_ratios_detail, name="device-ratios-detail"),

    # 终端
    path('saianapi/intapi/terminal_attributes', terminal_attributes, name="terminal_attributes"),
    path('saianapi/intapi/terminal_attributes/<int:pk>', terminal_attribute_detail, name="terminal_attributes_detail"),
    path('saianapi/intapi/terminals', terminal_list, name="terminal_list"),
    path('saianapi/intapi/terminals/<int:pk>', terminal_detail, name="terminal_detail"),
    path('saianapi/intapi/terminal_init', terminal_init, name="terminal_init"),

    # 用户日志
    path('saianapi/intapi/user_logs', user_logs, name="user_logs"),

    # 维度
    path('saianapi/intapi/dimensions', dimensions, name="dimensions"),
    path('saianapi/intapi/dimensions/<int:pk>', dimension, name="dimension"),
    # 维度属性
    path('saianapi/intapi/dimension_attributes', dimension_attributes, name="dimension-attributes"),
    path('saianapi/intapi/dimension_attributes/<int:pk>', dimension_attribute, name="dimension-attribute"),
    # 维度终端
    path('saianapi/intapi/dimension_terminals', dimension_terminals, name="dimension-terminals"),
    path('saianapi/intapi/dimension_terminals/<int:pk>', dimension_terminal, name="dimension-terminals"),
    # 维度类型
    path('saianapi/intapi/dimension_types', dimension_types, name="dimension-types"),

    # 数据流转
    path('saianapi/intapi/data_routes', data_routes, name="data-routes"),
    path('saianapi/intapi/data_routes/<int:pk>', data_route_detail, name="data-route-details"),

    # sim
    path('saianapi/intapi/sy_sims', sy_sims, name='sy-sims'),
    path('saianapi/intapi/sy_sims/<str:iccid>', sy_sim_detail, name='sy-sim-detail'),

    # project settings
    path('saianapi/intapi/project_settings', project_settings, name='project-settings'),

    # redis data
    path('saianapi/intapi/redis_data/<str:name>', redis_data, name='redis-data'),

    # drf -assets
    path('saianapi/intapi/drf_assets', drf_assets, name='drf-assets'),

    # 用户统计
    path('saianapi/intapi/user_stats', user_stats, name='user-stats'),

    # 设备参数快照
    path('saianapi/intapi/da_snapshots', snapshots, name='da-snapshots'),
    # 设备快照的历史记录
    path('saianapi/intapi/da_snapshot_histories', snapshot_histories, name='da-snapshot-histories'),


    # 空调末端供冷概况
    path('saianapi/intapi/terminals_stats', terminals_stats, name="terminals_stats"),
    # 设备在线统计总览数据
    path('saianapi/intapi/dashboards', dashboards, name='dashboard-chart'),
    # 设备运行状态总览数据
    path('saianapi/intapi/run_stats', run_stats, name='run-stats'),
    # 设备故障报警统计
    path('saianapi/intapi/issue_stats', issue_stats, name='issue-stats'),
    # 查询能耗趋势类型
    path('saianapi/intapi/dimension_ec_types', dimension_ec_types, name='dimension-ec-types'),
    # 查询能耗趋势属性
    path('saianapi/intapi/dimension_ec_items', dimension_ec_items, name='dimension-ec-items'),
    # 查询能耗趋势
    path('saianapi/intapi/ec_summaries', ec_summaries, name='ec-summaries'),
    # 查询分项排名
    path('saianapi/intapi/ec_rankings', ec_rankings, name='ec-rankings'),
    # 查询环比分析
    path('saianapi/intapi/qoq_stats', qoq_stats, name='qoq-stats'),
    # 查询分项环比分析
    path('saianapi/intapi/et_qoq_stats', et_qoq_stats, name='et-qoq-stats'),
    # 能耗汇总统计
    path('saianapi/intapi/total_stats', ec_total_stats, name='ec-total-stats'),

    path('saianapi/intapi/bulk_attributes', bulkupdate_attributes, name='bulkupdate_attributes'),

    path('saianapi/intapi/device_history_data_export', device_history_data_export, name="device_history_data_export"),
]
