import concurrent
import datetime
import json
import logging
import threading
import traceback
from collections import defaultdict

from django.core.management import BaseCommand, CommandError

from saian_api.coldsource.models import CsCop, CsEerAnalyse
from saian_api.dashboard.models import ProjectWeather
from saian_api.device.models import Device
from saian_api.project.models import Project
from saian_api.regions.models import Region
from saian_api.report.models import DeviceHourlyStat
from saian_api.scheduledtask.utils import get_projects, set_global_db
from saian_api.utils.db.SQLHelper import SQLHelper
from saian_api.utils.httpapi.weather import WeatherApi
from saian_api.utils.tools import is_number

def hour_stat_task(project_id, device_cache, month, day, hour):
    set_global_db(project_id)
    device_ids = [8310, 8293, 8292, 8291, 8290, 8289, 8288, 7998]

    with SQLHelper() as (conn, cur):
        # device_id = 8292
        # device = Device.objects.get(pk=device_id)
        begin = datetime.datetime(2024, month, day, hour, 0, 0)

        logging.info(f'recalc date: {begin}')
        created_at = begin.strftime('%Y-%m-%d %H:59:59')

        value_cache = {}
        for did in device_ids:
            value_cache[did] = defaultdict(list)
        # report_cache = defaultdict(list)
        end = begin + datetime.timedelta(hours=1)

        begin_str = begin.strftime('%Y-%m-%d %H:%M:%S')
        end_str = end.strftime('%Y-%m-%d %H:%M:%S')
        device_ids_str = ', '.join([f'{did}' for did in device_ids])

        cur.execute(f"select id, device_id, data, created_at from device_event_histories where device_id in ({device_ids_str}) and data like '%Meter_A_%' and created_at >= '{begin_str}' and created_at < '{end_str}'")

        # result = cur.fetchmany(3)  # 取前3条
        result = cur.fetchall()
        for event in result:
            device_id = int(event[1])
            data = json.loads(event[2])

            report_cache = value_cache[device_id]
            for k, v in data.items():
                if 'Meter_A_' in k:
                    report_cache[k].append(v)

        for did, report_cache in value_cache.items():
            # device = device_cache[did]
            device = Device.objects.get(pk=did)
            for idf, values in report_cache.items():
                if len(values):
                    values = [float(v) for v in values if is_number(v)]
                if len(values):
                    avg_value = sum(values) / len(values)
                    min_value = min(values)
                    max_value = max(values)

                    dhs = DeviceHourlyStat.objects.filter(device_id=device.id, identifier=idf, created_at=created_at).last()
                    if dhs is None:
                        DeviceHourlyStat.objects.create(
                            device_id=device.id,
                            mac=device.mac,
                            identifier=idf,
                            avg=avg_value,
                            min=min_value,
                            max=max_value,
                            created_at=created_at
                        )
                    else:
                        if dhs.avg != str(avg_value):
                            logging.info(f'\n{device.nick_name}, {idf}, {dhs.id}, {created_at}, old: {dhs.avg}, new: {avg_value}')
                            print(f'data: {did}, {report_cache} values: {values}')
                            dhs.avg = avg_value
                            # dhs.save()
                            pass
                        else:
                            # logging.info(f'\n{device.nick_name}, {idf}, {dhs.id}, {created_at}, same value')
                            pass
            logging.info(f'\t ## finish ## ')


def calc_day_stats(project_id, day):
    device_ids = [8310, 8293, 8292, 8291, 8290, 8289, 8288, 7998]
    device_cache = {}

    # for did in device_ids:
    #     device_cache[did] = Device.objects.get(pk=did)

    month = 8

    with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
        futures = []
        for hour in range(24):
            futures.append(executor.submit(hour_stat_task, project_id, device_cache, month, day, hour))

        # 等待所有任务完成
        for future in concurrent.futures.as_completed(futures):
            future.result()  # 捕获可能的异常

    # threads = []
    # for hour in range(24):
    #     thread = threading.Thread(target=hour_stat_task, args=(project_id, device_cache, month, day, hour))
    #     threads.append(thread)
    #     thread.start()
    #
    # for thread in threads:
    #     thread.join()

class Command(BaseCommand):
    help = "冷源能效COP日统计"

    def handle(self, *args, **options):
        # 从命令行解释的项目ID列表

        now = datetime.datetime.now()


        for project_id in [47]:
            try:
                # 设置全局数据库
                set_global_db(project_id)
                for day in range(18, 21):
                    print(f'begin day: {day + 1}')
                    calc_day_stats(project_id, day + 1)

            except CommandError:
                self.stderr.write(f"运行'冷源能效COP小时统计'任务失败，项目ID：{project_id}, 命令参数不合法！", ending='\n')
                continue
            except Exception as e:
                self.stderr.write(f"运行'冷源能效COP小时统计'任务失败，项目ID：{project_id}, error: {e.__str__()}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
                continue
