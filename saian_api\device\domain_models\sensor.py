import datetime
import logging

from django.core.exceptions import ObjectDoesNotExist

from saian_api.device.models import DeviceAttribute, Device
from saian_api.device.models import LivingDetection
from saian_api.issue.models import DeviceIssue
from saian_api.utils.db.Redis import RedisHelper
from .base import BaseDevice
from ...devdefine.models import AttributePrototype
from ...terminal.models import Terminal


class Co2Voc(BaseDevice):
    """
      通用气体传感器（co2和voc传感器）
    """
    @classmethod
    def alarm(cls, device, event):
        data = event.get('data', {})
        if data is not None and 'GatewayAlarm' in data:
            if data['GatewayAlarm']:
                DeviceIssue.objects.add_alarm(device, data['GatewayAlarmType'])
            else:
                DeviceIssue.objects.recover_alarm(device, '')


class Gas(BaseDevice):
    """
      管道式气体传感器
    """
    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)

        data = event.get('data', {})
        da = None

        try:
            if data is not None and 'Co2' in data:
                da = DeviceAttribute.objects.get_by_idf(device, 'LocalCO2')
                da.value = data['Co2']

            if data is not None and 'Tvoc' in data:
                da = DeviceAttribute.objects.get_by_idf(device, 'LocalTVOC')
                da.value = data['Tvoc']

            # 温度，湿度和露点温度是无效
            idfs = ['LocalTemp', 'DPTemp', 'LocalRH']
            for idf in idfs:
                if data is not None and idf in data:
                    da = DeviceAttribute.objects.get_by_idf(device, idf)
                    da.value = '--'
            if da is not None:
                da.save()
                DeviceAttribute.objects.save_to_redis(device, da)
        except ObjectDoesNotExist as e:
            logging.error(e.with_traceback)


class LivingV003(BaseDevice):
    """
      活物传感器003
    """
    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)

        data = event.get('data', {})
        da = None

        # if 'ReportTime' in data:
        #     attr = AttributePrototype.objects.get(device_prototype_id=device.device_prototype_id, identifier='ReportTime')
        #     da = DeviceAttribute.objects.get_by_idf(device, 'ReportTime')
        #     da.value = attr.options.split(',')[int(data['ReportTime'])]

        if data is not None and 'Light' in data:
            value = 0
            if data['Light'] == 'true':
                value = 1
            da = DeviceAttribute.objects.get_by_idf(device, 'Light')
            da.value = value

        if data is not None and 'Living' in data:
            desc = '有人活动'
            if data['Living'] == 'false':
                desc = '无人活动'

            ld = LivingDetection.objects.create(
                device_id=device.id,
                mac=device.mac,
                identifier='Living',
                value=data['Living'],
                desc=desc
            )
            ld.save()

        if data is not None and 'BodySensorStatus' in data:
            desc = '有人活动'
            if data['BodySensorStatus'] is False:
                desc = '无人活动'

            ld = LivingDetection.objects.create(
                device_id=device.id,
                mac=device.mac,
                identifier='BodySensorStatus',
                value=data['BodySensorStatus'],
                desc=desc
            )
            ld.save()

        if da is not None:
            da.save()
            DeviceAttribute.objects.save_to_redis(device, da)

    @classmethod
    def fault(cls, device, event):
        data = event.get('data', {})
        if data is not None and 'LocalTemp' in data:
            if float(data['LocalTemp']) < -20:
                glossary = '传感器不可用'

                if float(data['LocalTemp']) == -185:
                    glossary = '湿度CRC校验错误'
                if float(data['LocalTemp']) == -186:
                    glossary = '温度CRC校验错误'
                if float(data['LocalTemp']) == -187:
                    glossary = '发送失败'
                if float(data['LocalTemp']) == -188:
                    glossary = '没连接'

                DeviceIssue.objects.add_fault(device, glossary)
            else:
                DeviceIssue.objects.recover_fault(device, '')


class Living(LivingV003):
    """
      活物传感器
    """
    @classmethod
    def fault(cls, device, event):
        data = event.get('data', {})
        if data is not None and 'LocalTemp' in data:
            if float(data['LocalTemp']) < -20:
                glossary = '传感器不可用'

                if float(data['LocalTemp']) == -186:
                    glossary = '温度CRC校验错误'
                if float(data['LocalTemp']) == -187:
                    glossary = '发送失败（温度）'
                if float(data['LocalTemp']) == -188:
                    glossary = '没连接（温度）'

                DeviceIssue.objects.add_fault(device, glossary)
            else:
                DeviceIssue.objects.recover_fault(device, '温度CRC校验错误')
                DeviceIssue.objects.recover_fault(device, '发送失败（温度）')
                DeviceIssue.objects.recover_fault(device, '没连接（温度）')
                DeviceIssue.objects.recover_fault(device, '传感器不可用')

        if data is not None and 'LocalRH' in data:
            if float(data['LocalRH']) < -20:
                glossary = '传感器不可用'

                if float(data['LocalRH']) == -186:
                    glossary = '湿度CRC校验错误'
                if float(data['LocalRH']) == -187:
                    glossary = '发送失败（湿度）'
                if float(data['LocalRH']) == -188:
                    glossary = '没连接（湿度）'

                DeviceIssue.objects.add_fault(device, glossary)
            else:
                DeviceIssue.objects.recover_fault(device, '湿度CRC校验错误')
                DeviceIssue.objects.recover_fault(device, '发送失败（湿度）')
                DeviceIssue.objects.recover_fault(device, '没连接（湿度）')
                DeviceIssue.objects.recover_fault(device, '传感器不可用')


class Water(BaseDevice):
    """
      水浸传感器
    """
    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)

        data = event.get('data', {})
        da = None

        idfs = ['Path_1', 'Path_2', 'NotWorking']

        for idf in idfs:
            if idf in data:
                value = data[idf]
                idx = 0
                if data[idf] == 'true':
                    idx = 1
                da = DeviceAttribute.objects.get_by_idf(device, idf)
                ap = AttributePrototype.objects.query_by_idf(device, idf)
                if da is not None and ap is not None:
                    if ap.data_type == 30:
                        try:
                            options = ap.options.split(',')
                            value = options[idx]
                        except Exception as e:
                            logging.error(f'水浸传感器处理参数出错! device: {device.id}-{device.nick_name},'
                                          f' attr: {ap.id}-{ap.identifier}, value: {value}, err: {e.__str__()}')

                    da.value = value

        if da is not None:
            da.save()
            DeviceAttribute.objects.save_to_redis(device, da)

    @classmethod
    def fault(cls, device, event):
        data = event.get('data', {})
        idfs = {
            'Path_1': '通道1漏水',
            'Path_2': '通道2漏水',
            # 'NotWorking': '传感器故障'
        }
        for key, value in idfs.items():
            if key in data:
                if data[key] == 'true':
                    DeviceIssue.objects.add_fault(device, value)
                else:
                    DeviceIssue.objects.recover_fault(device, value)

        if 'NotWorking' in data:
            value = data['NotWorking']
            if value == 'true':
                DeviceIssue.objects.recover_fault(device, '传感器故障')
            else:
                DeviceIssue.objects.add_fault(device, '传感器故障')


class TempHum(BaseDevice):
    """
      温湿度传感器
    """
    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)

    @classmethod
    def fault(cls, device, event):
        data = event.get('data', {})
        if 'LocalTemp' in data:
            if float(data['LocalTemp']) < -20:
                glossary = '传感器不可用'

                if float(data['LocalTemp']) == -186:
                    glossary = '温度CRC校验错误'
                if float(data['LocalTemp']) == -187:
                    glossary = '发送失败（温度）'
                if float(data['LocalTemp']) == -188:
                    glossary = '没连接（温度）'

                DeviceIssue.objects.add_fault(device, glossary)
            else:
                DeviceIssue.objects.recover_fault(device, '温度CRC校验错误')
                DeviceIssue.objects.recover_fault(device, '发送失败（温度）')
                DeviceIssue.objects.recover_fault(device, '没连接（温度）')
                DeviceIssue.objects.recover_fault(device, '传感器不可用')

        if 'LocalRH' in data:
            if float(data['LocalRH']) < -20:
                glossary = '传感器不可用'

                if float(data['LocalRH']) == -186:
                    glossary = '湿度CRC校验错误'
                if float(data['LocalRH']) == -187:
                    glossary = '发送失败（湿度）'
                if float(data['LocalRH']) == -188:
                    glossary = '没连接（湿度）'

                DeviceIssue.objects.add_fault(device, glossary)
            else:
                DeviceIssue.objects.recover_fault(device, '湿度CRC校验错误')
                DeviceIssue.objects.recover_fault(device, '发送失败（湿度）')
                DeviceIssue.objects.recover_fault(device, '没连接（湿度）')
                DeviceIssue.objects.recover_fault(device, '传感器不可用')


class CellThe(BaseDevice):
    """
      电池温度计
    """
    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)

        data = event.get('data', {})
        if 'NightSaveMode' in data:
            da = DeviceAttribute.objects.get_by_idf(device, 'NightSaveMode')
            if da is not None:
                value = 0
                if data['NightSaveMode'] == 'true':
                    value = 1
                da.value = value
                da.save()
                DeviceAttribute.objects.save_to_redis(device, da)


class TSensor(BaseDevice):
    """
      贴片温度传感器
    """
    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)

        data = event.get('data', {})
        da = None

        if 'Temp' in data:
            da = DeviceAttribute.objects.get_by_idf(device, 'Temp')
            if da is not None:
                da.value = round(float(data['Temp']) - 200, 2)
                da.save()
                DeviceAttribute.objects.save_to_redis(device, da)


class SmokeSensor(BaseDevice):
    """烟感设备"""
    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)

        data = event.get('data', {})

        if 'update_time' in data:
            dt_str = data['update_time'].split('.')[0]
            update_time = datetime.datetime.strptime(dt_str, '%Y-%m-%d %H:%M:%S')

            timeout = device.device_prototype.timeout_set
            if not timeout:
                timeout = 3600

            span_seconds = (datetime.datetime.now() - update_time).total_seconds()
            if device.online and span_seconds > timeout:
                device.online = False
                device.status = 30
                device.save(update_fields=['online', 'status'])
                Terminal.objects.bulk_update_by_device(device, 'online', device.online)
            if (not device.online) and span_seconds < timeout:
                device.online = True
                if device.status != 40:
                    device.status = 20
                device.save(update_fields=['online', 'status'])
                # 更新终端在线状态
                for terminal in Terminal.objects.filter(device=device, online=False):
                    terminal.online = True
                    terminal.save()

            # 将设备的更新时间设置为上报数据的更新时间
            Device.objects.filter(pk=device.id).update(updated_at=update_time)
            Terminal.objects.filter(device_id=device.id).update(updated_at=update_time)

    @classmethod
    def fault(cls, device, event):
        super().fault(device, event)

        data = event.get('data', {})
        if 'state' in data:
            if data['state'] == 1:
                DeviceIssue.objects.add_fault(device, "烟感故障")
            elif data['state'] == 0:
                DeviceIssue.objects.recover_fault(device, "烟感故障")

    @classmethod
    def alarm(cls, device, event):
        super().alarm(device, event)
        data = event.get('data', {})
        if 'state' in data:
            if data['state'] == 2 or data['state'] == 3:
                DeviceIssue.objects.add_alarm(device, "烟感报警")
            elif data['state'] == 0:
                DeviceIssue.objects.recover_alarm(device, "烟感报警")


class XYPress(BaseDevice):
    # 星仪压力变送器
    @classmethod
    def update_attres(cls, device, event):
        data = event.get('data', {})

        if 'LocalPress' in data:
            da = DeviceAttribute.objects.get_by_idf(device, 'LocalPress')
            if da is not None:
                # 压力除以2
                da.value = round(float(data['LocalPress']) / 2, 2)
                da.save()
                DeviceAttribute.objects.save_to_redis(device, da)
                data['LocalPress'] = da.value
        super().update_attres(device, event)

    @classmethod
    def alarm(cls, device, event):
        super().alarm(device, event)

    @classmethod
    def fault(cls, device, event):
        super().fault(device, event)
