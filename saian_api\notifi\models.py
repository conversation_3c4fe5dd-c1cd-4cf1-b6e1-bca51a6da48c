import json
import logging
import os
from datetime import datetime, timedelta

import requests
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.core.mail import EmailMessage
from django.db import models
from django.db.models import Q
from django.db.models.deletion import CASCADE
from rest_framework import exceptions

from saian_api.devdefine.models import AttributePrototype
from saian_api.device.models import Device, DeviceAttribute
from saian_api.dimension.models import Dimension, DimensionTerminal, DimensionAttribute
from saian_api.group.models import GroupDevice, Group
from saian_api.message.models import Message
from saian_api.project.models import Project
from saian_api.terminal.models import Terminal, TerminalAttribute
from saian_api.user.models import WebUser, UserDevice
from saian_api.utils.alisms import AliSms
from saian_api.utils.db.Redis import RedisHelper
from saian_api.utils.httpapi import DOMAIN
from saian_api.utils.notifier import Sms
from django.conf import settings

from saian_api.utils.tools import is_number

# Create your models here.

class NotifyConfig(models.Model):
    """
    通知配置
    """
    # 配置针对的对象id, 如果为null，即为msg_type对应的所有场景都会发送通知
    concern_id = models.PositiveIntegerField(null=True)
    # 配置针对的对象类型, 如：device-设备，terminal-终端，device_prototype-设备类型，dimension-维度等
    concern_type = models.ForeignKey(ContentType, null=True, on_delete=models.CASCADE)
    concern = GenericForeignKey('concern_type', 'concern_id')
    # 接收通知的用户
    web_user = models.ForeignKey(WebUser, on_delete=models.CASCADE)
    # 消息类型，1-系统通知，2-冷源系统，3-末端系统，4-设备离线，5-快捷操作，6-设备联动，7-新设备接入，8-设备故障，9-设备报警，10-设备保养，11-参数超限，
    # ####### 12-日统计报告，13-周统计报告，14-月统计报告，15-年统计报告, 99-其他
    msg_type = models.IntegerField()
    # 故障内容，故障默认是短信通知的，这里用于某些具体故障需要另外的通知方式
    issue_name = models.CharField(max_length=255, null=True)
    # 通知方式，10-短信，20-站内消息，30-邮件，40-电话，50-微信消息
    noti_way = models.IntegerField()

    class Meta:
        db_table = 'notify_configs'


class NotifyLimitManager(models.Manager):
    @classmethod
    def parse_formula(cls, formula_array, device=None, dimension=None):
        condition = """"""
        display_condition = """"""
        for item in formula_array:
            identifier = item.get('idf', None)
            if identifier is not None:
                # 暂不支持维度
                if device is not None:
                    ap = AttributePrototype.objects.get_by_idf(device, identifier)
                    # da = DeviceAttribute.objects.get_by_ap(device, ap)
                    da = DeviceAttribute.objects.query_object_by_ap(device, ap)

                    idf_value = da.value if da else ap.default_value
                    if (ap.data_type == 20 or ap.data_type == 10) and is_number(idf_value):
                        idf_value = float(idf_value)
                        condition += f'{idf_value}'
                    else:
                        condition += f'"{idf_value}"'
                    display_condition += f'{ap.name}'

                    cmp_op = item.get('cmp_op', None)
                    if cmp_op is not None:
                        rel_value = item.get('value', ap.default_value)
                        condition += f'{cmp_op}'
                        display_condition += f'{cmp_op}'
                        if (ap.data_type == 20 or ap.data_type == 10) and is_number(idf_value):
                            condition += f'{float(rel_value)}'
                            display_condition += f'{float(rel_value)}'
                        else:
                            condition += f'"{rel_value}"'
                            display_condition += f'"{rel_value}"'

                    log_op = item.get('log_op', None)
                    if log_op is not None:
                        display_log_op = log_op
                        if log_op == '||':
                            display_log_op = '或'
                            log_op = ' or '
                        elif log_op == '&&':
                            display_log_op = '并'
                            log_op = ' and '
                        condition += f'{log_op}'
                        display_condition += f'{display_log_op}'

        if condition:
            return eval(condition), display_condition

    @classmethod
    def handle_limit_notify(cls, limits, device, data, label):
        for limit in limits:

            # 当前时刻是否位于生效时间段中
            in_time_range = True

            # 生效时间判断
            if limit.time_ranges is not None:
                in_time_range = False
                current_time = datetime.now().time()
                for time_range in limit.time_ranges:
                    from_at = time_range.get('from', None)
                    till_at = time_range.get('till', None)
                    if from_at is None or till_at is None:
                        continue

                    try:
                        from_at = datetime.strptime(from_at, '%H:%M').time()
                        till_at = datetime.strptime(till_at, '%H:%M').time()

                        if from_at <= till_at:  # 不跨午夜
                            in_time_range = from_at <= current_time <= till_at
                        else:  # 跨午夜
                            in_time_range = current_time >= from_at or current_time <= till_at

                        if in_time_range:
                            break

                    except Exception as e:
                        logging.error(f'超限配置检查时间范围出错！from-{from_at}, till-{till_at}, error-{e.__str__()}')

            if not in_time_range:
                continue

            if limit.identifier is not None:
                if limit.identifier in data:
                    try:
                        # 维度参数超限处理。因为维度参数取值来自于维度属性(DimensionAttribute)，不能直接使用data的值，故单独处理
                        if label.startswith('维度参数'):
                            ap = AttributePrototype.objects.get_by_idf(device, limit.identifier)
                            # 通过 ap 来确定维度属性，而不是 identifier
                            for da in DimensionAttribute.objects.filter(dimension_id=limit.concern_id, identifier=limit.identifier):
                                if str(ap.id) in da.ap_ids.split(','):
                                    value = da.value
                                    result = eval(limit.condition)
                                    if result is True:
                                        dimension = Dimension.objects.get(pk=limit.concern_id)
                                        content = f'{label}: 维度【{dimension.id}-{dimension.name}】参数({limit.identifier})超限: {value}'
                                        data = {
                                            'content': content,
                                            'dimension_id': dimension.id,
                                            'dimension_name': dimension.name,
                                            'limit_identifier': limit.identifier,
                                            'limit_condition': limit.condition,
                                            'value': value
                                        }
                                        Notification.objects.notify(11, device, data, dimension=dimension)

                        # 设备，终端，设备类型参数超限处理。
                        else:
                            value = data[limit.identifier]
                            result = eval(limit.condition)
                            if result is True:
                                content = f'{label}: 设备【{device.id}-{device.nick_name}】参数({limit.identifier})超限: {value}'
                                data = {
                                    'content': content,
                                    'device_id': device.id,
                                    'device_nickname': device.nick_name,
                                    'limit_identifier': limit.identifier,
                                    'limit_condition': limit.condition,
                                    'value': value
                                }
                                Notification.objects.notify(11, device, data)

                    except Exception as e:
                        logging.error(f'解析{label}失败，device: {device.id}-{device.nick_name}, error: {e.__str__()}')
                        continue
            else:
                try:
                    keys = data.keys()
                    formula_list = json.loads(limit.condition)
                    identifiers = [item.get('idf', None) for item in formula_list]

                    idf = list(set(keys) & set(identifiers))

                    if len(idf):
                        result, condition = cls.parse_formula(formula_list, device)
                        if result:
                            content = f'{label}: 设备【{device.id}-{device.nick_name}】参数({idf[0]})超限: {data.get(idf[0], None)}，条件: {condition}'
                            data = {
                                'content': content,
                                'device_id': device.id,
                                'device_nickname': device.nick_name,
                                'limit_identifier': idf[0],
                                'limit_condition': condition,
                                'value': data.get(idf[0], None)
                            }
                            Notification.objects.notify(11, device, data)

                except Exception as e:
                    logging.error(f'解析{label}失败，device: {device.id}-{device.nick_name}, error: {e.__str__()}')
                    continue

    def check_limit(self, device: Device, data):
        # check device
        device_model_id = ContentType.objects.get_for_model(device).id

        group_ids = set(GroupDevice.objects.filter(content_type_id=device_model_id, object_id=device.id).values_list('group_id', flat=True))

        device_limits = NotifyLimit.objects.filter(Q(concern_id=device.id) | Q(group_id__in=group_ids), concern_type=device_model_id)

        if device_limits.exists():
            self.handle_limit_notify(device_limits, device, data, '设备参数超限通知')

        # check device prototype
        dp_limits = NotifyLimit.objects.filter(concern_type=ContentType.objects.get_for_model(device.device_prototype).id,
                                               concern_id=device.device_prototype_id)
        if dp_limits.exists():
            self.handle_limit_notify(dp_limits, device, data, '设备类型参数超限通知')

        # check terminal
        terminals = Terminal.objects.filter(device=device, show_en=True)
        if terminals.exists():
            terminal_limits = NotifyLimit.objects.filter(concern_type=ContentType.objects.get_for_model(terminals.first()),
                                                         concern_id__in=[t.id for t in terminals])
            if terminal_limits.exists():
                self.handle_limit_notify(terminal_limits, device, data, '终端参数超限通知')

        # check dimension
        # - 维度是与终端绑定的，先获取设备的终端，再根据终端获取绑定的维度
        terminals = Terminal.objects.filter(device=device)
        dimension_ids = DimensionTerminal.objects.filter(terminal__in=terminals).values_list('dimension_id', flat=True)
        dimensions = Dimension.objects.filter(id__in=dimension_ids)
        if dimensions.exists():
            dimension_limits = NotifyLimit.objects.filter(concern_type=ContentType.objects.get_for_model(dimensions.first()),
                                                          concern_id__in=[d.id for d in dimensions])
            if dimension_limits.exists():
                self.handle_limit_notify(dimension_limits, device, data, '维度参数超限通知')


class NotifyLimit(models.Model):
    """参数范围配置"""
    # 参数所有者id
    concern_id = models.PositiveIntegerField(null=True)
    # 参数所有者类型
    concern_type = models.ForeignKey(ContentType, null=True, on_delete=models.CASCADE)
    concern = GenericForeignKey('concern_type', 'concern_id')
    # 参数标识, 如果identifier为空，则认为该超限配置是公式，公式存在condition字段，是一个对象数组json字符串
    identifier = models.CharField(max_length=255, null=True)
    # 判断条件，代码格式，用标识代表的参数值代入公式计算结果，如果为true则触发通知，如：value > 25，value就是参数值
    # 隐藏功能，当identifier为null时，xx即用执行联动的方式直接执行condition表示的代码xx
    # 隐藏功能，当identifier为null时，==解析condition为公式再判断==
    condition = models.TextField()
    # 关联的分组
    group = models.ForeignKey(Group, null=True, on_delete=models.SET_NULL)
    # 有效时间范围
    time_ranges = models.JSONField(null=True, default=None)

    class Meta:
        db_table = 'notify_limits'

    objects = NotifyLimitManager()

class NotifiManager(models.Manager):
    def send_fault_notify(self, device, glossary):
        # 故障通知，通知关注该设备的用户，无论是否在 notify_config 配置了
        device_users = list(UserDevice.objects.filter(device_id=device.id).values_list('web_user_id', flat=True))
        users = []
        for user in WebUser.objects.filter(mobile__isnull=False, id__in=device_users):
            if user.unionid:
                # 判断是否在小程序开启了短信通知
                mini_user = WebUser.objects.get_mini_user(user.unionid, device.project_id)
                if mini_user.get('enable_sms', False):
                    users.append(user)
            else:
                users.append(user)
        # users = WebUser.objects.filter(mobile__isnull=False, id__in=device_users)
        if '漏水' in glossary or '水浸' in glossary:
            self.send_save_notify(8, "leakage", device, users, glossary)
        else:
            self.send_save_notify(8, "device", device, users, glossary)

    def send_save_notify(self, msg_type, sms_type, device, users, content, status="故障", data=None, project=None):
        """
          给users群发短信通知并保存发送记录
        """
        if users is not None:
            if project is None:
                project = device.project
            for user in users:
                if user.unionid:
                    mini_user = WebUser.objects.get_mini_user(user.unionid, project.id)
                    if not mini_user.get('enable_sms', False):
                        continue

                time_now = datetime.now()
                beginning_of_day = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
                his = self.filter(device_id=device.id if device else None,
                                  web_user_id=user.id,
                                  noti_way=10,
                                  fault_identifier=content,
                                  created_at__range=(beginning_of_day, time_now))

                # 今天还没给用户发送过通知
                if not his:
                    # if user.mobile is not None and user.enable_sms:
                    if user.mobile is not None:
                        # 房间
                        if sms_type == 'room':
                            self.send_room_notify(device, user, content)

                        # 设备
                        elif sms_type == 'device':
                            if status == '超限':
                                self.send_limit_notify(device, user, data)
                            else:
                                self.send_sms_notify(device, user, status, content)

                        # 报告
                        elif sms_type == 'report':
                            project_name = data.get('project', '')
                            title = data.get('title')
                            self.send_report_notify(user, project_name, title)

                        # 868163042906930为测试用，特殊处理，每次都发通知
                        if device is not None and device.mac != '868163042906930':
                            if sms_type == 'leakage':
                                self.send_leakage_notify(device, user)

                        # 创建记录
                        ns = self.create(device_id=device.id if device else None,
                                         web_user_id=user.id,
                                         fault_identifier=content,
                                         user_name=user.name,
                                         device_name=device.nick_name if device else None,
                                         project_id=device.project_id if device else project.id,
                                         # project_name=device.project.name,
                                         content=data.get('content', None) if data else None,
                                         msg_type=msg_type,
                                         mobile=user.mobile,
                                         noti_way=10)
                        ns.save()

                # 868163042906930特殊处理，发送通知
                if device is not None and device.mac == '868163042906930':
                    self.send_leakage_notify(device, user)

    @staticmethod
    def send_room_notify(device, user, content):
        """
          发送房间超标短信通知
        """
        sender = '8822041229228'
        mobile = user.mobile
        TEMPLATE_ID = 'c4cf841be1164838a119578f22fc4826'
        deviceinfo = device.project.name + '，' + device.active_room.name
        templateParas = '["' + user.name + '","' + deviceinfo + '","' + content + '"]'

        # Sms.send(sender, mobile, TEMPLATE_ID, templateParas)
        templateCode = 'SMS_482450054'
        templateParam = "{\"user\":\"" + user.name + "\",\"address\":\"" + deviceinfo + "\",\"issue\":\"" + content + "\"}"
        templateParam = {
            'user': user.name,
            'address': deviceinfo,
            'issue': content
        }
        AliSms.send(mobile, templateCode, templateParam)

    @staticmethod
    def send_sms_notify(device, user, status, content):
        """
          发送设备状态异常(status)短信通知
        """
        sender = '8822041229228'
        mobile = user.mobile
        TEMPLATE_ID = 'dc0737d8bb3e4ad8a753d9e625e63a82'

        nick_name = device.nick_name
        if nick_name is None:
            nick_name = device.device_prototype.name
        deviceinfo = device.project.name + '，' + nick_name
        deviceinfo = deviceinfo.replace('.', '․')

        content = content.replace('.', '․')

        username = user.name.replace('.', '․')
        templateParas = '["' + status + '","' + username + '","' + deviceinfo + '","' + content + '"]'

        # Sms.send(sender, mobile, TEMPLATE_ID, templateParas)
        templateCode = 'SMS_485450124'
        # templateParam = "{\"status\":\"" + status + "\",\"user\":\"" + username + "\",\"address\":\"" + deviceinfo + "\",\"error\":\"" + content + "\"}"
        templateParam = {
            'status': status,
            'user': username,
            'address': deviceinfo,
            'error': content
        }

        AliSms.send(mobile, templateCode, templateParam)

    @staticmethod
    def send_report_notify(user, project_name, title):
        """发送分析报告短信通知"""
        sender = '8822041229228'
        mobile = user.mobile
        TEMPLATE_ID = '4dfe92dd78cb44aaa2d5542ccf484172'

        username = user.name.replace('.', '․')

        templateParas = f'["{username}","{project_name}","{title}"]'

        # Sms.send(sender, mobile, TEMPLATE_ID, templateParas)
        templateCode = 'SMS_482425060'
        templateParam = "{\"user\":\"" + username + "\",\"address\":\"" + project_name + "\",\"report\":\"" + title + "\"}"
        templateParam = {
            'user': username,
            'address': project_name,
            'report': title
        }
        AliSms.send(mobile, templateCode, templateParam)

    @staticmethod
    def send_limit_notify(device, user, data):
        """
            发送超限通知
        """
        sender = '8822041229228'
        mobile = user.mobile
        TEMPLATE_ID = '956acde986ca4f1a9d4835a2f635cd09'

        # limit_status = f'参数 {data["limit_identifier"]} 超限，当前值为 {data["value"]}'
        idf = data["limit_identifier"]
        ap = AttributePrototype.objects.get_by_idf(device, idf)
        limit_status_name = f'{ap.name}'
        limit_status_name = limit_status_name.replace('.', '․')
        limit_status_value = f'{data["value"]}'
        limit_status_value = limit_status_value.replace('.', '․')
        # limit_status = f'参数超限({int(float(data["value"]))})'
        name = user.name
        name = name.replace('.', '․')
        project = f'{device.project.name}的{device.nick_name}'
        project = project.replace('.', '․')

        templateParas = f'["{name}","{project}","{limit_status_name}","{limit_status_value}"]'

        templateCode = 'SMS_482455055'
        templateParam = "{\"user\":" + name + ",\"address\":" + project + ",\"ap\":" + limit_status_name + ",\"value\":" + limit_status_value + "}"
        templateParam = {
            'user': name,
            'address': project,
            'ap': limit_status_name,
            'value': limit_status_value
        }
        AliSms.send(mobile, templateCode, templateParam)

    @staticmethod
    def send_leakage_notify(device, user):
        """
          发送漏水短信通知
        """
        name = device.nick_name
        if device.nick_name is None:
            name = device.device_prototype.name

        sender = '8822041229228'
        mobile = user.mobile
        TEMPLATE_ID = 'c15a6108bcd04d8081709aafd2b7ab2d'
        deviceinfo = device.project.name + '，' + name
        templateParas = '["' + user.name + '","' + deviceinfo + '"]'

        # Sms.send(sender, mobile, TEMPLATE_ID, templateParas)
        templateCode = 'SMS_482625052'
        templateParam = "{\"user\":\"" + user.name +  "\",\"address\":\"" + deviceinfo + "\"}"
        templateParam = {
            'user': user.name,
            'address': deviceinfo
        }
        AliSms.send(mobile, templateCode, templateParam)

    @staticmethod
    def sms_verified_code(mobile, code):
        """
          发送验证码
        """
        sender = '8822040111253'
        mobile = mobile
        TEMPLATE_ID = '7eff9574a9084d278e550ca44a5e5d9f'
        templateParas = '[' + code + ']'
        # Sms.send(sender, mobile, TEMPLATE_ID, templateParas)
        templateCode = 'SMS_484130201'
        templateParam = "{\"code\":" + code + "}"
        templateParam = {
            'code': code
        }
        AliSms.send(mobile, templateCode, templateParam)

    @staticmethod
    def send_email(subject, content, to: list):
        try:
            email = EmailMessage(subject=subject, body=content, bcc=to, from_email=f'塞安物联<{settings.EMAIL_HOST_USER}>')
            email.send(fail_silently=False)
        except Exception as e:
            logging.error(f'发送邮件失败: {e.__str__()}, {content}')

    @staticmethod
    def send_email_with_attachment(subject, content, to: list):
        """
        发送通知邮件，并将详细信息作为附件发送。
        """
        try:
            email = EmailMessage(subject=subject,
                                 body="请查看附件。",
                                 bcc=to, from_email=f'塞安物联<{settings.EMAIL_HOST_USER}>')
            email.attach('log.txt', content.encode('utf-8'), 'text/plain')
            email.send(fail_silently=False)
        except Exception as e:
            logging.error(f'发送邮件失败: {e.__str__()}, {content}')

    @staticmethod
    def get_email_subject(project_name, msg_type):
        types = {
            '1': '系统通知', '2': '设备故障', '3': '设备报警', '4': '设备离线', '5': '新设备接入', '6': '设备保养',
            '7': '冷源系统', '8': '末端系统', '9': '快捷操作', '10': '设备联动', '11': '参数超限', '12': '日统计报告',
            '13': '周统计报告', '14': '月统计报告', '15': '年统计报告', '99': '其他'
        }

        email_type = types.get(str(msg_type), None)

        return f'【{project_name}】{email_type}' if project_name else email_type

    @classmethod
    def get_access_token(cls):
        wechat_appid = os.environ.get('WECHAT_APPID')
        wechat_secret = os.environ.get('WECHAT_SECRET')

        params = {
            "grant_type": "client_credential",
            "appid": wechat_appid,
            "secret": wechat_secret
        }

        # DOMAIN = 'https://api.weixin.qq.com'
        result = requests.post(f'{DOMAIN}/cgi-bin/stable_token', json=params)
        if result.status_code != 200:
            logging.error("Failed to get new access token.")
            return None
        else:
            data = result.json()
            token = data.get('access_token', None)
            expires_in = data.get('expires_in', 3600)

            if token is not None:
                RedisHelper.set_wechat_token(token, ex=expires_in)
                return token
            logging.error(f'failed to request access token, err: {data}')
            return None

    @staticmethod
    def get_micromsg_data(msg_type, device: Device, msg_data):
        miniprogram = {
            "appid": "wx917d895bd845263e",
            "pagepath": f"/devicePackage/pages/common-detail/detail?deviceId={device.id if device else None}&project={device.project_id}"
        }
        # 项目名称
        thing34 = {
            "value": device.project.name
        }
        thing17 = thing34
        thing22 = thing34
        thing8 = thing34
        # 设备昵称
        thing2 = {
            "value": device.nick_name
        }
        thing13 = thing2
        thing3 = thing2
        # MAC
        character_string20 = {
            "value": device.mac
        }
        thing6 = character_string20
        character_string24 = character_string20
        # 事件发生时间
        time4 = {
            "value": device.updated_at.strftime('%Y-%m-%d %H:%M:%S')
        }
        time2 = time4
        time6 = time4

        # 设备离线
        if msg_type == 4:
            return {
                "template_id": "s3ZxhM4itUfzalYRGP5PbYjyUBL9DyDguq4czt8KaKk",
                "miniprogram": miniprogram,
                "data": {
                    "thing17": thing17,
                    "thing2": thing2,
                    "thing6": thing6,
                    "phrase3": {
                        "value": "在线" if device.online else "离线"
                    },
                    "time4": time4
                }
            }

        # 设备故障
        elif msg_type == 8:
            glossary = msg_data.get('glossary', None)
            return {
                "template_id": "2YBDPLSbuZJ-SfydrG1zsjkGXJJ0giS9cZRf1NUwnDM",
                "miniprogram": miniprogram,
                "data": {
                    "thing22": thing22,
                    "thing23": {
                        'value': glossary
                    },
                    "thing13": thing13,
                    "character_string24": character_string24,
                    "time2": time2
                }
            }

        # 设备报警
        elif msg_type == 9:
            glossary = msg_data.get('glossary', None)
            return {
                "template_id": "ose_FjGfDRBz69SEd3pImwxSVAZMF_3UlTyrJ-nCh6U",
                "miniprogram": miniprogram,
                "data": {
                    "thing34": thing34,
                    "thing5": {
                        "value": glossary
                    },
                    "thing2": thing2,
                    "character_string20": character_string20,
                    "time4": time4
                }
            }

        # 设备参数超限
        elif msg_type == 11:
            identifier = msg_data.get('limit_identifier', None)
            condition = msg_data.get('limit_condition', None)
            if len(condition) > 20:
                condition = f'{condition[:17]}...'
            # condition = condition.replace('value', identifier)
            value = msg_data.get('value', None)
            return {
                "template_id": "eoXbn7htJ05qzfKNXnOzng0he0iO9Ej0Ww5uncWK7rw",
                "miniprogram": miniprogram,
                "data": {
                    "thing8": thing8,
                    "thing3": thing3,
                    "thing4": {
                        "value": condition
                    },
                    "thing5": {
                        "value": value
                    },
                    "time6": time6
                }
            }


    @classmethod
    def send_micro_message(cls, web_user: WebUser, msg_type, device, msg_data, is_repeat=False):
        openid = web_user.wechat_openid
        # if not openid:
        #     admin_web_user = WebUser.objects.using('syadmindb').filter(pk=web_user.id).last()
        #     if admin_web_user:
        #         openid = admin_web_user.wechat_openid

        if not openid:
            return

        data = cls.get_micromsg_data(msg_type, device, msg_data)
        data["touser"] = web_user.wechat_openid

        wechat_token = RedisHelper.get_wechat_token()
        if wechat_token is None:
            wechat_token = cls.get_access_token()

        params = {
            "access_token": wechat_token
        }

        # DOMAIN = 'https://api.weixin.qq.com'
        result = requests.post(f'{DOMAIN}/cgi-bin/message/template/send', params=params, json=data).json()
        if result.get('errcode') != 0:
            logging.info(f'发送微信通知失败。{web_user.id}-{web_user.name}, msg: {msg_data}, result: {result}')
            if not is_repeat:
                cls.get_access_token()
                cls.send_micro_message(web_user, msg_type, device, msg_data, is_repeat=True)

    @classmethod
    def send_debug_mail(cls, device, message):
        if os.environ.get('SEND_DEBUG_EMAIL', 'True') == 'True':
            receiver_id = [66]
            emails = [web_user.email for web_user in WebUser.objects.filter(id__in=receiver_id) if web_user.email]
            cls.send_email(cls.get_email_subject(device.project.name if device else None, 99), message, emails)

    @classmethod
    def send_debug_email_with_attachment(cls, project_name, message):
        if os.environ.get('SEND_DEBUG_EMAIL', 'True') == 'True':
            receiver_id = [66]
            emails = [web_user.email for web_user in WebUser.objects.filter(id__in=receiver_id) if web_user.email]
            cls.send_email_with_attachment(cls.get_email_subject(project_name, 99), message, emails)

    def send_notification(self, msg_type, queryset, device: [Device, None], data, uid='', project=None):
        notify = False
        if project is None:
            project = device.project
        # 短信
        sms_configs = queryset.filter(noti_way=10)
        if sms_configs.exists():
            if device is not None:
                # 设备相关短信通知
                config_users = [config.web_user_id for config in sms_configs]
                # 关注了该设备的用户
                device_users = list(UserDevice.objects.filter(device_id=device.id).values_list('web_user_id', flat=True))
                user_ids = list(set(config_users + device_users))
                users = WebUser.objects.filter(id__in=user_ids, mobile__isnull=False)
                sms_status = '离线'
                if msg_type == 8:
                    sms_status = '故障'
                elif msg_type == 9:
                    sms_status = '报警'
                elif msg_type == 11:
                    sms_status = '超限'
                content = data.get('glossary', sms_status)
                self.send_save_notify(msg_type, "device", device, users, content, data=data, status=sms_status)
                # self.cus_create(sms_configs, msg_type, device, content)
                notify = True
            else:
                # 设备无关短信通知(分析报告)
                config_users = [config.web_user_id for config in sms_configs]
                users = WebUser.objects.filter(id__in=config_users, mobile__isnull=False)
                content = data.get('title')
                self.send_save_notify(msg_type, "report", None, users, content, data=data, project=project)
                notify = True

        # 站内消息
        msg_configs = queryset.filter(noti_way__gte=20, noti_way__lt=30)
        content = data.get('content', '')
        if msg_configs.exists():
            users = [config.web_user for config in msg_configs]
            Message.objects.cus_create(msg_type, project.id, content, uid=uid, users=users)
            self.cus_create(msg_configs, msg_type, device, content, project=project)
            notify = True

        # 邮件
        email_configs = queryset.filter(noti_way=30, web_user__email__isnull=False)
        content = data.get('content')
        if email_configs.exists():
            emails = list(filter(lambda x: x, [config.web_user.email for config in email_configs]))
            self.send_email(self.get_email_subject(project.name if project else None, msg_type), content, emails)
            self.cus_create(email_configs, msg_type, device, content, project=project)
            notify = True

        # TODO 电话

        # 微信消息
        micro_configs = queryset.filter(noti_way=50)
        now = datetime.now()
        content = data.get('content')
        for config in micro_configs:
            # 15分钟内不发重复的消息
            if not Notification.objects.filter(web_user_id=config.web_user_id, noti_way=50, content=content, msg_type=msg_type,
                                               concern_id=config.concern_id, concern_type_id=config.concern_type_id,
                                               created_at__gte=(now - timedelta(minutes=15))).exists():
                self.send_micro_message(config.web_user, msg_type, device, data)
                self.cus_create([config], msg_type, device, content, project=project)
                notify = True
            else:
                logging.info(f'微信消息已存在，不重复发送')

        return notify

    def notify(self, msg_type, device, data, terminal=None, uid=None, dimension=None, project=None):
        queryset = NotifyConfig.objects.filter(msg_type=msg_type).order_by('noti_way')

        # None
        # none_queryset = queryset.filter(concern_id__isnull=True)
        # self.send_notification(msg_type, none_queryset, device, content, uid=uid)
        # 与具体设备无关的通知
        if device is None:
            self.send_notification(msg_type, queryset, device, data, uid=uid, project=project)
            return

        # 设备目标
        device_target_type = ContentType.objects.get_for_model(device)
        device_queryset = queryset.filter(concern_type=device_target_type, concern_id=device.id)
        if self.send_notification(msg_type, device_queryset, device, data, uid=uid):
            return

        # 终端目标
        if terminal is None:
            terminals = Terminal.objects.filter(device=device, show_en=True)
        else:
            terminals = [terminal]
        if len(terminals):
            terminal_target_type = ContentType.objects.get_for_model(Terminal)
            terminal_queryset = queryset.filter(concern_type=terminal_target_type, concern_id__in=[t.id for t in terminals])
            if self.send_notification(msg_type, terminal_queryset, device, data, uid=uid):
                return

        # 设备类型
        device_prototype = device.device_prototype
        dp_target_type = ContentType.objects.get_for_model(device_prototype)
        dp_queryset = queryset.filter(concern_type=dp_target_type, concern_id=device_prototype.id)
        if self.send_notification(msg_type, dp_queryset, device, data, uid=uid):
            return

        # 分组类型
        group_ids = GroupDevice.objects.filter(content_type=device_target_type, object_id=device.id).values_list('group_id', flat=True)
        group_target_type = ContentType.objects.get_for_model(Group)
        group_queryset = queryset.filter(concern_type=group_target_type, concern_id__in=group_ids)
        if self.send_notification(msg_type, group_queryset, device, data, uid=uid):
            return

        # 维度
        if dimension is not None:
            dimension_type = ContentType.objects.get_for_model(dimension)
            dimension_queryset = queryset.filter(concern_type=dimension_type, concern_id=dimension.id)
            if self.send_notification(msg_type, dimension_queryset, device, data):
                return

    def cus_create(self, configs, msg_type, device, content, project=None):
        if project is None:
            project = device.project
        records = []
        for config in configs:
            records.append(Notification(device_id=device.id if device else None,
                                        device_name=device.nick_name if device else None,
                                        web_user=config.web_user,
                                        user_name=config.web_user.name,
                                        project=project,
                                        project_name=project.name,
                                        content=content, msg_type=msg_type, noti_way=config.noti_way,
                                        concern_type=config.concern_type, concern_id=config.concern_id,
                                        email=config.web_user.email, mobile=config.web_user.mobile))

        self.bulk_create(records)

class Notification(models.Model):
    """
      短信，email通知记录
    """
    # 对应的设备
    device = models.ForeignKey(Device, null=True, on_delete=models.CASCADE)
    # 设备昵称
    device_name = models.CharField(max_length=255, null=True)
    # 对应的用户
    web_user = models.ForeignKey(WebUser, on_delete=CASCADE)
    # 用户名称
    user_name = models.CharField(max_length=128, null=True)
    # 对应的项目
    project = models.ForeignKey(Project, on_delete=CASCADE)
    # 项目明名称
    project_name = models.CharField(max_length=255, null=True)
    # 手机号码
    mobile = models.CharField(max_length=255, null=True)
    # email
    email = models.CharField(max_length=255, null=True)
    # 参数标识
    fault_identifier = models.CharField(max_length=255, null=True)
    # 故障描述
    fault_name = models.CharField(max_length=255, null=True)
    # 通知内容
    content = models.CharField(max_length=255, null=True)
    # 消息类型，1-系统通知，2-冷源系统，3-末端系统，4-设备离线，5-快捷操作，6-设备联动，7-新设备接入，8-设备故障，9-设备报警，10-设备保养，11-参数超限
    # ####### 12-日统计报告，13-周统计报告，14-月统计报告，15-年统计报告, 99-其他
    msg_type = models.IntegerField(default=8)
    # 通知方式，10-短信，20-站内消息，30-邮件，40-电话，50-微信消息
    noti_way = models.IntegerField(default=10)
    # 配置针对的对象id
    concern_id = models.PositiveIntegerField(null=True)
    # 配置针对的对象类型
    concern_type = models.ForeignKey(ContentType, null=True, on_delete=models.CASCADE)
    concern = GenericForeignKey('concern_type', 'concern_id')

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'notify_histories'

    objects = NotifiManager()


class VerifiedCodeManager(models.Manager):
    def create_cus(self, mobile):
        vc = self.filter(target=mobile).first()
        code = self.generate_code()

        if vc is not None:
            # 上次发送验证码和当前时间的时间间隔，如果小于1分钟，则返回错误
            t = (datetime.now() - vc.updated_at).seconds

            if t > 60:
                # 重复发送次数小于10次，正常发送，累计次数+1
                if vc.times < 10:
                    vc.times += 1
                    vc.verified_code = code
                    vc.save()
                else:
                    # 连续两次时间间隔大于30分钟，则重新计算几次
                    if t > 1800:
                        vc.times = 1
                        vc.verified_code = code
                        vc.save()
                    else:
                        raise exceptions.PermissionDenied(
                            detail={'status': 40006, 'detail': 'Time limited!'})
            else:
                raise exceptions.PermissionDenied(
                    detail={'status': 40006, 'detail': 'Time limited!'})
        else:
            self.create(
                target=mobile,
                times=1,
                verified_code=code
            )

        Notification.objects.sms_verified_code(mobile, code)

    # 随机生成6位数字
    def generate_code(self):
        import random

        return str(random.randint(100000, 999999))

    @staticmethod
    def send_verified_code(mobile, code):
        sender = '8822040111253'
        mobile = mobile
        TEMPLATE_ID = '7eff9574a9084d278e550ca44a5e5d9f'
        templateParas = '[' + code + ']'
        # Sms.send(sender, mobile, TEMPLATE_ID, templateParas)
        mobile = mobile
        templateCode = 'SMS_484130201'
        templateParam = "{\"code\":" + code + "}"
        templateParam = {
            'code': code
        }
        AliSms.send(mobile, templateCode, templateParam)

    # 检验验证码是否有效
    def is_valid_code(self, mobile, sms):
        verified_code = self.filter(
            target=mobile, verified_code=sms, used=False)
        if not verified_code.exists():
            return False
        now = datetime.now()
        verified_code = verified_code.last()
        period_seconds = (now - verified_code.created_at).seconds
        # 验证码是否超时
        if period_seconds > 300:
            return False

        # 通过验证
        return verified_code


class VerifiedCode(models.Model):
    """
      验证码定义
    """
    # 发送的目标，手机号码、email等
    target = models.CharField(max_length=255, db_index=True)
    # 验证码
    verified_code = models.CharField(max_length=20)
    # 累计发送次数
    times = models.IntegerField()
    # 验证码是否已经被使用
    used = models.BooleanField(default=False)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'verified_codes'

    objects = VerifiedCodeManager()
