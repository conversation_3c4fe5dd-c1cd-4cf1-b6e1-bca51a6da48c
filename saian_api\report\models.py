import datetime
from math import trunc

from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.db import models
from django.forms import IntegerField
from django.utils import translation
from kombu.exceptions import EncodeError

from saian_api.devdefine.models import AttributePrototype
from saian_api.utils import report

# Create your models here.


class DeviceMinuteStatManager(models.Manager):
    def do_stats(self, device, ap, da):
        now = da.updated_at
        value = da.value
        dmss = self.filter(device_id=device.id, identifier=ap.identifier).order_by('-created_at')
        # 若当前分钟没有统计记录
        if not dmss.exists() or (dmss.exists() and (now.strftime('%Y%m%d%H%M') > dmss.first().created_at.strftime('%Y%m%d%H%M'))):
            if ap.is_cum:
                # 累计值
                self.cum_val_dms(device, ap, value, now)
            else:
                self.create_cus(device, ap, value, now)

    def create_cus(self, device, ap, value, now):
        dms = self.create(
            device_id=device.id,
            mac=device.mac,
            identifier=ap.identifier,
            avg=value,
            created_at=now.strftime('%Y-%m-%d %H:%M:00')
        )
        dms.save()

    def cum_val_dms(self, device, ap, value, now):
        result = 0.0
        identifier = ap.identifier

        dmss = self.filter(device_id=device.id, identifier=identifier).order_by('-created_at')
        if dmss.exists():
            dms = dmss.first()
            # 当前数据上报时间和最后一条分钟统计记录的分钟间隔数
            interval = (now.replace(second=0) - dms.created_at).seconds / 60
            # 若当前分钟已有数据则不作记录
            if interval == 0:
                return
            # 小数点位
            fixed = len(ap.pre_cision.split('.')[1]) if ap.pre_cision and '.' in ap.pre_cision else 3
            # 平均每分钟增加的数值
            avg = round((float(value) - float(dms.avg)) / interval, fixed)

            for i in range(1, int(interval) + 1):
                created_at = dms.created_at + datetime.timedelta(minutes=i)
                result = value if i == interval else float(dms.avg) + i * avg
                # 创建记录
                self.create_cus(device, ap, round(float(result), fixed), created_at)
        else:
            # 若是第一条统计数据，则只需记录当前分钟
            result = value
            self.create_cus(device, ap, result, now)

    # 查询数据并按照报表格式返回
    def get_report_stat(self, device, identifier, from_at, till_at, interval):
        data = []
        five_multiples_under_60 = [0] + [i for i in range(5, 60, interval)]
        min_stats = self.filter(
            device_id=device.id, created_at__range=[from_at, till_at], identifier=identifier
        ).filter(created_at__minute__in=five_multiples_under_60).order_by('created_at')
        for stat in min_stats:
            item = {
                'time': datetime.datetime.strftime(stat.created_at, '%Y-%m-%d %H:%M:%S'),
                'value': stat.avg
            }
            data.append(item)

        return data


class DeviceMinuteStat(models.Model):
    """
    设备分钟统计
    """
    # 所属设备
    device_id = models.BigIntegerField(db_index=True)
    # mac
    mac = models.CharField(max_length=255, db_index=True)
    # 参数标识
    identifier = models.CharField(max_length=255)
    # 每分钟的第一条数据，如无上报，则取当前统计时刻往前3个数据进行平均处理
    avg = models.CharField(max_length=64)

    # 数据创建时间
    created_at = models.DateTimeField()
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'device_minute_stats'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['device_id', 'identifier']),
            models.Index(fields=['mac', 'identifier']),
            models.Index(fields=['device_id', 'created_at']),
            models.Index(fields=['mac', 'created_at']),
            models.Index(fields=['device_id', 'identifier', 'created_at']),
            models.Index(fields=['mac', 'identifier', 'created_at'])
        ]

    objects = DeviceMinuteStatManager()


class DeviceHourlyStatManager(models.Manager):
    def create_cus(self, device, ap, value, db, min='--', max='--', now=None):
        if now is None:
            now = datetime.datetime.now()
        created_at = (now - datetime.timedelta(hours=1)).strftime('%Y-%m-%d %H:59:59')
        exists = self.using(db).filter(device_id=device.id, identifier=ap.identifier, created_at=created_at).exists()
        if not exists:
            dhs = self.using(db).create(
                device_id=device.id,
                mac=device.mac,
                identifier=ap.identifier,
                avg=value,
                min=min,
                max=max,
                created_at=created_at
            )
            dhs.save(using=db)

    """
      由定时任务执行，每小时的10分执行一次
    """

    def do_stat(self):
        from saian_api.device.models import Device
        from saian_api.utils.utils import CeleryTaskUtils

        projects = CeleryTaskUtils.get_all_project()

        now = datetime.datetime.now()
        for project in projects:
            print(f"项目设备小时统计: {project['project_id']}-{project['project'].name}")
            db = project['db_name']
            report_cfgs = ReportConfigurer.objects.using(db).filter(target_type__model='DevicePrototype')
            if report_cfgs and len(report_cfgs) > 0:
                for cfg in report_cfgs:
                    devices = Device.objects.using(db).filter(device_prototype_id=cfg.target_id)
                    if devices:
                        for device in devices:
                            ap = AttributePrototype.objects.get_by_idf(device, cfg.identifier, db)
                            if ap is not None:
                                # 计算值
                                if ap.formula:
                                    if ap.is_cum:
                                        # 计算值 + 累计值
                                        # 通过公式计算的参数，值是累计值（如：表读数）
                                        self.calc_cum_val_stat(device, ap, db, now)
                                    else:
                                        # 计算值 + 非累计值
                                        # 通过公式计算的值，值是非累计
                                        self.calc_val_stat(device, ap, db, now)
                                else:
                                    if ap.is_cum:
                                        # 非计算值 + 累计值
                                        self.calc_cum_val_stat(device, ap, db, now)
                                    else:
                                        # 非计算值 + 非累计值
                                        self.val_stat(device, ap, db, now)
            print(f"项目设备小时统计任务完成: {project['project_id']}-{project['project'].name}")

    """
      计算值 + 累计值
    """

    def calc_cum_val_stat(self, device, ap, db, now=None):
        from saian_api.device.models import ParamRecord

        result = 0.0
        # 查询上一个小时的读数
        if now is None:
            now = datetime.datetime.now()
        one_hour_ago = now - datetime.timedelta(hours=1)
        pre_hour = one_hour_ago.strftime('%Y-%m-%d %H:05:00')
        pre_reading = ParamRecord.objects.get_by_hour(pre_hour, device, ap.identifier, db)
        current_hour = now.strftime('%Y-%m-%d %H:05:00')
        current_read = ParamRecord.objects.get_by_hour(current_hour, device, ap.identifier, db)

        if pre_reading is not None and current_read is not None:
            # 一院读数出现空字符串
            result = round(float(current_read.value if current_read.value != '' else 0), 2) - round(
                float(pre_reading.value if pre_reading.value != '' else 0), 2)
            # 小于0时，可能是换表，需要重新计算
            # 算法：上个小时最大的读数减去上个小时的读数 + 当前小时的读数
            if result < 0:
                beginning_of_hour = one_hour_ago.strftime('%Y-%m-%d %H:00:00')
                end_of_hour = one_hour_ago.strftime('%Y-%m-%d %H:59:59')
                records = ParamRecord.objects.using(db).filter(device_id=device.id, identifier=ap.identifier,
                                                               created_at__range=(beginning_of_hour, end_of_hour))

                if records and len(records) > 0:
                    values = [round(float(record.value), 2) for record in records]
                    result = max(values) - float(pre_reading.value) + float(current_read.value)

        # 创建记录
        self.create_cus(device, ap, result, db, now=now)

    """
      计算值 + 非累计值统计
      算法：查询上个小时的所有计算值集合，然后计算平均值
    """

    def calc_val_stat(self, device, ap, db, now=None):
        from saian_api.device.models import ParamRecord

        if now is None:
            now = datetime.datetime.now()
        one_hour_ago = now - datetime.timedelta(hours=1)
        beginning_of_hour = one_hour_ago.strftime('%Y-%m-%d %H:00:00')
        end_of_hour = one_hour_ago.strftime('%Y-%m-%d %H:59:59')
        records = ParamRecord.objects.using(db).filter(device_id=device.id, identifier=ap.identifier,
                                                       created_at__range=(beginning_of_hour, end_of_hour))

        if records and len(records) > 0:
            values = [float(record.value) for record in records]
            avg_value = 0 if len(values) == 0 else sum(values) / len(values)
            self.create_cus(device, ap, avg_value, max=max(values), min=min(values), db=db, now=now)

    """
      统计数值（非计算值、非累计值）类数据
    """

    def val_stat(self, device, ap, db, now=None):
        from saian_api.device.models import DeviceEvent

        if now is None:
            now = datetime.datetime.now()
        last_hour = now - datetime.timedelta(hours=1)
        beginning_of_hour = last_hour.strftime('%Y-%m-%d %H:00:00')
        end_of_hour = last_hour.strftime('%Y-%m-%d %H:59:59')
        records = (DeviceEvent.objects.using(db)
                   .filter(device_id=device.id, created_at__range=(beginning_of_hour, end_of_hour)))

        values = []

        if records and len(records) > 0:
            for record in records:
                try:
                    import json
                    data = json.loads(record.data)
                    if ap.identifier in data:
                        values.append(float(data[ap.identifier]))
                except EncodeError:
                    continue
                except ValueError:
                    continue
            if len(values) > 0:
                avg_value = 0 if len(values) == 0 else sum(values) / len(values)
                self.create_cus(device, ap, avg_value, db=db, max=max(values), min=min(values), now=now)

    # 查询数据并按照报表格式返回
    def get_report_stat(self, device, identifier, from_at, till_at):
        data = []
        hour_stats = self.filter(device_id=device.id, created_at__range=[from_at, till_at], identifier=identifier).order_by('created_at')
        for stat in hour_stats:
            item = {
                'time': datetime.datetime.strftime(stat.created_at, '%Y-%m-%d %H:%M:%S'),
                'value': round(float(stat.avg), 2),
                'min': round(float(stat.min), 2) if stat.min != '--' else 0,
                'max': round(float(stat.max), 2) if stat.max != '--' else 0,
            }
            data.append(item)

        return data


"""
  设备小时统计
"""


class DeviceHourlyStat(models.Model):
    # 所属设备
    device_id = models.BigIntegerField(db_index=True)
    # mac
    mac = models.CharField(max_length=255, db_index=True)
    # 参数标识
    identifier = models.CharField(max_length=255)
    # 平均值
    avg = models.CharField(max_length=64)
    # 最小值
    min = models.CharField(max_length=64)
    # 最小值
    max = models.CharField(max_length=64)

    # 数据创建时间
    created_at = models.DateTimeField()
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'device_hourly_stats'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['device_id', 'identifier']),
            models.Index(fields=['mac', 'identifier']),
            models.Index(fields=['device_id', 'created_at']),
            models.Index(fields=['mac', 'created_at']),
            models.Index(fields=['device_id', 'identifier', 'created_at']),
            models.Index(fields=['mac', 'identifier', 'created_at'])
        ]

    objects = DeviceHourlyStatManager()


class DeviceDailyStatManager(models.Manager):
    def create_cus(self, device, value, ap, db, min='--', max='--'):
        created_at = (datetime.datetime.now() - datetime.timedelta(days=1)).strftime('%Y-%m-%d 23:59:59')
        exists = self.using(db).filter(device_id=device.id, identifier=ap.identifier, created_at=created_at).exists()
        if not exists:
            dhs = self.using(db).create(
                device_id=device.id,
                mac=device.mac,
                identifier=ap.identifier,
                avg=value,
                min=min,
                max=max,
                created_at=created_at
            )
            dhs.save(using=db)

    """
      由定时任务执行，每天凌晨01点后分执行一次
    """

    def do_stat(self):
        yesterday = datetime.datetime.now() - datetime.timedelta(days=1)
        beginning_of_yesterday = yesterday.strftime('%Y-%m-%d 00:00:00')
        end_of_yesterday = yesterday.strftime('%Y-%m-%d 23:59:59')
        report.do_stat(self, DeviceHourlyStat.objects, beginning_of_yesterday, end_of_yesterday)

    # 查询数据并按照报表格式返回
    def get_report_stat(self, device, identifier, from_at, till_at):
        data = []
        daily_stats = self.filter(device_id=device.id, created_at__range=[from_at, till_at], identifier=identifier).order_by('created_at')
        for stat in daily_stats:
            item = {
                'time': datetime.datetime.strftime(stat.created_at, '%Y-%m-%d %H:%M:%S'),
                'value': round(float(stat.avg), 2) if stat.avg != '--' else 0,
                'min': round(float(stat.min), 2) if stat.min != '--' else 0,
                'max': round(float(stat.max), 2) if stat.max != '--' else 0,
            }
            data.append(item)

        return data


"""
  设备日统计
"""


class DeviceDailyStat(models.Model):
    # 所属设备
    device_id = models.BigIntegerField(db_index=True)
    # mac
    mac = models.CharField(max_length=255, db_index=True)
    # 参数标识
    identifier = models.CharField(max_length=255)
    # 平均值
    avg = models.CharField(max_length=64)
    # 最小值
    min = models.CharField(max_length=64)
    # 最小值
    max = models.CharField(max_length=64)

    # 数据创建时间
    created_at = models.DateTimeField()
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'device_daily_stats'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['device_id', 'identifier']),
            models.Index(fields=['mac', 'identifier']),
            models.Index(fields=['device_id', 'created_at']),
            models.Index(fields=['mac', 'created_at']),
            models.Index(fields=['device_id', 'identifier', 'created_at']),
            models.Index(fields=['mac', 'identifier', 'created_at'])
        ]

    objects = DeviceDailyStatManager()


class RoomHourlyStatManager(models.Manager):
    def get_report_stat(self, room, identifier, from_at, till_at):
        data = []
        hourly_stats = self.filter(
            active_room_id=room.id,
            created_at__range=[from_at, till_at],
            identifier=identifier
        ).order_by('created_at')

        for stat in hourly_stats:
            item = {
                'time': datetime.datetime.strftime(stat.created_at, '%Y-%m-%d %H:%M:%S'),
                'value': round(float(stat.avg), 2),
                'min': round(float(stat.min), 2) if stat.min != '--' else 0,
                'max': round(float(stat.max), 2) if stat.max != '--' else 0,
            }
            data.append(item)

        return data


"""
  房间小时统计
"""


class RoomHourlyStat(models.Model):
    # 所属房间
    active_room_id = models.BigIntegerField(db_index=True)
    # 房间编号
    room_no = models.CharField(max_length=255, db_index=True)
    # 参数标识
    identifier = models.CharField(max_length=255)
    # 平均值
    avg = models.CharField(max_length=64)
    # 最小值
    min = models.CharField(max_length=64)
    # 最小值
    max = models.CharField(max_length=64)

    # 数据创建时间
    created_at = models.DateTimeField()
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'room_hourly_stats'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['active_room_id', 'identifier']),
            models.Index(fields=['room_no', 'identifier']),
            models.Index(fields=['active_room_id', 'created_at']),
            models.Index(fields=['room_no', 'created_at']),
            models.Index(fields=['active_room_id', 'identifier', 'created_at']),
            models.Index(fields=['room_no', 'identifier', 'created_at'])
        ]

    objects = RoomHourlyStatManager()


class RoomDailyStatManager(models.Manager):
    def get_report_stat(self, room, identifier, from_at, till_at):
        data = []
        daily_stats = self.filter(
            active_room_id=room.id,
            created_at__range=[from_at, till_at],
            identifier=identifier
        ).order_by('created_at')

        for stat in daily_stats:
            item = {
                'time': datetime.datetime.strftime(stat.created_at, '%Y-%m-%d %H:%M:%S'),
                'value': round(float(stat.avg), 2),
                'min': round(float(stat.min), 2) if stat.min != '--' else 0,
                'max': round(float(stat.max), 2) if stat.max != '--' else 0,
            }
            data.append(item)

        return data


"""
  房间日统计
"""


class RoomDailyStat(models.Model):
    # 所属房间
    active_room_id = models.BigIntegerField(db_index=True)
    # mac
    room_no = models.CharField(max_length=255, db_index=True)
    # 参数标识
    identifier = models.CharField(max_length=255)
    # 平均值
    avg = models.CharField(max_length=64)
    # 最小值
    min = models.CharField(max_length=64)
    # 最小值
    max = models.CharField(max_length=64)

    # 数据创建时间
    created_at = models.DateTimeField()
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'room_daily_stats'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['active_room_id', 'identifier']),
            models.Index(fields=['room_no', 'identifier']),
            models.Index(fields=['active_room_id', 'created_at']),
            models.Index(fields=['room_no', 'created_at']),
            models.Index(fields=['active_room_id', 'identifier', 'created_at']),
            models.Index(fields=['room_no', 'identifier', 'created_at'])
        ]

    objects = RoomDailyStatManager()


class DeviceMontylyStatManager(models.Manager):
    def create_cus(self, device, value, ap, db, min='--', max='--'):
        # end_time - 上个月结束时间
        begin_time, end_time = report.last_month_time_range()
        exists = self.using(db).filter(device_id=device.id, identifier=ap.identifier, created_at=end_time).exists()
        if not exists:
            dhs = self.using(db).create(
                device_id=device.id,
                mac=device.mac,
                identifier=ap.identifier,
                avg=value,
                min=min,
                max=max,
                created_at=end_time
            )
            dhs.save(using=db)

    """
      由定时任务执行，每月第1天凌晨02点后分执行一次
    """

    def do_stat(self):
        begin_time, end_time = report.last_month_time_range()
        report.do_stat(self, DeviceDailyStat.objects, begin_time, end_time)

    def get_report_stat(self, device, identifier, from_at, till_at):
        # 查询数据并按照报表格式返回
        data = []
        monthly_stats = self.filter(device_id=device.id, created_at__range=[from_at, till_at], identifier=identifier).order_by('created_at')
        for stat in monthly_stats:
            item = {
                'time': datetime.datetime.strftime(stat.created_at, '%Y-%m-%d %H:%M:%S'),
                'value': round(float(stat.avg), 2) if stat.avg != '--' else 0,
                'min': round(float(stat.min), 2) if stat.min != '--' else 0,
                'max': round(float(stat.max), 2) if stat.max != '--' else 0,
            }
            data.append(item)

        return data


"""
  设备月统计
"""


class DeviceMonthlyStat(models.Model):
    # 所属设备
    device_id = models.BigIntegerField(db_index=True)
    # mac
    mac = models.CharField(max_length=255, db_index=True)
    # 参数标识
    identifier = models.CharField(max_length=255)
    # 平均值
    avg = models.CharField(max_length=64)
    # 最小值
    min = models.CharField(max_length=64)
    # 最小值
    max = models.CharField(max_length=64)

    # 数据创建时间
    created_at = models.DateTimeField()
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'device_monthly_stats'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['device_id', 'identifier']),
            models.Index(fields=['mac', 'identifier']),
            models.Index(fields=['device_id', 'created_at']),
            models.Index(fields=['mac', 'created_at']),
            models.Index(fields=['device_id', 'identifier', 'created_at']),
            models.Index(fields=['mac', 'identifier', 'created_at'])
        ]

    objects = DeviceMontylyStatManager()


class DeviceYearlyStatManager(models.Manager):
    def create_cus(self, device, value, ap, db, min='--', max='--'):
        # end_time - 上个月结束时间
        begin_time, end_time = report.last_year_time_range()
        exists = self.using(db).filter(device_id=device.id, identifier=ap.identifier, created_at=end_time).exists()
        if not exists:
            dhs = self.using(db).create(
                device_id=device.id,
                mac=device.mac,
                identifier=ap.identifier,
                avg=value,
                min=min,
                max=max,
                created_at=end_time
            )
            dhs.save(using=db)

    """
      由定时任务执行，每月第1天凌晨02点后分执行一次
    """

    def do_stat(self):
        begin_time, end_time = report.last_year_time_range()
        report.do_stat(self, DeviceMonthlyStat.objects, begin_time, end_time)


"""
  设备年统计
"""


class DeviceYearlyStat(models.Model):
    # 所属设备
    device_id = models.BigIntegerField(db_index=True)
    # mac
    mac = models.CharField(max_length=255, db_index=True)
    # 参数标识
    identifier = models.CharField(max_length=255)
    # 平均值
    avg = models.CharField(max_length=64)
    # 最小值
    min = models.CharField(max_length=64)
    # 最小值
    max = models.CharField(max_length=64)

    # 数据创建时间
    created_at = models.DateTimeField()
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'device_yearly_stats'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['device_id', 'identifier']),
            models.Index(fields=['mac', 'identifier']),
            models.Index(fields=['device_id', 'created_at']),
            models.Index(fields=['mac', 'created_at']),
            models.Index(fields=['device_id', 'identifier', 'created_at']),
            models.Index(fields=['mac', 'identifier', 'created_at'])
        ]

    objects = DeviceYearlyStatManager()


"""
  房间月统计
"""


class RoomMonthlyStat(models.Model):
    # 所属房间
    active_room_id = models.BigIntegerField(db_index=True)
    # mac
    room_no = models.CharField(max_length=255, db_index=True)
    # 参数标识
    identifier = models.CharField(max_length=255)
    # 平均值
    avg = models.CharField(max_length=64)
    # 最小值
    min = models.CharField(max_length=64)
    # 最小值
    max = models.CharField(max_length=64)

    # 数据创建时间
    created_at = models.DateTimeField()
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'room_monthly_stats'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['active_room_id', 'identifier']),
            models.Index(fields=['room_no', 'identifier']),
            models.Index(fields=['active_room_id', 'created_at']),
            models.Index(fields=['room_no', 'created_at']),
            models.Index(fields=['active_room_id', 'identifier', 'created_at']),
            models.Index(fields=['room_no', 'identifier', 'created_at'])
        ]


"""
  房间年统计
"""


class RoomYearlyStat(models.Model):
    # 所属房间
    active_room_id = models.BigIntegerField(db_index=True)
    # mac
    room_no = models.CharField(max_length=255, db_index=True)
    # 参数标识
    identifier = models.CharField(max_length=255)
    # 平均值
    avg = models.CharField(max_length=64)
    # 最小值
    min = models.CharField(max_length=64)
    # 最小值
    max = models.CharField(max_length=64)

    # 数据创建时间
    created_at = models.DateTimeField()
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'room_yearly_stats'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['active_room_id', 'identifier']),
            models.Index(fields=['room_no', 'identifier']),
            models.Index(fields=['active_room_id', 'created_at']),
            models.Index(fields=['room_no', 'created_at']),
            models.Index(fields=['active_room_id', 'identifier', 'created_at']),
            models.Index(fields=['room_no', 'identifier', 'created_at'])
        ]


class ReportConfigurerManager(models.Manager):
    @classmethod
    def get_report_label(cls, device, rc, ap_label=None):
        nick_name = device.nick_name

        if rc.device_idx is not None and rc.device_idx > 0:
            from saian_api.device.models import DeviceAttribute
            from saian_api.utils.tools import get_prefix

            prefix = get_prefix(rc.identifier)
            # NickName 有两种组成形式 {prefix}_{idx}_Nickname 和 {prefix}_NickName_{idx}, 第一种找不到再去找第二种
            # nick_name_attr = DeviceAttribute.objects.get_by_idf(device, f'{prefix}_{rc.device_idx}_NickName')
            nick_name_attr = DeviceAttribute.objects.query_object_by_idf(device, f'{prefix}_{rc.device_idx}_NickName')

            if nick_name_attr is None:
                # nick_name_attr = DeviceAttribute.objects.get_by_idf(device, f'{prefix}_NickName_{rc.device_idx}')
                nick_name_attr = DeviceAttribute.objects.query_object_by_idf(device, f'{prefix}_NickName_{rc.device_idx}')

            if nick_name_attr is not None:
                nick_name = nick_name_attr.value
                if nick_name is None:
                    nick_name = f'{rc.device_idx}#{prefix}'

        if ap_label is not None:
            label = f'{nick_name}{ap_label}' if not ap_label.startswith(nick_name) else ap_label
        else:
            label = rc.name

        return label


class ReportConfigurer(models.Model):
    """
      报表配置
    """
    # 报表目标，可能是设备类型，或房间
    target_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    target_id = models.PositiveBigIntegerField()
    target = GenericForeignKey('target_type', 'target_id')

    # 报表名字
    name = models.CharField(max_length=64, null=True)
    # 统计的设备属性标识
    identifier = models.CharField(max_length=255)
    # 单位
    unit = models.CharField(max_length=20)
    # 参考点
    refer_point = models.CharField(max_length=20)
    # 报表类型，10-折线图，20-饼状图
    chart_type = models.IntegerField()
    # 设备索引id
    device_idx = models.IntegerField()
    # 排序字段
    seq = models.IntegerField()
    # 是否开启分钟统计
    do_minstat = models.BooleanField(default=False)

    class Meta:
        db_table = 'report_configurers'

    objects = ReportConfigurerManager()


class ManualRecord(models.Model):
    """人工录入数据记录表"""
    # 内容可能是对应的是web_charts或user_charts
    # content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    # content_id = models.PositiveBigIntegerField()
    # content = GenericForeignKey('content_type', 'content_id')

    # 名称
    name = models.CharField(max_length=255)
    # 数据粒度，10-实时，20-小时，30-日，40-周，50-月
    gran = models.IntegerField(null=True)
    # 数据的日期
    data_time = models.DateTimeField(null=True)

    # 数据类型，10-折线图或柱状图，20-饼图，30-表格，40-单项数据
    data_type = models.IntegerField(default=10)
    # 数据，json字符串
    data = models.TextField()
    # 单位
    unit = models.CharField(max_length=255, null=True)
    # 用途, 10-图表，20-业务配置
    use_for = models.IntegerField(default=10)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'manual_records'


class UserChart(models.Model):
    """用户图表"""
    web_user = models.ForeignKey('user.WebUser', on_delete=models.CASCADE)
    name = models.CharField(max_length=255)
    uni_name = models.CharField(max_length=255)

    # 图表类型，10-定制图表，20-折线或柱状图，30-饼图，40-表格，50-单项数据
    chart_type = models.IntegerField()

    # 图表的数据来源，10 - 定制数据，20-设备原始数据，30-报表配置数据，40-能耗录入数据，50-基准能耗数据，
    # 60-人工录入数据，70-设备实时数据，80-维度统计数据，90-维度实时数据，100-天气数据，110-NA
    data_src = models.IntegerField()

    # 人工录入数据的名称
    data_name = models.CharField(max_length=255, null=True)

    # 数据粒度，10-实时，20-小时，30-日，40-周，50-月
    gran = models.IntegerField(null=True)

    # 查询数据的默认时间，如：近1小时，近6小时，近24小时，近7天，2023-01-01 ~ 2023-05-01等，自定义查询时间格式为：开始时间 ~ 结束时间
    query_time = models.CharField(max_length=255, default='近24小时')

    # 数据参数，为json格式，根据数据来源不同而不同，如：设备原始数据：[{"id": 1, "name": "现场温度"}]，人工录入数据：[{"name": "客房入住率"}]，天气数据：[{"name": "室外温度"}]
    data_params = models.TextField(null=True)

    # 样式配置数据，json格式，内容类似为：{"font": {"size": 12, "family": xxx}, "backgroud": {"color': red}}
    style_params = models.TextField(null=True)

    # 缩略图id或地址
    thumb = models.CharField(max_length=255, null=True)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'user_charts'

    def destroy_cus(self):
        content_type = ContentType.objects.get_for_model(self)
        uacs = UserAnalysisCharts.objects.filter(content_id=self.id, content_type=content_type)

        if uacs:
            uacs.delete()

        self.delete()


class UserAnalyse(models.Model):
    """用户图表组合"""
    web_user = models.ForeignKey('user.WebUser', on_delete=models.CASCADE)

    # 组合名称
    name = models.CharField(max_length=255)

    # 缩略图id或地址
    thumb = models.CharField(max_length=255, null=True)

    class Meta:
        db_table = 'user_analyses'


class UserAnalysisCharts(models.Model):
    """用户图表组合图表关系表"""
    user_analyse = models.ForeignKey(UserAnalyse, on_delete=models.CASCADE)

    # 内容可能是对应的是web_charts或user_charts
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    content_id = models.PositiveBigIntegerField()
    content = GenericForeignKey('content_type', 'content_id')

    # 排序字段
    seq = models.IntegerField(default=0)

    class Meta:
        db_table = 'user_analysis_charts'


class DlTask(models.Model):
    """导出文件记录"""
    # 创建任务的用户
    web_user_id = models.BigIntegerField(null=True)
    # 对应项目
    project_id = models.BigIntegerField(null=True)
    # 对应的设备
    device_id = models.BigIntegerField(null=True)
    # 数据的日期
    data_at = models.DateTimeField(null=True)
    # 任务状态。10-待执行，20-执行中，30-完成，40-失败
    status = models.IntegerField()
    # 名字
    name = models.CharField(max_length=255, null=True)
    # 文件路径
    file_path = models.CharField(max_length=255, null=True)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'dl_tasks'
        ordering = ['-created_at']

class StatReport(models.Model):
    """分析报告记录"""
    # 内容的标题，在报告中展示
    title = models.CharField(max_length=255)
    # 内容名称，主要是内部使用
    content_name = models.CharField(max_length=255)
    # 统计周期，10-日，20-周，30-月，40-年
    periods = models.IntegerField()
    # 报告数据的时间
    report_dt = models.DateTimeField()

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'stat_reports'
        ordering = ['report_dt']

class SettleParty(models.Model):
    """结算对象表"""
    # 对象名称
    name = models.CharField(max_length=255)
    # 能耗仪表的id
    ec_meter_id = models.IntegerField(null=True)
    # 结算方式，10-单台设备，20-平均值，多种方式以逗号隔开，如：10, 20
    settle_ways = models.CharField(max_length=255, null=True)
    # 设备参数过滤配置
    device_filter = models.CharField(max_length=255, null=True)
    # 运行时间的计算方式
    rt_stat = models.CharField(max_length=255)
    # 能耗计量的来源
    ec_src = models.CharField(max_length=255)
    # 可支持的统计周期，多个以逗号隔开，比如：月, 季度
    periods = models.CharField(max_length=255)
    # 记录创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 记录最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'settle_parties'

class SettleRefer(models.Model):
    '''结算基准表'''
    # 结算对象的id
    settle_party_id = models.IntegerField()
    # 结算参数名称，当结算方式有平均值时为必填
    param_name = models.CharField(max_length=255, null=True)
    # 当结算方式有单个设备时为必填，此idf为设备属性的idf，即结算参数为设备的属性
    param_idf = models.CharField(max_length=255, null=True)
    # 结算参数值，当结算方式有平均值时为必填
    param_value = models.CharField(max_length=255, null=True)
    # 结算参数的单位，当结算方式有平均值时为必填
    param_unit = models.CharField(max_length=255, null=True)
    # 结算基准参数对应设备工况参数的配置，json格式
    run_params = models.TextField()
    # 记录创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 记录最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'settle_refers'

class SettleRecord(models.Model):
    '''汇总结算记录表'''
    # 结算对象的id
    settle_party_id = models.IntegerField()
    # 统计周期，统计的周期，10-小时，20-日，30-周，40-月，50-季度
    period = models.IntegerField()
    # 换算的能耗值，默认单位：kWh
    conver_ec = models.CharField(max_length=255)
    # 实际计量的能耗值，默认单位：kWh
    actual_ec = models.CharField(max_length=255)
    # 节能率，单位：%
    es_rate = models.CharField(max_length=255)
    # 设备总运行时间，单位：分钟
    run_time = models.IntegerField()
    # 结算期时间
    settle_dt = models.DateTimeField()
    # 记录创建时间
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'settle_records'

class DevstlRecord(models.Model):
    '''设备(终端)结算记录表'''
    # 终端id
    terminal_id = models.IntegerField()
    # 结算对象的id
    settle_party_id = models.IntegerField()
    # 结算期的用电量
    power_cons = models.CharField(max_length=255, null=True)
    # 结算期的节电量
    es_value = models.CharField(max_length=255, null=True)
    # 各工况下的运行时间，json格式，如：{"HSpeedRTime": 170, "MSpeedRTime": 80, "LSpeedRTime": 100}
    con_rt = models.CharField(max_length=255, null=True)
    # 统计周期，统计的周期，10-小时，20-日，30-周，40-月，50-季度
    period = models.IntegerField()
    # 结算期时间
    settle_dt = models.DateTimeField()
    # 记录创建时间
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'devstl_records'
