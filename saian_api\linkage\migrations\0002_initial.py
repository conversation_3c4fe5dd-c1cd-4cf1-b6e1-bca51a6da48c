# Generated by Django 3.2.8 on 2021-12-30 15:16

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('linkage', '0001_initial'),
        ('project', '0002_project_web_users'),
        ('contenttypes', '0002_remove_content_type_name'),
        ('user', '0001_initial'),
        ('devdefine', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='snpvar',
            name='project',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='project.project'),
        ),
        migrations.AddField(
            model_name='linkagevar',
            name='linkage_rule',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='linkage.linkagerule'),
        ),
        migrations.AddField(
            model_name='linkagetrigger',
            name='group_action',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='linkage.linkagerule'),
        ),
        migrations.AddField(
            model_name='linkagetarget',
            name='linkage_rule',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='linkage.linkagerule'),
        ),
        migrations.AddField(
            model_name='linkagetarget',
            name='target_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype'),
        ),
        migrations.AddField(
            model_name='linkagesnpvar',
            name='linkage_rule',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='linkage.linkagerule'),
        ),
        migrations.AddField(
            model_name='linkagesnpvar',
            name='snp_var',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='linkage.snpvar'),
        ),
        migrations.AddField(
            model_name='linkagerule',
            name='project',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='project.project'),
        ),
        migrations.AddField(
            model_name='linkagerule',
            name='web_user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='user.webuser'),
        ),
        migrations.AddField(
            model_name='linkageattribute',
            name='attribute_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype'),
        ),
        migrations.AddField(
            model_name='linkageattribute',
            name='linkage_target',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='linkage.linkagetarget'),
        ),
        migrations.AddField(
            model_name='crossattributeprototype',
            name='attribute_prototype',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='devdefine.attributeprototype'),
        ),
        migrations.AddField(
            model_name='crossattributeprototype',
            name='cross_attribute',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='linkage.crossattribute'),
        ),
        migrations.AddField(
            model_name='crossattribute',
            name='attribute_prototypes',
            field=models.ManyToManyField(through='linkage.CrossAttributePrototype', to='devdefine.AttributePrototype'),
        ),
        migrations.AddField(
            model_name='crossattribute',
            name='project',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='project.project'),
        ),
    ]
