"""
    项目天气小时统计
"""
import datetime
import logging
import traceback

from django.core.management import BaseCommand, CommandError

from saian_api.dashboard.models import ProjectWeather
from saian_api.project.models import Project
from saian_api.regions.models import Region
from saian_api.scheduledtask.utils import get_projects, set_global_db
from saian_api.utils.httpapi.weather import WeatherApi
import numpy as np

class Command(BaseCommand):
    help = "项目小时天气"

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目ID列表
        projects = get_projects(self, options)
        now = datetime.datetime.now()
        created_at = now.strftime('%Y-%m-%d %H:00:00')

        for project_id in projects:
            try:
                # 设置全局数据库
                set_global_db(project_id)
                project = Project.objects.get(pk=project_id)

                if project.admin_region_id:
                    region = Region.objects.get(pk=project.admin_region_id)
                    weather_result = WeatherApi.get_weather(region.weather_code)
                    temp = weather_result.get('tem', None)
                    humidity = weather_result.get('humidity', None)
                    if humidity is not None:
                        humidity = humidity.replace('%', '')
                    pressure = weather_result.get('pressure', None)
                    wea = weather_result.get('wea_day', None)
                    air = weather_result.get('air_level', None)
                    uv = weather_result.get('uvDescription', None)

                    # 如果温湿度，气压是None，就取上一条记录的数据
                    if temp is None or humidity is None or pressure is None:
                        last_weather = ProjectWeather.objects.filter(type='hr').order_by('-created_at').first()
                        if temp is None:
                            temp = last_weather.temp
                        if humidity is None:
                            humidity = last_weather.humidity
                        if pressure is None:
                            pressure = last_weather.pressure

                    try:
                        wetbulb_temp = WeatherApi.get_wetbulb_temp(float(temp), float(humidity))
                    except Exception as e:
                        logging.error(f'计算湿球温度出错。temp-{temp}, humidity-{humidity}, pressure-{pressure}, error-{e.__str__()}')
                        wetbulb_temp = None

                    self.stdout.write(f"{project.name} {created_at}小时温湿度 >>> temp: {temp}, humidity: {humidity}, wetbulb_temp: {wetbulb_temp}")
                    if not ProjectWeather.objects.filter(project_id=project_id, created_at=created_at, type='hr').exists():
                        ProjectWeather.objects.create(
                            project=project,
                            temp=float(temp),
                            min_temp=float(temp),
                            max_temp=float(temp),
                            humidity=float(humidity),
                            min_humidity=float(humidity),
                            max_humidity=float(humidity),
                            pressure=float(pressure),
                            wea=wea,
                            air=air,
                            uv=uv,
                            type='hr',
                            created_at=created_at,
                            wetbulb_temp=round(wetbulb_temp, 2)
                        )

            except CommandError:
                self.stderr.write(f"运行'项目天气小时统计'任务失败，项目ID：{project_id}, 命令参数不合法！", ending='\n')
                continue
            except Exception as e:
                self.stderr.write(f"运行'项目天气小时统计'任务失败，项目ID：{project_id}, error: {e.__str__()}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
                continue
