# Generated by Django 3.2.8 on 2023-04-07 16:48

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('device', '0011_dataroute'),
    ]

    operations = [
        migrations.CreateModel(
            name='DyAttribute',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('mac', models.Char<PERSON>ield(max_length=255)),
                ('identifier', models.Char<PERSON>ield(max_length=255)),
                ('value', models.CharField(max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('device', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='device.device')),
            ],
            options={
                'db_table': 'dy_attributes',
            },
        ),
    ]
