import datetime
from statistics import mean

from django.db import models

from saian_api.devdefine.models import DevicePrototype, AttributePrototype
from saian_api.device.models import Device, DeviceAttribute
from saian_api.project.models import Project

# Create your models here.
"""
  末端定义
"""
# TODO 添加 project 和 device_prototype 唯一约束
class AcTerminal(models.Model):
    # 所属项目
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    # 所属设备类型
    device_prototype = models.ForeignKey(DevicePrototype, on_delete=models.CASCADE)
    # 制冷量
    cooling_cap = models.CharField(max_length=255, null=True)
    # 负荷率
    load_rate = models.CharField(max_length=255, null=True)

    class Meta:
        db_table = 'ac_terminals'

class ActColdStatManager(models.Manager):
    # 计算末端供冷需求冷量
    def calc_stats(self, device):
        ac_terminals = AcTerminal.objects.filter(project__id=device.project_id).all()

        # 未配置在 ac_terminal 的设备事件不触发
        if device.device_prototype_id not in [act.device_prototype_id for act in ac_terminals]:
            return

        # 一分钟内不重复计算
        now = datetime.datetime.now()
        recently_record = ActColdStat.objects.first()
        if recently_record and (now - recently_record.created_at).total_seconds() < 60:
            return

        # 当前负荷
        current_load = 0
        # 基准负荷
        base_load = 0
        # 负荷率
        load_rate = 0

        for act in ac_terminals:

            devices = Device.objects.filter(device_prototype_id=act.device_prototype_id)
            on_devices = list(devices.filter(sw_on=True, online=True))
            if len(on_devices) == 0:
                continue

            cooling_cap = float(act.cooling_cap)

            # 温控器
            if '温控器' in act.device_prototype.uni_name:

                hs_count = 0
                ms_count = 0
                ls_count = 0

                # 盘管运行风速：identifier=FCUFanSpeed
                ap = AttributePrototype.objects.query_by_idf(on_devices[0], 'FCUFanSpeed')
                if ap is None:
                    continue
                for device_attr in DeviceAttribute.objects.query_multi_device_object(on_devices, ap.id):
                    if device_attr is None:
                        continue
                    if device_attr.value == '低速':
                        ls_count += 1
                    elif device_attr.value == '中速':
                        ms_count += 1
                    else:
                        hs_count += 1

                # 温控器默认负荷率：自动-100%， 高速-100%， 中速-75%， 低速-50%
                current_load += (ls_count * 0.5 + ms_count * 0.75 + hs_count * 1) * cooling_cap
                base_load += devices.count() * cooling_cap

            else:
                # 其他设备的负荷值取水阀开度返回 identifier end with 'ValveLiftBack'
                attrs = AttributePrototype.objects.filter(device_prototype_id=act.device_prototype_id, identifier__endswith='ValveLiftBack')
                if attrs.count() > 1:
                    # 如果设备是网关，存在多个负荷值，则取平均值
                    ap_ids = [attr.id for attr in attrs]
                    for device in on_devices:
                        values = []
                        for device_attr in DeviceAttribute.objects.query_object_list(device, ap_ids):
                            if device_attr is None:
                                continue
                            values.append(float(device_attr.value))
                        load = mean(values)
                        current_load += (load / 100) * cooling_cap

                else:
                    values = []
                    for attr in attrs:
                        for device_attr in DeviceAttribute.objects.query_multi_device_object(on_devices, attr.id):
                            if device_attr is None:
                                continue
                            values.append(float(device_attr.value))
                    load = sum(values)
                    current_load += (load / 100) * cooling_cap

                base_load += devices.count() * cooling_cap

        # 计算负荷率，保留两位
        base_load = round(base_load, 2)
        current_load = round(current_load, 2)
        load_rate = round(current_load / base_load, 2) if base_load != 0 else 0

        act_cold_stat = ActColdStat(base_load=base_load, current_load=current_load, load_rate=load_rate)
        act_cold_stat.project_id = device.project_id
        act_cold_stat.save()

"""
   末端用冷统计
"""
class ActColdStat(models.Model):
    # 所属项目
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    # 基准负荷
    base_load = models.CharField(max_length=255, null=True)
    # 当前负荷
    current_load = models.CharField(max_length=255, null=True)
    # 负荷率
    load_rate = models.CharField(max_length=255, null=True)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'act_cold_stats'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['project_id', 'created_at'])
        ]

    objects = ActColdStatManager()
