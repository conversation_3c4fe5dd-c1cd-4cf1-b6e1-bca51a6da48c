import logging
import os
import re

from django.conf import settings
from rest_framework import authentication
from rest_framework import exceptions

from saian_api.user.models import WebUser
from saian_api.utils.httpapi.user import UserApi
from saian_api.utils.httpapi.device import DeviceAPI

# 自定义鉴权，通过后把用户信息放入request.user中


class LegacyAuth(authentication.BaseAuthentication):
    def authenticate_header(self, request):
        """用户鉴权失败时，返回 401，和旧接口一样"""
        return 'WWW-Authenticate'

    def authenticate(self, request):
        auth = request.headers.get('Authorization', None)
        if not auth:
            logging.error(f'Authentication failed! {auth}')
            raise exceptions.AuthenticationFailed('Authentication failed!')

        # 匹配header中的Authentication，获取token，user id和项目id
        searchObj = re.search(r'Token token=(.*), identity=(.*), project=(.*)', auth, re.M | re.I)

        if searchObj:
            token = searchObj.group(1)
            user_id = searchObj.group(2)
            project_id = searchObj.group(3)

            # 根据项目是否本地部署来确定不同的鉴权方式
            is_local_deployment = settings.LOCAL_DEPLOYMENT
            if is_local_deployment:
                user_by_token = WebUser.objects.filter(authentication_token=token)
                if not user_by_token.exists():
                    raise exceptions.AuthenticationFailed('Token失效')
                web_user = user_by_token.last()
                if not (web_user.id == int(user_id)):
                    raise exceptions.AuthenticationFailed('Token错误')

                current_user = {
                    'id': web_user.id,
                    'avatar': web_user.avatar,
                    'name': web_user.name,
                    'status': web_user.status,
                    'token': token,
                    'project_id': project_id
                }
            else:
                # 调用用户平台接口查询用户信息
                data = UserApi.myInfo(auth, project_id)
                user = data['data']['user']
                current_user = {
                    'id': user['id'],
                    'avatar': user['avatar'],
                    'name': user['name'],
                    'status': user['status'],
                    'token': token,
                    'project_id': project_id
                }

            drf_debug = os.environ.get('DRF_DEBUG', False)
            if project_id is not None and request.method == 'GET' and drf_debug is False:
                DeviceAPI.live_update(project_id)
        else:
            logging.error(f'找不到用户！')
            raise exceptions.NotFound('找不到用户！')

        # 把current_user放到request中，方便可以在整个请求上文引用
        return (current_user, None)
