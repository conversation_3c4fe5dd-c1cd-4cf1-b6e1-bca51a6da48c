from rest_framework import serializers

from saian_api.devdefine.models import DeviceProtocol, AddrSegment, AttributePrototype, DevicePrototypeRatio, DevicePrototype
from saian_api.devdefine.serializers import SimpleDevicePrototypeSerializer, SimpleDeviceTypeSerializer
from saian_api.device.models import Devi<PERSON><PERSON><PERSON>, Device
from saian_api.message.models import Message, UserMessage
from saian_api.report.models import ReportConfigurer
from saian_api.terminal.models import Terminal
from saian_api.utils.httpapi.image import ImageAPI
from saian_api.user.models import UserStat


class ProjectSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    name = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    project_type = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    intro = serializers.CharField(allow_null=True, allow_blank=True)
    address = serializers.CharField(allow_null=True, allow_blank=True)
    images = serializers.CharField(allow_null=True, allow_blank=True)
    regionid = serializers.CharField(allow_null=True, allow_blank=True)
    cooling_area = serializers.IntegerField(allow_null=True)
    electricity = serializers.IntegerField(allow_null=True)
    completed_at = serializers.CharField(allow_null=True, allow_blank=True)
    warranty = serializers.IntegerField(allow_null=True)
    contact_name = serializers.CharField(allow_null=True, allow_blank=True)
    contact_no = serializers.CharField(allow_null=True, allow_blank=True)
    contact_email = serializers.CharField(allow_null=True, allow_blank=True)
    director_name = serializers.CharField(allow_null=True, allow_blank=True)
    director_no = serializers.CharField(allow_null=True, allow_blank=True)
    director_email = serializers.CharField(allow_null=True, allow_blank=True)
    maintainer_name = serializers.CharField(allow_null=True, allow_blank=True)
    maintainer_no = serializers.CharField(allow_null=True, allow_blank=True)
    maintainer_email = serializers.CharField(allow_null=True, allow_blank=True)
    engineer_a_name = serializers.CharField(allow_null=True, allow_blank=True)
    engineer_a_no = serializers.CharField(allow_null=True, allow_blank=True)
    engineer_b_name = serializers.CharField(allow_null=True, allow_blank=True)
    engineer_b_no = serializers.CharField(allow_null=True, allow_blank=True)
    sales_agent = serializers.CharField(allow_null=True, allow_blank=True)
    building_owner = serializers.CharField(allow_null=True, allow_blank=True)
    building_user = serializers.CharField(allow_null=True, allow_blank=True)
    builder = serializers.CharField(allow_null=True, allow_blank=True)
    builder_contact_name = serializers.CharField(allow_null=True, allow_blank=True)
    builder_contact_no = serializers.CharField(allow_null=True, allow_blank=True)
    pmc = serializers.CharField(allow_null=True, allow_blank=True)
    pmc_contact_name = serializers.CharField(allow_null=True, allow_blank=True)
    pmc_contact_no = serializers.CharField(allow_null=True, allow_blank=True)
    admin_region_id = serializers.IntegerField(allow_null=True)
    hs_weight = serializers.IntegerField(allow_null=True)
    ms_weight = serializers.IntegerField(allow_null=True)
    ls_weight = serializers.IntegerField(allow_null=True)
    fan_time = serializers.IntegerField(allow_null=True)
    logo = serializers.CharField(allow_null=True, allow_blank=True)
    slogan = serializers.CharField(allow_null=True, allow_blank=True)
    en_temhum_sms = serializers.BooleanField(allow_null=True)
    en_gas_sms = serializers.BooleanField(allow_null=True)
    enable_web = serializers.BooleanField(allow_null=True)
    agent_id = serializers.IntegerField(allow_null=True)
    domain = serializers.CharField(allow_null=True, allow_blank=True)
    in_acc = serializers.BooleanField(allow_null=True)
    enable_ec = serializers.BooleanField(allow_null=True)
    en_ot_aircon = serializers.BooleanField(allow_null=True)
    settings = serializers.CharField(allow_null=True, allow_blank=True)


class ProjectChartSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    project_id = serializers.IntegerField()
    web_chart_id = serializers.IntegerField()
    num = serializers.IntegerField(allow_null=True)


class WebChartSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    name = serializers.CharField(allow_null=True, allow_blank=True)
    uni_name = serializers.CharField(allow_null=True, allow_blank=True)
    icon = serializers.CharField(allow_null=True, allow_blank=True)
    enabled = serializers.BooleanField(allow_null=True)

    def to_representation(self, instance):
        ret = super().to_representation(instance)

        images = ImageAPI.get_urls(request=self.context['request'], image_id=ret['icon'], size='originals')
        if images is not None and len(images) > 0:
            ret['icon'] = images[0]['image']

        return ret

class DeviceEventSerializer(serializers.ModelSerializer):
    class Meta:
        model = DeviceEvent
        fields = ['cmd', 'event_type', 'event_id', 'delivery_id', 'product_key', 'timestamp', 'ip', 'latitude',
                  'longitude', 'country', 'region', 'data', 'did', 'mac', 'device_id']

    def to_representation(self, instance):
        ret = super(DeviceEventSerializer, self).to_representation(instance)
        device = Device.objects.get(pk=ret['device_id'])
        ret['device'] = {
            'id': device.id,
            'nick_name': device.nick_name,
            'device_prototype': device.device_prototype.uni_name
        }
        return ret


class DeviceTypeSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    pid = serializers.IntegerField(allow_null=True, required=False)
    parent_id = serializers.IntegerField(allow_null=True, required=False)
    name = serializers.CharField(allow_null=True, allow_blank=True)
    icon = serializers.CharField(allow_null=True, allow_blank=True)
    uni_name = serializers.CharField(allow_null=True, allow_blank=True)
    is_default = serializers.BooleanField(allow_null=True)


class DevicePrototypeSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    device_type_id = serializers.IntegerField()
    pid = serializers.IntegerField(allow_null=True, required=False)
    parent_id = serializers.IntegerField(allow_null=True, required=False)
    name = serializers.CharField(allow_null=True, allow_blank=True)
    icons = serializers.CharField(allow_null=True, allow_blank=True)
    uni_name = serializers.CharField(allow_null=True, allow_blank=True)
    skip_config = serializers.BooleanField(allow_null=True)
    content = serializers.CharField(allow_null=True, allow_blank=True)
    m_name = serializers.CharField(allow_null=True, allow_blank=True)
    web_content = serializers.CharField(allow_null=True, allow_blank=True)
    prefix = serializers.CharField(allow_null=True, allow_blank=True)
    dashboard_attres = serializers.CharField(allow_null=True, allow_blank=True)
    detail_attres = serializers.CharField(allow_null=True, allow_blank=True)
    up_parser = serializers.CharField(allow_null=True, allow_blank=True)
    down_parser = serializers.CharField(allow_null=True, allow_blank=True)
    timeout_set = serializers.IntegerField(allow_null=True)


class AttributeTypeSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    name = serializers.CharField(allow_null=True, allow_blank=True)
    seq = serializers.IntegerField(allow_null=True)
    hidden = serializers.BooleanField(allow_null=True)
    uni_name = serializers.CharField(allow_null=True, allow_blank=True)
    in_add = serializers.BooleanField(allow_null=True)
    device_prototype_id = serializers.IntegerField()


class AttributePrototypeSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    name = serializers.CharField(allow_null=True, allow_blank=True)
    identifier = serializers.CharField(allow_null=True, allow_blank=True)
    remark = serializers.CharField(allow_null=True, allow_blank=True)
    read_only = serializers.BooleanField(required=False, allow_null=True)
    data_type = serializers.IntegerField(allow_null=True)
    pre_cision = serializers.CharField(allow_null=True, allow_blank=True)
    in_crement = serializers.CharField(allow_null=True, allow_blank=True)
    min_value = serializers.CharField(allow_null=True, allow_blank=True)
    max_value = serializers.CharField(allow_null=True, allow_blank=True)
    options = serializers.CharField(allow_null=True, allow_blank=True)
    length = serializers.IntegerField(allow_null=True)
    icon = serializers.CharField(allow_null=True, allow_blank=True)
    unit = serializers.CharField(allow_null=True, allow_blank=True)
    default_value = serializers.CharField(allow_null=True, allow_blank=True)
    seq = serializers.IntegerField(allow_null=True)
    is_key = serializers.BooleanField(allow_null=True)
    can_debug = serializers.BooleanField(allow_null=True)
    hidden = serializers.BooleanField(allow_null=True)
    in_add = serializers.BooleanField(allow_null=True)
    show_in_list = serializers.BooleanField(allow_null=True)
    formula = serializers.CharField(allow_null=True, allow_blank=True)
    is_cum = serializers.BooleanField(allow_null=True)
    device_prototype_id = serializers.IntegerField()
    attribute_type_id = serializers.IntegerField()
    label = serializers.CharField(allow_null=True, allow_blank=True)
    do_send = serializers.BooleanField(default=True)
    do_export = serializers.BooleanField(allow_null=True)
    send_immediate = serializers.BooleanField(allow_null=True)


class WebUserSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    unionid = serializers.CharField(allow_null=True, allow_blank=True)
    openid = serializers.CharField(allow_null=True, allow_blank=True)
    permissions = serializers.CharField(allow_null=True, allow_blank=True)
    avatar = serializers.CharField(allow_null=True, allow_blank=True)
    name = serializers.CharField(allow_null=True, allow_blank=True)
    username = serializers.CharField(allow_null=True, allow_blank=True)
    status = serializers.CharField(allow_null=True, allow_blank=True)
    is_super = serializers.BooleanField(allow_null=True)
    email = serializers.CharField(allow_null=True, allow_blank=True)
    mobile = serializers.CharField(allow_null=True, allow_blank=True)
    last_login = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    web_roles = serializers.ListSerializer(required=False, child=serializers.IntegerField())

class WebUserRoleSerializer(serializers.Serializer):
    web_user_id = serializers.IntegerField()
    web_role_id = serializers.IntegerField()


class WebRoleMenuSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    web_role_id = serializers.IntegerField()
    web_menu_id = serializers.IntegerField()


class WebRoleSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    name = serializers.CharField()
    en_name = serializers.CharField(allow_null=True, allow_blank=True)
    project_id = serializers.IntegerField()
    permissions = serializers.CharField(allow_null=True, allow_blank=True)


class UserProjectSerializer(serializers.Serializer):
    web_user_id = serializers.IntegerField()
    project_id = serializers.IntegerField()


class UserDeviceSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    unit_idx = serializers.IntegerField(allow_null=True)
    unit_prefix = serializers.CharField(allow_null=True, allow_blank=True)
    device_id = serializers.IntegerField()
    user_id = serializers.IntegerField()


class EcMeterSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    device_id = serializers.IntegerField(allow_null=True)
    name = serializers.CharField(allow_null=True, allow_blank=True)
    idx = serializers.IntegerField(allow_null=True)
    identifier = serializers.CharField(allow_null=True, allow_blank=True)
    enabled = serializers.BooleanField(allow_null=True)
    ec_type = serializers.IntegerField(allow_null=True)


class DeviceSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    project_id = serializers.IntegerField()
    device_type_id = serializers.IntegerField()
    device_prototype_id = serializers.IntegerField()
    nick_name = serializers.CharField(allow_null=True, allow_blank=True)
    mac = serializers.CharField(allow_null=True, allow_blank=True)
    did = serializers.CharField(allow_null=True, allow_blank=True)
    batch_no = serializers.CharField(allow_null=True, allow_blank=True)
    address = serializers.CharField(allow_null=True, allow_blank=True)
    status = serializers.IntegerField(allow_null=True)
    remark = serializers.CharField(allow_null=True, allow_blank=True)
    in_alarm = serializers.BooleanField(allow_null=True)
    wudid = serializers.CharField(allow_null=True, allow_blank=True)
    platform = serializers.IntegerField(allow_null=True)
    prd_key = serializers.CharField(allow_null=True, allow_blank=True)
    in_fault = serializers.BooleanField(allow_null=True)
    online = serializers.BooleanField(allow_null=True)
    sw_on = serializers.BooleanField(allow_null=True)
    gw_end_point = serializers.CharField(allow_null=True, allow_blank=True)
    agent_id = serializers.IntegerField(allow_null=True)
    in_acc = serializers.BooleanField(allow_null=True)
    ali_secret = serializers.CharField(allow_null=True, allow_blank=True)
    ali_region = serializers.CharField(allow_null=True, allow_blank=True)
    ali_device_name = serializers.CharField(allow_null=True, allow_blank=True)
    needs_m = serializers.BooleanField(allow_null=True)
    live_update = serializers.BooleanField(allow_null=True)

class DeviceListSerializer(serializers.ModelSerializer):
    device_type = SimpleDeviceTypeSerializer()
    device_prototype = SimpleDevicePrototypeSerializer()

    class Meta:
        model = Device
        fields = ['id', 'device_type', 'device_prototype', 'batch_no', 'nick_name', 'mac', 'did', 'address', 'status', 'remark', 'in_alarm', 'wudid',
                  'in_fault', 'platform', 'online', 'sw_on', 'prd_key', 'in_acc', 'needs_m', 'created_at', 'updated_at', 'project', 'gw_end_point']

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        ret['project_id'] = ret['project']
        ret.pop('project')

        return ret


class BuildingSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    project_id = serializers.IntegerField()
    image = serializers.CharField(allow_null=True, allow_blank=True)
    name = serializers.CharField(allow_null=True, allow_blank=True)
    address = serializers.CharField(allow_null=True, allow_blank=True)


class FloorSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    building_id = serializers.IntegerField()
    floor_no = serializers.IntegerField(allow_null=True)
    image = serializers.CharField(allow_null=True, allow_blank=True)
    name = serializers.CharField(allow_null=True, allow_blank=True)
    thumb = serializers.CharField(allow_null=True, allow_blank=True)
    coords_arr = serializers.CharField(allow_null=True, allow_blank=True)


class ActiveRoomSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    building_id = serializers.IntegerField()
    floor_id = serializers.IntegerField()
    image = serializers.CharField(allow_null=True, allow_blank=True)
    name = serializers.CharField(allow_null=True, allow_blank=True)
    min_temp = serializers.IntegerField(allow_null=True)
    max_temp = serializers.IntegerField(allow_null=True)
    min_humidity = serializers.IntegerField(allow_null=True)
    max_humidity = serializers.IntegerField(allow_null=True)
    min_tvoc = serializers.IntegerField(allow_null=True)
    max_tvoc = serializers.IntegerField(allow_null=True)
    min_co2 = serializers.IntegerField(allow_null=True)
    max_co2 = serializers.IntegerField(allow_null=True)
    min_pm25 = serializers.IntegerField(allow_null=True)
    max_pm25 = serializers.IntegerField(allow_null=True)
    show_in_datav = serializers.BooleanField(allow_null=True)


class WebRegionSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    name = serializers.CharField(allow_null=True, allow_blank=True)
    short_name = serializers.CharField(allow_null=True, allow_blank=True)
    level = serializers.IntegerField(allow_null=True)
    weather_code = serializers.CharField(allow_null=True, allow_blank=True)


class WebMenuSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    name = serializers.CharField(allow_null=True, allow_blank=True)
    seq = serializers.IntegerField(allow_null=True)
    enabled = serializers.BooleanField(allow_null=True)
    uni_name = serializers.CharField(allow_null=True, allow_blank=True)
    is_super = serializers.BooleanField(allow_null=True)
    parent_id = serializers.IntegerField(allow_null=True)
    agent_id = serializers.IntegerField(allow_null=True)


class ProjectWebMenuSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    project_id = serializers.IntegerField()
    web_menu_id = serializers.IntegerField()


class ReportConfigurerSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    configurable_id = serializers.IntegerField(required=False)
    configurable_type = serializers.CharField(allow_null=True, allow_blank=True, required=False)
    target_type_id = serializers.IntegerField(required=False)
    target_id = serializers.IntegerField(required=False)
    name = serializers.CharField(allow_null=True, allow_blank=True)
    identifier = serializers.CharField(allow_null=True, allow_blank=True)
    unit = serializers.CharField(allow_null=True, allow_blank=True)
    refer_point = serializers.CharField(allow_null=True, allow_blank=True)
    chart_type = serializers.IntegerField(allow_null=True)
    device_idx = serializers.IntegerField(allow_null=True)
    seq = serializers.IntegerField(allow_null=True)


class ReportConfigurationSerializer(serializers.ModelSerializer):
    class Meta:
        model = ReportConfigurer
        fields = '__all__'


class DeviceAttributeSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    attribute_prototype_id = serializers.IntegerField()
    device_id = serializers.IntegerField()
    value = serializers.CharField(allow_null=True, allow_blank=True)
    show_in_list = serializers.BooleanField(allow_null=True)

    def to_representation(self, instance):
        ret = super(DeviceAttributeSerializer, self).to_representation(instance)
        ap = AttributePrototype.objects.get(pk=ret['attribute_prototype_id'])
        ret['attribute_prototype'] = AttributePrototypeSerializer(ap).data
        return ret


class DeviceProtocolSerializer(serializers.ModelSerializer):
    class Meta:
        model = DeviceProtocol
        fields = '__all__'


class AddrSegmentSerializer(serializers.ModelSerializer):
    class Meta:
        model = AddrSegment
        fields = '__all__'


class EcUserSerializer(serializers.Serializer):
    unionid = serializers.CharField(allow_null=False, allow_blank=False)
    name = serializers.CharField(allow_null=False, allow_blank=False)
    username = serializers.CharField(allow_null=False, allow_blank=False)
    password = serializers.CharField(allow_null=False, allow_blank=False)
    project_id = serializers.IntegerField(allow_null=False)

class MessageSerializer(serializers.ModelSerializer):
    class Meta:
        model = Message
        exclude = ('web_user',)
        read_only_field = ('created_at', 'uid')

    def to_representation(self, instance):
        ret = super(MessageSerializer, self).to_representation(instance)
        useful, useless = UserMessage.objects.useful_count(ret.get('id'))
        ret['num_of_useful'] = useful
        ret['num_of_useless'] = useless
        # request = self.context['request']
        # user_message = UserMessage.objects.filter(message_id=ret.get('id'), web_user_id=request.user['id']).first()
        # ret['useful'] = user_message.useful if user_message else 9
        # ret['read'] = user_message.read if user_message else False

        return ret

class SnpVarSerializer(serializers.Serializer):
    project_id = serializers.IntegerField(allow_null=True, required=False)
    mac = serializers.CharField(allow_null=True, required=False)
    name = serializers.CharField()
    identifier = serializers.CharField()
    var_type = serializers.IntegerField()


class DevicePrototypeRatioSerializer(serializers.ModelSerializer):
    class Meta:
        model = DevicePrototypeRatio
        fields = '__all__'

    def to_representation(self, instance):
        ret = super(DevicePrototypeRatioSerializer, self).to_representation(instance)
        ret['device_prototype'] = SimpleDevicePrototypeSerializer(DevicePrototype.objects.get(pk=ret['device_prototype'])).data

        return ret


class TerminalSerializer(serializers.ModelSerializer):
    class Meta:
        model = Terminal
        # fields = ['id', 'device_id', 'idx', 'prefix', 'nick_name', 'terminal_type', 'terminal_label', 'device_prototype_id']
        fields = '__all__'


class SySimSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    iccid = serializers.CharField(max_length=255)
    imei = serializers.CharField(max_length=255, required=False, allow_null=True)
    sn = serializers.CharField(max_length=255, allow_blank=True, allow_null=True, required=False)
    msisdn = serializers.CharField(max_length=255)
    platform = serializers.CharField(max_length=255)
    status = serializers.CharField(max_length=24, allow_null=True, required=False)
    stop_reason = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    data_consumed = serializers.IntegerField(allow_null=True, default=0)

    remark = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    activated_at = serializers.DateTimeField(required=False, allow_null=True)
    opened_at = serializers.DateTimeField(required=False, allow_null=True)
    sync_at = serializers.DateTimeField(required=False, allow_null=True)
    created_at = serializers.DateTimeField(required=False, allow_null=True)
    updated_at = serializers.DateTimeField(required=False, allow_null=True)

    def to_representation(self, instance):
        ret = super().to_representation(instance)

        imei = ret.get('imei', None)

        ret['project'] = None
        ret['device_prototype'] = None

        if imei is not None:
            device = Device.objects.filter(mac=imei).last()
            if device is not None:
                if device.project_id:
                    ret['project'] = {
                        'id': device.project_id,
                        'name': device.project.name
                    }
                device_prototype = device.device_prototype
                ret['device_prototype'] = {
                    'id': device_prototype.id,
                    'name': device_prototype.name,
                    'uni_name': device_prototype.uni_name
                }

        return ret

class UserStatSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserStat
        fields = '__all__'
