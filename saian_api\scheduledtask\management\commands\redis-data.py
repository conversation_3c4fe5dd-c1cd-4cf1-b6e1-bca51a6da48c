"""
  同步 redis 数据到数据库
"""
import logging
import pickle
from fcntl import F_SETFL
from django.core.management.base import BaseCommand, CommandError

from saian_api.device.models import DeviceAttribute
from saian_api.terminal.models import TerminalAttribute
from saian_api.utils.db.Redis import RedisHelper
from saian_api.utils.tools import current_cwdays, object_to_dict
from saian_api.linkage.models import LinkageTrigger, LinkageRule
from saian_api.group.models import AcStrategies
from saian_api.project.models import Project
from saian_api.scheduledtask.utils import get_projects, hit_time, set_global_db

from datetime import datetime, timedelta
import traceback


class Command(BaseCommand):
    help = '同步 redis 数据任务'

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目列表
        projects = get_projects(self, options)

        # projects = [66]

        for project_id in projects:
            try:
                now = datetime.now()

                set_global_db(project_id)

                instance = RedisHelper.redis_instance(project_id, False)
                count = 0

                twenty_minutes_ago = now - timedelta(minutes=25)

                # redis device attributes
                cursor = 0
                while True:
                    cursor, keys = instance.scan(cursor, match='device_attribute:*', count=1000)
                    if len(keys):
                        objs = [pickle.loads(item) for item in instance.mget(keys) if item]
                        objs = [obj for obj in objs if obj.updated_at >= twenty_minutes_ago]
                        DeviceAttribute.objects.bulk_update(objs, ['value', 'updated_at'])
                        count += len(objs)

                    if cursor == 0:
                        break
                # redis terminal attributes
                cursor = 0
                while True:
                    cursor, keys = instance.scan(cursor, match='terminal_attribute:*', count=1000)
                    if len(keys):
                        objs = [pickle.loads(item) for item in instance.mget(keys) if item]
                        objs = [obj for obj in objs if obj.updated_at >= twenty_minutes_ago]
                        TerminalAttribute.objects.bulk_update(objs, ['value', 'updated_at'])
                        count += len(objs)

                    if cursor == 0:
                        break

                logging.info(f'同步项目-{project_id} redis 数据完成，共 {count} 条，耗时 {(datetime.now() - now).total_seconds()} 秒')

            except Exception as e:
                self.stderr.write(f"同步 redis 数据到数据库出错，project_id: {project_id}, err: {e.__str__()}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
                continue

