import logging

from rest_framework import serializers

from django.db.models import Q
from .models import Terminal, Category, CategoryTerminal, TerminalAttribute, TerminalNameplate
from ..devdefine.models import DevicePrototype, AttributePrototype
from ..devdefine.serializers import SimpleAttributePrototypeSerializer
from ..device.models import Device
from saian_api.user.models import UserDevice


class MiniTerminalSerializer(serializers.ModelSerializer):
    class Meta:
        model = Terminal
        fields = ['id', 'nick_name', 'device_prototype_id', 'device_id', 'idx', 'prefix']

    def to_representation(self, instance):
        ret = super(MiniTerminalSerializer, self).to_representation(instance)
        device = Device.objects.get(pk=ret['device_id'])
        ret['mac'] = device.mac
        return ret


class SimpleTerminalSerializer(serializers.ModelSerializer):
    class Meta:
        model = Terminal
        fields = ['id', 'idx', 'prefix', 'nick_name', 'terminal_type', 'terminal_label', 'device_id', 'device_prototype_id']


class TerminalSerializer(serializers.ModelSerializer):
    # device_prototype = DevicePrototypeSerializer()

    class Meta:
        model = Terminal
        fields = ['id', 'idx', 'prefix', 'in_fault', 'in_alarm', 'online', 'sw_on', 'terminal_temp',
                  'nick_name', 'terminal_type', 'terminal_label', 'device_id', 'device_prototype_id']

    def to_representation(self, instance):
        request = self.context['request']
        ret = super(TerminalSerializer, self).to_representation(instance)
        device = Device.objects.get(pk=ret['device_id'])

        if instance.prefix is not None and instance.idx is not None:
            user_device = UserDevice.objects.filter(
                web_user_id=request.user['id'], device_id=device.id, unit_prefix=instance.prefix, unit_idx=instance.idx).first()
        else:
            user_device = UserDevice.objects.filter(web_user_id=request.user['id'], device_id=device.id).first()
        terminal_dp = DevicePrototype.objects.get(pk=ret['device_prototype_id'])
        is_web = request.query_params.get('web', None)
        # custz_detail = True if (terminal_dp.web_content if is_web else terminal_dp.content) else False
        if is_web:
            custz_detail = True if terminal_dp.web_content else False
        else:
            # 小程序默认为True
            custz_detail = True

        ret.pop('device_id')
        ret.pop('device_prototype_id')

        terminal = Terminal.objects.get(pk=ret['id'])
        # 冷源的运行状态 identifier 有两种：等于 RunMode 或以 SysRunMode 结尾
        # cs_run_mode = TerminalAttribute.objects.filter(Q(identifier='RunMode') |
        #                                                Q(identifier__endswith='SysRunMode'),
        #                                                terminal=terminal).first()
        run_mode_ap = AttributePrototype.objects.filter(Q(identifier='RunMode') | Q(identifier__endswith='SysRunMode'),
                                                        device_prototype_id=device.device_prototype_id).last()
        cs_run_mode = TerminalAttribute.objects.query_object_by_ap(terminal, run_mode_ap, device.project_id) if run_mode_ap else None
        ret['dashboard_attres'] = terminal.dashboard_attres()

        ret['device_prototype'] = {
            'id': terminal_dp.id,
            'name': terminal_dp.name,
            'uni_name': terminal_dp.uni_name,
            'device_type_id': terminal_dp.device_type_id
        }
        ret['address'] = device.address
        ret['custz_detail'] = custz_detail
        ret['et_types'] = device.et_types(parse_detail=False) if (ret['terminal_type'] == 40 and ret['idx'] is None and not is_web) else []
        ret['key_attributes'] = terminal.key_attributes(device.project_id) if (
            ret['terminal_type'] == 40 and ret['idx'] is None and not is_web) else []
        ret['temp'] = device.current_temp()
        ret['hum'] = device.current_hum()
        ret['device_id'] = device.id
        ret['mac'] = device.mac
        ret['is_following'] = True if user_device is not None else False
        ret['following_id'] = user_device.id if user_device is not None else None
        ret['run_mode'] = cs_run_mode.value if cs_run_mode is not None else None

        return ret


class CategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        # fields = '__all__'
        exclude = ['terminals']
        read_only_fields = ('id', 'created_at', 'updated_at')


class CategoryWithTerminalCountSerializer(CategorySerializer):
    terminal_count = serializers.IntegerField(read_only=True)


class CategoryTerminalSerializer(serializers.ModelSerializer):
    terminal = TerminalSerializer()
    category = CategorySerializer()

    class Meta:
        model = CategoryTerminal
        fields = '__all__'


class TerminalAttributeSerializer(serializers.ModelSerializer):
    class Meta:
        model = TerminalAttribute
        fields = '__all__'

    def to_representation(self, instance):
        ret = super().to_representation(instance)

        try:
            ap = AttributePrototype.objects.get(pk=ret['attribute_prototype_id'])
            ret['attribute_prototype'] = SimpleAttributePrototypeSerializer(ap).data
        except AttributePrototype.DoesNotExist:
            logging.error(f"终端属性对应的参数点不存在: attribute_prototype_id-{ret['attribute_prototype_id']}")
            ret['attribute_prototype'] = None

        return ret


class TerminalNameplateSerializer(serializers.ModelSerializer):
    class Meta:
        model = TerminalNameplate
        fields = '__all__'
