import copy
import logging

from django.utils.deprecation import MiddlewareMixin

from saian_api.user.models import UserLog

class UserLogMiddleware(MiddlewareMixin):
    """用户请求日志中间件"""

    def __call__(self, request, *args, **kwargs):
        # 因为 body 只能读取一次，故这里使用 copy 保留一份副本，不直接读取
        try:
            # 忽略请求体携带的文件
            if request.FILES:
                data = None
            else:
                data = copy.copy(request.body).decode('utf-8')
        except Exception as e:
            logging.error(f'UserLogMiddleware error: {e.__str__()}')
            data = None

        response = self.get_response(request)

        # get_response 方法执行后，request 对象才有 user，创建 UserLog 时才能找到 excutor
        # 只记录用户操作的请求，但是 intapi 很难分辨是数据同步请求还是管理后台修改后的请求，简单起见，intapi 不记录 POST 请求
        # enapi 请求没有用户信息，也不记录
        if (not ('intapi' in request.get_full_path() and request.method == 'POST')) and ('enapi' not in request.get_full_path()):
            UserLog.objects.cus_create(request, data)

        return response
