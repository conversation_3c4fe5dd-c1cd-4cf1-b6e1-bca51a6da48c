"""
    结算管理（小时、日、周、月、季度、年）
"""
import traceback, datetime, json
from django.core.management import BaseCommand, CommandError
from django.db.models import Sum
from saian_api.scheduledtask.utils import get_projects, set_global_db
from saian_api.report.models import SettleParty, SettleRefer, SettleRecord, DevstlRecord
from saian_api.terminal.models import Terminal, TerminalAttribute
from saian_api.report.models import DeviceHourlyStat
from saian_api.device.models import Device, DeviceAttribute
from saian_api.devdefine.models import AttributePrototype
from saian_api.utils.db.Redis import RedisHelper

class Command(BaseCommand):
    help = "结算管理（小时、日、周、月、季度、年）"

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目ID列表
        projects = get_projects(self, options)

        now = datetime.datetime.now()

        for project_id in projects:
            try:
                # 设置全局数据库
                set_global_db(project_id)

                # 当前项目的所有结算对象
                settle_list = list(SettleParty.objects.all())

                for settle_item in settle_list:

                    period_list = [int(item) for item in settle_item.periods.split(',')]

                    # 小时
                    if 10 in period_list:
                        hourly_begin_time = (now - datetime.timedelta(hours=1)).strftime('%Y-%m-%d %H:00:00')
                        hourly_end_time = now.strftime('%Y-%m-%d %H:00:00')
                        self.calculation_settle(project_id, 10, hourly_begin_time, hourly_end_time, settle_item)

                    # 日
                    if 20 in period_list and now.hour == 0:
                        daily_begin_time = (now - datetime.timedelta(days=1)).strftime('%Y-%m-%d 00:00:00')
                        daily_end_time = now.strftime('%Y-%m-%d 00:00:00')
                        self.calculation_settle(project_id, 20, daily_begin_time, daily_end_time, settle_item)

                    # 周
                    if 30 in period_list and now.isoweekday() == 1 and now.hour == 0:
                        weekly_begin_time = (now - datetime.timedelta(days=7) - datetime.timedelta(days=now.weekday())).strftime('%Y-%m-%d 00:00:00')
                        weekly_end_time = (now - datetime.timedelta(days=now.weekday())).strftime('%Y-%m-%d 00:00:00')
                        self.calculation_settle(project_id, 30, weekly_begin_time, weekly_end_time, settle_item)

                    # 月
                    if 40 in period_list and now.day == 1 and now.hour == 0:
                        year = now.year - 1 if now.month == 1 else now.year
                        month = 12 if now.month == 1 else now.month - 1
                        monthly_begin_time = datetime.datetime.strptime('%s-%s-01 00:00:00' % (year, month), '%Y-%m-%d %H:%M:%S')
                        monthly_end_time = now.strftime('%Y-%m-01 00:00:00')
                        self.calculation_settle(project_id, 40, monthly_begin_time, monthly_end_time, settle_item)

                    # 季度
                    if 50 in period_list and now.month in [1, 4, 7, 10] and now.day == 1 and now.hour == 0:
                        four_seasons_time = [{'begin': 1, 'end': 3}, {'begin': 4, 'end': 6}, {'begin': 7, 'end': 9}, {'begin': 10, 'end': 12}]
                        current_month = now.month
                        index = next((i for i, item in enumerate(four_seasons_time) if item['begin'] <= current_month <= item['end']), None)
                        begin_year = now.year - 1 if index == 0 else now.year
                        end_month = 1 if index == 0 else four_seasons_time[index - 1]['end'] + 1
                        quarterly_begin_time = datetime.datetime(begin_year, four_seasons_time[index - 1]['begin'], 1, 0, 0, 0)
                        quarterly_end_time = datetime.datetime(now.year, end_month, 1, 0, 0, 0)
                        self.calculation_settle(project_id, 50, quarterly_begin_time, quarterly_end_time, settle_item)

                    # 年
                    if 60 in period_list and now.month == 1 and now.day == 1 and now.hour == 0:
                        yearly_begin_time = datetime.datetime.strptime('%s-%s-01 00:00:00' % (now.year - 1, 1), '%Y-%m-%d %H:%M:%S')
                        yearly_end_time = now.strftime('%Y-01-01 00:00:00')
                        self.calculation_settle(project_id, 60, yearly_begin_time, yearly_end_time, settle_item)
                        
            except CommandError:
                self.stderr.write(f"运行'结算管理（小时、日、周、月、季度、年）'任务失败，项目ID：{project_id}，命令参数不合法！", ending='\n')
                continue
            except Exception as e:
                self.stderr.write(f"运行'结算管理（小时、日、周、月、季度、年）'任务失败，项目ID：{project_id}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
                continue

    def calculation_settle(self, project_id, period, begin_time, end_time, settle_item):
        # 结算方式
        settle_way_list = [int(item) for item in settle_item.settle_ways.split(',')]

        # 结算对象（筛选出符合条件的终端）
        terminal_list = Terminal.objects.filter(show_en=True)

        # 是否有筛选条件
        if settle_item.device_filter:
            # 解析结算对象中设备的筛选条件
            device_filter = json.loads(settle_item.device_filter)
            # 筛选条件：设备类型
            if device_filter['device_prototype_ids']:
                terminal_list = terminal_list.filter(device_prototype_id__in=device_filter['device_prototype_ids'].split(','))
            # 筛选条件：设备参数
            parameter_screening_list = [item for item in list(device_filter.keys()) if item != 'device_prototype_ids']
            if parameter_screening_list:
                for device_prototype_id in device_filter['device_prototype_ids'].split(','):
                    for parameter_screening_item in parameter_screening_list:
                        attribute_prototype = AttributePrototype.objects.filter(identifier=parameter_screening_item, device_prototype_id=device_prototype_id).order_by('created_at').last()
                        if attribute_prototype:
                            # 缓存数据
                            cache_terminal_attribute = RedisHelper.scan_items(project_id, f'terminal_attribute:*_{attribute_prototype.id}', False)
                            cache_terminal_ids = [terminal_attribute.terminal_id for terminal_attribute in cache_terminal_attribute]
                            # 数据库数据
                            archive_terminal_ids = terminal_list.exclude(id__in=cache_terminal_ids).values_list('id', flat=True)
                            archive_terminal_attribute = TerminalAttribute.objects.filter(terminal_id__in=archive_terminal_ids, attribute_prototype_id=attribute_prototype.id)

                            terminal_attribute_list = cache_terminal_attribute + list(archive_terminal_attribute)

                            if int(attribute_prototype.data_type) == 20:
                                eligible_terminal_attribute = list(filter(lambda item: float(item.value) == float(device_filter[parameter_screening_item]), terminal_attribute_list))
                                eligible_terminal_ids = [item.terminal_id for item in eligible_terminal_attribute]
                                terminal_list = terminal_list.filter(id__in=eligible_terminal_ids)
                            else:
                                eligible_terminal_attribute = list(filter(lambda item: item.value == device_filter[parameter_screening_item], terminal_attribute_list))
                                eligible_terminal_ids = [item.terminal_id for item in eligible_terminal_attribute]
                                terminal_list = terminal_list.filter(id__in=eligible_terminal_ids)
                        else:
                            terminal_list = terminal_list.exclude(device_prototype_id=device_prototype_id)

        # 结算基准
        settle_refer_list = SettleRefer.objects.filter(settle_party_id=settle_item.id)
        for settle_refer_item in settle_refer_list:
            settle_refer_item.run_params = json.loads(settle_refer_item.run_params)

        # 结算方式：按单个设备结算；能耗来源：设备本身参数；运行时间的计算方式：默认；
        is_completed_single_calculation = DevstlRecord.objects.filter(terminal_id__in=terminal_list, period=period, settle_dt=begin_time)
        if 10 in settle_way_list and not is_completed_single_calculation and settle_item.ec_src == 'deviceself' and settle_item.rt_stat == 'run_params' and settle_refer_list:
            for terminal_item in terminal_list:
                # 当前设备在当前结算周期的能耗量（改造前）
                single_unmodified_terminal_ec = 0
                # 当前设备在当前结算周期的能耗量（改造后）
                single_modified_terminal_ec = DeviceHourlyStat.objects.filter(device_id=terminal_item.device_id, created_at__range=[begin_time, end_time], identifier=settle_refer_list[0].run_params['ec_idf']).aggregate(total=Sum('avg'))['total']
                single_modified_terminal_ec = single_modified_terminal_ec if single_modified_terminal_ec else 0
                # 当前设备在各工况的运行时间（以分钟为单位）
                single_terminal_con_rt = {}

                for settle_refer_item in settle_refer_list:
                    # 当前工况的运行时间
                    single_refer_run_time = DeviceHourlyStat.objects.filter(device_id=terminal_item.device_id, created_at__range=[begin_time, end_time], identifier=settle_refer_item.run_params['rt_idf']).aggregate(total=Sum('avg'))['total']
                    single_refer_run_time = int(single_refer_run_time) if single_refer_run_time else 0

                    single_terminal_con_rt[settle_refer_item.run_params['rt_idf']] = single_refer_run_time
                    # 当前工况的功率
                    single_refer_power = 0
                    device = Device.objects.filter(id=terminal_item.device_id).first()
                    if device:
                        attribute_prototype = AttributePrototype.objects.filter(identifier=settle_refer_item.param_idf, device_prototype_id=device.device_prototype_id).order_by('created_at').last()
                        device_attribute = DeviceAttribute.objects.query_object_by_idf(device, settle_refer_item.param_idf)
                        if attribute_prototype and attribute_prototype.unit == 'W' and device_attribute:
                            single_refer_power = float(device_attribute.value) / 1000  
                        if attribute_prototype and attribute_prototype.unit == 'kW' and device_attribute:
                            single_refer_power = float(device_attribute.value)                    

                    # 用于计算能耗的运行时间（分钟转换为小时）
                    single_computative_run_time = single_refer_run_time / 60 if single_refer_run_time else 0

                    single_unmodified_terminal_ec = single_unmodified_terminal_ec + single_computative_run_time * single_refer_power

                single_unmodified_terminal_ec = float(round(single_unmodified_terminal_ec * 10000, 2)) / 10000

                single_modified_terminal_ec = float(round(single_modified_terminal_ec, 2))
                single_modified_terminal_ec = int(single_modified_terminal_ec) if int(single_modified_terminal_ec) == single_modified_terminal_ec else single_modified_terminal_ec   

                es_value = single_unmodified_terminal_ec - single_modified_terminal_ec             
                es_value = float(round(es_value, 2))
                es_value = int(es_value) if int(es_value) == es_value else es_value

                devstl_record_item = DevstlRecord(
                    terminal_id = terminal_item.id,
                    settle_party_id = settle_item.id,
                    power_cons = single_modified_terminal_ec,
                    es_value = es_value,
                    con_rt = json.dumps(single_terminal_con_rt),
                    period = period,
                    settle_dt = begin_time
                )
                devstl_record_item.save()

        # 结算方式：按平均值结算；能耗来源：设备本身参数；运行时间的计算方式：默认；
        is_completed_average_calculation = SettleRecord.objects.filter(settle_party_id=settle_item.id, period=period, settle_dt=begin_time)
        if 20 in settle_way_list and not is_completed_average_calculation and settle_item.ec_src == 'deviceself' and settle_item.rt_stat == 'run_params' and settle_refer_list:
            # 当前结算对象在当前结算周期内的能耗量（改造前）
            collect_unmodified_party_ec = 0
            # 当前结算对象在当前结算周期内的能耗量（改造后）
            collect_modified_party_ec = 0
            # 当前结算对象在当前结算周期内的运行时间（所有设备、所有工况）
            collect_party_run_time = 0

            for terminal_item in terminal_list:
                collect_modified_terminal_ec = DeviceHourlyStat.objects.filter(device_id=terminal_item.device_id, created_at__range=[begin_time, end_time], identifier=settle_refer_list[0].run_params['ec_idf']).aggregate(total=Sum('avg'))['total']
                collect_modified_terminal_ec = collect_modified_terminal_ec if collect_modified_terminal_ec else 0
                collect_modified_party_ec = collect_modified_party_ec + collect_modified_terminal_ec

            for settle_refer_item in settle_refer_list:
                # 当前工况的运行时间
                collect_refer_run_time = 0

                for terminal_item in terminal_list:
                    # 当前设备的运行时间
                    collect_terminal_run_time = DeviceHourlyStat.objects.filter(device_id=terminal_item.device_id, created_at__range=[begin_time, end_time], identifier=settle_refer_item.run_params['rt_idf']).aggregate(total=Sum('avg'))['total']
                    collect_terminal_run_time = int(collect_terminal_run_time) if collect_terminal_run_time else 0

                    collect_refer_run_time = collect_refer_run_time + collect_terminal_run_time
                
                # 用于计算能耗的运行时间（分钟转换为小时）
                collect_computative_run_time = collect_refer_run_time / 60 if collect_refer_run_time else 0

                collect_refer_power = 0
                if settle_refer_item.param_unit == 'kW':
                    collect_refer_power = float(settle_refer_item.param_value)
                if settle_refer_item.param_unit == 'W':
                    collect_refer_power = float(settle_refer_item.param_value) / 1000

                collect_unmodified_party_ec = collect_unmodified_party_ec + collect_computative_run_time * collect_refer_power
                collect_party_run_time = collect_party_run_time + collect_refer_run_time


            collect_unmodified_party_ec = float(round(collect_unmodified_party_ec * 10000, 2) / 10000)

            collect_modified_party_ec = float(round(collect_modified_party_ec, 2))
            collect_modified_party_ec = int(collect_modified_party_ec) if collect_modified_party_ec == int(collect_modified_party_ec) else collect_modified_party_ec
            
            # 节能率
            economize_energy_ratio = (collect_unmodified_party_ec - collect_modified_party_ec) / collect_unmodified_party_ec * 100 if collect_unmodified_party_ec else 0
            es_rate = float(round(economize_energy_ratio, 6))
            es_rate = float(round(economize_energy_ratio, 2))
            es_rate = int(es_rate) if es_rate == int(es_rate) else es_rate

            collect_unmodified_party_ec = float(round(collect_unmodified_party_ec, 2))
            collect_unmodified_party_ec = int(collect_unmodified_party_ec) if collect_unmodified_party_ec == int(collect_unmodified_party_ec) else collect_unmodified_party_ec

            create_item = SettleRecord(
                settle_party_id = settle_item.id,
                period = period,
                conver_ec = collect_unmodified_party_ec,
                actual_ec = collect_modified_party_ec,
                es_rate = es_rate,
                run_time = int(collect_party_run_time),
                settle_dt = begin_time
            )
            create_item.save()
