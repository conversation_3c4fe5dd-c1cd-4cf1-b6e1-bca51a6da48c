# Generated by Django 3.2.19 on 2024-10-23 11:04

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('terminal', '0009_auto_20240411_1027'),
    ]

    operations = [
        migrations.CreateModel(
            name='TerminalNameplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('terminal_id', models.IntegerField(null=True)),
                ('np_id', models.Char<PERSON>ield(max_length=255)),
                ('np_name', models.CharField(max_length=255)),
                ('np_content', models.TextField()),
                ('np_img', models.Char<PERSON><PERSON>(max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'terminal_nameplates',
            },
        ),
    ]
