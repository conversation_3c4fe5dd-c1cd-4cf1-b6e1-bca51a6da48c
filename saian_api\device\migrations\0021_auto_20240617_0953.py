# Generated by Django 3.2.19 on 2024-06-17 09:53

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('device', '0020_alter_devicectrllog_retries'),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name='deviceeventhistory',
            name='device_even_project_36b620_idx',
        ),
        migrations.RemoveIndex(
            model_name='deviceeventhistory',
            name='device_even_nick_na_81dd43_idx',
        ),
        migrations.RemoveIndex(
            model_name='deviceeventhistory',
            name='device_even_nick_na_a38b41_idx',
        ),
        migrations.RemoveIndex(
            model_name='deviceeventhistory',
            name='device_even_device__bb46a5_idx',
        ),
        migrations.RemoveIndex(
            model_name='deviceeventhistory',
            name='device_even_project_cb6df2_idx',
        ),
        migrations.RemoveIndex(
            model_name='deviceeventhistory',
            name='device_even_nick_na_8fdca6_idx',
        ),
        migrations.RemoveIndex(
            model_name='deviceeventhistory',
            name='device_even_nick_na_90ea39_idx',
        ),
        migrations.RemoveIndex(
            model_name='deviceeventhistory',
            name='device_even_project_070c64_idx',
        ),
        migrations.RemoveIndex(
            model_name='deviceeventhistory',
            name='device_even_nick_na_3e4ea2_idx',
        ),
        migrations.RemoveIndex(
            model_name='deviceeventhistory',
            name='device_even_nick_na_451a23_idx',
        ),
        migrations.AlterField(
            model_name='deviceeventhistory',
            name='device_issue_id',
            field=models.BigIntegerField(null=True),
        ),
        migrations.AlterField(
            model_name='deviceeventhistory',
            name='device_prototype_id',
            field=models.BigIntegerField(),
        ),
        migrations.AlterField(
            model_name='deviceeventhistory',
            name='nick_name',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='deviceeventhistory',
            name='project_id',
            field=models.BigIntegerField(),
        ),
    ]
