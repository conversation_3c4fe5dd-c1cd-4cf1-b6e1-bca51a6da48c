import logging
import os

import requests

from saian_api.utils.db.Redis import RedisHelper
from saian_api.utils.httpapi import DOMAIN

class WechatApi:
    """微信公众号相关api调用"""

    @classmethod
    def get_wechat_token(cls):
        """获取缓存的 Access Token"""
        wechat_token = RedisHelper.get_wechat_token()
        if wechat_token is None:
            wechat_token = cls.query_access_token()

        return wechat_token

    @classmethod
    def query_access_token(cls):
        """调用 api，生成 Access Token"""
        wechat_appid = os.environ.get('WECHAT_APPID')
        wechat_secret = os.environ.get('WECHAT_SECRET')

        params = {
            "grant_type": "client_credential",
            "appid": wechat_appid,
            "secret": wechat_secret
        }

        # DOMAIN = 'https://api.weixin.qq.com'
        result = requests.post(f'{DOMAIN}/cgi-bin/stable_token', json=params)
        if result.status_code != 200:
            logging.error("Failed to get new access token.")
            return None
        else:
            data = result.json()
            token = data.get('access_token', None)
            expires_in = data.get('expires_in', 3600)

            if token is not None:
                RedisHelper.set_wechat_token(token, ex=expires_in)
                return token
            logging.error(f'failed to request access token, err: {data}')
            return None

    @classmethod
    def get_openid_list(cls):
        """查询所有用户的openid"""
        openid_list = []
        next_openid = ''

        url = f'{DOMAIN}/cgi-bin/user/get'
        params = {
            'access_token': cls.get_wechat_token(),
        }

        while True:
            params['next_openid'] = next_openid
            r = requests.get(url, params=params)
            if r.status_code != 200:
                logging.error(f'failed to get user openid, status code: {r.status_code}, res: {r.text}')
                break
            data = r.json()
            openid_list += data['data']['openid']
            next_openid = data['next_openid']

            if not next_openid:
                break

        return openid_list

    @classmethod
    def get_user_info(cls, openid):
        """根据用户的openid，查询用户信息"""
        url = '{DOMAIN}/cgi-bin/user/info'
        params = {
            'access_token': cls.get_wechat_token(),
            'openid': openid,
        }
        r = requests.get(url, params=params)
        if r.status_code != 200:
            logging.error(f'failed to get user info, status code: {r.status_code}, res: {r.text}')
            return None

        return r.json()

    @classmethod
    def handle_user_openid(cls, prj_openid_list: list):
        """返回新关注用户的 openid 与 unionid 的关联和取消关注用户 openid 列表"""
        wechat_openid_list = cls.get_openid_list()

        # 新关注的openid
        nf_openid = set(wechat_openid_list) - set(prj_openid_list)
        # 取消关注的openid
        uf_openid = set(prj_openid_list) - set(wechat_openid_list)

        nf = []
        for openid in nf_openid:
            user_info = cls.get_user_info(openid)
            if user_info is not None:
                unionid = user_info['unionid']
                nf.append({'openid': openid, 'unionid': unionid})

        return nf, list(uf_openid)

    @classmethod
    def ocr(cls, img_url):
        """基于小程序的通用印刷体 OCR 识别"""
        params = {
            'access_token': cls.get_wechat_token(),
            'img_url': img_url
        }
        result = requests.post(f'{DOMAIN}/cv/ocr/comm', data=params)

        if result.status_code != 200:
            logging.error(f'failed to get user info, status code: {result.status_code}, res: {result.text}')
            return None

        return result.json()


