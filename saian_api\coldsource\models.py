import datetime
import json
import logging
import re

from django.core.exceptions import MultipleObjectsReturned, ObjectDoesNotExist
from django.db import models
from django.db.models import Count

from saian_api.devdefine.models import DevicePrototype
from saian_api.devdefine.models import DevicePrototype
from saian_api.device.models import Device, ParamRecord, DeviceAttribute
from saian_api.device.models import Device, ParamRecord, DeviceAttribute
from saian_api.project.models import Project
from saian_api.project.models import Project
from saian_api.report.models import DeviceHourlyStat
from saian_api.report.models import DeviceHourlyStat, DeviceDailyStat
from saian_api.terminal.models import Terminal
from saian_api.user.models import WebUser
from saian_api.utils.tools import get_today_start_and_end, is_number
from saian_api.utils.utils import CeleryTaskUtils

# Create your models here.
class ColdSource(models.Model):
    """
      冷源定义
    """
    # 所属项目
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    # 冷源名字
    name = models.CharField(max_length=255)
    # 唯一名称
    uni_name = models.CharField(max_length=255, default='冷源')
    # 冷源平面图
    image = models.CharField(max_length=255, null=True)
    # 平面图控件位置数据
    coords_arr = models.TextField(null=True)
    # mac
    mac = models.CharField(max_length=255)
    # 最大小时电量
    max_hr_power_cons = models.CharField(max_length=255, null=True)
    # 冷源能耗分析的步进
    eer_step = models.CharField(max_length=255, null=True)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)
    #父菜单ID
    parent_id = models.BigIntegerField(null=True)

    class Meta:
        db_table = 'cold_sources'

    # 获取冷源当日用电量
    @property
    def today_power_cons(self):
        result = 0.0
        result = self.today_cons('EMeter_1_PowerCons')
        if result == 0.0:
            device = Device.objects.get(mac=self.mac)
            value = device.get_value_by_idf('TodayPowerConsum')
            if value is not None:
                result = float(value)

        return round(result, 2)

    # 绑定的电表设备id
    @property
    def pdevice_id(self):
        result = None
        try:
            ec_sources = EcSource.objects.filter(ec_type=10)
            if ec_sources.exists():
                result = ec_sources.first().device_id
        except ObjectDoesNotExist as e:
            logging.error(e.with_traceback)

        return result

    # 绑定的冷量表设备id
    @property
    def cdevice_id(self):
        result = None
        try:
            ec_sources = EcSource.objects.filter(ec_type=30)
            if ec_sources.exists():
                result = ec_sources.first().device_id
        except ObjectDoesNotExist as e:
            logging.error(e.with_traceback)

        return result

    # 当天用冷量
    @property
    def today_cool_cons(self):
        return self.today_cons('CMeter_1_CoolCons')

    # 根据identifier计算电表的当日度量值作为冷源当日用电量
    def today_cons(self, identifier):
        diff = 0.0
        device = Device.objects.get(mac=self.mac)
        beginning_of_today, end_of_today = get_today_start_and_end()
        time_now = datetime.datetime.now()
        prs = ParamRecord.objects.filter(device_id=device.id, identifier=identifier, created_at__time__range=(
            beginning_of_today, time_now)).order_by('-created_at')

        if prs is not None and len(prs) != 0:
            # 计算当天开始到目前时间的用量
            first = prs[0]
            if len(prs) >= 2:
                last = prs[len(prs) - 1]
                diff = float(first.value) - float(last.value)
            else:
                diff = float(first.value)

            # 如果差小于0，则认为是更换的仪表
            # 则去取出当天读数最大值 - 当天开始的读数值 + 最近一次的读数值
            if diff < 0:
                diff = float(max(prs.values_list('value', flat=True))) - float(first.value) + float(last.value)

        return round(diff, 2)


# def EcSourceeManager(models.Manager):
class EcSource(models.Model):
    """
      冷源能耗来源，构建能耗计算公式，用于冷源能耗分析
    """
    # 所属冷源
    cold_source = models.ForeignKey(ColdSource, on_delete=models.CASCADE)
    # 名称
    name = models.CharField(max_length=255, null=True, default=True)
    # 对应设备
    device = models.ForeignKey(Device, on_delete=models.CASCADE, null=True, blank=True)
    # 值
    identifier = models.CharField(max_length=255)
    # 能耗类型，10-电，20-水，30-冷，40-气，50-汽油
    ec_type = models.IntegerField()

    class Meta:
        db_table = 'ec_sources'

class CsCop(models.Model):
    """冷源COP"""
    # 名称
    name = models.CharField(max_length=255)
    # 所属冷源
    cold_source = models.ForeignKey(ColdSource, on_delete=models.CASCADE)
    # 计算公式，引用 EcSource ID
    formula = models.CharField(max_length=500, null=True)
    # 冷源或主机cop的参数点
    identifier = models.CharField(max_length=255, null=True)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'cs_cops'

class CsEerAnalyseManager(models.Manager):
    @classmethod
    def clac_cop(cls, now, cop_type):
        """计算冷源能耗分析"""
        for cs_cop in CsCop.objects.filter(formula__isnull=False):
            matches = re.findall(r'{{\w+}}', cs_cop.formula)

            if cop_type == 'hr':
                created_at = (now - datetime.timedelta(hours=1)).strftime('%Y-%m-%d %H:59:59')
            else:
                created_at = (now - datetime.timedelta(days=1)).strftime('%Y-%m-%d 23:59:59')

            exe_str = cs_cop.formula
            if matches and len(matches):
                for m in matches:
                    ec_source_id = int(m[2:len(m) - 2])
                    try:
                        ec_source = EcSource.objects.get(pk=ec_source_id)

                        data_from = DeviceHourlyStat.objects if cop_type == 'hr' else DeviceDailyStat.objects

                        dhs = data_from.filter(created_at=created_at, device_id=ec_source.device_id, identifier=ec_source.identifier).last()
                        if dhs is not None and dhs.avg:
                            exe_str = exe_str.replace(m, str(dhs.avg))
                    except EcSource.DoesNotExist:
                        continue

            try:
                if '/' in exe_str:
                    cold_str, power_str = exe_str.split('/')
                    cold = eval(cold_str)
                    power = eval(power_str)
                    eer = round(cold / power, 2)
                else:
                    cold = '--'
                    power = '--'
                    eer = eval(exe_str)

                if not CsEerAnalyse.objects.filter(cs_cop=cs_cop, created_at=created_at, type=cop_type).exists():
                    CsEerAnalyse.objects.create(cs_cop=cs_cop,
                                                power_cons=power,
                                                cold_cons=cold,
                                                value=eer,
                                                type=cop_type,
                                                created_at=created_at)

                if cs_cop.identifier:
                    cs = cs_cop.cold_source
                    device = Device.objects.filter(project__isnull=False, mac=cs.mac).last()
                    if device is not None:
                        da = DeviceAttribute.objects.get_by_idf(device, cs_cop.identifier)
                        if da is not None:
                            da.value = eer
                            da.save()
                            DeviceAttribute.objects.save_to_redis(device, da)

            except Exception as e:
                logging.error(f'Exception in CsEerAnalyse, formula: {exe_str}, error: {e.__str__()}')

    # 冷源能耗统计
    def do_stat(self, cust_date=None, project_id=None):
        if cust_date is not None and project_id is not None:
            db_name = f'prj{project_id}db'
            projects = [{'db_name': db_name, 'project': Project.objects.using(db_name).first(), 'project_id': project_id}]
        else:
            projects = CeleryTaskUtils.get_all_project()
        # projects = [{'db_name': 'prj27db', 'project': Project.objects.using('prj27db').first(), 'project_id': 27}]
        now = datetime.datetime.now() if cust_date is None else cust_date
        for project in projects:
            ec_sources = EcSource.objects.using(project['db_name']).all()
            cold_sources = ColdSource.objects.using(project['db_name']).all()

            # 判断项目是否有配置冷源能耗统计
            print(f"冷源能耗统计: {project['project_id']}-{project['project'].name} {'开始' if ec_sources.exists() else '项目未配置，跳过统计'} ")
            if ec_sources.exists():
                # 取出已配置的冷源id
                annotates = ec_sources.values('cold_source_id').annotate(count=Count('id'))
                for anno in annotates:
                    csid = anno['cold_source_id']
                    mac = cold_sources.filter(id=csid).last().mac
                    power_eval_str = ''
                    cold_eval_str = ''

                    # 公式涉及的设备id
                    devices = ec_sources.filter(cold_source_id=csid).values('device_id').annotate(count=Count(id))
                    device_ids = [device['device_id'] for device in list(filter(lambda x: x['device_id'] is not None, devices))]

                    # 拿到了冷源id，下面计算 eer

                    # 查询上一个小时的读数
                    hour_ago = now - datetime.timedelta(hours=1)
                    last_hour_begin = hour_ago.strftime('%Y-%m-%d %H:00:00')
                    last_hour_end = now.strftime('%Y-%m-%d %H:00:00')

                    last_hourly_stat = DeviceHourlyStat.objects.using(project['db_name']).filter(
                        created_at__range=[last_hour_begin, last_hour_end],
                        device_id__in=device_ids
                    )

                    # csid 下的冷量公式
                    cold_ec_sources_options = ec_sources.filter(cold_source_id=csid, ec_type=30).order_by('seq')
                    # csid 下的电量公式
                    power_ec_sources_options = ec_sources.filter(cold_source_id=csid, ec_type=10).order_by('seq')

                    # 判断上一个小时是否有读数
                    if last_hourly_stat.count() > 0:
                        # 如果有电量公式
                        if power_ec_sources_options.count() > 0:
                            for option in power_ec_sources_options:
                                # 操作值
                                if option.value_type == 10:
                                    # 前一小时
                                    stat = last_hourly_stat.filter(
                                        identifier=option.value,
                                        device_id=option.device_id
                                    ).order_by('-created_at')
                                    power = stat.last().avg if stat.exists() else 0
                                    power_eval_str = power_eval_str + str(power)

                                # 操作符 以及 倍数
                                # if option.value_type == 20:
                                else:
                                    power_eval_str = power_eval_str + option.value

                        # 如果有冷量公式
                        if cold_ec_sources_options.count() > 0:
                            for option in cold_ec_sources_options:
                                # 操作值
                                if option.value_type == 10:
                                    # 前一小时
                                    stat = last_hourly_stat.filter(
                                        identifier=option.value,
                                        device_id=option.device_id
                                    ).order_by('-created_at')
                                    cold = stat.last().avg if stat.exists() else 0
                                    cold_eval_str = cold_eval_str + str(cold)

                                # 操作符 以及 倍数
                                # if option.value_type == 20:
                                else:
                                    cold_eval_str = cold_eval_str + option.value
                        else:
                            # 如果没有冷量公式，则使用冷源的累计冷量（CumulatedColdConsum）作为冷量值
                            # 前一小时
                            stat = DeviceHourlyStat.objects.using(project['db_name']).filter(
                                created_at__range=[last_hour_begin, last_hour_end],
                                identifier='CumulatedColdConsum',
                                mac=mac
                            )
                            cold = stat.last().avg if stat.exists() else 0
                            cold_eval_str = str(cold)

                        # 根据计算公式得出总量
                        power_cons = eval(power_eval_str) if power_eval_str != '' else 0
                        cold_cons = eval(cold_eval_str) if cold_eval_str != '' else 0

                        power = power_cons
                        cold = cold_cons

                        analyse = CsEerAnalyse(
                            cold_source_id=csid,
                            power_cons=power,
                            cold_cons=cold,
                            eer=round(cold / power, 2) if power != 0 else 0,
                            created_at=hour_ago.strftime('%Y-%m-%d %H:59:59')
                        )
                        analyse.save(using=project['db_name'])
                    else:
                        print(
                            f'冷源能耗统计: {project["project_id"]}-{project["project"].name}：{last_hour_begin}-{last_hour_end} 时间段，DeviceHourlyStat 无数据！')
                    # 计算每天的冷源能效
                    if last_hour_end.endswith('00:00:00'):
                        power_eval_str = ''
                        cold_eval_str = ''

                        last_day_begin = (now - datetime.timedelta(days=1)).strftime('%Y-%m-%d 00:00:00')
                        last_day_stat = DeviceDailyStat.objects.using(project['db_name']).filter(
                            created_at__range=[last_day_begin, last_hour_end],
                            device_id__in=device_ids
                        )

                        # 如果有电量公式
                        if power_ec_sources_options.count() > 0:
                            for option in power_ec_sources_options:
                                # 操作值
                                if option.value_type == 10:
                                    # 前一天
                                    stat = last_day_stat.filter(
                                        identifier=option.value,
                                        device_id=option.device_id
                                    ).order_by('-created_at')
                                    power = stat.first().avg if stat.exists() else 0
                                    power_eval_str = power_eval_str + str(power)

                                # 操作符 以及 倍数
                                # if option.value_type == 20:
                                else:
                                    power_eval_str = power_eval_str + option.value

                        # 如果有冷量公式
                        if cold_ec_sources_options.count() > 0:
                            for option in cold_ec_sources_options:
                                # 操作值
                                if option.value_type == 10:
                                    # 前一天
                                    stat = last_day_stat.filter(
                                        identifier=option.value,
                                        device_id=option.device_id
                                    ).order_by('-created_at')
                                    cold = stat.first().avg if stat.exists() else 0
                                    cold_eval_str = cold_eval_str + str(cold)
                                # 操作符 以及 倍数
                                # if option.value_type == 20:
                                else:
                                    cold_eval_str = cold_eval_str + option.value
                        else:
                            # 如果没有冷量公式，则使用冷源的累计冷量（CumulatedColdConsum）作为冷量值
                            # 前一天
                            stat = DeviceDailyStat.objects.using(project['db_name']).filter(
                                created_at__range=[last_day_begin, last_hour_end],
                                identifier='CumulatedColdConsum',
                                mac=mac
                            )
                            cold = stat.order_by('created_at').last().avg if stat.exists() else 0
                            cold_eval_str = str(cold)

                        # 根据计算公式得出总量
                        power_cons = eval(power_eval_str) if power_eval_str != '' else 0
                        cold_cons = eval(cold_eval_str) if cold_eval_str != '' else 0

                        power = power_cons
                        cold = cold_cons

                        analyse = CsEerAnalyse(
                            cold_source_id=csid,
                            power_cons=power,
                            cold_cons=cold,
                            eer=round(cold / power, 2) if power != 0 else 0,
                            created_at=(now - datetime.timedelta(days=1)).strftime('%Y-%m-%d 23:59:59')
                        )
                        analyse.save(using=project['db_name'])


class CsEerAnalyse(models.Model):
    # 对应的冷源
    # cold_source = models.ForeignKey(ColdSource, null=True, on_delete=models.CASCADE)
    # Cs COP
    cs_cop = models.ForeignKey(CsCop, null=True, default=None, on_delete=models.CASCADE)
    # 用电量
    power_cons = models.CharField(max_length=255)
    # 用冷量
    cold_cons = models.CharField(max_length=255)
    # 冷源COP值
    value = models.CharField(max_length=255)
    # 类型。hr-每小时，di-每日
    type = models.CharField(max_length=10, default='hr')

    # 数据创建时间
    created_at = models.DateTimeField()
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'cs_eer_analyses'

    objects = CsEerAnalyseManager()


class EcMeterManager(models.Manager):
    def update_meters(self, device_id, event):
        device = Device.objects.get(pk=device_id)
        data = json.loads(event).get('data', None)
        if data is not None:
            meter_qty = device.get_value_by_idf('MeterQty')
            if meter_qty is not None and is_number(meter_qty) and int(meter_qty) > 0:
                # 默认为电表
                ec_type = 10

                if device.device_prototype.uni_name == '水表网关' or device.device_prototype.uni_name == 'DTU水表网关':
                    ec_type = 20

                if device.device_prototype.uni_name == 'DTU流量计网关':
                    ec_type = 30

                for i in range(int(meter_qty)):
                    idx = str(i + 1)
                    meter_status = ''.join(['MeterStatus_', idx])
                    if meter_status in data:
                        in_use = False
                        if data[meter_status] != '未连接':
                            in_use = True
                        name = ''.join(['仪表-', idx])

                        # 电表
                        if ec_type == 10:
                            name = device.get_value_by_idf(''.join(['Meter_NickName_', idx]))
                            if name is not None:
                                name = ''.join(['电表-', idx])
                            identifier = ''.join(['Meter_Power_Cons_', idx])

                        # 水表
                        if ec_type == 20:
                            name = device.get_value_by_idf(''.join(['Meter_', idx, '_NickName']))
                            if name is not None:
                                name = ''.join(['水表-', idx])
                            identifier = ''.join(['Meter_', idx, '_Water_Cons'])

                        # 能量表（冷量或热量）
                        if ec_type == 30:
                            name = device.get_value_by_idf(''.join(['Meter_', idx, '_NickName']))
                            if name is not None:
                                name = ''.join(['能量表-', idx])
                            identifier = ''.join(['Meter_', idx, '_Cool_Cons'])

                        meter = self.find_by(device.id, idx)
                        if name is None:
                            name = ''.join(['仪表-', idx])
                        if meter is not None:
                            meter.enabled = True
                            meter.name = name
                        else:
                            if name is None:
                                name = '仪表'
                            meter = self.create(
                                device_id=device.id,
                                name=name,
                                idx=idx,
                                enabled=in_use,
                                identifier=identifier,
                                ec_type=ec_type
                            )

                        meter.save()

    def find_by(self, device_id, idx):
        result = None
        try:
            result = self.get(device_id=device_id, idx=idx)
        except ObjectDoesNotExist:
            pass
        except MultipleObjectsReturned:
            result = self.filter(device_id=device_id, idx=idx).first()

        return result


class EcMeter(models.Model):
    """
      能耗计量表定义
    """
    # 对应设备
    device = models.ForeignKey(Device, on_delete=models.SET_NULL, null=True)
    # 昵称
    name = models.CharField(max_length=255)
    # 索引
    idx = models.IntegerField(null=True)
    # 参数标识
    identifier = models.CharField(max_length=255)
    # 是否启用
    enabled = models.BooleanField(default=False)
    # 能耗类型，10-电，20-水，30-冷，40-气，50-汽油
    ec_type = models.IntegerField()
    # 是否为虚拟仪表
    is_virtual = models.BooleanField(default=False)
    # 虚拟仪表时计算仪表能耗数的计算公式
    formula = models.TextField(null=True, blank=True)
    # 终端的id，相当于 device 和 idx 组合
    terminal = models.ForeignKey(Terminal, on_delete=models.SET_NULL, null=True)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'ec_meters'

    objects = EcMeterManager()


class ManualMeterReading(models.Model):
    """手工录入记录表"""
    # 录入数据的用户
    web_user = models.ForeignKey(WebUser, on_delete=models.SET_NULL, null=True)
    # 对应的仪表
    ec_meter = models.ForeignKey(EcMeter, on_delete=models.CASCADE)
    # 能耗标识
    identifier = models.CharField(max_length=255)
    # 仪表读数值
    value = models.CharField(max_length=255)
    # 数据录入时间
    reading_time = models.DateTimeField()
    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'manual_meter_readings'
