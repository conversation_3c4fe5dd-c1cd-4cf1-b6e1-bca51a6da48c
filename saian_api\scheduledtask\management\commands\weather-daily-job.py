"""
    项目天气日统计
"""
import datetime
import traceback

from django.core.management import BaseCommand, CommandError

from saian_api.dashboard.models import ProjectWeather
from saian_api.dashboard.tasks.tasks import computeWeatherData
from saian_api.project.models import Project
from saian_api.scheduledtask.utils import get_projects, set_global_db

class Command(BaseCommand):
    help = "项目日天气"

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目ID列表
        projects = get_projects(self, options)
        yesterday = datetime.datetime.now() - datetime.timedelta(days=1)
        begin = yesterday.strftime('%Y-%m-%d 00:00:00')
        end = yesterday.strftime('%Y-%m-%d 23:59:59')

        for project_id in projects:
            try:
                # 设置全局数据库
                set_global_db(project_id)
                project = Project.objects.get(pk=project_id)

                weathers = ProjectWeather.objects.filter(created_at__range=[begin, end], type='hr', project_id=project_id)
                weather_daily = computeWeatherData(weathers, project_id, 'di', end)

                record = ProjectWeather.objects.filter(type='di', created_at=end, project_id=project_id).last()
                if record is None:
                    weather_daily.save()
                else:
                    weather_daily.id = record.id
                    # weather_daily.save()

                self.stdout.write(f"项目：{project.name} 昨日温湿度统计完成 >>> temp: {weather_daily.temp}, humidity: {weather_daily.humidity}",
                                  ending='\n')

            except CommandError:
                self.stderr.write(f"运行'项目天气日统计'任务失败，项目ID：{project_id}, 命令参数不合法！", ending='\n')
                continue
            except Exception as e:
                self.stderr.write(f"运行'项目天气日统计'任务失败，项目ID：{project_id}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
                continue
