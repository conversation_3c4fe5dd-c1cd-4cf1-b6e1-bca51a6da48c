import logging

import requests
import urllib3
import base64
import hashlib
import os
# -*- coding: utf-8 -*-
import time
import uuid

from saian_api.utils.httpapi import DOMAIN

urllib3.disable_warnings()
# 必填,请参考"开发准备"获取如下数据,替换为实际值
# APP接入地址(在控制台"应用管理"页面获取)+接口访问URI
url = f'{DOMAIN}/sms/batchSendSms/v1'
APP_KEY = os.environ.get('DRF_SMS_KEY')
APP_SECRET = os.environ.get('DRF_SMS_SECRET')

# 选填,短信状态报告接收地址,推荐使用域名,为空或者不填表示不接收状态报告
statusCallBack = ""

'''
选填,使用无变量模板时请赋空值 TEMPLATE_PARAM = '';
模板中的每个变量都必须赋值，且取值不能为空
'''
# TEMPLATE_PARAM = '["369751"]' #模板变量，此处以单变量验证码短信为例，请客户自行生成6位验证码，并定义为字符串类型，以杜绝首位0丢失的问题（例如：002569变成了2569）。


def buildWSSEHeader(app_key, app_secret):
    """
    构造X-WSSE参数值
    @param app_key: string
    @param app_secret: string
    @return: string
    """
    now = time.strftime('%Y-%m-%dT%H:%M:%SZ')  # Created
    nonce = str(uuid.uuid4()).replace('-', '')  # Nonce
    digest = hashlib.sha256((nonce + now + app_secret).encode()).hexdigest()

    digestBase64 = base64.b64encode(digest.encode()).decode()  # PasswordDigest
    return 'UsernameToken Username="{}",PasswordDigest="{}",Nonce="{}",Created="{}"'.format(app_key, digestBase64, nonce, now)


class Sms:
    @staticmethod
    def send(sender, mobile, TEMPLATE_ID, templateParas):
        """
          向短信平台发送短信
        """
        # 请求Headers
        header = {'Authorization': 'WSSE realm="SDP",profile="UsernameToken",type="Appkey"',
                  'X-WSSE': buildWSSEHeader(APP_KEY, APP_SECRET)}
        # 请求Body
        formData = {'from': sender,
                    'to': mobile,
                    'templateId': TEMPLATE_ID,
                    'templateParas': templateParas,
                    'statusCallback': statusCallBack,
                    # 'signature': signature #使用国内短信通用模板时,必须填写签名名称
                    }
        # 为防止因HTTPS证书认证失败造成API调用失败,需要先忽略证书信任问题
        r = requests.post(url, data=formData, headers=header, verify=False)
        logging.info(f'send sms from_data: {formData} return: {r.status_code}-{r.text}')  # 打印响应信息
