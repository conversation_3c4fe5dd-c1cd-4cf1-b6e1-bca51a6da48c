import datetime
import json
import traceback

from django.core.management import BaseCommand, CommandError
from django.db.models import Q, Sum

from saian_api.devdefine.models import AttributePrototype
from saian_api.device.models import Device, ParamRecordHistory, ParamRecord, DeviceAttribute, DeviceEvent
from saian_api.dimension.models import DimensionAttribute, DimensionHourlyStat, DimensionDailyStat, DimensionMonthlyStat
from saian_api.scheduledtask.utils import get_projects, set_global_db
from saian_api.utils.db.Redis import RedisHelper
from saian_api.utils.tools import get_idx

class Command(BaseCommand):
    help = "人医热水系统 Cur 类参数计算"


    def handle(self, *args, **options):
        project_id = 102
        set_global_db(project_id)

        now = datetime.datetime.now()
        calc_threshold = now - datetime.timedelta(minutes=5)
        # 当年开始时间
        year_begin = datetime.datetime(now.year, 1, 1, 0, 0)
        # 当月开始时间
        month_begin = datetime.datetime(now.year, now.month, 1, 0, 0)
        # 当日开始时间
        day_begin = datetime.datetime(now.year, now.month, now.day, 0, 0)
        # 当前小时整点
        hour_begin = datetime.datetime(now.year, now.month, now.day, now.hour, 0)
        # 当前分钟整点
        minute_begin = datetime.datetime(now.year, now.month, now.day, now.hour, now.minute)
        # 昨日开始
        yesterday_begin = day_begin - datetime.timedelta(days=1)

        # 设备ID，根维度，水箱容量idf
        HEAT_WATER_DEVICES = [
            (25949, 10, 'CurTankCapacityTotal'),
            (25953, 14, 'CurTankCapacity'),
            (25955, 18, 'CurTankCapacity')
        ]

        for device_id, root_dimension_id, tank_cap_idf in HEAT_WATER_DEVICES:
            device = Device.objects.get(id=device_id)
            dp_id = device.device_prototype_id

            cac_das = DimensionAttribute.objects.filter(dimension__parent_id=root_dimension_id, updated_at__gte=calc_threshold).order_by('id')
            cur_aps = AttributePrototype.objects.filter(device_prototype_id=dp_id).filter(
                Q(identifier__contains='Cur') | Q(identifier__contains='Today')
            )
            cop_aps = list(AttributePrototype.objects.filter(device_prototype_id=dp_id, identifier__endswith='COP'))
            eer_aps = list(AttributePrototype.objects.filter(device_prototype_id=dp_id, identifier__endswith='EER'))

            cur_event_data = {}

            # 当前水箱容量
            if now.minute % 5 == 0:
                tank_cap_da = DeviceAttribute.objects.query_object_by_idf(device, tank_cap_idf)
                if tank_cap_da:
                    cur_event_data[tank_cap_idf] = tank_cap_da.value

            for da in cac_das:
                prefix = 'Power'
                if da.ec_type == 20:
                    if '补水' in da.name:
                        prefix = 'MT'
                elif da.ec_type == 30:
                    prefix = 'Cool'
                elif da.ec_type == 60:
                    prefix = 'Heat'
                idx = get_idx(da.identifier)
                # 当前分钟, 如果 idx 是 None，说明是制热量
                if idx is None:
                    cur_min_ap = next((ap for ap in cur_aps if
                                       prefix in ap.identifier and
                                       'CurMin' in ap.identifier and
                                       '_' not in ap.identifier), None)
                else:
                    cur_min_ap = next((ap for ap in cur_aps if
                                       prefix in ap.identifier and
                                       'CurMin' in ap.identifier and
                                       f'_{idx}' in ap.identifier), None)

                if cur_min_ap is not None:
                    cur_min_da = DeviceAttribute.objects.query_object_by_ap(device, cur_min_ap)

                    tail_index = 2
                    tail_value = None
                    tail_dt = None
                    while True:
                        last_da_value = RedisHelper.get_list_tail_item(project_id, f'dimension_attribute:{da.id}', tail_index, True)
                        if last_da_value:
                            prev_da_value = json.loads(last_da_value)
                            prev_dt = datetime.datetime.fromisoformat(prev_da_value['dt'])
                            prev_value = prev_da_value['value']
                            if (now - prev_dt).total_seconds() >= 60:
                                tail_value = prev_value
                                tail_dt = prev_dt
                                break
                            else:
                                tail_index += 1
                        else:
                            break

                    if tail_dt is not None and tail_value is not None:
                        prev_dt = tail_dt
                        prev_value = tail_value

                        diff_value = float(da.value) - float(prev_value)
                        diff_minute = (now - prev_dt).seconds / 60

                        cur_min_cons = round(diff_value / diff_minute, 4)

                        if str(cur_min_da.value) != str(cur_min_cons):
                            cur_event_data[cur_min_ap.identifier] = cur_min_da.value

                        cur_min_da.value = cur_min_cons
                        cur_min_da.updated_at = now
                        print(f'更新{da.name}-{cur_min_ap.name} value 为: {cur_min_cons}, '
                              f'redis key: recently_updated_{now.minute:02}:{device.device_prototype_id}')

                        DeviceAttribute.objects.save_to_redis(device, cur_min_da)
                        # 保存设备参的属性类型id到缓存，快照任务需要使用
                        RedisHelper.add_set(device.project_id, f'recently_updated_{now.minute:02}:{device.device_prototype_id}',
                                            cur_min_ap.id, True, 120)
                        RedisHelper.push_list(device.project_id,
                                              f'device_event_{now.strftime("%Y%m%d%H")}:{device.id}_{cur_min_ap.identifier}',
                                              cur_min_cons, True, 7200)

                # 当前小时
                if idx is None:
                    cur_hour_ap = next((ap for ap in cur_aps if
                                        prefix in ap.identifier and
                                        'CurHr' in ap.identifier and
                                        '_' not in ap.identifier), None)
                else:
                    cur_hour_ap = next((ap for ap in cur_aps if
                                        prefix in ap.identifier and
                                        ('CurHour' in ap.identifier or 'CurHr' in ap.identifier) and
                                        f'_{idx}' in ap.identifier), None)

                cur_hour_cons = 0
                if cur_hour_ap is not None:
                    cur_hour_da = DeviceAttribute.objects.query_object_by_ap(device, cur_hour_ap)
                    cur_hour_cons = da.calc_current_hour_cons()

                    if str(cur_hour_da.value) != str(cur_hour_cons):
                        cur_event_data[cur_hour_ap.identifier] = cur_hour_da.value

                    cur_hour_da.value = cur_hour_cons
                    cur_hour_da.updated_at = now
                    print(f'更新{da.name}-{cur_hour_ap.name} value 为: {cur_hour_cons}, '
                          f'redis key: recently_updated_{now.minute:02}:{device.device_prototype_id}')

                    DeviceAttribute.objects.save_to_redis(device, cur_hour_da)
                    RedisHelper.add_set(device.project_id, f'recently_updated_{now.minute:02}:{device.device_prototype_id}',
                                        cur_hour_ap.id, True, 120)
                    RedisHelper.push_list(device.project_id,
                                          f'device_event_{now.strftime("%Y%m%d%H")}:{device.id}_{cur_hour_ap.identifier}',
                                          cur_hour_cons, True, 7200)

                # 今日
                if idx is None:
                    today_ap = next((ap for ap in cur_aps if
                                     prefix in ap.identifier and
                                     'Today' in ap.identifier and
                                     '_' not in ap.identifier), None)
                else:
                    today_ap = next((ap for ap in cur_aps if
                                     prefix in ap.identifier and
                                     'Today' in ap.identifier and
                                     f'_{idx}' in ap.identifier), None)

                today_cons = 0
                if today_ap is not None:
                    today_da = DeviceAttribute.objects.query_object_by_ap(device, today_ap)
                    today_cons = cur_hour_cons
                    hourly_value = DimensionHourlyStat.objects.filter(dimension_id=da.dimension_id, dimension_attribute_id=da.id,
                                                                      created_at__gte=day_begin).aggregate(sum=Sum('avg'))['sum']
                    if hourly_value:
                        today_cons = cur_hour_cons + hourly_value
                    # if str(today_cons) != today_da.value and now.minute > 5:
                    if now.minute > 5:
                        today_cons = round(today_cons, 2)
                        if str(today_da.value) != str(today_cons):
                            cur_event_data[today_ap.identifier] = today_da.value

                        today_da.value = today_cons
                        today_da.updated_at = now
                        print(f'更新{da.name}-{today_ap.name} value 为: {today_cons}, '
                              f'redis key: recently_updated_{now.minute:02}:{device.device_prototype_id}')

                        DeviceAttribute.objects.save_to_redis(device, today_da)
                        RedisHelper.add_set(device.project_id, f'recently_updated_{now.minute:02}:{device.device_prototype_id}',
                                            today_ap.id, True, 120)
                        RedisHelper.push_list(device.project_id,
                                              f'device_event_{now.strftime("%Y%m%d%H")}:{device.id}_{today_ap.identifier}',
                                              today_cons, True, 7200)

                # 当月
                if idx is None:
                    cur_month_ap = next((ap for ap in cur_aps if
                                         prefix in ap.identifier and
                                         'Month' in ap.identifier and
                                         '_' not in ap.identifier), None)
                else:
                    cur_month_ap = next((ap for ap in cur_aps if
                                         prefix in ap.identifier and
                                         'Month' in ap.identifier and
                                         f'_{idx}' in ap.identifier), None)

                cur_month_cons = 0
                if cur_month_ap is not None:

                    cur_month_da = DeviceAttribute.objects.query_object_by_ap(device, cur_month_ap)
                    cur_month_cons = today_cons
                    daily_value = DimensionDailyStat.objects.filter(dimension_id=da.dimension_id, dimension_attribute_id=da.id,
                                                                    created_at__gte=month_begin).aggregate(sum=Sum('avg'))['sum']
                    if daily_value:
                        cur_month_cons = today_cons + daily_value
                    # if str(cur_month_cons) != cur_month_da.value and now.minute > 5:
                    if now.minute > 5:
                        cur_month_cons = round(cur_month_cons, 2)
                        if cur_month_da.value != cur_month_cons:
                            cur_event_data[cur_month_ap.identifier] = cur_month_da.value

                        cur_month_da.value = cur_month_cons
                        cur_month_da.updated_at = now
                        print(f'更新{da.name}-{cur_month_ap.name} value 为: {cur_month_cons}, '
                              f'redis key: recently_updated_{now.minute:02}:{device.device_prototype_id}')

                        DeviceAttribute.objects.save_to_redis(device, cur_month_da)
                        RedisHelper.add_set(device.project_id, f'recently_updated_{now.minute:02}:{device.device_prototype_id}',
                                            cur_month_ap.id, True, 120)
                        RedisHelper.push_list(device.project_id,
                                              f'device_event_{now.strftime("%Y%m%d%H")}:{device.id}_{cur_month_ap.identifier}',
                                              cur_month_cons, True, 7200)

                # 当年
                if idx is None:
                    cur_year_ap = next((ap for ap in cur_aps if
                                        prefix in ap.identifier and
                                        'Year' in ap.identifier and
                                        '_' not in ap.identifier), None)
                else:
                    cur_year_ap = next((ap for ap in cur_aps if
                                        prefix in ap.identifier and
                                        'Year' in ap.identifier and
                                        f'_{idx}' in ap.identifier), None)

                cur_year_cons = 0
                if cur_year_ap is not None:
                    cur_year_da = DeviceAttribute.objects.query_object_by_ap(device, cur_year_ap)
                    cur_year_cons = cur_month_cons
                    monthly_value = DimensionMonthlyStat.objects.filter(dimension_id=da.dimension_id, dimension_attribute_id=da.id,
                                                                        created_at__gte=year_begin).aggregate(sum=Sum('avg'))['sum']
                    if monthly_value:
                        cur_year_cons = cur_month_cons + monthly_value
                    # if str(cur_year_cons) != cur_year_da.value and now.minute > 5:
                    if now.minute > 5:
                        cur_year_cons = round(cur_year_cons, 2)
                        if str(cur_year_da.value) != str(cur_year_cons):
                            cur_event_data[cur_year_ap.identifier] = cur_year_da.value

                        cur_year_da.value = cur_year_cons
                        cur_year_da.updated_at = now

                        DeviceAttribute.objects.save_to_redis(device, cur_year_da)
                        RedisHelper.add_set(device.project_id, f'recently_updated_{now.minute:02}:{device.device_prototype_id}',
                                            cur_year_ap.id, True, 120)
                        RedisHelper.push_list(device.project_id,
                                              f'device_event_{now.strftime("%Y%m%d%H")}:{device.id}_{cur_year_ap.identifier}',
                                              cur_year_cons, True, 7200)

            # COP EER
            # - min
            self.update_device_cop_eer(device, now, cur_event_data, cur_aps, cop_aps, eer_aps, 'CurMinHeat', 'PowerCons_2_CurMinElectricity',
                                       'PowerCons_1_CurMinElectricity', 'CurMinCOP', 'CurMinEER', 'Min')
            # hour
            self.update_device_cop_eer(device, now, cur_event_data, cur_aps, cop_aps, eer_aps, 'CurHrHeat', 'PowerCons_2_CurHourElectricity',
                                       'PowerCons_1_CurHourElectricity', 'CurHrCOP', 'CurHrEER', 'Hr')
            # day
            self.update_device_cop_eer(device, now, cur_event_data, cur_aps, cop_aps, eer_aps, 'TodayHeat', 'PowerCons_2_TodayElectricity',
                                       'PowerCons_1_TodayElectricity', 'TodayCOP', 'TodayEER', 'Today')
            # month
            self.update_device_cop_eer(device, now, cur_event_data, cur_aps, cop_aps, eer_aps, 'CurMonthHeat', 'PowerCons_2_CurMonthElectricity',
                                       'PowerCons_1_CurMonthElectricity', 'CurMonthCOP', 'CurMonthEER', 'Month')
            # year
            self.update_device_cop_eer(device, now, cur_event_data, cur_aps, cop_aps, eer_aps, 'CurYearHeat', 'PowerCons_2_CurYearElectricity',
                                       'PowerCons_1_CurYearElectricity', 'CurYearCOP', 'CurYearEER', 'Year')

            if cur_event_data:
                DeviceEvent.objects.create(
                    device_prototype_id=device.device_prototype_id,
                    cmd=None,
                    event_type='cur_data_calc',
                    event_id=None,
                    delivery_id=None,
                    product_key=None,
                    timestamp=None,
                    ip=None,
                    latitude=None,
                    longitude=None,
                    country=None,
                    region=None,
                    data=json.dumps(cur_event_data, ensure_ascii=False),
                    did=None,
                    mac=device.mac,
                    nick_name=device.nick_name,
                    device_id=device.id,
                    project_id=device.project_id
                )
            device.save()

    @classmethod
    def update_device_cop_eer(cls, device, now, cur_event_data, cur_aps, cop_aps, eer_aps,
                              heat_key, unit_power_key, total_power_key,
                              cop_key, eer_key, label):
        """计算并保存 COP 和 EER"""
        heat_ap = next((ap for ap in cur_aps if ap.identifier == heat_key), None)
        unit_power_ap = next((ap for ap in cur_aps if ap.identifier == unit_power_key), None)
        total_power_ap = next((ap for ap in cur_aps if ap.identifier == total_power_key), None)

        if not (heat_ap and unit_power_ap and total_power_ap):
            return

        heat = DeviceAttribute.objects.query_object_by_ap(device, heat_ap)
        unit_power = DeviceAttribute.objects.query_object_by_ap(device, unit_power_ap)
        total_power = DeviceAttribute.objects.query_object_by_ap(device, total_power_ap)

        if not (heat and unit_power and total_power):
            return

        # 计算 COP / EER
        cop = round(float(heat.value) / float(unit_power.value), 2) if float(unit_power.value) else 0
        eer = round(float(heat.value) / float(total_power.value), 2) if float(total_power.value) else 0

        # 更新 COP
        cop_ap = next((ap for ap in cop_aps if ap.identifier == cop_key), None)
        if cop_ap:
            cop_da = DeviceAttribute.objects.query_object_by_ap(device, cop_ap)
            if cop_da:
                if str(cop_da.value) != str(cop):
                    cur_event_data[cop_ap.identifier] = cop

                cop_da.value = cop
                cop_da.updated_at = now
                DeviceAttribute.objects.save_to_redis(device, cop_da)
                RedisHelper.add_set(device.project_id, f'recently_updated_{now.minute:02}:{device.device_prototype_id}',
                                    cop_ap.id, True, 120)
                RedisHelper.push_list(device.project_id,
                                      f'device_event_{now.strftime("%Y%m%d%H")}:{device.id}_{cop_ap.identifier}',
                                      cop, True, 7200)

        # 更新 EER
        eer_ap = next((ap for ap in eer_aps if ap.identifier == eer_key), None)
        if eer_ap:
            eer_da = DeviceAttribute.objects.query_object_by_ap(device, eer_ap)
            if eer_da:
                if str(eer_da.value) != str(eer):
                    cur_event_data[eer_ap.identifier] = eer

                eer_da.value = eer
                eer_da.updated_at = now
                DeviceAttribute.objects.save_to_redis(device, eer_da)
                RedisHelper.add_set(device.project_id, f'recently_updated_{now.minute:02}:{device.device_prototype_id}',
                                    eer_ap.id, True, 120)
                RedisHelper.push_list(device.project_id,
                                      f'device_event_{now.strftime("%Y%m%d%H")}:{device.id}_{eer_ap.identifier}',
                                      eer, True, 7200)

        print(f'device: {device.id}-{device.nick_name}, {label}_cop: {cop}, {label}_eer: {eer}')
