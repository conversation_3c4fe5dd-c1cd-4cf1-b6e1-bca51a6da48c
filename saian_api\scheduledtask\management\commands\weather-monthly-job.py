"""
    项目天气月统计
"""
import datetime
import traceback

from django.core.management import BaseCommand, CommandError

from saian_api.dashboard.models import ProjectWeather
from saian_api.dashboard.tasks.tasks import computeWeatherData
from saian_api.project.models import Project
from saian_api.scheduledtask.utils import get_projects, set_global_db

class Command(BaseCommand):
    help = "项目月天气"

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目ID列表
        projects = get_projects(self, options)
        today = datetime.datetime.now()
        last_day_of_last_month = datetime.date(today.year, today.month, 1) - datetime.timedelta(1)
        first_day_of_last_month = datetime.date(last_day_of_last_month.year, last_day_of_last_month.month, 1)
        begin = first_day_of_last_month.strftime('%Y-%m-%d 00:00:00')
        end = last_day_of_last_month.strftime('%Y-%m-%d 23:59:59')

        for project_id in projects:
            try:
                # 设置全局数据库
                set_global_db(project_id)
                project = Project.objects.get(pk=project_id)

                weathers = ProjectWeather.objects.filter(created_at__range=[begin, end], project_id=project_id, type='di')
                weather_monthly = computeWeatherData(weathers, project_id, 'mth', end)

                record = ProjectWeather.objects.filter(type='mth', created_at=end, project_id=project_id).last()
                if record is None:
                    weather_monthly.save()
                else:
                    weather_monthly.id = record.id
                    weather_monthly.save()

                self.stdout.write(f"项目：{project.name} 上月温湿度统计完成 >>> temp: {weather_monthly.temp}, humidity: {weather_monthly.humidity}",
                                  ending='\n')

            except CommandError:
                self.stderr.write(f"运行'项目天气月统计'任务失败，项目ID：{project_id}，命令参数不合法！", ending='\n')
                continue
            except Exception as e:
                self.stderr.write(f"运行'项目天气月统计'任务失败，项目ID：{project_id}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
                continue
