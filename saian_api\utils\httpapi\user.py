import logging
from json.decoder import JSONDecodeError

import requests
from rest_framework import exceptions

from . import DOMAIN
from .base import BaseAPI

"""
  用户相关api
"""
logger = logging.getLogger('django')
class UserApi:
    def getUsers(request):
        '''
            调用设备平台接口查询项目的用户列表
        '''
        url = DOMAIN + "/saianapi/v1/web_users?prjid=11"
        r = requests.get(url, headers=BaseAPI.headers(request))
        return r.json()

    @staticmethod
    def myInfo(auth_str, project_id):
        """
            调用设备平台接口查询自己的信息
        """
        url = DOMAIN + "/saianadmin/v1/web_users/my"
        headers = {'Authorization': auth_str}
        r = requests.get(url, headers=headers)

        if r.status_code != 200:
            logger.error(f'Failed to query user info from admin3! status_code: {r.status_code}, txt: {r.text}')
            url = DOMAIN + "/saianapi/v1/web_users/my?prjid=" + str(project_id)
            r = requests.get(url, headers=headers)
            if r.status_code != 200:
                logger.error('Failed to query user info from admin!')
                raise exceptions.AuthenticationFailed('Failed to query user info from admin!')

        try:
            data = r.json()
        except JSONDecodeError:
            raise exceptions.ParseError('响应格式错误或解析json数据出错！')

        return data

    @staticmethod
    def update_profile(request):
        """
            更新用户信息
        """
        # url = (DOMAIN if settings.NGINX_DOMAIN is None else settings.NGINX_DOMAIN) + "/saianapi/v1/web_users/my"
        url = DOMAIN + "/saianapi/v1/web_users/my"
        headers = {'Authorization': request.headers['Authorization']}
        data = request.data
        r = requests.put(url, headers=headers, data=data)

        if r.status_code != 200:
            logger.error("Cannot Update User Password!")
            return None
        try:
            data = r.json()
        except(JSONDecodeError):
            logger.error('响应格式错误或解析json数据出错！')
            # raise exceptions.ParseError('响应格式错误或解析json数据出错！')
        return data
