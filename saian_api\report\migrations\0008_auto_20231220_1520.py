# Generated by Django 3.2.19 on 2023-12-20 15:20

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('report', '0007_userchart_data_name'),
    ]

    operations = [
        migrations.CreateModel(
            name='DeviceMinuteStat',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('device_id', models.BigIntegerField(db_index=True)),
                ('mac', models.CharField(db_index=True, max_length=255)),
                ('identifier', models.Char<PERSON>ield(max_length=255)),
                ('avg', models.Char<PERSON>ield(max_length=64)),
                ('created_at', models.DateTimeField()),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'device_minute_stats',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='deviceminutestat',
            index=models.Index(fields=['device_id', 'identifier'], name='device_minu_device__0b9624_idx'),
        ),
        migrations.AddIndex(
            model_name='deviceminutestat',
            index=models.Index(fields=['mac', 'identifier'], name='device_minu_mac_cc750b_idx'),
        ),
        migrations.AddIndex(
            model_name='deviceminutestat',
            index=models.Index(fields=['device_id', 'created_at'], name='device_minu_device__292e81_idx'),
        ),
        migrations.AddIndex(
            model_name='deviceminutestat',
            index=models.Index(fields=['mac', 'created_at'], name='device_minu_mac_044e94_idx'),
        ),
        migrations.AddIndex(
            model_name='deviceminutestat',
            index=models.Index(fields=['device_id', 'identifier', 'created_at'], name='device_minu_device__d771e3_idx'),
        ),
        migrations.AddIndex(
            model_name='deviceminutestat',
            index=models.Index(fields=['mac', 'identifier', 'created_at'], name='device_minu_mac_48a058_idx'),
        ),
    ]
