from datetime import datetime, timedelta

from django.contrib.contenttypes.models import ContentType
from django.core.exceptions import ObjectDoesNotExist
from rest_framework import exceptions
from django.db.models import Q
from rest_framework import status
from rest_framework import views
from rest_framework import viewsets
from rest_framework.response import Response

from saian_api.notifi.serializers import VerifiedCodeSerializer, NotifyConfigSerializer, NotifyLimitSerializer, NotificationSerializer
# 用户登陆发送验证码
from saian_api.user.models import WebUser
from saian_api.utils.utils import AuthUtils
from .models import VerifiedCode, NotifyConfig, NotifyLimit, Notification
from ..devdefine.models import DevicePrototype
from ..device.models import Device
from ..group.models import Group, GroupDevice
from ..terminal.models import Terminal
from ..user.serializers import SimpleWebUserSerializer
from ..utils.sy_jsonrenderer import SyJSONRender

# Create your views here.
class VerifiedCodeViewSet(viewsets.GenericViewSet):
    def create(self, request):
        serializer = VerifiedCodeSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        mobile = serializer.validated_data['mobile']

        VerifiedCode.objects.create_cus(mobile)

        res_data = {
            'status': status.HTTP_200_OK,
            'data': None
        }

        return Response(res_data)

class LoginVerifiedCodeView(views.APIView):
    authentication_classes = []

    def save_and_send_verified_code(self, mobile, times):
        sms_code = AuthUtils.generate_random_code()
        VerifiedCode.objects.send_verified_code(mobile, sms_code)
        verified_code = VerifiedCode(target=mobile, verified_code=sms_code, times=times)
        verified_code.save()

    def post(self, request):
        mobile = request.data.get('mobile', None)
        # 检查提交数据是否有手机号
        if mobile is None:
            return Response({
                'status': 400,
                'message': '请输入手机号码'
            }, status=status.HTTP_400_BAD_REQUEST)
        # 检查手机号是否已经注册
        if not WebUser.objects.filter(mobile=mobile).exists():
            return Response({
                'status': 40002,
                'message': '用户未注册'
            })

        mobile_sms_records = VerifiedCode.objects.filter(target=mobile).order_by('created_at')
        if mobile_sms_records.exists():
            last_code = mobile_sms_records.last()
            now = datetime.now()
            period_seconds = (now - last_code.created_at).seconds
            if period_seconds <= 60:
                # 一分钟只发送一次
                return Response({
                    'status': 40001,
                    'message': '太快了，请过会再发!'
                })
            elif period_seconds <= 1800:
                # 三十分钟内最多只能发送十次
                half_hour_ago = now - timedelta(minutes=30)
                count = VerifiedCode.objects.filter(created_at__range=(half_hour_ago, now)).count()
                if count > 10:
                    return Response({
                        'status': 40006,
                        'message': '验证码次数超限!'
                    })
                # 三十分钟内未达到限制，times 加一
                else:
                    self.save_and_send_verified_code(mobile, count + 1)
            else:
                self.save_and_send_verified_code(mobile, 1)
        else:
            self.save_and_send_verified_code(mobile, 1)
        return Response({'status': status.HTTP_200_OK})

class NotifyConfigView(viewsets.ModelViewSet):
    serializer_class = NotifyConfigSerializer
    renderer_classes = (SyJSONRender,)

    @classmethod
    def get_concern_type_model(cls, concern_type):
        target_model = None
        if concern_type == 'device':
            target_model = Device
        elif concern_type == 'terminal':
            target_model = Terminal
        elif concern_type == 'device_prototype':
            target_model = DevicePrototype
        elif concern_type == 'group':
            target_model = Group
        return target_model

    def get_queryset(self):
        queryset = NotifyConfig.objects.all()

        web_user = self.request.query_params.get('uid', None)
        if web_user is not None:
            if web_user == '.':
                queryset = queryset.filter(web_user_id=self.request.user['id'])
            else:
                queryset = queryset.filter(web_user_id__in=web_user.split(','))

        msg_type = self.request.query_params.get('msg_type', None)
        if msg_type is not None:
            queryset = queryset.filter(msg_type__in=msg_type.split(','))

        scene = self.request.query_params.get('scene', None)
        if scene is not None:
            queryset = queryset.filter(msg_type=scene)

        noti_way = self.request.query_params.get('noti_way', None)
        if noti_way is not None:
            queryset = queryset.filter(noti_way__in=noti_way.split(','))

        device_id = self.request.query_params.get('device_id', None)
        if device_id is not None:
            prefix = self.request.query_params.get('prefix', None)
            idx = self.request.query_params.get('idx', None)

            query = Q(concern_type__isnull=True)

            # device
            device_model = ContentType.objects.get_for_model(Device)
            query |= Q(concern_type=device_model, concern_id=device_id)

            # terminal
            terminal_model = ContentType.objects.get_for_model(Terminal)
            terminal = Terminal.objects.filter(device_id=device_id, prefix=prefix, idx=idx).last()
            if terminal is not None:
                query |= Q(concern_type=terminal_model, concern_id=terminal.id)

            # device_prototype
            dp_model = ContentType.objects.get_for_model(DevicePrototype)
            query |= Q(concern_type=dp_model, concern_id=Device.objects.get(pk=device_id).device_prototype_id)

            # group
            group_model = ContentType.objects.get_for_model(Group)
            device_group = GroupDevice.objects.filter(object_id=device_id, content_type=device_model).values_list('group_id', flat=True)
            query |= Q(concern_type=group_model, concern_id__in=device_group)

            queryset = queryset.filter(query)

        return queryset.order_by('-id')

    def list(self, request, *args, **kwargs):
        group_by = request.query_params.get('group_by', None)
        if group_by is None:
            data = super().list(request, *args, **kwargs).data

            return Response({
                'status': status.HTTP_200_OK,
                'notify_configs': data['results'],
                'count': data['count']
            })
        else:
            data = []
            configs = self.get_queryset()
            if group_by == 'user':
                user_list = list(set(configs.values_list('web_user_id', flat=True)))
                for uid in user_list:
                    web_user = WebUser.objects.get(pk=uid)
                    user_serializer = SimpleWebUserSerializer(web_user)
                    user_configs = configs.filter(web_user_id=uid)
                    config_serializer = self.serializer_class(user_configs, many=True, context={'group_by': group_by})
                    data.append({
                        **user_serializer.data,
                        'configs': config_serializer.data
                    })
            elif group_by == 'concern':
                concern_list = configs.values('concern_type', 'concern_id').annotate()
                concern_list = [dict(t) for t in set([tuple(d.items()) for d in concern_list])]
                for concern in concern_list:

                    if concern.get('concern_type', None) is not None:
                        concern_type = ContentType.objects.get(pk=concern.get('concern_type'))
                        concern_item = {
                            "id": concern.get('concern_id'),
                            "concern_type": concern_type.model_class().__name__,
                        }
                        try:
                            target = concern_type.model_class().objects.get(pk=concern['concern_id'])
                            if concern_type.model == 'device' or concern_type.model == 'terminal':
                                concern_item['name'] = target.nick_name
                            else:
                                concern_item['name'] = target.name
                        except ObjectDoesNotExist:
                            concern_item['name'] = None
                    else:
                        concern_item = {
                            'id': None,
                            'concern_type': None,
                            'name': None
                        }
                    concern_queryset = configs.filter(concern_type=concern.get('concern_type'), concern_id=concern.get('concern_id'))
                    concern_serializer = NotifyConfigSerializer(concern_queryset, many=True, context={'group_by': group_by})

                    data.append({
                        **concern_item,
                        "configs": concern_serializer.data,
                    })
            return Response({
                'status': status.HTTP_200_OK,
                'data': data
            })

    def partial_update(self, request, *args, **kwargs):
        concern_type = request.data.get('concern_type', None)
        # if concern_type is None:
        #     raise exceptions.ValidationError(detail={'detail': 'concern_type must exist. '})

        data = request.data
        if concern_type is not None:
            target_model = self.get_concern_type_model(concern_type)
            if target_model is not None:
                concern_type = ContentType.objects.get_for_model(target_model)
                data['concern_type'] = concern_type.id
        instance = self.get_object()
        if 'msg_types' in data:
            data['msg_type'] = data['msg_types']
        if 'noti_ways' in data:
            data['noti_way'] = data['noti_ways']
        serializer = self.get_serializer(instance, data=data, partial=True)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        # data = super().partial_update(request, *args, **kwargs).data
        return Response({
            'status': status.HTTP_200_OK,
            'notify_config': serializer.data
        })

    def create(self, request, *args, **kwargs):
        concern_type = request.data.get('concern_type', None)
        concern_id = request.data.get('concern_id', None)
        user_ids = request.data.get('user_ids', None)
        msg_types = request.data.get('msg_types', None)
        noti_ways = request.data.get('noti_ways', None)
        if user_ids is None:
            raise exceptions.ValidationError(detail={'detail': 'user_ids must exist. '})
        elif user_ids == '.':
            user_ids = f"{self.request.user['id']}"
        if msg_types is None:
            raise exceptions.ValidationError(detail={'detail': 'msg_types must exist. '})
        if noti_ways is None:
            raise exceptions.ValidationError(detail={'detail': 'noti_ways must exist. '})

        data = request.data
        if concern_type is not None:
            target_model = self.get_concern_type_model(concern_type)
            if target_model is not None:
                concern_type = ContentType.objects.get_for_model(target_model).id
                data['concern_type'] = concern_type

        targets = []

        for uid in user_ids.split(','):
            for msg_type in msg_types.split(','):
                for way in noti_ways.split(','):
                    configs = NotifyConfig.objects.filter(web_user_id=uid, msg_type=msg_type, noti_way=way, concern_type=concern_type,
                                                          concern_id=concern_id)
                    if not configs.exists():
                        o = data.copy()
                        o['msg_type'] = msg_type
                        o['web_user'] = uid
                        o['noti_way'] = way
                        targets.append(o)

        serializer = NotifyConfigSerializer(data=targets, many=True)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)

        return Response({
            'status': status.HTTP_200_OK,
            'notify_config': serializer.data
        })

    def retrieve(self, request, *args, **kwargs):
        data = super().retrieve(request, *args, **kwargs).data
        return Response({
            'status': status.HTTP_200_OK,
            'notify_config': data
        })

    def destroy(self, request, *args, **kwargs):
        super().destroy(request, *args, **kwargs)
        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })


class NotifyLimitView(viewsets.ModelViewSet):
    renderer_classes = (SyJSONRender,)
    serializer_class = NotifyLimitSerializer

    def get_queryset(self):
        queryset = NotifyLimit.objects.all()

        identifier = self.request.query_params.get('identifier', None)
        if identifier is not None:
            queryset = queryset.filter(identifier__icontains=identifier)

        ctype = self.request.query_params.get('ctype', None)
        if ctype is not None:
            target_model = NotifyConfigView.get_concern_type_model(ctype)
            if target_model is not None:
                model_type_id = ContentType.objects.get_for_model(target_model).id
                queryset = queryset.filter(concern_type=model_type_id)

                cid = self.request.query_params.get('cid', None)
                if cid is not None:
                    queryset = queryset.filter(concern_id=cid)
                    group_ids = []
                    if ctype == 'device' or ctype == 'terminal':
                        group_ids += list(GroupDevice.objects.filter(content_type_id=model_type_id, object_id=cid)
                                          .values_list('group_id', flat=True).distinct())
                        dp_id = target_model.objects.get(pk=cid).device_prototype_id
                        group_query = NotifyLimit.objects.filter(group_id__in=group_ids)
                        dp_queryset = NotifyLimit.objects.filter(concern_type=ContentType.objects.get_for_model(DevicePrototype), concern_id=dp_id)
                        queryset = queryset.union(group_query, dp_queryset)

        return queryset.order_by('-id')

    def list(self, request, *args, **kwargs):
        data = super().list(request, *args, **kwargs).data
        return Response({
            'status': status.HTTP_200_OK,
            'notify_limits': data['results'],
            'count': data['count']
        })

    def partial_update(self, request, *args, **kwargs):
        notify_limit = self.get_object()
        data = request.data

        concern_type = request.data.get('concern_type', None)
        if concern_type is not None:
            # raise exceptions.ValidationError(detail={'detail': 'concern_type must exist. '})
            target_model = NotifyConfigView.get_concern_type_model(concern_type)
            if target_model is not None:
                data['concern_type'] = ContentType.objects.get_for_model(target_model).id

        serializer = self.get_serializer(notify_limit, data=data, partial=True)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        return Response({
            'status': status.HTTP_200_OK,
            'notify_limit': serializer.data
        })

    def create(self, request, *args, **kwargs):
        concern_type = request.data.get('concern_type', None)
        if concern_type is None:
            raise exceptions.ValidationError(detail={'detail': 'concern_type must exist. '})

        data = request.data
        target_model = NotifyConfigView.get_concern_type_model(concern_type)
        if target_model is not None:
            data['concern_type'] = ContentType.objects.get_for_model(target_model).id

        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)

        return Response({
            'status': status.HTTP_200_OK,
            'notify_limit': serializer.data
        })

    def destroy(self, request, *args, **kwargs):
        super().destroy(request, *args, **kwargs)
        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def retrieve(self, request, *args, **kwargs):
        result = super().retrieve(request, *args, **kwargs).data
        return Response({
            'status': status.HTTP_200_OK,
            'notify_limit': result
        })



class NotificationView(viewsets.ModelViewSet):
    serializer_class = NotificationSerializer
    renderer_classes = (SyJSONRender,)

    def get_queryset(self):
        queryset = Notification.objects.all()

        uid = self.request.query_params.get('uid', None)
        if uid is not None:
            queryset = queryset.filter(web_user_id=uid)

        search = self.request.query_params.get('search', None)
        if search is not None:
            queryset = queryset.filter(Q(device_name__icontains=search) |
                                       Q(user_name__icontains=search) |
                                       Q(content__icontains=search) |
                                       Q(fault_name__icontains=search))

        msg_type = self.request.query_params.get('msg_type')
        if msg_type is not None:
            queryset = queryset.filter(msg_type=msg_type)

        scene = self.request.query_params.get('scene')
        if scene is not None:
            queryset = queryset.filter(msg_type=scene)

        noti_way = self.request.query_params.get('noti_way', None)
        if noti_way is not None:
            queryset = queryset.filter(noti_way__in=noti_way.split(','))

        from_at = self.request.query_params.get('from', None)
        till_at = self.request.query_params.get('till', None)

        if from_at is not None:
            from_at = datetime.strptime(from_at, '%Y%m%d%H%M%S')
            queryset = queryset.filter(created_at__gte=from_at)

        if till_at is not None:
            till_at = datetime.strptime(till_at, '%Y%m%d%H%M%S')
            queryset = queryset.filter(created_at__lte=till_at)

        return queryset.order_by('-created_at')

    def list(self, request, *args, **kwargs):
        data = super().list(request, *args, **kwargs).data
        histories = data['results']
        count = data['count']

        return Response({
            'status': status.HTTP_200_OK,
            'histories': histories,
            'count': count
        })
