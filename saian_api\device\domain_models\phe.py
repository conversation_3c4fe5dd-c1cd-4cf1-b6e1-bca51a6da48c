from saian_api.device.models import DeviceAttribute
from saian_api.issue.models import DeviceIssue
from .base import BaseDevice
from ...terminal.models import Terminal

"""
  板换
"""
class PlateTypeExchange(BaseDevice):
    @classmethod
    def update_attres(self, device, event):
        super().update_attres(device, event)

        data = event.get('data', {})
        # 系统总控处理
        if 'SystemMode' in data:
            if 'RunStatus' in data:
                run_status = data['RunStatus']
            else:
                run_status = device.get_value_by_idf('RunStatus')

            value = self.__sys_modes(data['SystemMode'], run_status)
            mode_da = DeviceAttribute.objects.get_by_idf(device, 'SystemMode')
            if mode_da is not None:
                mode_da.value = value
                mode_da.save()
                DeviceAttribute.objects.save_to_redis(device, mode_da)

        # 更新为裸数据，不需要另外处理
        raw_idfs = ['RunMode', 'SecondaryByValveRemoteStatus']

        for idf in raw_idfs:
            if idf in data:
                da = DeviceAttribute.objects.get_by_idf(device, idf)
                if da is not None:
                    da.value = data[idf]
                    da.save()
                    DeviceAttribute.objects.save_to_redis(device, da)

        # 开关机状态处理
        if 'RunStatus' in data:
            if int(data['RunStatus']) == 0:
                device.sw_on = False
            else:
                device.sw_on = True
            device.save()
            Terminal.objects.bulk_update_by_device(device, 'sw_on', device.sw_on)


    @classmethod
    def fault(self, device, event):
        self.__fault_alarm(device, event, 'Fault', ['工频故障', '变频故障'])

    @classmethod
    def alarm(self, device, event):
        self.__fault_alarm(device, event, 'Alarm', ['工频启动失败', '变频启动失败'])

    @classmethod
    def __fault_alarm(self, device, event, issue_type, issues):
        data = event.get('data', {})
        # 水泵数量
        pump_qty = device.get_value_by_idf('CPumpQty')

        if pump_qty is not None:
            if int(pump_qty) > 0:
                for i in range(int(pump_qty)):
                    idx = str(i + 1)
                    key = ''.join(['CPump_', idx, '_', issue_type, 'Type'])
                    if key in data:
                        if 1 <= int(data[key]) <= 2:
                            method = ''.join(['add_', str.lower(issue_type)])
                            getattr(DeviceIssue.objects, method)(device, issues[data[key] - 1])
                        else:
                            for issue in issues:
                                method = ''.join(['recover_', str.lower(issue_type)])
                                getattr(DeviceIssue.objects, method)(device, issue)

    # 系统总控处理
    # 1）当“系统总控”=2时，界面显示“远程”
    # 2）当“系统总控”=1时，且“板换系统运行状态”=1，界面显示“本地”
    # 3）当“系统总控”=1时，且“板换系统运行状态”=0，界面显示“停止”
    @classmethod
    def __sys_modes(self, mode, status):
        result = '2'
        if int(mode) == 0:
            result = '0'
        if int(mode) == 1 and int(status) == 1:
            result = '1'
        if int(mode) == 1 and int(status) == 0:
            result = '1'

        return result
