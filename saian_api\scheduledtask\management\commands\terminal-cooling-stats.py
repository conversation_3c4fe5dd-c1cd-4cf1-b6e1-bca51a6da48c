"""
    末端供冷概况小时统计
"""
import datetime
import json
import traceback

from django.core.management import CommandError, BaseCommand
from django.db.models import Count
from django.db.models import Q

from saian_api.project.models import Project
from saian_api.scheduledtask.utils import get_projects, set_global_db
from saian_api.terminal.models import Terminal, TerminalAttribute, TerminalCoolingStats


class Command(BaseCommand):
    help = "末端供冷概况小时统计"

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目ID列表
        projects = get_projects(self, options)
        now = datetime.datetime.now()

        for project_id in projects:
            try:
                # 设置全局数据库
                set_global_db(project_id)
                project = Project.objects.get(pk=project_id)

                self.stdout.write(f"末端供冷概况小时统计开始: {project.name}", ending='\n')

                # 判断项目是否有配置末端供冷概况统计

                project_settings = json.loads(project.settings) if project.settings else {}
                dis_termstats = project_settings.get('dis_termstats', None)

                if dis_termstats is None or not dis_termstats:
                    self.stdout.write(f"项目: {project.name} 未配置，跳过统计", ending='\n')
                    continue

                else:
                    terminals = list(Terminal.objects.filter(terminal_type=10, show_en=True, online=True, sw_on=True))
                    terminal_ids = [t.id for t in terminals]

                    tas = list(TerminalAttribute.objects.filter(
                        Q(identifier__contains='LocalTemp') | Q(identifier__contains='temperature') | Q(identifier='ReturnAirTemp') |
                        Q(identifier__endswith='RATemp') | Q(identifier__endswith='AHURATemp') | Q(identifier__endswith='_SATemp'),
                        terminal_id__in=terminal_ids))

                    cic, cuc = 0, 0

                    for terminal in terminals:
                        terminal_attrs = list(filter(lambda x: x.terminal_id == terminal.id, tas))
                        temp_tas = list(filter(lambda x: 'LocalTemp' in x.identifier or x.identifier == 'temperature', terminal_attrs))
                        if len(temp_tas) == 0:
                            temp_tas = list(filter(lambda x: x.identifier == 'ReturnAirTemp' or x.identifier.endswith('_RATemp')
                                                   or x.identifier.endswith('_EAFRATemp') or x.identifier == 'RATemp', terminal_attrs))
                        if len(temp_tas) == 0:
                            temp_tas = list(filter(lambda x: x.identifier.endswith('_SATemp'), terminal_attrs))

                        if TerminalAttribute.objects.filter(use_for=10, terminal_id=terminal.id).exists() and float(temp_tas[0].value) < 50:
                            diff = float(temp_tas[0].value) - float(TerminalAttribute.objects.get(use_for=10, terminal_id=terminal.id).value)
                            cic += diff
                            cuc += abs(diff)

                    TerminalCoolingStats.objects.create(
                        cic=round(cic, 2),
                        cuc=round(cuc, 2)
                    )
                    self.stdout.write(f"末端供冷概况小时统计任务完成: {project.name}, cic: {round(cic, 2)}, cuc: {round(cuc, 2)}", ending='\n')

            except CommandError:
                self.stderr.write(f"运行'末端供冷概况小时统计'任务失败，项目ID：{project_id}，命令参数不合法！", ending='\n')
                continue
            except Exception as e:
                self.stderr.write(f"运行'末端供冷概况小时统计'任务失败，项目ID：{project_id}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
                continue
