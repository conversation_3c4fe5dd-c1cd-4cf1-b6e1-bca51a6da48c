from saian_api.devdefine.models import AttributePrototype
from saian_api.dimension.models import Dimension, DimensionAttribute
from saian_api.terminal.models import TerminalAttribute
from saian_api.utils.utils import CeleryTaskUtils

"""
  设备日、月、年统计
"""
def do_stat(manager, data_from, begin_time, end_time):
    from saian_api.device.models import Device
    from saian_api.report.models import ReportConfigurer

    projects = CeleryTaskUtils.get_all_project()

    for project in projects:
        db = project['db_name']
        report_cfgs = ReportConfigurer.objects.using(db).filter(target_type__model='DevicePrototype')
        if report_cfgs:
            for cfg in report_cfgs:
                devices = Device.objects.using(db).filter(device_prototype_id=cfg.target_id)
                if devices:
                    for device in devices:
                        ap = AttributePrototype.objects.get_by_idf(device, cfg.identifier, db)
                        if ap is not None:
                            stats = data_from.using(db).filter(device_id=device.id, identifier=ap.identifier,
                                                               created_at__range=(begin_time, end_time))
                            values = [float(stat.avg) for stat in stats]
                            if ap.is_cum:
                                sum_value = 0 if len(values) == 0 else sum(values)
                                manager.create_cus(device, sum_value, ap, db)
                            else:
                                avg_value = 0 if len(values) == 0 else sum(values) / len(values)
                                manager.create_cus(device, avg_value, ap, db, '--' if len(values) == 0 else min(values),
                                                   '--' if len(values) == 0 else max(values))

def do_stat_cce(cus_create, data_from, begin_time, end_time, stat_type):
    # 华为云CCE定时任务的设备统计（日-di，月-mo，年-yr）
    from saian_api.device.models import Device
    from saian_api.report.models import ReportConfigurer
    report_cfgs = ReportConfigurer.objects.filter(target_type__model='DevicePrototype')

    if report_cfgs.exists():
        for cfg in report_cfgs:
            devices = Device.objects.filter(device_prototype_id=cfg.target_id)
            if devices.exists():
                for device in devices:
                    ap = AttributePrototype.objects.get_by_idf(device, cfg.identifier)
                    if ap is not None and ap.data_type == 20:
                        stats = data_from.filter(device_id=device.id, identifier=ap.identifier,
                                                 created_at__range=(begin_time, end_time))
                        # 日统计， 当天用量，当天COP，取最后一刻的数据
                        if stat_type == 'di' and 'Today' in ap.identifier and '当天' in ap.name:
                            last_stat = stats.order_by('created_at').last()
                            values = [float(last_stat.avg) if last_stat is not None else 0]
                            if ap.is_cum:
                                from saian_api.device.models import ParamRecord
                                last_pr = ParamRecord.objects.filter(created_at__lte=end_time, device_id=device.id, identifier=ap.identifier
                                                                     ).order_by('created_at').last()
                                if last_pr is not None:
                                    values = [float(last_pr.value)]
                                else:
                                    values = [sum([float(stat.avg) for stat in stats])]
                        else:
                            values = []

                            for stat in stats:
                                try:
                                    values.append(float(stat.avg))
                                except (ValueError, TypeError):
                                    continue

                        if ap.is_cum:
                            sum_value = 0 if len(values) == 0 else sum(values)
                            cus_create(device, sum_value, ap, end_time)
                        else:
                            avg_value = 0 if len(values) == 0 else sum(values) / len(values)
                            min_value = '--' if len(values) == 0 else min(values)
                            max_value = '--' if len(values) == 0 else max(values)
                            cus_create(device, avg_value, ap, end_time, min_value=min_value, max_value=max_value)


def do_dimension_stat_cce(cus_create, data_from, begin_time, end_time):
    # 华为云CCE定时任务的维度统计（日，周，月，年）
    # for dimension in Dimension.objects.all():
    for attr in DimensionAttribute.objects.filter(ta_ids__isnull=False):
        ap_ids = list(TerminalAttribute.objects.filter(id__in=attr.ta_ids.split(',')).values_list('attribute_prototype_id', flat=True))
        ap = AttributePrototype.objects.filter(id__in=ap_ids).first()
        # 维度只统计数字类型
        if ap is not None and ap.data_type == 20:
            stats = data_from.filter(dimension_id=attr.dimension_id, dimension_attribute_id=attr.id, created_at__range=(begin_time, end_time))
            values = [float(stat.avg) for stat in stats]
            if ap.is_cum:
                sum_value = 0 if len(values) == 0 else sum(values)
                cus_create(attr.dimension_id, attr.id, end_time, sum_value)
            else:
                if len(values) == 0:
                    cus_create(attr.dimension_id, attr.id, end_time, 0)
                else:
                    avg_value = sum(values) / len(values)
                    cus_create(attr.dimension_id, attr.id, end_time, avg_value, min(values), max(values))


def last_month_time_range():
    """
      上个月开始时间和结束时间
    """
    import datetime

    now = datetime.datetime.now()
    year = now.year
    current_month = now.month
    last_month = current_month - 1

    if current_month == 1:
        year = year - 1
        last_month = 12

    begin_time_str = '%s-%s-01 00:00:00' % (year, last_month)

    current_month_begin = '%s-%s-01 00:00:00' % (now.year, current_month)
    last_day = datetime.datetime.strptime(current_month_begin, '%Y-%m-%d %H:%M:%S') - datetime.timedelta(days=1)

    begin_of_last_month = datetime.datetime.strptime(begin_time_str, '%Y-%m-%d %H:%M:%S')
    end_of_last_month = last_day.strftime('%Y-%m-%d 23:59:59')

    return begin_of_last_month, end_of_last_month


def last_year_time_range():
    """
      上年开始时间和结束时间
    """
    import datetime

    year = datetime.datetime.now().year
    last_year = year - 1

    begin_time_str = '%s-01-01 00:00:00' % last_year
    end_time_str = '%s-12-31 23:59:59' % last_year

    return begin_time_str, end_time_str
