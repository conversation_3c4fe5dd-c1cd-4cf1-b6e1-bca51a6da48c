import json
from json import <PERSON><PERSON><PERSON><PERSON>ode<PERSON>rror

from rest_framework import serializers

from saian_api.device.models import Device, LivingDetection
from saian_api.report.models import DeviceHourlyStat
from saian_api.utils.httpapi.image import ImageAPI
from saian_api.utils.tools import fill_element
from .models import ColdSource, EcMeter, EcSource, ManualMeterReading, CsCop
from ..terminal.models import Terminal
from ..user.models import WebUser


class ColdSourceListSerializer(serializers.ModelSerializer):
    class Meta:
        model = ColdSource
        fields = ['id', 'coords_arr', 'uni_name', 'name', 'image', 'mac',
                  'max_hr_power_cons', 'eer_step','parent_id', 'updated_at']
        read_only_fields = ('id', 'updated_at')

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        device = Device.objects.filter(mac=ret['mac'], project_id__isnull=False).last()
        if device is not None:
            ret['device_id'] = device.id
            ret['device_name'] = device.nick_name
            ret['image'] = ImageAPI.get_url(self.context['request'], image_id=ret['image'], size='originals')
            ret['custz_detail'] = device.device_prototype.web_content is not None
            # attres = []
            # for roa in device.ro_attres:
            #     attr = roa.attribute_prototype
            #     attres.append({
            #         'identifier': attr.identifier,
            #         'unit': attr.unit,
            #         'value': roa.value
            #     })
            ret['attres'] = device.cs_attres
            ret['today_power_cons'] = instance.today_power_cons
            ret['pdevice_id'] = instance.pdevice_id
            ret['today_cool_cons'] = instance.today_cool_cons
            ret['cdevice_id'] = instance.cdevice_id

            no_coords_arr = self.context['request'].query_params.get('no_coords_arr', None)
            if no_coords_arr is None:
                ret['coords_arr'] = fill_element(instance.coords_arr)
            else:
                ret['coords_arr'] = []

        return ret


class ColdSourceDetailSerializer(serializers.ModelSerializer):
    class Meta:
        model = ColdSource
        fields = ['id', 'coords_arr', 'name', 'uni_name', 'image', 'mac',
                  'max_hr_power_cons', 'eer_step','parent_id', 'updated_at']
        read_only_fields = ('id', 'updated_at')

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        device = Device.objects.filter(mac=ret['mac'], project_id__isnull=False).last()
        if device is not None:
            ret['device_id'] = device.id
            ret['device_name'] = device.nick_name
            ret['coords_arr'] = fill_element(instance.coords_arr)
            ret['today_power_cons'] = instance.today_power_cons
            ret['today_cool_cons'] = instance.today_cool_cons

            try:
                img = json.loads(ret['image'])
                if isinstance(img, int):
                    ret['image'] = ImageAPI.get_url(self.context['request'], image_id=ret['image'], size='originals')
            except JSONDecodeError:
                pass

        return ret


class ColdSourceStatsSerializer(serializers.ModelSerializer):
    class Meta:
        model = ColdSource
        fields = ['id', 'coords_arr', 'name', 'image', 'mac',
                  'max_hr_power_cons', 'eer_step', 'updated_at']
        read_only_fields = ('id', 'updated_at')

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        device = Device.objects.filter(mac=ret['mac'], project_id__isnull=False).last()
        if device is not None:
            ret['device_id'] = device.id
            ret['name'] = device.nick_name
            ret['coords_arr'] = fill_element(instance.coords_arr)
            ret['image'] = ImageAPI.get_url(self.context['request'], image_id=ret['image'], size='originals')
            ret['today_power_cons'] = instance.today_power_cons
            ret['today_cool_cons'] = instance.today_cool_cons

        return ret

class ColdSourceAnalyseSerializer(serializers.ModelSerializer):
    class Meta:
        model = DeviceHourlyStat
        fields = '__all__'


class EcSourceModelSerializer(serializers.ModelSerializer):
    class Meta:
        model = EcSource
        fields = '__all__'
        read_only_fields = ['id']

class CsCopSerializer(serializers.ModelSerializer):
    class Meta:
        model = CsCop
        fields = '__all__'
        read_only_fields = ['id']

class EcSourceDetailSerializer(serializers.Serializer):
    # did = serializers.CharField(null=True, blank=True, max_length=200)
    value = serializers.CharField(max_length=200)
    seql = serializers.IntegerField()
    value_type = serializers.IntegerField()


class EcSourceSerializer(serializers.Serializer):
    csid = serializers.IntegerField()
    ec_type = serializers.IntegerField()


class EcMeterSerializer(serializers.ModelSerializer):
    class Meta:
        model = EcMeter
        fields = '__all__'

    def to_representation(self, instance):
        ret = super(EcMeterSerializer, self).to_representation(instance)

        if ret.get('device', None):
            device = Device.objects.get(pk=ret['device'])
            ret['device'] = {
                'id': device.id,
                'nick_name': device.nick_name
            }
        else:
            ret['device'] = None

        if ret.get('terminal', None):
            terminal = Terminal.objects.get(pk=ret['terminal'])
            ret['terminal'] = {
                'id': terminal.id,
                'nick_name': terminal.nick_name,
                'device_id': terminal.device_id
            }

        return ret


class LivingDetectionSerializer(serializers.ModelSerializer):
    class Meta:
        model = LivingDetection
        fields = ['desc', 'created_at']


class MeterReadingSerializer(serializers.ModelSerializer):
    class Meta:
        model = ManualMeterReading
        exclude = ('updated_at',)

    def to_representation(self, instance):
        ret = super(MeterReadingSerializer, self).to_representation(instance)
        user_id = ret['web_user']
        try:
            web_user = WebUser.objects.get(pk=user_id)
            ret['web_user'] = {
                'id': web_user.id,
                'name': web_user.name
            }
        except WebUser.DoesNotExist:
            ret['web_user'] = None
        return ret
