import datetime
import re

from django.core.management import BaseCommand
from django.db.models import Sum

from saian_api.dimension.models import DimensionHourlyStat, DimensionAttribute
from saian_api.report.models import DeviceHourlyStat, DeviceDailyStat
from saian_api.scheduledtask.utils import set_global_db
from saian_api.utils.tools import is_number

class Command(BaseCommand):
    help = '删除解除项目绑定的设备'

    def handle(self, *args, **options):
        project_ids = [74]
        # project_ids = settings.PROJECTS

        # 从命令行解释的项目ID列表
        for project_id in project_ids:
            print(f'project: {project_id}')
            # 设置全局数据库
            set_global_db(project_id)

            device_id = 12983
            identifier = 'Meter_Power_Cons_3'

            target_model = DeviceDailyStat

            target_time_beg = datetime.datetime.fromisoformat('2024-07-03 23:59:59')
            target_time_til = datetime.datetime.fromisoformat('2024-08-27 23:59:59')
            target_records = target_model.objects.filter(device_id=device_id, identifier=identifier,
                                                         created_at__range=[target_time_beg, target_time_til])

            refer_time_beg = datetime.datetime.fromisoformat('2024-05-03 23:59:59')
            refer_time_til = datetime.datetime.fromisoformat('2024-06-27 23:59:59')
            refer_records = target_model.objects.filter(device_id=device_id, identifier=identifier,
                                                        created_at__range=[refer_time_beg, refer_time_til])

            target_sum = target_records.aggregate(avg=Sum('avg')).get('avg')
            print(f'target_sum: {target_sum}')
            # target_sum = 21.59999999999127
            refer_values = refer_records.values_list('avg', flat=True)
            refer_sum = sum([float(v) for v in refer_values])
            if refer_sum == 0:
                continue
            proportion = [float(v) / refer_sum for v in refer_values]

            target_values = [p * target_sum for p in proportion]

            # target_values.reverse()
            for v in target_values:
                print(f'{v}')

                # print(target_values)
                # print(target_sum, sum(target_values))

            # formula_das = DimensionAttribute.objects.filter(formula__isnull=False)
            # for da in formula_das:
            #     created_at = target_time_till
            #     print(f'dimension_attribute_id: {da.id}-{da.name}')
            #     while created_at >= target_time_begin:
            #
            #         da_ids = re.findall(r'{{(.*?)}}', da.formula)
            #         da_values = []
            #         for da_id in da_ids:
            #             dhs = DimensionHourlyStat.objects.filter(created_at=created_at, dimension_attribute_id=da_id).last()
            #             # da = DimensionAttribute.objects.get(pk=da_id)
            #             da_values.append(float(dhs.avg) if dhs is not None and is_number(dhs.avg) else 0)
            #         # 构建计算表达式
            #         eval_str = da.formula
            #         for v in da_values:
            #             eval_str = re.sub(r'{{(.*?)}}', f'{v}', eval_str, 1)
            #
            #         try:
            #             value = eval(eval_str)
            #             print(f'\tcreated_at: {created_at}, value: {value}')
            #         except Exception as e:
            #             self.stdout.write(f'计算维度属性 {da.id}-{da.name} 公式时出错，表达式为: {eval_str}; ERROR: {e.__str__()}')
            #
            #         created_at = created_at - datetime.timedelta(hours=1)


            #
            # da_list = '587.999999999799 814.3864592348757 861.5887487615562 905.7061084202006'
            #
            # da_s_sum = 3169.6813164164315
            #
            # new_sum = 2893.00000000014915
            #
            # proportion = [float(da) / da_s_sum for da in da_list.split(' ')]
            #
            # for i, port in enumerate(proportion):
            #     print(proportion[i] * new_sum)
            #
