import datetime
import logging
import traceback

from django.core.management import BaseCommand, CommandError

from saian_api.coldsource.models import ColdSource
from saian_api.device.models import Device, DeviceAttribute
from saian_api.report.models import DlTask
from saian_api.report.tasks import sys_ec_report
from saian_api.scheduledtask.utils import get_projects, set_global_db

class Command(BaseCommand):
    help = '定时导出节能报告'

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目ID列表
        projects = get_projects(self, options)
        now = datetime.datetime.now()
        # now = datetime.datetime.strptime('20240516000500', '%Y%m%d%H%M%S')
        for project_id in projects:
            try:
                # 设置全局数据库
                set_global_db(project_id)

                device = None
                cs_mac = list(ColdSource.objects.values_list('mac', flat=True).distinct())
                cs_devices = Device.objects.filter(mac__in=cs_mac)
                da = None
                for cs_device in cs_devices:
                    da = DeviceAttribute.objects.query_object_by_idf(cs_device, 'CSSWStatus')
                    if da is not None:
                        device = cs_device
                        break
                if da is None:
                    logging.error(f'找不到合适的冷源!')

                yesterday = (now - datetime.timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
                from_at = yesterday.strftime('%Y%m%d%H%M%S')
                till_at = (yesterday + datetime.timedelta(days=1)).strftime('%Y%m%d%H%M%S')
                # 导出前一日节能报告
                dlt = DlTask.objects.create(
                    web_user_id=None,
                    project_id=project_id,
                    device_id=device.id,
                    data_at=yesterday,
                    status=10,
                    name=None,
                    file_path=None,
                )
                sys_ec_report(project_id, 'di', from_at, till_at, da.id, dlt.id)

                # 每月第一天，导出上个月的节能报告
                if now.day == 1:
                    dlt = DlTask.objects.create(
                        web_user_id=None,
                        project_id=project_id,
                        device_id=device.id,
                        data_at=yesterday.replace(day=1),
                        status=10,
                        name=None,
                        file_path=None,
                    )
                    sys_ec_report(project_id, 'mo', from_at, till_at, da.id, dlt.id)

            except CommandError:
                self.stderr.write(f"运行'导出节能报告'任务失败，项目ID：{project_id}，命令参数不合法！", ending='\n')
                continue
            except Exception as e:
                self.stderr.write(f"运行'导出节能报告'任务失败，项目ID：{project_id}, error: {e.__str__()}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
                continue
