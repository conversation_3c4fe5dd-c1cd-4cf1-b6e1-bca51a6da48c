from django.db import connections
from rest_framework import serializers

from saian_api import settings
from saian_api.project.models import WebMenu
from saian_api.user.models import WebUser, WebRole, UserSearches, UserLog, UserStat
from saian_api.utils.httpapi.image import ImageAPI

class WebMenuSerializer(serializers.ModelSerializer):
    class Meta:
        model = WebMenu
        fields = '__all__'

class WebRoleSerializer(serializers.ModelSerializer):
    # web_menus = WebMenuSerializer(many=True)

    class Meta:
        model = WebRole
        fields = ['id', 'project', 'name', 'en_name', 'permissions', 'web_menus', 'dashboards', 'created_at']
        # read_only_fields = ['created_at', 'updated_at']

    def to_representation(self, instance):
        ret = super(WebRoleSerializer, self).to_representation(instance)
        ret['menu_ids'] = ret['web_menus']
        return ret


class SimpleWebUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = WebUser
        fields = ['id', 'name', 'email', 'mobile']

class WebUserSerializer(serializers.ModelSerializer):
    # owner = serializers.HiddenField(
    #     default=serializers.CurrentUserDefault()
    # )

    class Meta:
        model = WebUser
        # fields = '__all__'
        exclude = ['password']

class WebUserInfoSerializer(serializers.ModelSerializer):
    web_roles = WebRoleSerializer(many=True)

    class Meta:
        model = WebUser
        fields = ['id', 'username', 'name', 'mobile', 'email', 'status', 'avatar', 'permissions', 'ec_token', 'is_super',
                  'created_at', 'updated_at', 'web_roles']

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        if len(ret['web_roles']):
            if len(ret['web_roles']) == 1:
                last_role = ret['web_roles'][0]
            else:
                last_role = ret['web_roles'][-1]
            ret['user_role'] = {
                'id': last_role.get('id'),
                'name': last_role.get('name'),
                'dashboards': last_role.get('dashboards')
            }
        else:
            ret['user_role'] = {}
        ret['roles'] = [role['en_name'] for role in ret['web_roles']]
        ret.pop('web_roles', None)

        if ret['avatar'] is not None and ret['avatar'].isdigit():
            ret['avatar'] = ImageAPI.get_url(self.context['request'], ret['avatar'], 'originals')
        return ret


class UserSearchSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserSearches
        fields = ['id', 'key_words', 'created_at']


class UserDeviceSerializer(serializers.Serializer):
    class Meta:
        model = UserSearches
        fields = ['id', 'unit_idx', 'unit_prefix', 'device_id', 'user_id']


class UserLogSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserLog
        fields = '__all__'

class UserStatSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserStat
        fields = '__all__'
