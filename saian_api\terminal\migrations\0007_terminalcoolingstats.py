# Generated by Django 3.2.19 on 2023-12-20 15:20

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('terminal', '0006_terminalattribute_use_for'),
    ]

    operations = [
        migrations.CreateModel(
            name='TerminalCoolingStats',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cic', models.CharField(max_length=255)),
                ('cuc', models.CharField(max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('terminal', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='terminal.terminal')),
            ],
            options={
                'db_table': 'terminal_cooling_stats',
                'ordering': ['-created_at'],
            },
        ),
    ]
