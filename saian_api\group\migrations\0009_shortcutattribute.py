# Generated by Django 3.2.8 on 2023-03-03 15:42

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('group', '0008_acstrategies'),
    ]

    operations = [
        migrations.CreateModel(
            name='ShortcutAttribute',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('object_id', models.PositiveBigIntegerField()),
                ('value', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('content_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('shortcut', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='group.shortcut')),
            ],
            options={
                'db_table': 'shortcut_attributes',
                'ordering': ['-created_at'],
            },
        ),
    ]
