import json
import logging
import re
from collections import Counter
from datetime import datetime, timedelta
import calendar
from itertools import chain
from json import JSONDecodeError

from django.contrib.contenttypes.models import ContentType

from saian_api.devdefine.models import AttributePrototype
from saian_api.device.models import Device, DeviceAttribute, RoomDevice, ParamRecord
from saian_api.group.models import GroupDevice
from saian_api.terminal.models import Terminal, TerminalAttribute
from saian_api.utils.httpapi.image import ImageAPI


def get_prefix(identifier):
    """
      identifier的规范为Prefix_index_xxx或Prefix_xxx_index
      根据identifier解析出prefix，并返回
    """
    try:
        prefix = identifier[0:identifier.index("_")]
    except ValueError:
        prefix = None
    return prefix

def get_idx(identifier):
    """
        identifier的规范为Prefix_index_xxx或Prefix_xxx_index
        根据identifier解析出index，并返回
    """
    match = re.search(r'_(\d+)', identifier)
    if match:
        return int(match.group(1))
    else:
        return None


def get_qty(device, prefix):
    """
      根据设备和子设备前缀得到该设备的子设备数量配置
    """
    qty = device.get_value_by_idf(str(prefix) + 'Qty')

    if qty is None:
        qty = device.get_value_by_idf(str(prefix) + '_Qty')

    if qty is None:
        qty = 0

    return int(qty)


def fill_element(coords_arr):
    """
        根据平面图（冷源、楼层）控件的铺设数据（coords_arr）
        把设备参数（identifier）的其他数据加上，如：参数值、单位等
    """
    result = None
    if coords_arr is not None:
        try:
            json_arr = json.loads(coords_arr)
            mac_list = [item['mac'] for item in json_arr if item.get('mac', None) is not None]
            identifier_list = [item['identifier'] for item in json_arr if item.get('identifier', None) is not None]

            target_devices = list(Device.objects.filter(mac__in=mac_list))
            target_terminals = list(Terminal.objects.filter(device__in=target_devices, idx__isnull=False))
            dp_ids = list(set([device.device_prototype_id for device in target_devices]))
            target_aps = list(AttributePrototype.objects.filter(device_prototype_id__in=dp_ids, identifier__in=identifier_list))
            ap_ids = [ap.id for ap in target_aps]
            # target_das = list(DeviceAttribute.objects.filter(device__in=target_devices, attribute_prototype_id__in=ap_ids))
            target_das = []
            for device in target_devices:
                target_das += DeviceAttribute.objects.query_object_list(device, ap_ids)

        except Exception as e:
            logging.error(f'解析平面图数据出错: {coords_arr}, ERROR: {e.__str__()}')
            return None
        for coord in json_arr:
            if coord['type'] != 'text' and coord['type'] != 'image':

                if coord.get('mac', None) is None:  # 没有绑定设备
                    # 判断是否有效的分组控件
                    if coord['type'] == 'table_GroupTable' and coord.get('groupId', None) and coord.get('identifiers', None):
                        values = []

                        group_id = coord['groupId']
                        identifiers = coord['identifiers']
                        flat_identifiers = list(chain(*identifiers))
                        devices = None
                        terminals = None
                        device_prototype_id = None
                        project_id = None

                        device_model = ContentType.objects.get_for_model(Device)
                        group_devices = GroupDevice.objects.filter(group_id=group_id, content_type=device_model)
                        if group_devices.exists():
                            device_ids = group_devices.values_list('object_id', flat=True)
                            devices = list(Device.objects.filter(id__in=device_ids))
                            if len(devices):
                                device_prototype_id = devices[0].device_prototype_id
                                project_id = devices[0].project_id
                        else:
                            terminal_model = ContentType.objects.get_for_model(Terminal)
                            group_devices = GroupDevice.objects.filter(group_id=group_id, object_type=terminal_model)
                            terminal_ids = group_devices.values_list('object_id', flat=True)
                            terminals = list(Terminal.objects.filter(id__in=terminal_ids))
                            if len(terminals):
                                device_prototype_id = terminals[0].device_prototype_id
                                project_id = terminals[0].device.project_id

                        if devices and device_prototype_id:
                            aps = list(AttributePrototype.objects.filter(device_prototype_id=device_prototype_id, identifier__in=flat_identifiers))

                            for obj in devices:
                                das = DeviceAttribute.objects.query_object_list(obj, [ap.id for ap in aps])

                                obj_values = []
                                obj_units = []
                                obj_names = []
                                for attrs in identifiers:
                                    attrs_values = []
                                    attrs_units = []
                                    attrs_names = []
                                    for idf in attrs:
                                        ap = next((ap for ap in aps if ap.identifier == idf), None)
                                        if ap is None:
                                            attrs_values.append(None)
                                            attrs_units.append(None)
                                            attrs_names.append(None)
                                        else:
                                            da = next((da for da in das if da.attribute_prototype_id == ap.id), None)
                                            if da is None:
                                                attrs_values.append(None)
                                            else:
                                                attrs_values.append(round(float(da.value), 2) if is_number(da.value) else da.value)
                                            attrs_units.append(ap.unit)
                                            attrs_names.append(ap.name)
                                    obj_values.append(attrs_values)
                                    obj_units.append(attrs_units)
                                    obj_names.append(attrs_names)
                                values.append({
                                    'did': obj.id,
                                    'nick_name': obj.nick_name,
                                    'online': obj.online,
                                    'sw_on': obj.sw_on,
                                    'status': obj.status,
                                    'values': obj_values,
                                    'units': obj_units,
                                    'names': obj_names
                                })
                        elif terminals and device_prototype_id:
                            aps = list(AttributePrototype.objects.filter(device_prototype_id=device_prototype_id, identifier__in=flat_identifiers))

                            for obj in terminals:
                                tas = TerminalAttribute.objects.query_object_list(obj, [ap.id for ap in aps], project_id)

                                obj_values = []
                                for attrs in identifiers:
                                    attrs_values = []
                                    for idf in attrs:
                                        ap = next((ap for ap in aps if ap.identifier == idf), None)
                                        if ap is None:
                                            attrs_values.append(None)
                                        else:
                                            ta = next((ta for ta in tas if ta.attribute_prototype_id == ap.id), None)
                                            if ta is None:
                                                attrs_values.append(None)
                                            else:
                                                attrs_values.append(round(float(ta.value), 2) if is_number(ta.value) else ta.value)
                                    obj_values.append(attrs_values)
                                values.append({
                                    'did': obj.id,
                                    'nick_name': obj.nick_name,
                                    'online': obj.online,
                                    'sw_on': obj.sw_on,
                                    'status': obj.status,
                                    'values': obj_values
                                })

                        coord['values'] = values
                    # 处理下一个元素
                    continue
                device = next(filter(lambda x: x.mac == coord['mac'], target_devices), None)

                if device is not None:
                    # 解析多参数控件
                    if coord.get('identifiers', None) is not None:
                        values = []
                        for idf in coord['identifiers']:
                            value = {
                                'data_type': 30,
                                'value': 0,
                                'unit': '',
                                'var_name': ''
                            }
                            ap = next(filter(lambda x: x.device_prototype_id == device.device_prototype_id and x.identifier == idf, target_aps), None)
                            if ap is None:
                                ap = AttributePrototype.objects.get_by_idf(device, idf)
                            if ap is not None:
                                value['data_type'] = ap.data_type
                                value['unit'] = ap.unit
                                value['var_name'] = ap.name

                                da = next(filter(lambda x: x.device_id == device.id and x.attribute_prototype_id == ap.id, target_das), None)
                                if da is None:
                                    da = device.get_attr_by_name(idf)
                                if da is not None:
                                    value['value'] = da.value
                                    try:
                                        if ap.data_type != 10:
                                            value['value'] = round(float(da.value), get_precision(ap.pre_cision)) if da.value is not None else 0

                                    except ValueError:
                                        pass
                                if idf.endswith('RemoteStatus'):
                                    if value['value'] == 1:
                                        value['value'] = '远程'
                                    elif value['value'] == 0:
                                        if idf.find('Host'):
                                            value['value'] = '手动'
                                        else:
                                            value['value'] = '本地'
                            values.append(value)
                        coord['values'] = values

                    if coord.get('identifier', None) is not None and coord['identifier'] == 'NickName':
                        coord['value'] = device.nick_name
                        coord['data_type'] = 80
                    else:
                        ap = None
                        if coord.get('identifier', None) is not None and coord['identifier'] != '':
                            # ap = AttributePrototype.objects.get_by_idf(device, coord['identifier'])
                            aps = list(filter(lambda x: x.device_prototype_id == device.device_prototype_id and x.identifier == coord['identifier'],
                                              target_aps))
                            if len(aps):
                                ap = aps[0]
                            else:
                                ap = AttributePrototype.objects.get_by_idf(device, coord['identifier'])

                            coord['data_type'] = 30
                            coord['value'] = 0
                            coord['unit'] = ""
                            coord['var_name'] = ""

                        if ap is not None:
                            coord['data_type'] = ap.data_type
                            coord['in_crement'] = ap.in_crement
                            coord['options'] = ap.options
                            # 查询设备参数的当前值
                            # value = device.get_value_by_idf(coord['identifier'])
                            das = list(filter(lambda x: x.device_id == device.id and x.attribute_prototype_id == ap.id, target_das))
                            if len(das):
                                da = das[0]
                            else:
                                da = device.get_attr_by_name(coord['identifier'])
                            if da is not None:
                                value = da.value
                                try:
                                    if ap.data_type != 10:
                                        value = round(float(value), get_precision(ap.pre_cision)) if value is not None else 0

                                except ValueError:
                                    pass
                            else:
                                value = '--'
                            coord['value'] = str(value)
                            coord["unit"] = ap.unit

                            if coord['identifier'].endswith('RemoteStatus'):
                                if coord['value'] == 1:
                                    coord['value'] = '远程'
                                elif coord['value'] == 0:
                                    if coord['identifier'].find('Host'):
                                        coord['value'] = '手动'
                                    else:
                                        coord['value'] = '本地'

                            # 下列设备不显示单位
                            if (
                                    device.device_prototype.uni_name == '柜式空调（吊顶）' or
                                    device.device_prototype.uni_name == '柜式空调' or
                                    '传感器' in device.device_prototype.uni_name
                            ):
                                coord['unit'] = ''

                            coord['var_name'] = ap.name

                            if coord['type'] == 'dailyMeterCons' and ap.is_cum is True:  # 仪表每日用量控件
                                today = datetime.now()
                                created_at = today.strftime('%Y-%m-%d 00:05:00')
                                pr = ParamRecord.objects.filter(device_id=device.id, identifier=coord['identifier'], created_at=created_at).first()
                                if pr is not None:
                                    coord['value'] = round(float(coord['value']) - float(pr.value), 2)

                    coord['status'] = device.status
                    coord['in_alarm'] = device.in_alarm
                    coord['online'] = device.online
                    coord['in_fault'] = device.in_fault
                    coord['sw_status'] = device.sw_on
                    coord['nick_name'] = device.nick_name
                    coord['device_prototype_id'] = device.device_prototype_id

                    if coord.get('identifier', None):
                        idf = coord['identifier']
                        idx = get_idx(idf)
                        if idx is not None:
                            prefix = get_prefix(idf)
                            terminals = list(filter(lambda x: x.device_id == device.id and x.idx == idx and x.prefix == prefix, target_terminals))
                            if len(terminals):
                                terminal = terminals[0]
                                coord['status'] = terminal.status
                                coord['in_alarm'] = terminal.in_alarm
                                coord['online'] = terminal.online
                                coord['in_fault'] = terminal.in_fault
                                coord['sw_status'] = terminal.sw_on
                                coord['nick_name'] = f'{terminal.nick_name}'

                    coord['address'] = device.address
                    coord['addr'] = device.address
                    coord['device_id'] = device.id

                    if device.active_room is not None:
                        coord['room_name'] = device.active_room.name

                if coord['type'] == 'image':
                    images = ImageAPI.get_url(image_id=coord['image'], size='thumb')
                    if images is not None:
                        coord['image'] = images[0].image

                elif coord['type'] == 'room-tew':  # 房间功率控件
                    room_id = coord.get('room', None)
                    if room_id and is_number(room_id):
                        room_devices = RoomDevice.objects.filter(active_room_id=room_id).select_related('active_room', 'content_type')
                        devices = [device.content_object for device in room_devices]

                        temps = []
                        power = {
                            'ac': 0,
                            'outlet': 0,
                            'light': 0
                        }
                        cons = {
                            'ac': 0,
                            'outlet': 0,
                            'light': 0
                        }
                        for obj in devices:
                            if isinstance(obj, Device):
                                pass
                            elif isinstance(obj, Terminal):
                                if obj.device_prototype_id == 489:  # 派诺分体空调
                                    # 温度
                                    temp_ta = TerminalAttribute.objects.query_object_by_idf(obj, 'temperature_real')
                                    if temp_ta is not None and temp_ta.value:
                                        temps.append(float(temp_ta.value))
                                    # 功率
                                    power_ta = TerminalAttribute.objects.query_object_by_idf(obj, 'current_power')
                                    if power_ta is not None and power_ta.value:
                                        power['ac'] = power['ac'] + float(power_ta.value)
                                    # 电表读数
                                    cons_ta = TerminalAttribute.objects.query_object_by_idf(obj, 'power_cons')
                                    if cons_ta is not None and cons_ta.value:
                                        cons['ac'] = cons['ac'] + float(cons_ta.value)

                                elif obj.device_prototype_id == 490:  # 拓强智能插座
                                    # 功率
                                    power_ta = TerminalAttribute.objects.query_object_by_idf(obj, 'Meter_W')
                                    if power_ta is not None and power_ta.value:
                                        power['outlet'] = power['outlet'] + float(power_ta.value)
                                    # 电表读数
                                    cons_ta = TerminalAttribute.objects.query_object_by_idf(obj, 'Meter_Power_Cons')
                                    if cons_ta is not None and cons_ta.value:
                                        cons['outlet'] = cons['outlet'] + float(cons_ta.value)
                                elif obj.device_prototype_id == 419:  # SPM91 电表，照明
                                    # 功率
                                    power_ta = TerminalAttribute.objects.query_object_by_idf(obj, 'Meter_W')
                                    if power_ta is not None and power_ta.value:
                                        power['light'] = power['light'] + float(power_ta.value)
                                    # 电表读数
                                    cons_ta = TerminalAttribute.objects.query_object_by_idf(obj, 'Meter_Power')
                                    if cons_ta is not None and cons_ta.value:
                                        cons['light'] = cons['light'] + float(cons_ta.value)

                        coord['value'] = {
                            'temp': round(sum(temps) / len(temps), 2) if len(temps) else 0,
                            'power': power,
                            'cons': cons
                        }

        result = json_arr

    return result

def get_today_start_and_end():
    """
      获取当天的开始时间和结束时间
    """
    import pytz, datetime

    tz = pytz.timezone('Asia/Shanghai')
    today = datetime.datetime.now(tz=tz)
    start = today.replace(hour=0, minute=0, second=0, microsecond=0)
    end = start + datetime.timedelta(1)

    return start, end

def calc_days(time_1, time_2):
    """
      计算两个时间相差的天数
      time_1, time_2为字符串
    """
    import time, datetime

    date_1 = time.strptime(time_1, '%Y-%m-%d')
    date_2 = time.strptime(time_2, '%Y-%m-%d')

    dt_1 = datetime.datetime(date_1[0], date_1[1], date_1[2])
    dt_2 = datetime.datetime(date_2[0], date_2[1], date_2[2])

    return abs((dt_2 - dt_1).days)

def validate_value(attr, value):
    """
      根据属性校验值是否符合定义
    """
    try:
        # 布尔值
        if attr.data_type == 10:
            if int(value) != 0 and int(value) != 1:
                return False

        # 数值
        if attr.data_type == 20:
            return value.isdigit()

        # 枚举值
        if attr.data_type == 30:
            return value in attr.options

    except ValueError:
        return False

    return True

def validate_week(week):
    """
      校验字符串为“周x”格式，即：周一,周二...周日的组合
    """
    full_str = '周一,周二,周三,周四,周五,周六,周日'

    week_days = week.split(',')

    for day in week_days:
        if day not in full_str:
            return False

    return True

def dict_value_to_list(dict, key):
    """
      把字典的某个参数值转为列表返回
      如：dict([{"a":1},{"a":2}])，返回：list[1,2]
    """
    return [ele[key] for ele in dict]


def verify_str(str):
    """
      校验字符串为非josn串
    """
    if '{' in str or '}' in str or '[' in str or ']' in str or ':' in str:
        return False

    return True

def int_to_16bins(value):
    """
      将整形转为16位二进制，如果不够16位则在右边补0
    """
    bins = '{0:b}'.format(int(float(value)))
    if len(bins) < 16:
        for i in range(16 - len(bins)):
            bins += '0'

    return bins

def current_cwdays():
    """将当前时间的星期转换为中文的星期，如：0-周日，1-周一"""
    cwday = '周日'
    if datetime.now().weekday() == 0:
        cwday = '周一'

    if datetime.now().weekday() == 1:
        cwday = '周二'

    if datetime.now().weekday() == 2:
        cwday = '周三'

    if datetime.now().weekday() == 3:
        cwday = '周四'

    if datetime.now().weekday() == 4:
        cwday = '周五'

    if datetime.now().weekday() == 5:
        cwday = '周六'

    return cwday

def is_int(str):
    """
        判断字符串是否为整型
    """
    try:
        if not str.isdigit():
            return False
        else:
            int(str)
        return True
    except ValueError:
        pass

    return False

def to_int(str):
    """
        数字字符串转为整形
    """
    try:
        return int(str)
    except ValueError:
        try:
            float(str)
            return int(float(str))
        except ValueError:
            return -1


def get_precision(pre_cision):
    """
        将 attribute_prototype 中的 pre_cision 转换为保留的小数位数，例: 0.001 -> 3
    """
    if pre_cision and '.' in pre_cision:
        return len(pre_cision.split('.')[1])
    return 3

def is_number(str_content):
    """判断字符串内容是否整数或浮点数"""
    try:
        float(str_content)
        return True
    except Exception as _:
        return False

def convert_to_timestamp(value):
    """把数据记录的时间转为时间戳"""
    epoch = datetime(1970, 1, 1)
    return int((value - epoch).total_seconds())

def get_day_range(dt=None):
    """获取 dt 天的开始时间和结束时间"""
    if dt is None:
        dt = datetime.now()
    if isinstance(dt, str):
        dt_begin = datetime.strptime(dt[:8], '%Y%m%d')
    else:
        dt_begin = datetime(year=dt.year, month=dt.month, day=dt.day)
    dt_end = dt_begin + timedelta(days=1) - timedelta(seconds=1)

    return dt_begin, dt_end

def get_month_range(dt=None, month_range=None):
    """获取 dt 月的开始时间和结束时间"""
    if dt is None:
        dt = datetime.now()
    if isinstance(dt, str):
        dt_begin = datetime.strptime(dt[:6], '%Y%m')
    else:
        dt_begin = datetime(year=dt.year, month=dt.month, day=1)

    if month_range is None:
        month_range = calendar.monthrange(dt_begin.year, dt_begin.month)[1]
    dt_end = dt_begin + timedelta(days=month_range) - timedelta(seconds=1)

    return dt_begin, dt_end

def get_year_range(dt=None):
    """获取 dt 年的开始时间和结束时间"""
    if dt is None:
        dt = datetime.now()
    if isinstance(dt, str):
        dt_begin = datetime.strptime(dt[:4], '%Y')
    else:
        dt_begin = datetime(year=dt.year, month=1, day=1)
    dt_end = datetime(dt_begin.year, 12, 31, 23, 59, 59)

    return dt_begin, dt_end

def getCommandMeaning(device, data_str):
    """翻译下发指令"""
    meaning = {}
    ap_cache = {}
    try:
        data = json.loads(data_str.replace("'", '"').replace(" True", " true").replace(" False", " false"))
        for k, v in data.items():
            value = v
            ap = ap_cache.get(f'{k}_{device.device_prototype_id}', None)
            if ap is None:
                ap = AttributePrototype.objects.filter(device_prototype_id=device.device_prototype_id, identifier=k).order_by('created_at').last()
                if ap is None:
                    ap = AttributePrototype.objects.get_by_idf(device, k)
                if ap is not None:
                    ap_cache[f'{k}_{device.device_prototype_id}'] = ap

            if ap is not None:
                try:
                    if ap.data_type == 30 and isinstance(v, int):
                        value = ap.options.split(',')[v]
                    elif ap.data_type == 10:
                        if ap.options and ap.options.find(',') >= 0:
                            idx = 1 if (v == '1' or v == 1) else 0
                            value = ap.options.split(',')[idx]
                except IndexError:
                    logging.error(f'日志内容转译出错：options-{ap.options}, idx-{v}')
                meaning[ap.name] = value
            else:
                meaning[k] = v
    except JSONDecodeError:
        meaning = None

    return meaning


def object_to_dict(obj) -> dict:
    """
        将对象转换为字典
    """

    result = obj.__dict__
    result.pop('_state')

    for k, v in result.items():
        if isinstance(v, datetime):
            result[k] = datetime.strftime(v, '%Y-%m-%d %H:%M:%S')

    return result

def gran_to_Str(gran):
    """把数据粒度转为字符串的形式"""
    gran_str = 'rt'

    if int(gran) == 20:
        gran_str = 'hr'

    if int(gran) == 30:
        gran_str = 'di'

    if int(gran) == 40:
        gran_str = 'wk'

    if int(gran) == 50:
        gran_str = 'mo'

    if int(gran) == 60:
        gran_str = 'min'

    if int(gran) == 70:
        gran_str = '5min'

    return gran_str

def ec_type_to_unit(ec_type):
    """能耗类型转为标准单位，10-电，20-水，30-冷，40-气，50-汽油"""
    if not ec_type:
        return None

    unit = 'kWh'

    if int(ec_type) == 20 or int(ec_type) == 40:
        unit = 'm³'

    if int(ec_type) == 30:
        unit = 'kWh'

    if int(ec_type) == 50:
        unit = 'L'

    return unit


def most_frequent(lst):
    """找出列表中最常出现的元素"""
    if not lst:
        return None  # 如果列表为空，返回 None

    counter = Counter(lst)
    max_frequency = max(counter.values())

    # 找出所有频率最高的元素
    most_frequent_elements = [key for key, count in counter.items() if count == max_frequency]

    # 返回这些元素中排在最后面的那个
    for element in reversed(lst):
        if element in most_frequent_elements:
            return element

def replenishment_time(now, time_ranges):

    time_period_list = []

    for time_item in time_ranges:
        item = {}

        from_year = now.year if time_item['from'][:4] == 'YYYY' else int(time_item['from'][:4])
        from_month = now.month if time_item['from'][5:7] == 'MM' else int(time_item['from'][5:7])
        from_day = int(time_item['from'][8:10])
        from_max_day = calendar.monthrange(from_year, from_month)[1]
        if from_max_day < from_day:
            from_month = from_month + 1
            from_day = 1
        from_at = (datetime(from_year, from_month, from_day)).strftime('%Y-%m-%d 00:00:00')
        item['from_at'] = datetime.strptime(from_at, "%Y-%m-%d %H:%M:%S")

        till_year = now.year if time_item['till'][:4] == 'YYYY' else int(time_item['from'][:4])
        till_month = now.month if time_item['till'][5:7] == 'MM' else int(time_item['till'][5:7])
        till_max_day = calendar.monthrange(till_year, till_month)[1]
        till_day = till_max_day if till_max_day < int(time_item['till'][8:10]) else int(time_item['till'][8:10])
        till_at = (datetime(till_year, till_month, till_day) + timedelta(days=1)).strftime('%Y-%m-%d 00:00:00')
        item['till_at'] = datetime.strptime(till_at, "%Y-%m-%d %H:%M:%S")

        time_period_list.append(item)
    
    return time_period_list

def is_tou_identifier(identifier: str):
    """ 判断 identifier 是否 '峰平谷尖' 用电量 """
    if not identifier:
        return False
    if identifier.endswith('_P') or identifier.endswith('_F') or identifier.endswith('_V') or identifier.endswith('_S'):
        return True
    if '_P_' in identifier or '_F_' in identifier or '_V_' in identifier or '_S_' in identifier:
        return True
    return False

def sanitize_filename(name: str) -> str:
    # 只保留中文、字母、数字、下划线、横杠，其他全部替换成 "_"
    name = re.sub(r'[^0-9A-Za-z\u4e00-\u9fff\-_]', '_', name)
    # 去掉结尾的点和空格（Windows 不允许）
    name = name.strip(" .")
    # 防止空文件名
    if not name:
        name = "_"
    # 限制长度（255 字符，常见文件系统限制）
    return name[:255]
