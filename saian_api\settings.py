"""
Django settings for saian_api project.

Generated by 'django-admin startproject' using Django 3.2.8.

For more information on this file, see
https://docs.djangoproject.com/en/3.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/3.2/ref/settings/
"""
import logging
import os
from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.environ['DRF_SECRET_KEY']

# SECURITY WARNING: don't run with debug turned on in production!
# DEBUG = os.environ.get('DRF_DEBUG', False) 无法从环境变量中正确读取设置，可能因为读到的是字符串'False'
DEBUG = False

# 如果环境变量配置了 DRF_NGINX_DOMAIN，则 myInfo 接口使用 DRF_NGINX_DOMAIN（************）
# 只有 30, 45, 169 几个服务器设置了 DRF_NGINX_DOMAIN
NGINX_DOMAIN = os.environ.get('DRF_NGINX_DOMAIN', None)

ALLOWED_HOSTS = ['localhost', '127.0.0.1', 'prj.syense.cn', 'demo.syense.cn', '*************', '10.247.*.*', '************',
                 'stage.syense.cn', 'bg.syense.cn', 'syense.cn', '4fe3-120-84-9-10.ngrok.io', '***********', '************']

ALLOWED_HOSTS += ['10.247.{}.{}'.format(i, j) for i in range(256) for j in range(256)]
# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',
    'corsheaders',
    'django_extensions',
    'saian_api.building.apps.BuildingConfig',
    'saian_api.devdefine.apps.DevdefineConfig',
    'saian_api.device.apps.DeviceConfig',
    'saian_api.group.apps.GroupConfig',
    'saian_api.intapi.apps.IntapiConfig',
    'saian_api.issue.apps.IssueConfig',
    'saian_api.linkage.apps.LinkageConfig',
    'saian_api.maint.apps.MaintConfig',
    'saian_api.message.apps.MessageConfig',
    'saian_api.project.apps.ProjectConfig',
    'saian_api.report.apps.ReportConfig',
    'saian_api.user.apps.UserConfig',
    'saian_api.coldsource.apps.ColdsourceConfig',
    'saian_api.dashboard.apps.DashboardConfig',
    'saian_api.act.apps.ActConfig',
    'saian_api.notifi.apps.NotifiConfig',
    'saian_api.regions.apps.RegionsConfig',
    'saian_api.image.apps.ImageConfig',
    'saian_api.terminal.apps.TerminalConfig',
    'saian_api.scheduledtask.apps.ScheduledtaskConfig',
    'saian_api.openapi.apps.OpenapiConfig',
    'saian_api.dimension.apps.DimensionConfig'
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    # RouterMiddleware
    'saian_api.utils.middlewares.db.RouterMiddleware',
    # RequestLogMiddleware
    'saian_api.utils.middlewares.request_log.RequestLogMiddleware',
    'saian_api.utils.middlewares.user_log.UserLogMiddleware'
]

DATABASE_ROUTERS = ['saian_api.utils.middlewares.db.DatabaseRouter']

ROOT_URLCONF = 'saian_api.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

# WSGI_APPLICATION = 'saian_api.wsgi.application'

# Database
# https://docs.djangoproject.com/en/3.2/ref/settings/#databases

# 是否本地部署
LOCAL_DEPLOYMENT = False
# 本地部署只有一个数据库，名称写在这里，需要时直接引用
LOCAL_DEPLOYMENT_DB = 'prj47db'

# 定时任务可用的项目（ projects，web_regions ）
TASK_PROJECTS = [1, 11, 25, 27, 28, 32, 40, 46, 47, 48, 58, 67, 68, 69]
# 后台接口可用的项目
PROJECTS = [1, 4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34,
            35, 36, 37, 38, 40, 42, 43, 44, 46, 47, 48, 49, 50, 58, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81,
            82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110]


def prod_db_config():
    result = {
        'default': {}
    }
    # 项目 id 列表，配置对应的数据库
    for prj in PROJECTS:
        name = f'prj{prj}db'
        db_port_key = 'DRF_DB_PORT'
        db_host_key = 'DRF_DB_HOST'
        if prj > 100:
            db_port_key = 'DRF_DB_PORT_1'
            db_host_key = 'DRF_DB_HOST_1'
        result[name] = {
            'ENGINE': 'django.db.backends.mysql',  # 数据库引擎
            'NAME': name,  # 数据库名，自己本地创建
            'USER': os.environ['DRF_DB_USER'],  # 数据库用户名
            'PASSWORD': os.environ['DRF_DB_PASSWORD'],  # 数据库密码
            'HOST': os.environ[db_host_key],  # MySQL服务所在主机IP
            'PORT': os.environ[db_port_key],  # MySQL服务端口,
            'CONN_MAX_AGE': 600
        }
    return result


def dev_db_config():
    return {
        'default': {
            'ENGINE': 'django.db.backends.mysql',  # 数据库引擎
            # 'NAME': os.environ['DRF_DB_NAME'],  # 数据库名，自己本地创建
            'NAME': 'prj11db',  # 数据库名，自己本地创建
            'USER': 'prjdbuser',  # 数据库用户名
            'PASSWORD': 'xxxxxx',  # 数据库密码ls
            'HOST': '*************',  # MySQL服务所在主机IP
            'PORT': 9600,  # MySQL服务端口
            'CONN_MAX_AGE': 600
        },
        # 'prj1db': {
        #     'ENGINE': 'django.db.backends.mysql',  # 数据库引擎
        #     # 'NAME': os.environ['DRF_DB_NAME'],  # 数据库名，自己本地创建
        #     'NAME': 'prjdb',  # 数据库名，自己本地创建
        #     'USER': os.environ['DRF_DB_USER'],  # 数据库用户名
        #     'PASSWORD': os.environ['DRF_DB_PASSWORD'],  # 数据库密码
        #     'HOST': os.environ['DRF_DB_HOST'],  # MySQL服务所在主机IP
        #     'PORT': os.environ['DRF_DB_PORT'],  # MySQL服务端口
        #     'CONN_MAX_AGE': 300
        # # },
        # 'prj27db': {
        #     'ENGINE': 'django.db.backends.mysql',  # 数据库引擎
        #     # 'NAME': os.environ['DRF_DB_NAME'],  # 数据库名，自己本地创建
        #     'NAME': 'prj27db',  # 数据库名，自己本地创建
        #     'USER': os.environ['DRF_DB_USER'],  # 数据库用户名
        #     'PASSWORD': os.environ['DRF_DB_PASSWORD'],  # 数据库密码
        #     'HOST': os.environ['DRF_DB_HOST'],  # MySQL服务所在主机IP
        #     'PORT': os.environ['DRF_DB_PORT'],  # MySQL服务端口
        #     'CONN_MAX_AGE': 300
        # }
    }


# ============== prod ===================
DATABASES = prod_db_config()
# =======================================

# ============== dev ===================
# DATABASES = dev_db_config()
# DATABASES = {
#     'default': {},
#     'prjdb': {
#         'ENGINE': 'django.db.backends.mysql',
#         'NAME': 'prjdb',
#         'USER': 'prjdbu',
#         'PASSWORD': 'abcDB@1234',
#         'HOST': '*************',
#         'PORT': '9600',
#         'CONN_MAX_AGE': 300
#     }
# }
# ======================================


# Password validation
# https://docs.djangoproject.com/en/3.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# 添加 CORS 配置
# 1. 设置白名单
CORS_ORIGIN_WHITELIST = (
    'https://127.0.0.1:8080',
    'https://localhost:8080',
    'http://localhost:9527',  # 凡是出现在白名单中的域名，都可以访问后端接口
    'http://localhost:9528',
    'https://prj.syense.cn',
    'https://stage.syense.cn',
    'https://syense.cn',
    'https://bg.syense.cn',
    'https://dev.syense.cn',
    'https://demo.syense.cn',
)
# 2. 设置 CORS Cookie
CORS_ALLOW_CREDENTIALS = True  # 指明在跨域访问中，后端是否支持对cookie的操作
# 3. 允许请求头包含 "project"
CORS_ALLOW_HEADERS = [
    "accept",
    "accept-encoding",
    "authorization",
    "content-type",
    "dnt",
    "origin",
    "user-agent",
    "x-csrftoken",
    "x-requested-with",
    "project",  # 项目ID
    "Content-disposition",  # 上传文件时需要的头部
]

# 配置图片存储路径
MEDIA_ROOT = os.path.join(BASE_DIR)

# Django 日志配置
class ShortPathFilter(logging.Filter):
    def filter(self, record):
        # 提取路径最后三级
        path_parts = record.pathname.replace("\\", "/").split("/")
        if len(path_parts) >= 3:
            record.short_path = "/".join(path_parts[-3:])
        else:
            record.short_path = record.pathname
        return True


LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': os.path.join(BASE_DIR, 'logs', 'django.log'),
            'formatter': 'verbose',
            # 使用过滤器
            'filters': ['request_info'],
        },
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
            # 使用过滤器
            'filters': ['request_info', 'shortpath_filter'],
        },
    },
    'filters': {
        # 注册该过滤器
        'request_info': {'()': 'saian_api.utils.middlewares.request_log.RequestLogFilter'},
        'shortpath_filter': {'()': 'saian_api.settings.ShortPathFilter'}
    },
    'loggers': {
        'django': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        '': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },

    },
    'formatters': {
        'verbose': {
            'format': '%(asctime)s|%(levelname)s|项目ID: %(project)s|用户ID: %(identity)s|%(method)s|DATA: %(data)s|%(pathname)s|%(funcName)s|%(lineno)s|%(message)s',
            'datefmt': "%Y-%m-%d %H:%M:%S"
        },
        'simple': {
            'format': '%(asctime)s|%(levelname)s|项目ID: %(project)s|用户ID: %(identity)s|%(method)s|%(short_path)s|%(lineno)s|%(message)s',
            'datefmt': "%Y-%m-%d %H:%M:%S"
        },
    },
}

# Internationalization
# https://docs.djangoproject.com/en/3.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'Asia/Shanghai'

USE_I18N = True

USE_L10N = True

USE_TZ = False

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.2/howto/static-files/

STATIC_URL = '/static/'

# Default primary key field type
# https://docs.djangoproject.com/en/3.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

REST_FRAMEWORK = {
    'DEFAULT_PAGINATION_CLASS': 'saian_api.utils.standard_pagination.StandardResultsSetPagination',
    'PAGE_SIZE': 20,
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'saian_api.utils.legacy_auth.LegacyAuth',
    )
}

# celery配置
# REDIS_URL = 'redis://:' + os.environ['REDIS_PSW'] + '@*************:9602/1'
REDIS_URL = os.environ['REDIS_URL']

# Email 配置
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
# EMAIL_USE_TLS = False  # 是否使用TLS安全传输协议(用于在两个通信应用程序之间提供保密性和数据完整性。)
EMAIL_USE_SSL = True  # 是否使用SSL加密，qq企业邮箱要求使用
EMAIL_HOST = '************'  # 发送邮件的邮箱 的 SMTP服务器
EMAIL_PORT = 9465  # 发件箱的SMTP服务器端口
EMAIL_HOST_USER = '<EMAIL>'  # 发送邮件的邮箱地址
EMAIL_HOST_PASSWORD = os.environ.get('DRF_EMAIL_PWD')  # 发送邮件的邮箱密码

os.environ['DJANGO_ALLOW_ASYNC_UNSAFE'] = 'true'
