"""
    指令缓存和重试
"""

import json
import traceback
import datetime
from django.db.models import Q
from django.core.management import BaseCommand, CommandError
from saian_api.utils.inthttpapi.device import DeviceApi
from saian_api.devdefine.models import AttributePrototype
from saian_api.device.models import Device, DeviceAttribute, DeviceCtrlLog
from saian_api.project.models import Project
from saian_api.scheduledtask.utils import get_projects, set_global_db
from saian_api.utils.tools import is_number

class Command(BaseCommand):
    help = "指令缓存和重试"

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目ID列表
        projects = get_projects(self, options)
        now = datetime.datetime.now()

        for project_id in projects:
            try:
                # 设置全局数据库
                set_global_db(project_id)

                # 判断项目是否开启指令缓存和重试的功能配置
                en_retry = False

                project = Project.objects.get(pk=project_id)
                if project.settings:
                    en_retry = json.loads(project.settings).get('en_retry', False)

                if en_retry:
                    collect_data_start_time = now - datetime.timedelta(hours=24)
                    queryset = DeviceCtrlLog.objects.filter((Q(retries__isnull=True) | Q(retries__gte=0)), project_id=project_id, errcode=0,
                                                            updated_at__gt=collect_data_start_time, updated_at__lte=now).order_by('-updated_at')

                    ultramodern = {}
                    device_ctrl_logs = []
                    for item in queryset:
                        data = json.loads(item.data)
                        keys = sorted(data.keys())

                        key = f'{item.device_id}_{",".join(keys)}'

                        if key not in ultramodern:
                            ultramodern[key] = 1
                            device_ctrl_logs.append(item)

                    # device_ctrl_logs = sorted((list(ultramodern.values())), key=lambda obj: obj.updated_at)
                    for log in device_ctrl_logs:
                        log_data = json.loads(log.data)

                        # 日志对应的设备是否在线
                        device = Device.objects.get(pk=log.device_id)

                        # 情况：1、设备在线 指令是否生效已定；2、设备在线 指令是否生效未定；3、设备不在线 指令是否生效已定；4、设备不在线 指令是否生效未定；
                        if device.online and log.result == '完成':
                            if (now - datetime.timedelta(minutes=5)) < log.updated_at:
                                # 距离上一次更新不足五分钟, 判断是否下发成功
                                is_success = True

                                for key, value in log_data.items():

                                    device_attribute = DeviceAttribute.objects.query_object_by_idf(device, key)

                                    if device_attribute is None:
                                        is_success = False
                                        continue

                                    ap = AttributePrototype.objects.get(pk=device_attribute.attribute_prototype_id)

                                    # 如果指令包含“立即下发参数”，则不检查重试
                                    if ap.send_immediate == 1:
                                        break

                                    # 设备参数当前值
                                    if is_number(device_attribute.value):
                                        da_value = float(device_attribute.value)
                                    else:
                                        da_value = device_attribute.value

                                    # 下发指令值
                                    if is_number(is_number(value)):
                                        log_value = float(value)
                                    else:
                                        log_value = value

                                    if da_value != log_value:
                                        is_success = False
                                        continue

                                if is_success:
                                    log.result = '成功'
                                    log.save()

                            else:
                                # 五分钟后，重试或认定失败
                                if log.retries is not None and log.retries >= 3:
                                    log.result = '重试失败'
                                    log.save()

                                elif log.retries != -1 or log.retries is None:
                                    DeviceApi.send_ctrl(device, log_data, device.mac)
                                    log.retries = (log.retries if log.retries else 0) + 1
                                    log.save()

            except CommandError:
                self.stderr.write(f"运行'指令缓存和重试'任务失败，项目ID：{project_id}，命令参数不合法！", ending='\n')
                continue
            except Exception as e:
                self.stderr.write(f"运行'指令缓存和重试'任务失败，项目ID：{project_id}, error: {e.__str__()}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
                continue
