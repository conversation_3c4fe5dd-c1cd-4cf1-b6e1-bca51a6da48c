# Generated by Django 3.2.19 on 2023-07-25 16:36

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('device', '0016_auto_20230721_1737'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='roomdevice',
            options={'ordering': ['active_room']},
        ),
        migrations.RemoveField(
            model_name='roomdevice',
            name='device',
        ),
        migrations.AddField(
            model_name='roomdevice',
            name='content_type',
            field=models.ForeignKey(default=17, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='roomdevice',
            name='object_id',
            field=models.BigIntegerField(default=9030),
            preserve_default=False,
        ),
    ]
