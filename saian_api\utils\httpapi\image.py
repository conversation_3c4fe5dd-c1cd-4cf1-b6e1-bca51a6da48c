import logging
from json.decoder import JSONDecodeError

import requests
from django.conf import settings
from rest_framework import exceptions

from . import DOMAIN
from .base import BaseAPI
from ..db.Redis import RedisHelper
from ...image.models import Image

"""
  图片相关api
"""
class ImageAPI():
    """
      根据id获取图片的路径，默认是id为619的图片，多个id以逗号隔开
      size有四种：thumb(180px), medium(360px), large(720px), original(原图)
      响应数据为json集合，如：[{"id":xxx, "image": "xxx"}]，image为图片的路径
    """
    @classmethod
    def get_urls(cls, request, image_id='619', size='medium'):
        # image_id 也有可能是图片路径字符串
        if settings.LOCAL_DEPLOYMENT:
            if image_id is None:
                image_id = '619'
            ids = image_id.split(',')
            result = []
            for id in ids:
                if id.isdigit():
                    image = Image.objects.filter(pk=int(id))
                else:
                    image_url = id
                    image = Image.objects.filter(image=image_url[1:])

                if image.exists():
                    image = image.last()
                    result.append({
                        'id': image.id,
                        'image': image.image.url
                    })
                else:
                    result.append({
                        'id': None,
                        'image': None
                    })
            return result
        else:
            if image_id is None:
                image_id = 619

            # 先从缓存查询图片地址
            image = RedisHelper.get_value(0, f'image:{size}_{image_id}', True)
            if image:
                return [{'id': image_id, 'image': image}]

            # NGINX_DOMAIN = settings.NGINX_DOMAIN
            # if NGINX_DOMAIN is None:
            url = DOMAIN + '/saianapi/v1/images?ids=[' + str(image_id) + ']&size=' + str(size)
            if request is not None:
                r = requests.get(url, headers=BaseAPI.headers(request))
            else:
                r = requests.get(url)

            if r.status_code != 200:
                logging.error(f'查询图片地址出错, image_id={image_id}')
                return None
                # raise exceptions.AuthenticationFailed('获取图片路径出错！')

            try:
                res = r.json()
                # 缓存图片地址，保留一天
                for item in res['data']:
                    RedisHelper.set_value(0, f'image:{size}_{item["id"]}', item['image'], True, 3600 * 24)

            except JSONDecodeError:
                logging.error(f'获取图片路径，响应格式错误或解析json数据出错！image_id={image_id}')
                # raise exceptions.ParseError('获取图片路径，响应格式错误或解析json数据出错！')
                return None
            except Exception as e:
                logging.error(f'获取图片路径失败: {e.__str__()}')
                return None

            return res['data']

    @classmethod
    def get_url(cls, request, image_id='619', size='medium'):
        images = cls.get_urls(request, image_id=image_id, size=size)

        if images is not None and len(images) > 0:
            return images[0]['image']
        else:
            return None
