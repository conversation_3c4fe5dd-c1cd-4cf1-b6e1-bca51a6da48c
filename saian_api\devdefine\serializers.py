from rest_framework import serializers

from .models import AttributePrototype, DeviceType, DevicePrototype, AttributeType
from ..device.models import DeviceAttribute, Device

# from ..device.serializers import DeviceSerializer


class DeviceTypeSerializer(serializers.ModelSerializer):
    parent = serializers.PrimaryKeyRelatedField(queryset=DeviceType.objects.all())

    class Meta:
        model = DeviceType
        fields = ['id', 'name', 'icon', 'uni_name', 'is_default', 'parent', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']


class DevicePrototypeSerializer(serializers.ModelSerializer):
    # device_type = serializers.PrimaryKeyRelatedField(queryset=DeviceType.objects.all())
    # parent = serializers.PrimaryKeyRelatedField(queryset=DevicePrototype.objects.all())
    # device_type = DeviceTypeSerializer()

    class Meta:
        model = DevicePrototype
        # fields = ['id', 'name', 'm_name', 'uni_name', 'skip_config', 'content', 'web_content', 'prefix', 'dashboard_attres',
        # 'detail_attres', 'icons', 'parent', 'created_at', 'updated_at', 'device_type']
        fields = ['id', 'name', 'uni_name', 'device_type_id']
        read_only_fields = ['id', 'created_at', 'updated_at']

class AttributeTypeSerializerV5(serializers.ModelSerializer):
    class Meta:
        model = AttributeType
        fields = ['id', 'name', 'uni_name', 'device_prototype_id']

class AttributeTypeSerializer(serializers.ModelSerializer):
    # device_prototype = DevicePrototypeSerializer()

    class Meta:
        model = AttributeType
        # fields = ['id', 'name', 'seq', 'hidden', 'uni_name', 'in_add', 'device_prototype']
        # read_only_fields = ['id', 'created_at', 'updated_at']
        fields = ['id', 'name', 'uni_name']
        read_only_fields = ['id']

    def to_representation(self, instance):
        ret = super().to_representation(instance)

        # attrs = instance.attributeprototype_set.filter(in_add=True, hidden=False)
        attrs = AttributePrototype.objects.filter(attribute_type_id=instance.id, in_add=True, hidden=False)
        if self.context.get('read_only', False):
            attrs = attrs.filter(read_only=True)
        attrs_fields = []
        for attr in attrs:
            attr_fields = {
                'id': attr.id,
                'name': attr.name,
                'identifier': attr.identifier,
                'data_type': attr.data_type,
                'in_crement': attr.in_crement,
                'min_value': attr.min_value,
                'max_value': attr.max_value,
                'options': attr.options,
                'default_value': attr.default_value,
                'unit': attr.unit,
                'pre_cision': attr.pre_cision,
                'remark': attr.remark,
                'value': attr.default_value
            }

            attrs_fields.append(attr_fields)

        ret['attres'] = attrs_fields

        return ret


class AttributePrototypeSerializer(serializers.ModelSerializer):
    # device_prototype = serializers.PrimaryKeyRelatedField(queryset=DevicePrototype.objects.all())
    # attribute_type = serializers.PrimaryKeyRelatedField(queryset=AttributeType.objects.all())

    class Meta:
        model = AttributePrototype
        # fields = ['id', 'name', 'identifier', 'remark', 'read_only', 'data_type', 'pre_cision',
        # 'in_crement', 'min_value', 'max_value', 'options', 'length', 'icon', 'unit', 'default_value',
        # 'seq', 'send_immediate', 'is_key', 'can_debug', 'hidden', 'in_add', 'show_in_list', 'formula',
        # 'is_cum', 'label', 'do_export', 'created_at', 'updated_at' ,'device_prototype', 'attribute_type']
        # read_only_fields = ['id', 'created_at', 'updated_at']
        fields = ['id', 'name', 'label', 'identifier', 'read_only', 'data_type', 'options', 'default_value', 'unit', 'min_value', 'max_value']
        read_only_fields = ['id']


class WebDeviceAttributeSerializer(serializers.ModelSerializer):
    class Meta:
        model = AttributePrototype
        fields = ['id', 'name', 'identifier', 'remark', 'read_only', 'data_type', 'pre_cision',
                  'in_crement', 'min_value', 'max_value', 'options', 'length', 'icon', 'unit', 'default_value',
                  'seq', 'send_immediate', 'is_key', 'can_debug', 'hidden', 'in_add', 'show_in_list', 'formula',
                  'is_cum', 'label', 'do_export', 'created_at', 'updated_at', 'device_prototype', 'attribute_type']
        read_only_fields = ['id', 'created_at', 'updated_at']

    def to_representation(self, instance):
        ret = super(WebDeviceAttributeSerializer, self).to_representation(instance)
        device_id = self.context['request'].query_params.get('device_id')
        # device_attr = DeviceAttribute.objects.filter(device_id=device_id, attribute_prototype_id=ret['id'])
        device = Device.objects.get(pk=device_id)
        device_attr = DeviceAttribute.objects.query_object_by_idf(device, ret['identifier'])
        if device_attr:
            ret['value'] = device_attr.value
        # ret['value'] = device_attr.last().value if device_attr.exists() else None
        # if device_attr.exists():
        #     ret['value'] = device_attr.last().value
        else:
            DeviceAttribute.objects.create(
                attribute_prototype_id=ret['id'],
                value=ret['default_value'],
                show_in_list=False if ret['show_in_list'] is None else ret['show_in_list'],
                device_id=device_id
            )
            ret['value'] = ret['default_value']

        return ret


class AttributeInLinkSerializer(serializers.Serializer):
    device_type_id = serializers.PrimaryKeyRelatedField(queryset=DeviceType.objects.all(), required=False)
    device_prototype_id = serializers.IntegerField(required=False)


# 简单的设备原型，只包含 ID 和 名字
class SimpleDevicePrototypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = DevicePrototype
        fields = ['id', 'name', 'uni_name']


class SimpleDeviceTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = DeviceType
        fields = ['id', 'name', 'uni_name']


class SimpleAttributePrototypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = AttributePrototype
        fields = ['id', 'identifier', 'name', 'is_cum', 'data_type', 'options']
