from django.db.models.fields import <PERSON><PERSON><PERSON><PERSON>
from rest_framework import serializers
from rest_framework.validators import UniqueValidator

from saian_api.device.models import Device
from saian_api.project.models import Project, WebPanel, WebChart, ProjectChart, ProjectPanel, WebManual, WebMenu
from saian_api.regions.models import Region
from saian_api.utils.httpapi.image import ImageAPI


class RegionInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = Region
        fields = ['id', 'name', 'short_name']


# 简单的项目信息，只包含 ID 和 名字
class SimpleProjectSerializer(serializers.ModelSerializer):
    class Meta:
        model = Project
        fields = ['id', 'name']


class ProjectSerializer(serializers.ModelSerializer):
    # id = serializers.IntegerField()
    # name = serializers.CharField(max_length=255)
    # address = serializers.CharField(max_length=255)
    # cooling_area = serializers.IntegerField()

    class Meta:
        model = Project
        fields = ['id', 'name', 'address', 'cooling_area', 'project_type', 'en_gas_sms', 'en_temhum_sms', 'images',
                  'logo', 'slogan', 'admin_region_id']
        read_only_fields = ('id',)

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        devices = Device.objects.offline_rate(instance)
        ret['offline_rate'] = devices
        ret['image'] = ret['images'][0] if len(ret['images']) > 0 else ''
        ret['project_logo'] = ret['logo']
        region_id = ret['admin_region_id']
        if region_id:
            province_id = '%s0000' % str(region_id)[0:2]
            city_id = '%s00' % str(region_id)[0:4]
            district_id = region_id
            ret['province'] = RegionInfoSerializer(Region.objects.get(pk=province_id)).data
            ret['city'] = RegionInfoSerializer(Region.objects.get(pk=city_id)).data
            ret['district'] = RegionInfoSerializer(Region.objects.get(pk=district_id)).data

        web_manuals = WebManual.objects.all()
        if web_manuals.exists():
            manual = web_manuals.order_by('created_at').last()
            manual_serializer = WebManualSerializer(manual)
            ret['user_manual'] = manual_serializer.data

        return ret


class WebMenuSerializer(serializers.ModelSerializer):
    class Meta:
        model = WebMenu
        fields = '__all__'

class WebManualSerializer(serializers.ModelSerializer):
    class Meta:
        model = WebManual
        fields = '__all__'
        read_only_fields = ('created_at',)


"""
    总览页配置    
"""
class WebChartSerializer(serializers.ModelSerializer):
    enabled = serializers.BooleanField(default=False)
    uni_name = serializers.CharField(max_length=255, validators=[UniqueValidator(queryset=WebChart.objects.all())])
    chart_type = serializers.IntegerField()
    data_src = serializers.IntegerField()
    data_params = serializers.JSONField()
    style_params = serializers.JSONField(required=False)

    class Meta:
        model = WebChart
        fields = '__all__'
        read_only_fields = ('id','created_at', 'updated_at')

    def to_representation(self, instance):
        ret = super(WebChartSerializer, self).to_representation(instance)
        ret['icon'] = ImageAPI.get_url(request=self.context['request'], image_id=ret['icon'])
        return ret

    def validate_chart_type(self, value):
        if int(value) == 10:
            raise serializers.ValidationError('不能操作系统图表！')


class WebChartSerializerSimplify(serializers.ModelSerializer):
    class Meta:
        model = WebChart
        fields = ['id', 'name', 'uni_name']
        read_only_fields = ('id', 'name', 'uni_name')

class ProjectChartWriteSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProjectChart
        fields = ['id', 'num', 'web_chart']

class ProjectChartReadSerializer(serializers.ModelSerializer):
    web_chart = WebChartSerializerSimplify()

    class Meta:
        model = ProjectChart
        fields = ['id', 'num', 'web_chart']


"""
    用户看板配置
"""
class WebPanelSerializer(serializers.ModelSerializer):
    class Meta:
        model = WebPanel
        fields = ['id', 'name', 'uni_name', 'icon', 'created_at']
        read_only_fields = ('id',)


class WebPanelSerializerSimplify(serializers.ModelSerializer):
    class Meta:
        model = WebPanel
        fields = ['id', 'name', 'uni_name']
        read_only_fields = ('id', 'name', 'uni_name')


class ProjectPanelWriteSerializer(serializers.ModelSerializer):
    web_panel_id = serializers.PrimaryKeyRelatedField(source='web_panel', queryset=WebPanel.objects.all())
    web_panel = WebPanelSerializerSimplify(read_only=True)

    class Meta:
        model = ProjectPanel
        fields = ['num', 'web_panel', 'web_panel_id']

    def to_representation(self, instance):
        ret = super(ProjectPanelWriteSerializer, self).to_representation(instance)
        ret.pop('web_panel_id')
        return ret


class ProjectPanelSerializer(serializers.ModelSerializer):
    web_panel = WebPanelSerializerSimplify()

    class Meta:
        model = ProjectPanel
        fields = ['id', 'num', 'web_panel']
        read_only_fields = fields

class WebProjectSerializer(serializers.ModelSerializer):
    class Meta:
        model = Project
        fields = ['name', 'images', 'admin_region_id', 'address', 'logo', 'slogan', 'intro']
