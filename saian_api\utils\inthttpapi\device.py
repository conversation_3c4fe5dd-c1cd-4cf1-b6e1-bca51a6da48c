import json
import logging

import requests


from . import DOMAIN
from .base import BaseAPI
from ...devdefine.models import AttributePrototype, DevicePrototype

"""
  设备相关api
"""
logger = logging.getLogger('django')


class DeviceApi:
    @staticmethod
    def send_ctrl(device, changes, mac=None):
        """
            作为第三方向设备平台发下设备指令
        """
        from ..tools import is_number
        from saian_api.device.models import Device

        if mac is None:
            mac = device.mac

        headers = BaseAPI.headers('saianapp/v1/device_ctrls')
        slave_id = 0

        # 没配置网关就发去旧框架
        if not device.gw_end_point:
            url = DOMAIN + "/saianapp/v1/device_ctrls"
            data = {
                'mac': mac,
                'changes': json.dumps(changes)
            }
        else:
            # 默认是DTU，PLC设备
            res = 'saiants/v1/dtu_controls'
            slave_id = device.get_value_by_idf("SlaveId")

            # 机智云
            if device.platform == 10:
                res = 'saiangw/v1/device_controls'

            # 派诺
            if device.platform == 20:
                res = 'saiangw/v1/pnd_controls'

            # 移动onenet
            if device.platform == 30:
                res = 'saiangw/v1/onenet_controls'

            # 阿里云
            if device.platform == 50:
                res = 'saiangw/v1/aliiot_controls'

            # 自研 + Modbus设备(需要下发功能)
            if device.platform == 40 and device.device_prototype.uni_name in {"有人IO-808", "温湿度显示屏", "SPM95", "星仪温度变送器",
                                                                              "星仪压力变送器", "VRV空调网关(DTU)"}:
                res = 'saianmbs/v2/writeRequest'
                slave_id = device.get_value_by_idf("SlaveId")

            headers = BaseAPI.headers(res)

            url = f'{device.gw_end_point}/{res}'

            trans_data = {}
            for k, v in changes.items():
                trans_data[k] = v
                ap = AttributePrototype.objects.filter(device_prototype_id=device.device_prototype_id, identifier=k).last()
                if ap is not None:
                    # 发送到网关的指令，如果是枚举类型，需要将枚举值转换为枚举序号
                    if ap.data_type == 30:
                        options = ap.options.split(',')
                        try:
                            idx = options.index(v)
                            trans_data[k] = idx
                        except Exception as e:
                            logging.error(f'下发指令枚举类型转换失败: {e.__str__()}')
                            trans_data[k] = v
                    # 如果是布尔类型，需要将值转换为 True or False
                    elif ap.data_type == 10:
                        try:
                            if isinstance(v, str) and v.isdigit():
                                v = int(v)
                            if isinstance(v, int):
                                v = bool(v)
                            trans_data[k] = v
                        except Exception as e:
                            logging.error(f'下发指令布尔值转换失败: {e.__str__()}')
                            trans_data[k] = v

            prototype = device.device_prototype.uni_name
            if slave_id != 0 and slave_id is not None:
                mac = mac[:-2]
                if Device.objects.filter(mac=mac).exists():
                    prototype = Device.objects.get(mac=mac).device_prototype.uni_name

            # 由PLC网关XS处理的设备类型, 下发处理，需要把设备类型改成PLC网关XS
            elif prototype in ['冷源XS', '三相电表V2', '南沙城冷源', '南沙城冷却塔']:
                [mac, dev_id] = device.mac.split('_', 1)
                dp = Device.objects.get(mac=mac).device_prototype
                prototype = dp.uni_name

            prd_key = ''
            if device.prd_key:
                prd_key = device.prd_key

            data = {
                'mac': mac,
                'attrs': json.dumps(trans_data),
                'prototype': prototype,
                'did': device.did if device.did else '',
                'prdKey': prd_key,
                'productKey': prd_key,
                'deviceName': device.mac,
                'deviceSecret': device.ali_secret,
                'region': device.ali_region,
                'topic': '/user/get',
                'slaveId': slave_id,
            }
            headers['Content-Type'] = 'application/json'
            headers['project'] = f'{device.project_id}'
        # logging.info(f'post to: {url}, data: {data}, headers:{headers}')
        r = requests.post(url, headers=headers, json=data)
        # logging.info(f'response: {r.status_code}')
        return r


class AdminDeviceApi:
    @staticmethod
    def get_device(pk):
        res = f'saianadmin/intapi/devices/{pk}'
        headers = BaseAPI.admin_intapi_header(res)
        url = f'{DOMAIN}/{res}'

        r = requests.get(url, headers=headers)
        return r

    @staticmethod
    def create_device(data):
        """
            向 syadmin 管理后台发送创建设备请求
        """
        headers = BaseAPI.admin_intapi_header('saianadmin/intapi/devices')
        url = DOMAIN + '/saianadmin/intapi/devices'

        r = requests.post(url, headers=headers, data=data)

        return r

    @staticmethod
    def update_device(pk, data):
        """向 syadmin 管理后台发送更新设备请求"""
        res = f'saianadmin/intapi/devices/{pk}'
        headers = BaseAPI.admin_intapi_header(res)
        url = f'{DOMAIN}/{res}'

        r = requests.put(url, headers=headers, json=data)

        return r

    @staticmethod
    def update_project_web_menus(data):
        res = f'saianadmin/intapi/project_web_menus'
        headers = BaseAPI.admin_intapi_header(res)
        headers['Content-Type'] = 'application/json'
        url = f'{DOMAIN}/{res}'

        r = requests.post(url, headers=headers, data=json.dumps(data))

        return r

    @classmethod
    def get_infrared_codes(cls, params):
        res = 'saianadmin/intapi/infrared_codes'
        headers = BaseAPI.admin_intapi_header(res)
        headers['Content-Type'] = 'application/json'
        # DOMAIN = 'https://admin3.syense.cn'
        url = f'{DOMAIN}/{res}'
        r = requests.get(url, headers=headers, params=params)

        return r
