import datetime
import logging
import json
import traceback
from collections import defaultdict

from threading import Thread

from ..device.views import DeviceTimerViewSet
from ..scheduledtask.utils import set_global_db
from django.contrib.contenttypes.models import ContentType
from django.core.exceptions import ObjectDoesNotExist
from django.db import transaction
from django.db.models import Q, Prefetch
from django.utils.module_loading import import_string
from rest_framework import status
from rest_framework import viewsets, exceptions
from rest_framework.response import Response

from saian_api.devdefine.models import AttributePrototype, DevicePrototype
from saian_api.device.models import Device, DeviceTimer, DeviceLimit
from saian_api.linkage.models import CrossAttribute, LinkageRule, LinkageTarget, LinkageTrigger
from saian_api.user.models import WebUser
from saian_api.utils.sy_jsonrenderer import SyJSONRender
from .models import (ActionLog, Group, GroupAction, ActionAttribute,
                     GroupDevice, ActionTimer, ActionTimerAttribute,
                     Shortcut, AcStrategies, ShortcutAttribute)
from .serializers import (
    GroupModelSerializer,
    GroupSerializer,
    ActionTimerModelSerializer,
    ActionTimerSerializer,
    GroupActionModelSerializer,
    ShortcutSerializer,
    ActionLogsSerializer,
    AcStrategiesSerializer
)
from ..terminal.models import Terminal
from ..utils.db.Redis import RedisHelper
from ..utils.httpapi.image import ImageAPI
from ..utils.tools import is_number

# Create your views here.


class GroupModelViewSet(viewsets.ModelViewSet):
    serializer_action_class = {
        'list': GroupModelSerializer,
        'create': GroupSerializer,
        'retrieve': GroupModelSerializer,
        'partial_update': GroupSerializer,
        'destroy': GroupModelSerializer
    }
    renderer_classes = (SyJSONRender,)

    def get_object(self):
        try:
            group = Group.objects.get(
                pk=self.kwargs['pk'],
                # web_user_id=self.request.user['id'],
                project_id=self.request.user['project_id']
            )
        except ObjectDoesNotExist:
            raise exceptions.NotFound('Group not found!')

        return group

    # 根据http的action选择serializer
    def get_serializer_class(self):
        try:
            return self.serializer_action_class[self.action]
        except (KeyError, AttributeError):
            return super().get_serializer_class()

    def get_queryset(self):
        queryset = Group.objects.filter(Q(web_user_id=self.request.user['id']) | Q(shared=True), project_id=self.request.user['project_id'])

        device_id = self.request.query_params.get('device_id', None)
        if device_id is not None:
            device_model = ContentType.objects.get_for_model(Device)
            group_ids = GroupDevice.objects.filter(object_id=device_id, content_type=device_model).values_list('group_id', flat=True)
            queryset = queryset.filter(id__in=group_ids)

        terminal_id = self.request.query_params.get('terminal_id', None)
        if terminal_id is not None:
            terminal_model = ContentType.objects.get_for_model(Terminal)
            group_ids = GroupDevice.objects.filter(object_id=terminal_id, content_type=terminal_model).values_list('group_id', flat=True)
            queryset = queryset.filter(id__in=group_ids)

        search = self.request.query_params.get('search', None)
        if search is not None:
            queryset = queryset.filter(name__contains=search)

        return queryset.order_by('-created_at')

    def list(self, request, *args, **kwargs):
        user_cache = {}
        # groups = super(GroupModelViewSet, self).list(request, *args, **kwargs).data
        is_cross = request.query_params.get('is_cross', None)
        queryset = self.get_queryset()
        count = queryset.count()

        if is_cross is None:
            queryset = self.paginate_queryset(queryset)
        groups_data = self.get_serializer_class()(queryset, many=True, context={'is_cross': is_cross}).data
        groups = []
        web_users = list(WebUser.objects.filter(id__in=[g['web_user'] for g in groups_data]))

        for group in groups_data:
            if group is None:
                continue
            try:
                creator_id = group.get('web_user')
                creator = user_cache.get(creator_id, None)
                if creator is None:
                    web_user = next(filter(lambda x: x.id == creator_id, web_users), None)
                    if web_user is None:
                        web_user = WebUser.objects.get(pk=creator_id)

                    avatar = ImageAPI.get_url(request, web_user.avatar, size='thumb') if is_number(web_user.avatar) else web_user.avatar
                    creator = {
                        'id': web_user.id,
                        'name': web_user.name,
                        'avatar': avatar,
                        'web': {
                            'id': web_user.id,
                            'name': web_user.name,
                            'avatar': avatar
                        }
                    }

                    # 小程序用户
                    unionid = web_user.unionid
                    project_id = request.user['project_id']

                    mini_user = WebUser.objects.get_mini_user(unionid, project_id)

                    if mini_user:
                        if mini_user.get('id', None):
                            creator['id'] = mini_user['id']
                        if mini_user.get('avatar', None):
                            creator['avatar'] = mini_user['avatar']
                        if mini_user.get('mini_username', None):
                            creator['name'] = mini_user['mini_username']

                    user_cache[creator_id] = creator

                group['creator'] = creator

                groups.append(group)
            except Exception as e:
                logging.error(f'处理分组创建者信息时出错: {e.__str__()}, {e.__cause__}')
                traceback.print_exc()

        if is_cross is not None:
            count = len(groups)
            per_page = self.request.query_params.get('per_page', 200)
            page = self.request.query_params.get('page', 1)
            if per_page is not None and page is not None:
                page = int(page)
                per_page = int(per_page)
                groups = groups[(page - 1) * per_page: (page - 1) * per_page + per_page]

        res_data = {
            "count": count,
            'groups': groups
        }

        return Response(res_data)

    def create(self, request, *args, **kwargs):
        serializer = GroupSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        project_id = request.user.get('project_id')

        with transaction.atomic(using=f'prj{project_id}db'):
            shared = False
            if serializer.validated_data.get('shared', None) is not None:
                shared = serializer.validated_data['shared']
            group_serializer = GroupModelSerializer(data={
                'name': serializer.validated_data['name'],
                'project': self.request.user['project_id'],
                'web_user': self.request.user['id'],
                'shared': shared
            })

            if group_serializer.is_valid(raise_exception=True):
                group = group_serializer.save()

            device_ids = request.data.get('device_ids', None)
            terminal_ids = request.data.get('terminal_ids', None)
            dp_ids = request.data.get('dp_ids', None)
            actions = request.data.get('actions', None)

            # 分组成员，可以是设备，可以是终端，也可以是设备类型
            if device_ids is not None:
                for did in device_ids:
                    GroupDevice.objects.create(group=group, content_object=Device.objects.get(pk=did))

            elif terminal_ids is not None:
                for tid in terminal_ids:
                    GroupDevice.objects.create(group=group, content_object=Terminal.objects.get(pk=tid))

            else:
                if dp_ids is not None:
                    for dpid in dp_ids:
                        GroupDevice.objects.create(group=group, content_object=DevicePrototype.objects.get(pk=dpid))
                else:
                    raise exceptions.ValidationError('DP or Device is required!')

            # 分组操作
            if actions is not None:
                for action in actions:
                    if action['attr_ids'] is not None:
                        ga = GroupAction.objects.create(group=group, name=action['name'])

                        for attr_id in action['attr_ids']:
                            # 跨类型属性
                            if dp_ids is not None and len(dp_ids) > 1:
                                cross_attr = CrossAttribute.objects.get(pk=attr_id)
                                ActionAttribute.objects.create(group_action=ga, content_object=cross_attr)

                            # 单个设备类型
                            if dp_ids is not None and len(dp_ids) == 1:
                                ap = AttributePrototype.objects.get(pk=attr_id)
                                ActionAttribute.objects.create(group_action=ga, content_object=ap)
                    else:
                        raise exceptions.ValidationError('Attr_ids is required for group action!')

        res_data = {
            'status': status.HTTP_200_OK,
            'group': {
                'id': group.id,
                'name': serializer.data['name'],
                'shared': group.shared
            }
        }

        return Response(res_data)

    def partial_update(self, request, *args, **kwargs):
        group = self.get_object()

        # serializer = GroupSerializer(data=request.data)
        # serializer.is_valid(raise_exception=True)
        project_id = request.user['project_id']
        name = request.data.get('name', None)
        shared = request.data.get('shared', None)
        device_ids = request.data.get('device_ids', None)
        terminal_ids = request.data.get('terminal_ids', None)
        dp_ids = request.data.get('dp_ids', None)
        actions = request.data.get('actions', None)

        with transaction.atomic(using=f'prj{project_id}db'):
            group_data = {}
            if name is not None:
                group_data['name'] = name

            if shared is not None:
                group_data['shared'] = shared

            groupmodel_serializer = GroupModelSerializer(group, data=group_data, partial=True)

            if groupmodel_serializer.is_valid(raise_exception=True):
                groupmodel_serializer.save()

            if device_ids is not None:
                group_data['device_ids'] = device_ids

            if terminal_ids is not None:
                group_data['terminal_ids'] = terminal_ids

            if dp_ids is not None:
                group_data['dp_ids'] = dp_ids

            if actions is not None:
                group_data['actions'] = actions

            group_serializer = GroupSerializer(data=group_data)
            group_serializer.is_valid(raise_exception=True)

            # 分组成员类型，设备或设备类型
            if group.groupdevice_set.all().exists():
                group_member_type = group.groupdevice_set.all()[0].content_type
                group_member_object = group.groupdevice_set.all()[0].content_object
            else:
                group_member_type = None
                group_member_object = None
            # 分组成员为设备
            member_ids = []
            # 如果 group 内没有绑定成员，则以 device_ids 或 dp_ids 区分分组成员类型
            if isinstance(group_member_object, Device) or device_ids is not None:
                if device_ids is not None:
                    member_ids = device_ids
                    member_model = import_string('saian_api.device.models.Device')

            elif isinstance(group_member_object, Terminal) or terminal_ids is not None:
                if terminal_ids is not None:
                    member_ids = terminal_ids
                    member_model = import_string('saian_api.terminal.models.Terminal')

            # 分组成员为设备类型
            elif isinstance(group_member_object, DevicePrototype) or dp_ids is not None:
                if dp_ids is not None:
                    member_ids = dp_ids
                    member_model = import_string('saian_api.devdefine.models.DevicePrototype')

            existing_member_ids = list(group.groupdevice_set.all().values_list('object_id', flat=True))

            # 待移除出分组的成员id
            deleting_ids = [id for id in existing_member_ids if id not in member_ids]

            # 待添加到分组的设成员id
            adding_ids = [id for id in member_ids if id not in existing_member_ids]

            # 删除旧成员
            deleting_objects = GroupDevice.objects.filter(group=group, object_id__in=deleting_ids)
            deleting_objects.delete()

            # 添加新成员
            for member_id in adding_ids:
                try:
                    member = member_model.objects.get(pk=member_id)
                    GroupDevice.objects.create(group=group, content_object=member)
                except ObjectDoesNotExist:
                    # 某个不存在的成员，不影响其他的成员的处理
                    logging.warning('Member not exist id is %s, type is %s' % (member_id, group_member_type.name))

            # 分组操作
            if actions is not None:
                for action in actions:
                    if action['action_id'] is not None:
                        try:
                            ga = GroupAction.objects.get(group=group, pk=action['action_id'])
                            if action['name'] is not None:
                                ga.partial_update(name=action['name'])
                            if action['attr_ids'] is not None:
                                attr_object = ga.actionattribute_set.all()[0].content_object
                                attr_type = ga.actionattribute_set.all()[0].content_type

                                # 跨类型属性
                                if isinstance(attr_object, CrossAttribute):
                                    attr_model = import_string('saian_api.linkage.models.CrossAttribute')

                                # 单个设备类型
                                if isinstance(attr_object, AttributePrototype):
                                    attr_model = import_string('saian_api.devdefine.models.AttributePrototype')

                                existing_attr_ids = ga.actionattribute_set.all().values_list('object_id', flat=True)

                                deleting_attr_ids = [id for id in existing_attr_ids if id not in action['attr_ids']]
                                adding_attr_ids = [id for id in action['attr_ids'] if id not in existing_attr_ids]

                                deleting_attr_objects = ActionAttribute.objects.filter(group_action=ga, object_id__in=deleting_attr_ids)
                                deleting_attr_objects.delete()

                                for attr_id in adding_attr_ids:
                                    try:
                                        attr = attr_model.objects.get(pk=attr_id)
                                        ActionAttribute.objects.create(group_action=ga, content_object=attr)
                                    except ObjectDoesNotExist:
                                        logging.warning('Attribute not exist id is %s, type is %s' % (attr_id, attr_type.name))
                        except ObjectDoesNotExist:
                            logging.warning('Group action not exist id is %s' % action['action_id'])
                    else:
                        raise exceptions.ValidationError('Action id must be present!')

        res_data = {
            'group': {
                'id': group.id,
                'name': group.name,
                'shared': group.shared
            }
        }

        return Response(res_data)

    def retrieve(self, request, *args, **kwargs):
        group = super().retrieve(request, *args, **kwargs).data
        group_id = group.get('id')

        # 获取 device prototype
        dp_type_id = ContentType.objects.get(model='device').id
        gds = GroupDevice.objects.filter(content_type_id=dp_type_id, group_id=group_id)
        device_ids = [gd.object_id for gd in gds]
        group['device_ids'] = device_ids

        dp_cache = {}
        group_devices = []

        devices = Device.objects.filter(pk__in=device_ids)
        for device in devices:
            dp = dp_cache.get(device.device_prototype_id, None)
            if dp is None:
                dp = device.device_prototype
                dp_cache[device.device_prototype_id] = dp

            result = {
                'device_id': device.id,
                'device': {
                    'id': device.id,
                    'name': device.nick_name
                },
                'device_prototype': {
                    'id': dp.id,
                    'name': dp.name,
                    'uni_name': dp.uni_name
                }
            }
            group_devices.append(result)

        group['devices'] = group_devices

        # 获取 terminal
        terminal_type_id = ContentType.objects.get(model='terminal').id
        gts = GroupDevice.objects.filter(content_type_id=terminal_type_id, group_id=group_id)
        terminal_ids = [gt.object_id for gt in gts]
        group['terminal_ids'] = terminal_ids

        terminals = Terminal.objects.filter(pk__in=terminal_ids)

        group_terminals = []
        for terminal in terminals:
            dp = dp_cache.get(terminal.device_prototype_id, None)
            if dp is None:
                dp = terminal.device_prototype
                dp_cache[terminal.device_prototype_id] = dp
            result = {
                'terminal_id': terminal.id,
                'terminal': {
                    'id': terminal.id,
                    'name': terminal.nick_name
                },
                'device_prototype': {
                    'id': dp.id,
                    'name': dp.name,
                    'uni_name': dp.uni_name,
                    'parent_id': dp.parent_id
                }
            }
            group_terminals.append(result)

        group['terminals'] = group_terminals

        if len(device_ids):
            dp_ids = set(Device.objects.filter(id__in=device_ids).values_list('device_prototype_id', flat=True))
        else:
            dp_ids = set()
            terminals = Terminal.objects.filter(id__in=terminal_ids)
            terminal_dps = set(terminal.device_prototype_id for terminal in terminals)
            for dp_id in terminal_dps:
                dp = DevicePrototype.objects.get(pk=dp_id)
                aps = AttributePrototype.objects.filter(device_prototype_id=dp.id)
                dp_ids.add(dp.parent_id if (dp.parent_id and not aps.exists()) else dp_id)

        group['dp_ids'] = dp_ids

        return Response(group)

    def destroy(self, request, *args, **kwargs):

        # 快捷操作与分组操作绑定，定时策略与分组定时绑定

        # 删除快捷操作
        group_actions = GroupAction.objects.filter(group_id=kwargs['pk']).values_list('id', flat=True)
        Shortcut.objects.filter(op_type=10, op_id__in=group_actions).delete()

        # 删除策略
        group_timers = ActionTimer.objects.filter(group_action_id__in=group_actions).values_list('id', flat=True)
        AcStrategies.objects.filter(st_type=10, st_id__in=group_timers).delete()

        # 一个联动可以绑定多个写入目标，删除分组时只删除对应的联动目标记录
        LinkageTarget.objects.filter(target_type=ContentType.objects.get_for_model(Group), target_id=kwargs['pk']).delete()

        super().destroy(request, *args, **kwargs)
        return Response()


class GroupActionViewSet(viewsets.ModelViewSet):
    serializer_class = GroupActionModelSerializer
    renderer_classes = (SyJSONRender,)

    def get_queryset(self):
        queryset = GroupAction.objects.filter(Q(group__shared=True) | Q(group__web_user_id=self.request.user['id']),
                                              group__project_id=self.request.user['project_id'])
        gid = self.request.query_params.get('gid', None)
        if gid is not None:
            queryset = GroupAction.objects.filter(group_id=gid)

        return queryset

    def get_object(self):
        try:
            return GroupAction.objects.get(pk=self.kwargs['pk'])
        except ObjectDoesNotExist:
            raise exceptions.NotFound('Group action not found!')

    def list(self, request, *args, **kwargs):
        actions = super().list(request, *args, **kwargs).data

        res_data = {
            "count": actions['count'],
            'group_actions': actions['results']
        }

        for action in res_data['group_actions']:
            group_id = action['group']['id']
            terminal_type = ContentType.objects.get_for_model(Terminal)
            if GroupDevice.objects.filter(content_type=terminal_type, group_id=group_id).exists():
                terminal_ids = GroupDevice.objects.filter(content_type=terminal_type, group_id=group_id).values_list('object_id', flat=True)
                terminals = Terminal.objects.filter(id__in=terminal_ids).prefetch_related(
                    Prefetch('terminalattribute_set')
                )
                # 构建 identifier -> [terminal.nick_name] 映射
                identifier_to_terminal_names = defaultdict(list)
                for terminal in terminals:
                    for attr in terminal.terminalattribute_set.all():
                        identifier_to_terminal_names[attr.identifier].append(terminal.nick_name)

                # 遍历 timer 并填充 terminal_names
                for attr in action.get('attributes', []):
                    attr['terminal_names'] = identifier_to_terminal_names.get(attr['identifier'], [])
                    if len(attr['terminal_names']):
                        attr['label'] = attr['label'] + f'（{"，".join(attr["terminal_names"])}）'

        return Response(res_data)

    def create(self, request, *args, **kwargs):
        data = request.data
        data['group'] = data['group_id']
        serializer = GroupActionModelSerializer(data=data)
        serializer.is_valid(raise_exception=True)

        project_id = request.user.get('project_id')
        is_shortcut = request.data.get('is_shortcut', None)
        with transaction.atomic(using=f'prj{project_id}db'):
            group_id = data.get('group', None)
            name = data.get('name', None)
            attr_ids = data.get('attr_ids', None)

            group = Group.objects.get(pk=group_id)

            group_action = GroupAction.objects.create(
                name=name,
                group=group
            )
            if is_shortcut is not None and is_shortcut == 1:
                shortcut = Shortcut.objects.create(name=name, op_type=10, op_id=group_action.id)
                shortcut_attributes = request.data.get('shortcut_attributes', None)
                # 创建分组操作快捷操作同时提供快捷操作属性默认值
                if shortcut_attributes is not None:
                    if group.is_cross():
                        attr_model = import_string('saian_api.linkage.models.CrossAttribute')
                    else:
                        attr_model = import_string('saian_api.devdefine.models.AttributePrototype')
                    for sa in shortcut_attributes:
                        ShortcutAttribute.objects.create(shortcut=shortcut,
                                                         content_type=ContentType.objects.get_for_model(attr_model),
                                                         object_id=sa['attr_id'],
                                                         value=sa['value'])

            if group.is_cross():
                attr_model = import_string('saian_api.linkage.models.CrossAttribute')
            else:
                attr_model = import_string('saian_api.devdefine.models.AttributePrototype')

            for attr_id in attr_ids:
                attr = attr_model.objects.get(pk=attr_id)
                ActionAttribute.objects.create(
                    group_action_id=group_action.id,
                    content_object=attr
                )
        res_data = {
            'group_action': self.get_serializer(group_action).data
        }

        return Response(res_data)

    def partial_update(self, request, *args, **kwargs):
        action = super().partial_update(request, *args, **kwargs).data

        attr_ids = request.data.get('attr_ids', None)
        is_shortcut = request.data.get('is_shortcut', None)

        if self.get_object().group.is_cross():
            attr_model = import_string('saian_api.linkage.models.CrossAttribute')
        else:
            attr_model = import_string('saian_api.devdefine.models.AttributePrototype')

        group_action = self.get_object()
        shortcut = Shortcut.objects.filter(op_type=10, op_id=group_action.id).last()

        if attr_ids is not None:
            existing_ids = group_action.actionattribute_set.all().values_list('object_id', flat=True)
            deleting_ids = [attr_id for attr_id in existing_ids if attr_id not in attr_ids]
            adding_ids = [attr_id for attr_id in attr_ids if attr_id not in existing_ids]

            # 删除属性
            ActionAttribute.objects.filter(group_action_id=group_action.id, object_id__in=deleting_ids).delete()
            if shortcut is not None:
                target_model = ContentType.objects.get_for_model(attr_model)
                ShortcutAttribute.objects.filter(shortcut=shortcut, object_id__in=deleting_ids, content_type=target_model).delete()

            # 添加新属性
            for attr_id in adding_ids:
                attr = attr_model.objects.get(pk=attr_id)
                action_attribute = ActionAttribute.objects.create(
                    group_action_id=group_action.id,
                    content_object=attr
                )
                # 新属性添加到已有的分组定时中。
                for action_timer in ActionTimer.objects.filter(group_action_id=group_action.id):
                    ActionTimerAttribute.objects.create(
                        action_timer=action_timer,
                        action_attribute_id=action_attribute.id,
                        value=attr.default_value
                    )

                if shortcut is not None:
                    ShortcutAttribute.objects.create(shortcut=shortcut,
                                                     content_type=ContentType.objects.get_for_model(attr_model),
                                                     object_id=attr_id,
                                                     value=attr.default_value)

        if is_shortcut is not None:
            if is_shortcut == 1:
                if shortcut is None:
                    shortcut = Shortcut.objects.create(name=action.get('name'), op_type=10, op_id=action.get('id'))
                    action['is_shortcut'] = True
                    action['shortcut_id'] = shortcut.id
                shortcut_attributes = request.data.get('shortcut_attributes', None)

                # 更新分组操作快捷操作时更新快捷操作属性默认值
                if shortcut_attributes is not None:
                    for sa_data in shortcut_attributes:
                        sa = ShortcutAttribute.objects.filter(shortcut=shortcut,
                                                              content_type=ContentType.objects.get_for_model(attr_model),
                                                              object_id=sa_data['attr_id'])
                        if sa.exists():
                            sa = sa.last()
                            sa.value = sa_data['value']
                            sa.save()
                        else:
                            ShortcutAttribute.objects.create(shortcut=shortcut,
                                                             content_type=ContentType.objects.get_for_model(attr_model),
                                                             object_id=sa_data['attr_id'],
                                                             value=sa_data['value'])

            elif is_shortcut == 0:
                if shortcut is not None:
                    shortcut.delete()
                    action['is_shortcut'] = False

        res_data = {
            'group_action': action
        }

        return Response(res_data)

    def destroy(self, request, *args, **kwargs):
        shortcuts = Shortcut.objects.filter(op_type=10, op_id=kwargs['pk'])
        shortcuts.delete()

        action_timer_ids = ActionTimer.objects.filter(group_action_id=kwargs['pk']).values_list('id', flat=True)
        ac_strategies = AcStrategies.objects.filter(st_type=10, st_id__in=action_timer_ids)
        ac_strategies.delete()

        super().destroy(request, *args, **kwargs)
        return Response()


class ActionTimerViewSet(viewsets.ModelViewSet):
    serializer_action_class = {
        'list': ActionTimerModelSerializer,
        'create': ActionTimerSerializer,
        'retrieve': ActionTimerModelSerializer,
        'partial_update': ActionTimerSerializer,
        'destroy': ActionTimerModelSerializer
    }
    renderer_classes = (SyJSONRender,)

    def get_queryset(self):
        queryset = ActionTimer.objects.all()
        group_action_id = self.request.query_params.get('group_action_id', None)
        group_id = self.request.query_params.get('gid', None)
        if group_action_id is not None and group_id is not None:
            queryset = queryset.filter(group_action_id=group_action_id)

        if group_id is not None:
            ga_ids = GroupAction.objects.filter(group_id=group_id).values_list('id', flat=True)
            queryset = queryset.filter(group_action_id__in=ga_ids)

        return queryset

    def get_serializer_class(self):
        try:
            return self.serializer_action_class[self.action]
        except (KeyError, AttributeError):
            return super().get_serializer_class()

    def list(self, request, *args, **kwargs):
        group_action_id = request.query_params.get('group_action_id', None)
        if group_action_id is None:
            group_action_id = kwargs.get('ga_id', None)
        if group_action_id is None:
            timers = super(ActionTimerViewSet, self).list(request, *args, **kwargs).data
            res_data = {
                "count": timers['count'],
                'timers': timers['results']
            }
        else:
            timers = ActionTimer.objects.filter(group_action_id=group_action_id)
            serializer = ActionTimerModelSerializer(timers, many=True)
            res_data = {
                'count': timers.count(),
                'timers': serializer.data
            }

        return Response(res_data)

    # 提取创建逻辑，方便策略视图函数调用
    @classmethod
    def create_action_timer(cls, data, project_id):
        data['run_date'] = data.get('run_date', None)

        if data['run_date'] == '':
            data['run_date'] = None

        data['run_time'] = data.get('run_time', None)

        if data['run_time'] == '':
            data['run_time'] = None

        data['week'] = data.get('week', None)

        if data['week'] == '':
            data['week'] = None

        for attr in data['attributes']:
            if isinstance(attr.get('value'), bool):
                attr['value'] = str(int(attr.get('value')))

        data['time_ranges'] = data.get('time_ranges', None)

        serializer = ActionTimerSerializer(data=data)
        serializer.is_valid(raise_exception=True)

        with transaction.atomic(using=f'prj{project_id}db'):
            repeat = data.get('repeat', False)
            run_date = data.get('run_date', None)
            run_date = run_date if run_date else None
            run_time = data.get('run_time', None)
            run_time = run_time if run_time else None
            week = data.get('week', None)
            week = week if week else None
            enabled = data.get('enabled', False)
            group_action_id = data['group_action_id']
            time_ranges = data['time_ranges']

            at_serializer = ActionTimerModelSerializer(data={
                'group_action': group_action_id,
                'repeat': repeat,
                'run_date': run_date,
                'run_time': run_time,
                'week': week,
                'enabled': enabled,
                'time_ranges': time_ranges
            })

            if at_serializer.is_valid(raise_exception=True):
                action_timer = at_serializer.save()

            attributes = data.get('attributes', None)

            for attr in attributes:
                value = attr['value']

                ActionTimerAttribute.objects.create(
                    action_timer_id=action_timer.id,
                    action_attribute_id=attr['attr_id'],
                    value=str(int(value)) if isinstance(value, bool) else value
                )

            AcStrategies.objects.create(
                st_type=10,
                st_id=action_timer.id,
                status=20 if action_timer.is_finished else 10,
                created_at=action_timer.created_at,
                updated_at=action_timer.updated_at
            )
        return action_timer

    def create(self, request, *args, **kwargs):
        # 处理输入
        data = request.data

        # 兼容旧版路径
        ga_id = kwargs.get('ga_id', None)
        if ga_id is not None:
            data['group_action_id'] = ga_id

        # data['run_date'] = request.data.get('run_date', None)
        #
        # if data['run_date'] == '':
        #     data['run_date'] = None
        #
        # data['run_time'] = request.data.get('run_time', None)
        #
        # if data['run_time'] == '':
        #     data['run_time'] = None
        #
        # data['week'] = request.data.get('week', None)
        #
        # if data['week'] == '':
        #     data['week'] = None
        #
        # for attr in data['attributes']:
        #     if isinstance(attr.get('value'), bool):
        #         attr['value'] = str(int(attr.get('value')))
        #
        # serializer = ActionTimerSerializer(data=data)
        # serializer.is_valid(raise_exception=True)
        #
        # project_id = request.user.get('project_id')
        # with transaction.atomic(using=f'prj{project_id}db'):
        #     repeat = request.data.get('repeat', False)
        #     run_date = request.data.get('run_date', None)
        #     run_date = run_date if run_date else None
        #     run_time = request.data.get('run_time', None)
        #     run_time = run_time if run_time else None
        #     week = request.data.get('week', None)
        #     week = week if week else None
        #     enabled = request.data.get('enabled', False)
        #     group_action_id = data['group_action_id']
        #     # if group_action is None:
        #     #     group_action = kwargs.get('ga_id', None)
        #
        #     at_serializer = ActionTimerModelSerializer(data={
        #         'group_action': group_action_id,
        #         'repeat': repeat,
        #         'run_date': run_date,
        #         'run_time': run_time,
        #         'week': week,
        #         'enabled': enabled,
        #     })
        #
        #     if at_serializer.is_valid(raise_exception=True):
        #         action_timer = at_serializer.save()
        #
        #     attributes = request.data.get('attributes', None)
        #
        #     for attr in attributes:
        #         # action_attribute = ActionAttribute.objects.filter(
        #         #     object_id=attr['attr_id'],
        #         #     group_action=action_timer.group_action
        #         # ).first()
        #         value = attr['value']
        #
        #         ActionTimerAttribute.objects.create(
        #             action_timer_id=action_timer.id,
        #             action_attribute_id=attr['attr_id'],
        #             value=str(int(value)) if isinstance(value, bool) else value
        #         )
        #
        #     AcStrategies.objects.create(
        #         st_type=10,
        #         st_id=action_timer.id,
        #         status=20 if action_timer.is_finished else 10,
        #         created_at=action_timer.created_at,
        #         updated_at=action_timer.updated_at
        #     )
        action_timer = self.create_action_timer(data, request.user['project_id'])

        res_data = {
            'action_timer': {
                'id': action_timer.id,
                'repeat': action_timer.repeat,
                'run_date': action_timer.run_date,
                'run_time': action_timer.run_time,
                'week': action_timer.week,
                'enabled': action_timer.enabled,
                'is_finished': action_timer.is_finished,
                'created_at': action_timer.created_at,
                'attributers': data.get('attributes', None),
                'time_ranges': action_timer.time_ranges
            }
        }

        return Response(res_data)

    def partial_update(self, request, *args, **kwargs):
        action_timer = self.get_object()

        repeat = request.data.get('repeat', None)
        run_date = request.data.get('run_date', None)
        run_time = request.data.get('run_time', None)
        week = request.data.get('week', None)
        enabled = request.data.get('enabled', None)
        attributes = request.data.get('attributes', None)
        project_id = request.user.get('project_id')
        time_ranges = request.data.get('time_ranges', None)

        with transaction.atomic(using=f'prj{project_id}db'):
            if repeat is not None:
                action_timer.repeat = repeat

            if run_date is not None:
                try:
                    action_timer.run_date = datetime.datetime.strptime(run_date, '%Y-%m-%d')
                except ValueError:
                    action_timer.run_date = None
                if week is None:
                    action_timer.week = None
            # else:
            #     action_timer.run_date = None

            if run_time is not None:
                action_timer.run_time = datetime.datetime.strptime(run_time, '%H:%M:%S')

            if week is not None:
                try:
                    action_timer.week = week
                except ValueError:
                    action_timer.week = None
                if run_date is None:
                    action_timer.run_date = None

            if enabled is not None:
                action_timer.enabled = enabled

            if attributes is not None:
                existing_ids = action_timer.action_attributes.values_list('id', flat=True)
                for attr in attributes:
                    value = attr['value']
                    if attr['attr_id'] in existing_ids:
                        aa = ActionTimerAttribute.objects.get(action_attribute_id=attr['attr_id'], action_timer=action_timer)
                        aa.value = int(value) if isinstance(value, bool) else value
                        aa.save()
                    else:
                        ActionTimerAttribute.objects.create(
                            action_timer_id=action_timer.id,
                            action_attribute_id=attr['attr_id'],
                            value=int(value) if isinstance(value, bool) else value
                        )

                attr_ids = [attr['attr_id'] for attr in attributes]

                delete_ids = [id for id in existing_ids if id not in attr_ids]

                ActionTimerAttribute.objects.filter(action_timer=action_timer, action_attribute_id__in=delete_ids).delete()

            if 'time_ranges' in request.data:
                action_timer.time_ranges = time_ranges

            action_timer.save()

        res_data = {
            'action_timer': {
                'id': action_timer.id,
                'repeat': action_timer.repeat,
                'run_date': action_timer.run_date,
                'run_time': action_timer.run_time,
                'week': action_timer.week,
                'enabled': action_timer.enabled,
                'is_finished': action_timer.is_finished,
                'created_at': action_timer.created_at,
                'attributers': attributes,
                'time_ranges': action_timer.time_ranges
            }
        }

        return Response(res_data)

    def destroy(self, request, *args, **kwargs):
        ac_strategies = AcStrategies.objects.filter(st_type=10, st_id=kwargs['pk'])
        ac_strategies.delete()

        super().destroy(request, *args, **kwargs)
        return Response()


class ExecuteActionViewSet(viewsets.GenericViewSet):
    def create(self, request):
        group_action_id = request.data.get('group_action_id', None)
        attributes = request.data.get('attributes', None)
        project_id = request.user.get('project_id')
        set_global_db(project_id)

        if group_action_id is None or not GroupAction.objects.filter(pk=group_action_id).exists():
            raise exceptions.ValidationError('Group Action must be present!')

        if attributes is None:
            raise exceptions.ValidationError('Attributes must be present!')

        for attr in attributes:
            if not ActionAttribute.objects.filter(pk=attr['attr_id'], group_action_id=group_action_id).exists():
                raise exceptions.ValidationError('Attribute not exist!')

        user_id = request.user.get('id')
        user = WebUser.objects.using(f'prj{project_id}db').get(pk=user_id)

        HandleShortcut(kwargs={'op_type': 10, 'op_id': group_action_id, 'changes': attributes,
                               'request': request, 'project_id': project_id, 'user': user}).start()

        res_data = {
            'status': status.HTTP_200_OK,
            'data': None
        }

        return Response(res_data)


# 快捷操作列表
class ShortcutViewSet(viewsets.ModelViewSet):
    serializer_class = ShortcutSerializer
    renderer_classes = (SyJSONRender,)

    def get_queryset(self):
        queryset = Shortcut.objects.all()

        search = self.request.query_params.get('search', None)
        if search is not None:
            queryset = queryset.filter(Q(name__icontains=search))
        
        return queryset.order_by('-created_at')

    def list(self, request, *args, **kwargs):
        queryset = super().list(request, *args, **kwargs).data

        # results = [shortcut for shortcut in queryset['results'] if queryset is not None]
        res_data = {
            "count":  self.get_queryset().count(),
            'shortcuts': queryset['results']
        }
        return Response(res_data)

    def retrieve(self, request, *args, **kwargs):
        shortcut = super().retrieve(request, *args, **kwargs).data
        return Response({
            'status': status.HTTP_200_OK,
            'shortcut': shortcut
        })


# 执行快捷操作
class ExecuteShortcutViewSet(viewsets.ModelViewSet):

    def list(self, request, *args, **kwargs):
        group_action_id = request.query_params.get('group_action_id', None)
        if group_action_id is None:
            group_action_id = kwargs.get('ga_id', None)
        if group_action_id is None:
            timers = super(ActionTimerViewSet, self).list(request).data
            res_data = {
                "count": timers['count'],
                'timers': timers['results']
            }
        else:
            timers = ActionTimer.objects.filter(group_action_id=group_action_id)
            serializer = ActionTimerModelSerializer(timers, many=True)
            res_data = {
                'count': timers.count(),
                'timers': serializer.data
            }

        return Response(res_data)

    def create(self, request):
        project_id = request.user.get('project_id')
        shortcut_id = request.data.get('shortcut_id', None)
        changes = request.data.get('changes', None)

        user_id = request.user.get('id')
        if shortcut_id is None or not Shortcut.objects.filter(pk=shortcut_id).exists():
            return Response(status=status.HTTP_400_BAD_REQUEST, data={'error': "Shortcut must be present"})

        shortcut = Shortcut.objects.get(id=shortcut_id)
        user = WebUser.objects.get(pk=user_id)

        if shortcut.op_type == 10 and changes is None:
            # 如果 change 为空，则通过 shortcut_attributes 表查找下发指令
            changes = {}
            for sa in ShortcutAttribute.objects.filter(shortcut_id=shortcut_id, value__isnull=False):
                changes[sa.content_object.identifier] = sa.value

            if not len(changes):
                return Response(status=status.HTTP_400_BAD_REQUEST, data={'error': "Changes must be present"})

        HandleShortcut(kwargs={'op_type': shortcut.op_type, 'op_id': shortcut.op_id, 'changes': changes,
                               'request': request, 'project_id': project_id, 'user': user}).start()

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })


class HandleShortcut(Thread):
    """新线程执行快捷操作任务"""

    def __init__(self, group=None, name=None, args=(), kwargs=None):
        Thread.__init__(self, group=group, name=name, args=args, kwargs=kwargs)

    def run(self):
        self.process_shortcut(self._kwargs)

    @classmethod
    def process_shortcut(cls, data):
        op_type = data.get('op_type')
        op_id = data.get('op_id', False)
        user = data.get('user')
        changes = data.get('changes', False)
        request = data.get('request', False)
        is_trial = data.get('is_trial', False)
        project_id = data.get('project_id')
        set_global_db(project_id)

        try:
            if op_type == 10:
                if isinstance(changes, list):
                    for attr in changes:
                        if not ActionAttribute.objects.filter(pk=attr['attr_id'], group_action_id=op_id).exists():
                            raise exceptions.ValidationError('Attribute not exist!')
                ga = GroupAction.objects.get(pk=op_id)
                ga.execute_by_manual(user, changes)

            elif op_type == 20:
                rule = LinkageRule.objects.get(pk=op_id)
                rule.execute(user, is_trial)

        except Exception as e:
            logging.error(f"后台线程执行快捷操作时出错: {e.__str__()}")


# 查询操作记录
class ActionLogsViewSet(viewsets.ModelViewSet):
    serializer_class = ActionLogsSerializer
    renderer_classes = (SyJSONRender,)

    @classmethod
    def handle_executor_info(cls, request, logs: list):
        user_cache = {}

        for log in logs:
            executor = log['executor']
            if executor['executor_type'] == 'WebUser':
                user = user_cache.get(executor['id'], None)
                if user is None:
                    user = WebUser.objects.get_executor_info(request, executor['id'])
                    user_cache[executor['id']] = user
                log['executor'] = user

            elif executor['executor_type'] == 'ActionTimer':
                try:
                    timer = ActionTimer.objects.get(pk=executor['id'])
                    group_action = timer.group_action
                    group = group_action.group
                    executor['name'] = f'{group.name}-{group_action.name}'

                except ActionTimer.DoesNotExist:
                    pass

            elif executor['executor_type'] == 'LinkageTrigger':
                try:
                    lt = LinkageTrigger.objects.get(pk=executor['id'])
                    linkage = LinkageRule.objects.get(pk=lt.linkage_rule_id)
                    executor['name'] = linkage.name

                except LinkageRule.DoesNotExist:
                    pass
                except LinkageTrigger.DoesNotExist:
                    pass

            elif executor.get('executor_type', None) == 'Admin':
                executor['name'] = '管理后台'

    def get_queryset(self):
        queryset = ActionLog.objects.all()

        shortcut_id = self.request.query_params.get('shortcut_id', None)
        if shortcut_id is not None:
            shortcut = Shortcut.objects.get(pk=shortcut_id)
            queryset = queryset.filter(op_type=shortcut.op_type, op_id=shortcut.op_id)

        executor = self.request.query_params.get('executor', None)
        if executor is not None:
            queryset = queryset.filter(actor_name=executor)

        op_type = self.request.query_params.get('op_type', None)
        if op_type is not None:
            queryset = queryset.filter(op_type=op_type)

        op_id = self.request.query_params.get('op_id', None)
        if op_id is not None:
            queryset = queryset.filter(op_id=op_id)

        search = self.request.query_params.get('search', None)
        if search is not None:
            queryset = queryset.filter(Q(actor_name__icontains=search) | Q(action_name__icontains=search))

        from_at = self.request.query_params.get('from', None)
        if from_at is not None:
            from_dt = datetime.datetime.strptime(from_at[:8], '%Y%m%d')
            queryset = queryset.filter(created_at__gte=from_dt.strftime('%Y-%m-%d 00:00:00'))
        till_at = self.request.query_params.get('till', None)
        if till_at is not None:
            till_dt = datetime.datetime.strptime(till_at[:8], '%Y%m%d')
            queryset = queryset.filter(created_at__lte=till_dt.strftime('%Y-%m-%d 23:59:59'))

        return queryset

    def list(self, request, *args, **kwargs):
        logs = super().list(request, *args, **kwargs).data

        self.handle_executor_info(request, logs['results'])

        return Response({
            'status': status.HTTP_200_OK,
            'action_logs': logs['results'],
            'count': logs['count']
        })

    def retrieve(self, request, *args, **kwargs):
        log = super().retrieve(request, *args, **kwargs).data

        self.handle_executor_info(request, [log])

        return Response({
            'status': status.HTTP_200_OK,
            'data': log
        })


# 运行策略
class AcStrategiesViewSet(viewsets.ModelViewSet):
    serializer_class = AcStrategiesSerializer
    renderer_classes = (SyJSONRender,)

    def get_object(self):
        try:
            return AcStrategies.objects.get(pk=self.kwargs['pk'])
        except ObjectDoesNotExist:
            raise exceptions.NotFound('AcStrategy not found!')

    def get_queryset(self):
        queryset = AcStrategies.objects.all()
        st_type = self.request.query_params.get('type', None)
        st_id = self.request.query_params.get('id', None)
        st_status = self.request.query_params.get('status', None)

        if st_type is not None:
            types = st_type.split(',')
            queryset = queryset.filter(st_type__in=types)
        if st_id is not None:
            queryset = queryset.filter(pk=st_id)
        if st_status is not None:
            queryset = queryset.filter(status=st_status)

        return queryset

    def list(self, request, *args, **kwargs):
        search = request.query_params.get('search', None)
        enabled = request.query_params.get('enabled', None)
        repeat = request.query_params.get('repeat', None)

        if search is None and enabled is None and repeat is None:
            data = super().list(request, *args, **kwargs).data
            ac_strategies = data['results']
            count = data['count']
        else:
            queryset = self.get_queryset()
            serializer = self.get_serializer(queryset, many=True)
            ac_strategies = serializer.data

            if search:
                ac_strategies = list(filter(lambda x: search in x.get('name', ''), ac_strategies))
            if repeat:
                ac_strategies = list(filter(lambda x: x.get('repeat', None) is (True if repeat == '1' else False), ac_strategies))
            if enabled:
                ac_strategies = list(filter(lambda x: x.get('enabled', None) is (True if enabled == '1' else False), ac_strategies))

            # 处理分页
            count = len(ac_strategies)
            page = int(request.query_params.get('page', 1))
            per_page = int(request.query_params.get('per_page', 200))
            start = (page - 1) * per_page
            end = start + per_page
            ac_strategies = ac_strategies[start: end]

        return Response({
            'status': status.HTTP_200_OK,
            'ac_strategies': ac_strategies,
            'count': count
        })

    def retrieve(self, request, *args, **kwargs):
        ac_strategy = super(AcStrategiesViewSet, self).retrieve(request, *args, **kwargs).data
        return Response({
            'status': status.HTTP_200_OK,
            'ac_strategy': ac_strategy
        })

    def partial_update(self, request, *args, **kwargs):
        ac_strategy = self.get_object()

        repeat = request.data.get('repeat', None)
        enabled = request.data.get('enabled', None)
        run_date = request.data.get('run_date', None)
        run_time = request.data.get('run_time', None)
        run_week = request.data.get('run_week', None)
        interval = request.data.get('interval', None)
        identifiers = request.data.get('identifiers', None)
        up_value = request.data.get('up_value', None)
        low_value = request.data.get('low_value', None)
        time_ranges = request.data.get('time_ranges', None)

        st_type = ac_strategy.st_type
        st_id = ac_strategy.st_id

        if st_type == 10:  # 10-定时分组操作
            st = ActionTimer.objects.get(pk=st_id)
        elif st_type == 20:  # 20-定时联动操作
            st = LinkageTrigger.objects.get(linkage_rule_id=st_id)
        elif st_type == 30:  # 30-被动联动操作
            st = LinkageRule.objects.get(pk=st_id)
        elif st_type == 40:  # 40-设备软定时
            st = DeviceTimer.objects.get(pk=st_id)
        elif st_type == 50:  # 50-末端温度限制
            st = DeviceLimit.objects.get(pk=st_id)
        else:
            st = None

        if st is None:
            return Response()

        if repeat is not None:
            st.repeat = repeat
        if enabled is not None:
            if st_type == 20:
                lr = st.linkage_rule
                lr.enabled = enabled
                lr.save()
            else:
                st.enabled = enabled
        if run_date is not None:
            try:
                st.run_date = datetime.datetime.strptime(run_date, '%Y-%m-%d')
            except ValueError:
                st.run_date = None
        if run_time is not None:
            st.run_time = datetime.datetime.strptime(run_time, '%H:%M:%S') if run_time != '' else None
        if run_week is not None:
            try:
                st.week = run_week
                st.wdays = run_week
            except ValueError:
                st.week = None
                st.wdays = None
        if interval is not None:
            st.interval = interval if interval != '' else None
        if up_value is not None:
            st.up_value = up_value
        if low_value is not None:
            st.low_value = low_value

        if identifiers is not None:
            if st_type == 10:
                existing_ids = list(st.actiontimerattribute_set.values_list('id', flat=True))
                for attr in identifiers:
                    value = attr['value']
                    if int(attr['attr_id']) in existing_ids:
                        aa = ActionTimerAttribute.objects.get(pk=attr['attr_id'], action_timer=st)
                        aa.value = int(value) if isinstance(value, bool) else value
                        aa.save()
                    else:
                        ActionTimerAttribute.objects.create(
                            action_timer_id=st.id,
                            action_attribute_id=attr['attr_id'],
                            value=int(value) if isinstance(value, bool) else value
                        )
                attr_ids = [attr['attr_id'] for attr in identifiers]
                delete_ids = [id for id in existing_ids if id not in attr_ids]
                ActionTimerAttribute.objects.filter(action_timer=st, action_attribute_id__in=delete_ids).delete()

            elif st_type == 40:
                # existing_aps = json.loads(st.identifiers)
                #
                # deleting_aps = [ap for ap in existing_aps if ap['identifier'] not in [ap['identifier'] for ap in identifiers]]
                # adding_aps = [ap for ap in identifiers if ap['identifier'] not in [ap['identifier'] for ap in existing_aps]]
                #
                # for d_ap in deleting_aps:
                #     existing_aps.pop(existing_aps.index(d_ap))
                #
                # for a_ap in adding_aps:
                #     existing_aps.append(a_ap)

                # st.identifiers = json.dumps(identifiers)
                st.identifiers = json.dumps(identifiers, ensure_ascii=False)

        if 'time_ranges' in request.data:
            st.time_ranges = time_ranges

        st.save()
        return Response()

    def destroy(self, request, *args, **kwargs):
        ac_strategy = self.get_object()

        st = None
        if ac_strategy.st_type == 10:
            st = ActionTimer.objects.filter(pk=ac_strategy.st_id)

        elif ac_strategy.st_type == 20 or ac_strategy.st_type == 30:
            st = LinkageRule.objects.filter(pk=ac_strategy.st_id)

            shortcuts = Shortcut.objects.filter(op_type=20, op_id=ac_strategy.st_id)
            shortcuts.delete()

        elif ac_strategy.st_type == 40:
            st = DeviceTimer.objects.filter(pk=ac_strategy.st_id)

        elif ac_strategy.st_type == 50:
            st = DeviceLimit.objects.filter(pk=ac_strategy.st_id)

        if st is not None:
            st.delete()

        super().destroy(request, *args, **kwargs)
        return Response()

    def create(self, request, *args, **kwargs):
        strategy_type = request.data.get('type', None)
        if strategy_type is None:
            raise exceptions.ValidationError(detail={'detail': 'type is required!'})

        data = request.data
        project_id = request.user['project_id']

        # 10-定时分组操作
        if strategy_type == 10:
            group_action_id = request.data.get('group_action_id', None)
            if group_action_id is None:
                raise exceptions.ValidationError(detail={'detail': 'group_action_id is required!'})
            ActionTimerViewSet.create_action_timer(data, project_id)
        # 40-设备云定时
        elif strategy_type == 40:
            DeviceTimerViewSet.create_device_timer(data, project_id)
        else:
            raise exceptions.ValidationError(detail={'detail': 'not a valid type!'})

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })


class GroupDeviceView(viewsets.ModelViewSet):
    def create(self, request, *args, **kwargs):
        group_id = request.data.get('group_id', None)
        device_ids = request.data.get('device_ids', None)
        terminal_ids = request.data.get('terminal_ids', None)

        if group_id is not None:
            if device_ids is not None:
                device_content_type = ContentType.objects.get_for_model(Device)
                for device_id in device_ids.split(','):
                    if not GroupDevice.objects.filter(group_id=group_id,
                                                      content_type=device_content_type,
                                                      object_id=device_id).exists():
                        GroupDevice.objects.create(group_id=group_id, content_type=device_content_type, object_id=device_id)

            elif terminal_ids is not None:
                terminal_content_type = ContentType.objects.get_for_model(Terminal)
                for terminal_id in terminal_ids.split(','):
                    if not GroupDevice.objects.filter(group_id=group_id,
                                                      content_type=terminal_content_type,
                                                      object_id=terminal_id).exists():
                        GroupDevice.objects.create(group_id=group_id, content_type=terminal_content_type, object_id=terminal_id)
            else:
                raise exceptions.ValidationError(detail='device_ids 或 terminal_ids 不能为空！')

        else:
            raise exceptions.ValidationError(detail='group_id不能为空！')

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def destroy(self, request, *args, **kwargs):
        group_id = request.query_params.get('group_id', None)
        device_ids = request.query_params.get('device_ids', None)
        terminal_ids = request.query_params.get('terminal_ids', None)

        if group_id is not None:
            if device_ids is not None:
                GroupDevice.objects.filter(group_id=group_id,
                                           content_type=ContentType.objects.get_for_model(Device),
                                           object_id__in=device_ids.split(',')).delete()
            elif terminal_ids is not None:
                GroupDevice.objects.filter(group_id=group_id,
                                           content_type=ContentType.objects.get_for_model(Terminal),
                                           object_id__in=terminal_ids.split(',')).delete()

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })


class ActionAttributes(viewsets.ModelViewSet):
    def create(self, request, *args, **kwargs):
        group_action_id = request.data.get('ga_id', None)
        attr_ids = request.data.get('attr_ids', None)

        if group_action_id is not None and attr_ids is not None:
            ap_content_type = ContentType.objects.get_for_model(AttributePrototype)
            for ap_id in attr_ids.split(','):
                if not ActionAttribute.objects.filter(group_action_id=group_action_id,
                                                      content_type=ap_content_type,
                                                      object_id=ap_id).exists():
                    ActionAttribute.objects.create(group_action_id=group_action_id, content_type=ap_content_type, object_id=ap_id)
        else:
            raise exceptions.ValidationError(detail='ga_id 和 attr_id 不能为空！')

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def destroy(self, request, *args, **kwargs):
        group_action_id = request.query_params.get('ga_id', None)
        attr_ids = request.query_params.get('attr_ids', None)

        if group_action_id is not None and attr_ids is not None:
            ActionAttribute.objects.filter(group_action_id=group_action_id,
                                           content_type=ContentType.objects.get_for_model(AttributePrototype),
                                           object_id__in=attr_ids.split(',')).delete()

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })
