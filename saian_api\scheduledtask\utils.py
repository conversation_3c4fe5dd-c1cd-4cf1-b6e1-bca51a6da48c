import traceback
from datetime import datetime

from django.core.management.base import CommandError

from saian_api.utils.middlewares.db import request_cfg
from saian_api.utils.tools import current_cwdays, is_number
from saian_api.utils.tools import is_int

def get_projects(command, options):
    """
      从管理命令行中解析出项目并返回项目ID列表
    """
    args = options['projects']

    projects = []
    for arg in args:
        try:
            idx = str(arg).find('-')
            if idx == -1:
                raise CommandError('参数格式不符合要求，格式：项目名-ID')

            project_name = arg[0:idx]
            project_id = arg[idx + 1:len(arg)]
            if is_int(project_id):
                projects.append(project_id)

        except Exception:
            command.stderr.write(traceback.format_exc(), ending='')
            continue

    return projects

def set_global_db(project_id):
    """
      根据项目id设置全局数据库
    """
    db_name = f'prj{project_id}db' if is_number(project_id) else project_id
    # 设置全局数据库
    request_cfg.cfg = db_name

def hit_time(command, run_date, run_time, last_run_dt, wdays, interval, repeat, time_now=datetime.now()):
    """
      根据提供的参数判断是否在任务的运行时间内，即是否满足定时运行的条件
      run_date - 任务运行日期，run_time - 任务运行时间，last_run_dt - 任务上次运行时间（时间戳），wdays - 任务运行的周
      interval - 时间间隔（单位：秒）, repeat - 是否重复
      time_now - 定时任务触发时间
    """
    # 重复任务检查周'日'（即：周X）和时间
    # 是否满足日（每日或周'日'）
    hit_day = False

    # 定时间隔运行
    if interval is not None and interval != '' and int(interval) != 0:
        second_passed = datetime.now().timestamp() - last_run_dt
        if second_passed > interval:
            return True

    # 周定时
    if wdays is not None and wdays != '':
        if current_cwdays() in wdays:
            hit_day = True
    # 日定时
    else:
        if repeat:
            hit_day = True

        # 单次运行，日期是否命中
        if run_date is not None and run_date != '':
            # 测试用
            # time_now = datetime.strptime('2022-08-13 09:00:23', '%Y-%m-%d %H:%M:%S')
            # current_date = time_now.strftime('%Y-%m-%d')
            current_date = datetime.now().strftime('%Y-%m-%d')
            if run_date.strftime('%Y-%m-%d') == current_date:
                hit_day = True

    # 满足周定时或日定时
    if hit_day:
        if run_time is not None and run_time != '':
            # 测试用
            # time_now = datetime.strptime('2022-08-12 09:00:23', '%Y-%m-%d %H:%M:%S')

            # time_now = datetime.now()
            current_dt = int(time_now.timestamp())
            current_dt_min = time_now.strftime('%H:%M')
            run_time = datetime.strptime(time_now.strftime("%Y-%m-%d") + ' ' + run_time.strftime('%H:%M:%S'), '%Y-%m-%d %H:%M:%S')
            run_dt = int(run_time.timestamp())
            run_dt_min = run_time.strftime('%H:%M')

            # 当前时间大于运行时间且上一次的执行时间小于运行时间，即为执行时间
            if current_dt > run_dt and current_dt_min == run_dt_min and run_dt > last_run_dt:
                return True

    return False
