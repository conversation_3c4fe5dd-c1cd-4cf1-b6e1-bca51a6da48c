from saian_api.issue.models import DeviceIssue
from .base import BaseDevice
from ...terminal.models import Terminal

class FauGateway(BaseDevice):
    """
      新风机网关
    """
    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)
        data = event.get('data', {})

        # 开关状态处理
        if 'FAUStatus' in data:
            value = data['FAUStatus']
            stop_values = ['BA停止', '原PLC停止', '节能停止', '旋钮停止', '本地停止', '停机', '关机']
            if value in stop_values:
                device.sw_on = False
            else:
                device.sw_on = True
            device.save()
            Terminal.objects.bulk_update_by_device(device, 'sw_on', device.sw_on)

    @classmethod
    def fault(cls, device, event):
        data = event.get('data', {})

        idf = 'FAUFault'
        idf_type = 'FAUFaultType'
        if idf in data:
            faults = [
                '新风机相序错误',
                '新风机变频故障',
                '新风机消防联动',
                '新风机启动失败',
                '风阀开启异常',
                '新风机工频故障'
            ]
            if data[idf] == 1:
                if 1 <= data[idf_type] <= 6:
                    DeviceIssue.objects.add_fault(device, faults[data[idf_type] - 1])
            else:
                for issue in faults:
                    DeviceIssue.objects.recover_fault(device, issue)

    @classmethod
    def alarm(cls, device, event):
        data = event.get('data', {})
        if 'GatewayAlarm' in data:
            if data['GatewayAlarm'] == 1 and data['GatewayAlarmType'] != 0:
                if data['GatewayAlarmType'] == 1:
                    DeviceIssue.objects.add_alarm(device, '通讯超时')
                if data['GatewayAlarmType'] == 2:
                    DeviceIssue.objects.add_alarm(device, '通讯错误')
            else:
                DeviceIssue.objects.recover_alarm(device, '通讯超时')
                DeviceIssue.objects.recover_alarm(device, '通讯错误')

        idf = 'FAUAlarm'
        idf_type = 'FAUAlarmType'
        if idf in data:
            alarms = [
                '新风机风压异常',
                '新风阀开阀异常',
                '新风阀关阀异常',
                '回风阀开阀异常',
                '回风阀关阀异常'
            ]
            if data[idf] == 1:
                if 1 <= data[idf_type] <= 5:
                    DeviceIssue.objects.add_alarm(device, alarms[data[idf_type] - 1])
            else:
                for issue in alarms:
                    DeviceIssue.objects.recover_alarm(device, issue)

class RdhFauGateway(BaseDevice):
    """
      转轮新风机网关
    """
    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)

        data = event.get('data', {})

        # 开关状态处理
        if 'RDHFAUStatus' in data:
            value = data['RDHFAUStatus']
            if value == '停止':
                device.sw_on = False
            else:
                device.sw_on = True
            device.save()
            Terminal.objects.bulk_update_by_device(device, 'sw_on', device.sw_on)

    @classmethod
    def fault(cls, device, event):
        data = event.get('data', {})
        if 'RDHFAUFault' in data:
            faults = [
                '新风机相序错误',
                '新风机消防联动',
                '新风机再生风机过载',
                '新风机转轮电机过载',
                '新风机送风风机过载',
                '新风机超温保护',
                '新风机转轮异常'
            ]

            if data['RDHFAUFault'] == 1:
                if 1 <= data['RDHFAUFaultType'] <= 7:
                    DeviceIssue.objects.add_fault(device, faults[data['RDHFAUFaultType'] - 1])
            else:
                for issue in faults:
                    DeviceIssue.objects.recover_fault(device, issue)

    @classmethod
    def alarm(cls, device, event):
        data = event.get('data', {})
        if 'GatewayAlarm' in data:
            if data['GatewayAlarm'] == 1 and data['GatewayAlarmType'] != 0:
                if data['GatewayAlarmType'] == 1:
                    DeviceIssue.objects.add_alarm(device, '通讯超时')
                if data['GatewayAlarmType'] == 2:
                    DeviceIssue.objects.add_alarm(device, '通讯错误')
            else:
                DeviceIssue.objects.recover_alarm(device, '通讯超时')
                DeviceIssue.objects.recover_alarm(device, '通讯错误')

        if 'RDHFAUAlarm' in data:
            alarms = [
                '新风机再生风缺风',
                '新风机再生风超温'
            ]
            if data['RDHFAUAlarm'] == 1:
                if 1 <= data['RDHFAUAlarmType'] <= 2:
                    DeviceIssue.objects.add_alarm(device, alarms[data['RDHFAUAlarmType'] - 1])
            else:
                for issue in alarms:
                    DeviceIssue.objects.recover_alarm(device, issue)

class FAValve(BaseDevice):
    """PLC新风阀"""
    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)
        data = event.get('data', {})

        # 开关状态处理
        if 'FAValve_SW' in data:
            device.sw_on = data['FAValve_SW']
            device.save()
            Terminal.objects.bulk_update_by_device(device, 'sw_on', device.sw_on)

    @classmethod
    def fault(cls, device, event):
        pass

    @classmethod
    def alarm(cls, device, event):
        pass
