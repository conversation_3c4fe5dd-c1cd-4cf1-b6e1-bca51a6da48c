from celery.schedules import crontab

from saian_api.celery import celery_app

celery_app.conf.beat_schedule = {
    'project_weather_hour': {
        'task': 'saian_api.dashboard.tasks.hour_task',
        'schedule': crontab(minute=0, hour='*')  # 每小时的0分执行任务
    },
    'project_weather_day': {
        'task': 'saian_api.dashboard.tasks.day_task',
        'schedule': crontab(minute=0, hour=0, day_of_month='*')  # 每天0时0分执行任务
    },
    'project_weather_month': {
        'task': 'saian_api.dashboard.tasks.month_task',
        'schedule': crontab(minute=0, hour=0, day_of_month=1)  # 每月一号0时0分执行任务
    },
    'project_weather_year': {
        'task': 'saian_api.dashboard.tasks.year_task',
        'schedule': crontab(minute=0, hour=0, day_of_month=1, month_of_year=1)  # 每年一月一号0时0分执行任务
    }
}