# Generated by Django 3.2.8 on 2022-09-26 16:01

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('devdefine', '0007_attributeprototype_do_send'),
        ('project', '0005_project_settings'),
        ('device', '0007_alter_deviceeventhistory_device_issue_id'),
    ]

    operations = [
        migrations.CreateModel(
            name='DeviceLimit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('limit_type', models.IntegerField()),
                ('name', models.CharField(max_length=100)),
                ('attribute_prototypes', models.Char<PERSON>ield(max_length=255)),
                ('remark', models.CharField(blank=True, max_length=255, null=True)),
                ('up_value', models.Char<PERSON>ield(max_length=20)),
                ('low_value', models.<PERSON><PERSON><PERSON><PERSON>(max_length=20)),
                ('enabled', models.<PERSON><PERSON>an<PERSON>ield(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('device_prototype', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='devdefine.deviceprototype')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='project.project')),
            ],
            options={
                'db_table': 'device_limits',
                'ordering': ['-created_at'],
            },
        ),
    ]
