from collections import defaultdict

from django.contrib.contenttypes.models import ContentType
from django.db.models import Prefetch
from rest_framework import serializers
from rest_framework.exceptions import ValidationError

from saian_api.project.models import Project
from saian_api.user.models import WebUser
from .models import CrossAttribute, LinkageRule, LinkageSnpVar, LinkageTrigger, LinkageVar, SnpVar
from ..group.models import Shortcut
from ..terminal.models import Terminal

class LinkageRuleSerializer(serializers.ModelSerializer):
    class Meta:
        model = LinkageRule
        fields = ['id', 'name', 'trigger_type', 'code', 'enabled', 'remark', 'show_in_so', 'so_name', 'group', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']

    def to_representation(self, rule_ins):
        ret = super().to_representation(rule_ins)

        # 列表不返回源码
        view = self.context['view']
        if view.action == 'list':
            ret.pop('code')
        rule_targets = rule_ins.linkagetarget_set.all()
        targets = []

        if rule_targets is not None and len(rule_targets) != 0:
            for target in rule_targets:
                target_type = ContentType.objects.get(pk=target.target_type_id)

                type = 10
                name = None
                if target_type.name == 'group':
                    type = 10
                    name = target.target.name if target.target is not None else None

                identifier_to_terminal_names = None
                if target_type.name == 'device':
                    type = 20
                    name = target.target.nick_name if target.target is not None else None

                    if Terminal.objects.filter(device_id=target.target.id).count() > 1:
                        terminals = Terminal.objects.filter(device_id=target.target.id).prefetch_related(
                            Prefetch('terminalattribute_set')
                        )
                        # 构建 identifier -> [terminal.nick_name] 映射
                        identifier_to_terminal_names = defaultdict(str)
                        for terminal in terminals:
                            for attr in terminal.terminalattribute_set.all():
                                identifier_to_terminal_names[attr.identifier] = terminal.nick_name

                # # 规则详情返回目标的参数
                # if view.action == 'retrieve':
                idfs = []
                attrs = target.linkageattribute_set.all()
                if attrs is not None and len(attrs) != 0:
                    for attr in attrs:
                        ap = attr.attribute
                        if ap:
                            result = {
                                'id': ap.id,
                                'name': ap.name,
                                'identifier': ap.identifier
                            }
                            if identifier_to_terminal_names is not None:
                                result['terminal_name'] = identifier_to_terminal_names.get(ap.identifier, '')
                                if result['terminal_name']:
                                    result['name'] = f"{result['terminal_name']}/{result['name']}"
                            idfs.append(result)

                targets.append({
                    'id': target.id,
                    'name': name,
                    'target_id': target.target.id if target.target is not None else None,
                    'target_type': type,
                    'attributes': idfs
                })
                # else:
                #     targets.append({
                #         'id': target.target.id,
                #         'name': name,
                #         'type': type
                #     })

        ret['targets'] = targets

        creator = WebUser.objects.get(pk=rule_ins.web_user_id)

        ret['creator'] = {
            'id': creator.id,
            'name': creator.name
        }

        # 详情时才返回
        if view.action == 'retrieve':
            trigger_settings = LinkageTrigger.objects.filter(linkage_rule_id=rule_ins.id)
            if len(trigger_settings) != 0:
                ts = trigger_settings.first()
                ret['exe_setting'] = {
                    'repeat': ts.repeat,
                    'run_date': ts.run_date,
                    'run_time': ts.run_time,
                    'wdays': ts.wdays,
                    'interval': ts.interval,
                    'is_finished': ts.is_finished,
                    'time_ranges': ts.time_ranges
                }
            else:
                ret['exe_setting'] = {}

            shortcut = Shortcut.objects.filter(op_type=20, op_id=ret['id'])
            ret['is_shortcut'] = shortcut.exists()

        if ret['group'] is not None:
            group = rule_ins.group
            ret['group'] = {
                'id': group.id,
                'name': group.name,
            }

        return ret

class LinkageTargetSerializer(serializers.Serializer):
    # 目标id
    target_id = serializers.IntegerField(required=True)
    # 目标类型，10-分组，20-设备
    target_type = serializers.CharField(required=True, max_length=200)
    # 联动规则id
    rule_id = serializers.IntegerField(required=True)
    # 目标的参数标识
    idfs = serializers.ListField()

class LinkageTriggerSerializer(serializers.ModelSerializer):
    class Meta:
        model = LinkageTrigger
        fields = ['repeat', 'run_date', 'run_time', 'wdays', 'interval', 'is_finished', 'last_run_at', 'created_at', 'time_ranges']
        read_only_fields = ['created_at', 'last_run_at', 'is_finished']

    def create(self, validated_data):
        return LinkageTrigger.objects.create(**validated_data)

# 所有的变量定义
class LinkageAllVarSerializer(serializers.Serializer):
    id = serializers.IntegerField(read_only=True)
    rule_id = serializers.IntegerField(required=True)
    scope_type = serializers.IntegerField(required=True)
    name = serializers.CharField(required=True)
    mac = serializers.CharField(min_length=1, max_length=32, allow_null=True)
    identifier = serializers.CharField(required=True, min_length=1, max_length=128)
    is_trigger = serializers.BooleanField()
    var_type = serializers.IntegerField(required=True)
    var_name = serializers.CharField(required=True)


# 规则级变量
class LinkageVarSerializer(serializers.ModelSerializer):
    linkage_rule = serializers.PrimaryKeyRelatedField(queryset=LinkageRule.objects.all())

    class Meta:
        model = LinkageVar
        # fields = ['id', 'name', 'mac', 'identifier', 'is_trigger', 'linkage_rule']
        fields = '__all__'
        read_only_fields = ['id']

    def validate(self, attrs):
        if 'var_type' in attrs:
            if attrs['var_type'] == 20:
                if attrs.get('data_type', None) is None:
                    raise ValidationError({'data_type': ['this field is required.']})
                data_type = attrs['data_type']
                if attrs.get('value', None) is None and data_type != 90:
                    raise ValidationError({'value': ['this field is required.']})
                if data_type == 90:
                    if attrs.get('min_value', None) is None:
                        raise ValidationError({'min_value': ['this field is required.']})
                    if attrs.get('max_value', None) is None:
                        raise ValidationError({'max_value': ['this field is required.']})
            elif attrs['var_type'] == 10:
                if attrs.get('mac', None) is None:
                    raise ValidationError({'mac': ['this field is required.']})

        return attrs

    def create(self, validated_data):
        return LinkageVar.objects.create(**validated_data)


# 项目或系统级变量
class SnpVarSerializer(serializers.ModelSerializer):
    project = serializers.PrimaryKeyRelatedField(queryset=Project.objects.all())

    class Meta:
        model = SnpVar
        fields = ['id', 'project', 'name', 'mac', 'identifier', 'var_type']
        read_only_fields = ['id']

    def create(self, validated_data):
        return SnpVar.objects.create(**validated_data)

# 系统级或项目级变量配置
class LinkageSnpVarSerializer(serializers.ModelSerializer):
    linkage_rule = serializers.PrimaryKeyRelatedField(queryset=LinkageRule.objects.all())
    snp_var = serializers.PrimaryKeyRelatedField(queryset=SnpVar.objects.all())

    class Meta:
        model = LinkageSnpVar
        fields = ['id', 'is_trigger', 'linkage_rule', 'snp_var']
        read_only_fields = ['id']

    def create(self, validated_data):
        return LinkageSnpVar.objects.create(**validated_data)

#  跨类型属性
class CrossAttributeSerializer(serializers.Serializer):
    name = serializers.CharField(min_length=1, max_length=200)
    identifier = serializers.CharField(min_length=1, max_length=200)
    data_type = serializers.ChoiceField(choices=[10, 20, 30, 40, 80])
    default_value = serializers.CharField(min_length=1, max_length=200, required=False)
    min = serializers.CharField(min_length=1, max_length=200, required=False)
    max = serializers.CharField(min_length=1, max_length=200, required=False)
    remark = serializers.CharField(max_length=250, required=False, allow_blank=True)
    ap_ids = serializers.ListField(
        child=serializers.IntegerField()
    )


    def validate_ap_ids(self, value):
        if len(value) < 2:
            raise serializers.ValidationError('At least 2 ids is required!')

        return value

class CrossAttributeModelSerializer(serializers.ModelSerializer):
    class Meta:
        model = CrossAttribute
        fields = ['id', 'name', 'identifier', 'data_type', 'remark', 'default_value', 'min', 'max']
        read_only_fields = ['id']

    def to_representation(self, instance):
        ret = super(CrossAttributeModelSerializer, self).to_representation(instance)

        cross = CrossAttribute.objects.get(pk=ret['id']).crossattributeprototype_set.all()
        aps = [ap.attribute_prototype for ap in cross]
        dps = set([ap.device_prototype for ap in aps])

        device_prototypes_fields = []

        for dp in dps:
            device_prototypes_fields.append({
                'id': dp.id,
                'name': dp.name,
                'attribute_prototypes': [{
                    'id': ap.id,
                    'name': ap.name
                } for ap in filter(lambda x: x.device_prototype_id == dp.id, aps)]
            })

        ret['device_prototypes'] = device_prototypes_fields

        return ret

class ExecuteRuleSerializer(serializers.Serializer):
    # 要执行的规则id
    rule_id = serializers.IntegerField(required=True)
    # 是否试执行
    is_trial = serializers.BooleanField(default=True)
