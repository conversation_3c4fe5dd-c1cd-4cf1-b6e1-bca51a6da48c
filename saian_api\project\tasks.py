import datetime
import io
import logging
import os

import requests
from openpyxl.reader.excel import load_workbook

from saian_api import settings
from saian_api.celery import celery_app
from saian_api.device.models import Device, DeviceAttribute
from saian_api.device.views import DeviceAttributeViewV5
from saian_api.dimension.models import DimensionAttribute, PpvConfig
from saian_api.linkage.models import LinkageRule
from saian_api.project.models import Project
from saian_api.scheduledtask.utils import set_global_db
from saian_api.terminal.models import Terminal, TerminalAttribute
from saian_api.user.models import WebUser
from saian_api.utils.inthttpapi import DOMAIN
from saian_api.utils.inthttpapi.base import BaseAPI
from saian_api.utils.tools import ec_type_to_unit


@celery_app.task(name='saian_api.project.tasks.switch_ac_run_mode', max_retries=3)
def switch_ac_run_mode(run_mode, with_cs, project_id, web_user_id):
    set_global_db(project_id)
    executor = WebUser.objects.filter(pk=web_user_id).last()

    # with_cs判断是否需要同步冷源运行模式
    cs_devices = None
    if with_cs is not None and with_cs == 1:
        cs_devices = Device.objects.filter(terminal__in=Terminal.objects.filter(terminal_type=40, idx__isnull=True)).distinct()
        if cs_devices is not None:
            for device in cs_devices:
                set_run_mode_by_device(device, run_mode, project_id, executor)

    # 切换所有类型为末端-10的终端的运行模式
    terminal_devices = Device.objects.filter(terminal__in=Terminal.objects.filter(terminal_type=10, terminalattribute__use_for=20)).distinct()
    for device in terminal_devices:
        set_run_mode_by_device(device, run_mode, project_id, executor)

    rules = LinkageRule.objects.filter(trigger_type=40)
    if rules.exists():
        [rule.execute(executor, False) for rule in rules]


@celery_app.task(name='saian_api.project.tasks.set_run_mode_by_device', max_retries=5)
def set_run_mode_by_device(device_id, run_mode, project_id, web_user_id):
    set_global_db(project_id)
    device = Device.objects.get(pk=device_id)
    executor = WebUser.objects.filter(pk=web_user_id).last()
    terminals = Terminal.objects.filter(device=device, show_en=True)
    run_mode_tas = TerminalAttribute.objects.exclude(value=run_mode).filter(use_for=20, terminal__in=terminals)
    if run_mode_tas.exists():
        # if terminals.count() != run_mode_tas.count():
        #     logging.error(f"系统总控：设备终端和设备运行模式的ta个数不相等, 设备: {device.id}-{device.nick_name}")
        changes = {}
        for ta in run_mode_tas:
            changes[ta.identifier] = run_mode
        device.send_ctrls(executor, changes)

@celery_app.task(name='saian_api.project.tasks.execute_rule', max_retries=3)
def execute_rule(rule_id, web_user_id,  project_id):
    set_global_db(project_id)
    executor = WebUser.objects.filter(pk=web_user_id).last()
    rule = LinkageRule.objects.get(pk=rule_id)
    rule.execute(executor, False)
