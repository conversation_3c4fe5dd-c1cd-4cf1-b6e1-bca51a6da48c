# Generated by Django 3.2.8 on 2021-12-30 15:16

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('device', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='DeviceMaintenance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_id', models.BigIntegerField(db_index=True)),
                ('device_type', models.CharField(max_length=255, null=True)),
                ('total_device', models.IntegerField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'device_maintenances',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Maintenance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('high_speed_time', models.IntegerField()),
                ('mid_speed_time', models.IntegerField()),
                ('low_speed_time', models.IntegerField()),
                ('hs_weight', models.IntegerField()),
                ('ms_weight', models.IntegerField()),
                ('ls_weight', models.IntegerField()),
                ('total_time', models.IntegerField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('device', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='device.device')),
                ('device_maintenance', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='maint.devicemaintenance')),
            ],
            options={
                'db_table': 'maintenances',
                'ordering': ['-created_at'],
            },
        ),
    ]
