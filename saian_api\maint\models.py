import logging

from django.db import models
from django.db import transaction
from rest_framework import exceptions

from saian_api.device.models import Device
from saian_api.message.models import Message
from saian_api.notifi.models import Notification
from saian_api.project.models import Project
# Create your models here.
from saian_api.utils.utils import CeleryTaskUtils

class DeviceMaintenanceManager(models.Manager):
    """
      创建设备维护记录（目前只支持盘管）
      DeviceMaintenance为汇总记录，包括：该次保养多少设备，时间，设备类型，所属项目
      Maintenance为保养设备的详情，包括：设备当时的各种参数值，如：高速运行时间、中速运行时间、低俗运行时间等
    """
    def create_m(self, validated_data):
        from .models import Maintenance

        devices = Device.objects.filter(pk__in=validated_data['device_ids'], project_id=validated_data['project_id'])
        project = Project.objects.get(pk=validated_data['project_id'])

        if devices is not None and len(devices) > 0:
            with transaction.atomic(using=f"prj{project}db"):
                dm = self.create(
                    user_id=validated_data['user_id'],
                    device_type='盘管',
                    total_device=len(devices),
                    project_id=validated_data['project_id']
                )

                for device in devices:
                    high_speed_time = device.get_value_by_idf('HighSpeedTime')
                    mid_speed_time = device.get_value_by_idf('MidSpeedTime')
                    low_speed_time = device.get_value_by_idf('LowSpeedTime')

                    Maintenance.objects.create(
                        device_id=device.id,
                        device_maintenance_id=dm.id,
                        high_speed_time=high_speed_time if high_speed_time is not None else 0,
                        mid_speed_time=mid_speed_time if mid_speed_time is not None else 0,
                        low_speed_time=low_speed_time if low_speed_time is not None else 0,
                        hs_weight=project.hs_weight,
                        ms_weight=project.ms_weight,
                        ls_weight=project.ls_weight,
                        total_time=device.cal_run_time()
                    )
                    device.needs_m = False
                    device.save()

                    Message.objects.maintain_complete(device)
                    data = {
                        'content': f'设备保养通知: 设备【{device.id}-{device.nick_name}】已完成保养',
                        'device_id': device.id,
                        'device_nickname': device.nick_name
                    }
                    Notification.objects.notify(10, device, data)

        else:
            raise exceptions.NotFound('Devices not found!')

        return dm

    """
      定时任务检查设备是否到达保养时间，如果需要保养则更新设备needs_m为true
      当前只支持盘管
    """
    def cron_check_m(self):
        # projects = Project.object.all()
        projects = CeleryTaskUtils.get_all_project()
        for project in projects:
            print(f"设备保养时间检查: {project['project'].name}-{project['db_name']}")
            db = project['db_name']
            devices = Device.objects.using(db).filter(device_type_id=14, needs_m=False, project_id=project['project'].id)
            if len(devices) > 0:
                try:
                    for device in devices:
                        if device.cal_run_time(db) > project['project'].fan_time * 0.8:
                            device.needs_m = True
                            device.save(using=db)

                            Message.objects.maintain(device, db=db)
                except Exception as e:
                    logging.warning(e.with_traceback)

# 设备保养汇总记录
class DeviceMaintenance(models.Model):
    # 所属项目
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    # 实施保养的用户
    user_id = models.BigIntegerField(db_index=True)
    # 设备类型，多个以逗号隔开
    device_type = models.CharField(max_length=255, null=True)
    # 保养的设备总数
    total_device = models.IntegerField()

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'device_maintenances'
        ordering = ['-created_at']

    objects = DeviceMaintenanceManager()

# 设备保养详细记录
class Maintenance(models.Model):
    # 所属设备
    device = models.ForeignKey(Device, on_delete=models.CASCADE)
    # 所属保养汇总
    device_maintenance = models.ForeignKey(DeviceMaintenance, on_delete=models.CASCADE)
    # 高速运行累计时间
    high_speed_time = models.IntegerField()
    # 中速运行累计时间
    mid_speed_time = models.IntegerField()
    # 低速运行累计时间
    low_speed_time = models.IntegerField()
    # 高速权重
    hs_weight = models.IntegerField()
    # 中速权重
    ms_weight = models.IntegerField()
    # 低速权重
    ls_weight = models.IntegerField()
    # 总运行时间
    total_time = models.IntegerField()

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'maintenances'
        ordering = ['-created_at']
