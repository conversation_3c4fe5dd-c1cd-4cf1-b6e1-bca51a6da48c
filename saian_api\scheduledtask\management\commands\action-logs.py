"""
新建快捷操作表
"""
import traceback

from django.core.management import BaseCommand, CommandError

from saian_api.group.models import ActionLog, GroupAction
from saian_api.linkage.models import LinkageRule
from saian_api.project.models import Project
from saian_api.scheduledtask.utils import get_projects, set_global_db


class Command(BaseCommand):
    help = "更新操作记录信息"

    # def add_arguments(self, parser):
    #     parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目列表
        projects = [48, 58, 66, 67, 68, 69, 70, 71]

        for project_id in projects:
            # project_id = 66
            try:
                set_global_db(project_id)
                project = Project.objects.get(pk=project_id)
                self.stdout.write(f"更新操作记录: {project.name}", ending='\n')
                action_logs = ActionLog.objects.all()
                gas = GroupAction.objects.all()
                linkage_rules = LinkageRule.objects.all()
                count = 0
                for log in action_logs:
                    if gas.filter(name=log.action_name).count() > 0:
                        op_id = gas.filter(name=log.action_name).last().id
                        log.op_type = 10
                        log.op_id = op_id
                        count = count + 1
                        log.save()
                    elif linkage_rules.filter(name=log.action_name).count() > 0:
                        op_id = linkage_rules.filter(name=log.action_name).last().id
                        log.op_type = 20
                        log.op_id = op_id
                        count = count + 1
                        log.save()
                self.stdout.write(f'\t更新操作记录{count}条。', ending='\n')
            except CommandError:
                self.stderr.write(f"运行'更新操作记录'任务失败，项目ID：{project_id}，命令参数不合法！", ending='\n')
                # continue
            except Exception as e:
                self.stderr.write(f"运行'更新操作记录'任务失败，项目ID：{project_id}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
            # continue
