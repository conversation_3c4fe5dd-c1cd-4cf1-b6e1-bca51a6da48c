import datetime
import json
import logging
import asyncio
import time
from urllib.parse import unquote

from threading import Thread

import requests
from chinese_calendar import is_workday
from django.conf import settings
from django.core.exceptions import ObjectDoesNotExist
from django.db.models import Q
from django.shortcuts import get_object_or_404
from rest_framework import exceptions
from rest_framework import status
from rest_framework import views, viewsets
from rest_framework.parsers import FileUploadParser
from rest_framework.request import Request
from rest_framework.response import Response
from saian_api.project.tasks import set_run_mode_by_device, switch_ac_run_mode

from saian_api.regions.models import Region
from saian_api.utils.httpapi.image import ImageAPI
from saian_api.utils.httpapi.weather import WeatherApi
from saian_api.utils.sy_jsonrenderer import ResponseUtils, SyJSONRender
from .models import (
    Project,
    ProjectWebMenu,
    WebChart,
    ProjectChart,
    WebPanel,
    ProjectPanel,
    WebMenu,
    WebManual
)
from .serializers import (
    ProjectSerializer,
    WebChartSerializer,
    ProjectChartReadSerializer,
    WebPanelSerializer,
    ProjectPanelSerializer,
    WebManualSerializer,
    ProjectChartWriteSerializer,
    ProjectPanelWriteSerializer,
    WebProjectSerializer,
    WebMenuSerializer
)
# Create your views here.
from saian_api.user.models import WebUser, WebRole
from ..building.models import ActiveRoom
from ..coldsource.models import ColdSource
from ..devdefine.models import DeviceType, AttributePrototype, DevicePrototype
from ..devdefine.serializers import SimpleDevicePrototypeSerializer
from ..device.models import Device
from ..report.models import RoomHourlyStat
from ..scheduledtask.utils import set_global_db
from ..terminal.models import Terminal, TerminalAttribute
from ..user.models import UserLog
from ..utils.httpapi import DOMAIN
from ..utils.inthttpapi.base import BaseAPI
from ..utils.tools import fill_element

loop = asyncio.new_event_loop()
asyncio.set_event_loop(loop)
"""
    项目详情
"""


class ProjectDetail(views.APIView):
    serializer_class = ProjectSerializer

    # renderer_classes = (SyJSONRender,)

    def get_object(self, pk):
        try:
            return Project.objects.get(pk=pk)
        except Project.DoesNotExist:
            raise exceptions.NotFound('Project not found!')

    def get(self, request, pk, format=None):
        # 取http头的项目id，而不用url里的
        pk = request.user['project_id']
        project = self.get_object(pk)
        serializer = ProjectSerializer(project)
        return Response(ResponseUtils.custom_response_format(serializer.data, 'data', False))

    def put(self, request, pk):
        """ 更新项目详情 """
        project = self.get_object(pk)
        serializer = ProjectSerializer(instance=project, data=request.data)
        if serializer.is_valid():
            serializer.save()

            is_local_deployment = settings.LOCAL_DEPLOYMENT

            # 如果不是本地部署项目，需要与管理后台同步项目信息
            if not is_local_deployment:
                # 在旧框架，默认用户是角色是"第三方用户"，并无权限更新项目详情
                # url = f'{DOMAIN}/saianapi/v1/web_projects/{pk}'
                # headers = {'Authorization': request.headers.get('Authorization')}
                # r = requests.put(url, headers=headers, data=request.data)
                # if r.status_code != 200:
                #     logging.error(f'向旧接口更新项目详情时出错. status_code: {r.status_code}, content: {r.text}')

                pass

            return Response(ResponseUtils.custom_response_format(serializer.data))
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

# class ProjectDetail(generics.RetrieveAPIView):
#     queryset = Project.objects.all()
#     serializer_class = ProjectSerializer
#     renderer_classes = (SyJSONRender, )


"""
    总览页配置View
"""


class WebChartsView(viewsets.GenericViewSet):
    def list(self, request):
        charts = WebChart.objects.filter(enabled=True)
        serializer = WebChartSerializer(charts, many=True, context={'request': request})

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'web_charts': serializer.data
            },
            'total': len(serializer.data)
        })

    def create(self, request):
        serializer = WebChartSerializer(data=request.data, context={'request': request})
        if serializer.is_valid(raise_exception=True):
            serializer.save()

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'web_chart': serializer.data
            }
        })

    def partial_update(self, request, *args, **kwargs):
        chart = WebChart.objects.get(pk=kwargs['pk'])

        if chart.chart_type == 10:
            raise exceptions.PermissionDenied('系统图表不能修改！')

        serializer = WebChartSerializer(chart, data=request.data, context={'request': request}, partial=True)
        if serializer.is_valid(raise_exception=True):
            serializer.save()

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'web_chart': serializer.data
            }
        })

    def retrieve(self, request, *args, **kwargs):
        chart = WebChart.objects.get(pk=kwargs['pk'])
        serializer = WebChartSerializer(chart, context={'request': request})

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'web_chart': serializer.data
            }
        })

    def destroy(self, request, *args, **kwargs):
        chart = WebChart.objects.get(pk=kwargs['pk'])

        if chart.chart_type == 10:
            raise exceptions.PermissionDenied('系统图表不能删除！')

        chart.delete()

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })


""" 
    总览页组件列表
"""


class ProjectChartListView(views.APIView):
    def get(self, request):
        charts = ProjectChart.objects.filter(project_id=request.user['project_id']).order_by('num')
        serializer = ProjectChartReadSerializer(charts, many=True)
        return Response(ResponseUtils.custom_response_format(serializer.data, 'project_charts', True))

    def post(self, request):
        num = request.data['num']
        exist = ProjectChart.objects.filter(project_id=request.user['project_id'], num=num).exists()
        if exist:
            return Response({
                'status': status.HTTP_400_BAD_REQUEST,
                'msg': f'位置{num}已存在'
            }, status=status.HTTP_400_BAD_REQUEST)
        data = request.data
        data['web_chart'] = data['web_chart_id']
        serializer = ProjectChartWriteSerializer(data=data)
        if serializer.is_valid():
            serializer.save(project_id=request.user['project_id'])
            return Response(ResponseUtils.custom_response_format(serializer.data, 'project_chart', True))
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ProjectChartDetailView(views.APIView):
    def get(self, request, pk):
        project_id = request.user['project_id']
        chart = get_object_or_404(ProjectChart, pk=pk, project_id=project_id)
        data = ProjectChartReadSerializer(chart).data
        project_chart_name = chart.web_chart.name

        # 设备在线统计
        if project_chart_name == '设备在线统计':
            online_devices = []
            offline_devices = []

            offline_count = 0
            online_count = 0

            device_types = DeviceType.objects.filter(parent__isnull=False)
            for device_type in device_types:
                devices = Device.objects.filter(device_type_id=device_type.id, project__id=project_id)
                on_devices = devices.filter(online=True)
                off_devices = devices.filter(online=False)
                if on_devices.count():
                    online_devices.append({
                        'on_value': on_devices.count(),
                        'name': device_type.name
                    })
                    online_count += on_devices.count()
                if off_devices.count():
                    offline_devices.append({
                        'off_value': off_devices.count(),
                        'name': device_type.name
                    })
                    offline_count += off_devices.count()

            device_stats = [online_devices, offline_devices]

            data['chart_data'] = {
                'data': [i for stat in device_stats for i in stat],
                'offline': offline_count,
                'online': online_count
            }
        # 故障报警统计
        elif project_chart_name == '故障报警统计':
            from saian_api.issue.models import DeviceIssue
            seven_days = []
            thirty_days = []

            # 默认请求的是故障-10, 切换类型时请求 '/saianapi/v2/issue_stats?day=30&type=20&project_id=11'
            issue_type = 10

            dt_ids = Device.objects.filter(
                project_id=request.user['project_id'],
            ).values_list('device_type_id', flat=True).distinct()
            for dt_id in list(set(dt_ids)):
                device_type = DeviceType.objects.get(pk=dt_id)
                current_time = datetime.datetime.now()
                from_7_day = (current_time - datetime.timedelta(days=7)).strftime('%Y-%m-%d 00:00:00')
                from_30_day = (current_time - datetime.timedelta(days=30)).strftime('%Y-%m-%d 00:00:00')

                device_ids = Device.objects.filter(device_type_id=dt_id).values_list('id', flat=True)

                seven_say_stat = {
                    'device_type': device_type.name,
                    'uni_name': device_type.uni_name,
                    'total': DeviceIssue.objects.filter(
                        created_at__range=[from_7_day, current_time],
                        device_id__in=device_ids,
                        project_id=request.user['project_id'],
                        issue_type=issue_type
                    ).count()
                }
                thirty_day_stat = {
                    'device_type': device_type.name,
                    'uni_name': device_type.uni_name,
                    'total': DeviceIssue.objects.filter(
                        created_at__range=[from_30_day, current_time],
                        device_id__in=device_ids,
                        project_id=request.user['project_id'],
                        issue_type=issue_type
                    ).count()
                }

                if seven_say_stat not in seven_days:
                    seven_days.append(seven_say_stat)
                if thirty_day_stat not in thirty_days:
                    thirty_days.append(thirty_day_stat)

            data['chart_data'] = {
                'seven_days': seven_days,
                'thirty_days': thirty_days
            }
        # 设备总览
        elif project_chart_name == '设备总览':
            device_types = DeviceType.objects.filter(parent__isnull=False)
            run_stats = []
            for device_type in device_types:
                devices = Device.objects.filter(device_type_id=device_type.id, project__id=project_id)

                if devices.count():
                    run_stats.append({
                        'device_type': device_type.name,
                        'fault_total': devices.filter(in_fault=True).count(),
                        'offline_total': devices.filter(online=False).count(),
                        'run_total': devices.filter(online=True, sw_on=True).count(),
                        'stop_total': devices.filter(online=True, sw_on=False).count(),
                        'total': devices.count()
                    })
            data['chart_data'] = {
                'run_stats': run_stats
            }
        # 房间温湿度趋势
        elif project_chart_name == '房间温湿度趋势':
            now = datetime.datetime.now()
            time = []
            rooms = []
            # 初始化时间
            for hour in range(0, now.hour):
                time.append(f"{hour}:00")

            active_rooms = ActiveRoom.objects.all()

            today_begin = now - datetime.timedelta(hours=now.hour, minutes=now.minute, seconds=now.second, microseconds=now.microsecond)

            for room in active_rooms:
                stats = []

                room_hour_stats = RoomHourlyStat.objects.filter(active_room_id=room.id, identifier='LocalTemp',
                                                                created_at__range=[today_begin, now]).order_by('created_at')

                for stat in room_hour_stats:
                    stats.append(stat.avg)

                rooms.append({
                    'data': stats,
                    # 默认值
                    'lineStyle': {'width': 4},
                    'name': room.name,
                    'smooth': True,
                    'type': 'line'
                })

            data['chart_data'] = {
                'rooms': rooms,
                'time': time
            }

        return Response(data=ResponseUtils.custom_response_format(data, 'project_chart', True))

    def delete(self, request, pk):
        chart = get_object_or_404(ProjectChart, pk=pk, project_id=request.user['project_id'])
        chart.delete()
        return Response({'status': 200})


"""
    用户看板配置View
"""


class WebPanelsApiView(views.APIView):
    def get(self, request):
        panels = WebPanel.objects.filter(enabled=True)
        serializer = WebPanelSerializer(panels, many=True)
        return Response(data=ResponseUtils.format(serializer.data, 'web_panels'))


"""
    用户看板组件列表
"""


class ProjectPanelsListView(views.APIView):

    def get(self, request):
        project_id = request.user['project_id']
        project = get_object_or_404(Project, pk=project_id)

        project_panels = ProjectPanel.objects.filter(project=project).order_by('num')
        serializer = ProjectPanelSerializer(project_panels, many=True)

        return Response(data=ResponseUtils.format(data=serializer.data, result_key='project_panels'))

    def post(self, request):
        project_id = request.user['project_id']
        project = get_object_or_404(Project, pk=project_id)

        num = request.data['num']
        is_exist = ProjectPanel.objects.filter(project=project, num=num).exists()
        if is_exist:
            return Response({
                'status': status.HTTP_400_BAD_REQUEST,
                'msg': f'位置{num}已存在'
            }, status=status.HTTP_400_BAD_REQUEST)

        serializer = ProjectPanelWriteSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save(project=project)
            return Response(ResponseUtils.format(serializer.data, 'project_panel'), status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


"""
    删除以配置的用户看板组件
"""


class ProjectPanelDetailView(views.APIView):
    def delete(self, request, pk):
        project = get_object_or_404(Project, pk=request.user['project_id'])
        project_panel = get_object_or_404(ProjectPanel, pk=pk, project=project)
        project_panel.delete()
        return Response({'status': status.HTTP_200_OK})


class WebMenuViewSet(viewsets.ViewSet):
    def get_object(self):
        return get_object_or_404(WebMenu, pk=self.kwargs['pk'])

    def list(self, request):
        menu_ids = ProjectWebMenu.objects.filter(project_id=request.user['project_id']).values_list('web_menu_id', flat=True)
        queryset = WebMenu.objects.filter(id__in=menu_ids, parent=None, enabled=True, is_super=False).order_by('id')
        menus = []
        for menu in queryset:
            menu_fields = {
                'id': menu.id,
                'name': menu.name,
                'page_content': menu.page_content,
                'is_cus': menu.is_cus,
                'seq': menu.seq,
                'display': menu.display
            }

            children = []
            for child in menu.children.filter(id__in=menu_ids, enabled=True, is_super=False).order_by('id'):
                child_fields = {
                    'id': child.id,
                    'name': child.name,
                    'page_content': child.page_content,
                    'is_cus': menu.is_cus,
                    'seq': menu.seq,
                    'display': child.display
                }
                children.append(child_fields)

            menu_fields['children'] = children
            menus.append(menu_fields)

        res_data = {
            'status': status.HTTP_200_OK,
            'data': {
                'menus': menus
            }
        }

        return Response(res_data)

    def create(self, request):
        request.data['agent_id'] = 9
        request.data['seq'] = request.data.get('seq', 0)
        request.data['enabled'] = True
        request.data['is_cus'] = True
        request.data['is_super'] = False

        uni_name = request.data.get('uni_name', None)
        role_ids = request.data.get('role_ids', None)
        pid = request.data.get('pid', None)

        if uni_name is None:
            request.data['uni_name'] = request.data.get('name')

        if role_ids:
            role_ids = role_ids.split(',')
            request.data.pop('role_ids')
        else:
            web_user = get_object_or_404(WebUser, pk=request.user['id'])
            role_ids = web_user.web_roles.values_list('id', flat=True)

        if pid:
            request.data['parent'] = pid
            request.data.pop('pid')

        serializer = WebMenuSerializer(data=request.data)

        if serializer.is_valid():
            if role_ids:
                roles = WebRole.objects.filter(id__in=role_ids)
                serializer.save()

                ProjectWebMenu.objects.create(project_id=request.user.get('project_id'), web_menu_id=serializer.data['id'])

                for role in roles:
                    role.web_menus.add(serializer.data['id'])

            res_data = {
                'status': status.HTTP_200_OK,
                'data': serializer.data
            }

            return Response(res_data)

        return Response(status=status.HTTP_400_BAD_REQUEST)

    def retrieve(self, request, pk=None):
        menu = self.get_object()

        res_data = {
            'status': status.HTTP_200_OK,
            'data': {
                'id': menu.id,
                'name': menu.name,
                'uni_name': menu.uni_name,
                'seq': menu.seq,
                'enabled': menu.enabled,
                'is_cus': menu.is_cus,
                'page_content': fill_element(menu.page_content) if menu.page_content else menu.page_content,
                'display': menu.display
            }
        }

        return Response(res_data)

    def partial_update(self, request, pk=None):
        menu = self.get_object()
        role_ids = request.data.get('role_ids', None)
        pid = request.data.get('pid', None)

        if pid:
            request.data['parent'] = pid
            request.data.pop('pid')

        serializer = WebMenuSerializer(menu, data=request.data, partial=True)

        if serializer.is_valid(raise_exception=True):
            serializer.save()

            if role_ids:
                roles = WebRole.objects.filter(web_menus__id=menu.id)

                existing_ids = [role.id for role in roles]
                # 转为数字
                new_ids = [int(new_id) for new_id in role_ids.split(',')]

                added_ids = [x for x in new_ids if x not in existing_ids]
                rm_ids = [y for y in existing_ids if y not in new_ids]

                # 添加权限到新的角色
                for aid in added_ids:
                    role = WebRole.objects.get(pk=aid)
                    role.web_menus.add(menu.id)

                # 移除无权限的角色
                for rid in rm_ids:
                    role = WebRole.objects.get(pk=rid)
                    role.web_menus.remove(menu.id)

        res_data = {
            'status': status.HTTP_200_OK,
            'data': {
                'id': menu.id,
                'name': menu.name,
                'uni_name': menu.uni_name,
                'seq': menu.seq,
                'enabled': menu.enabled,
                'is_cus': menu.is_cus,
                'page_content': fill_element(menu.page_content) if menu.page_content else menu.page_content,
                'display': menu.display
            }
        }

        return Response(res_data)

    def destroy(self, request, pk=None):
        menu = self.get_object()

        project_menu = ProjectWebMenu.objects.filter(web_menu_id=menu.id)

        if project_menu:
            project_menu.delete()

        roles = WebRole.objects.filter(web_menus__id=menu.id)

        if roles:
            for role in roles:
                role.web_menus.remove(menu)

        menu.delete()

        return Response({
            'status': 200,
            'data': None
        })


"""
    用户说明上传
"""


class WebManualView(views.APIView):
    parser_classes = (FileUploadParser,)

    def post(self, request):
        file_obj = request.data['file']
        # 解码还原文件名
        file_obj.name = unquote(file_obj.name)
        web_manual = WebManual(file=file_obj)
        web_manual.save()
        serializer = WebManualSerializer(web_manual)
        return Response(ResponseUtils.custom_response_format(serializer.data))


"""
    用户说明详情
"""


class WebManualDetailView(views.APIView):

    def get(self, request, pk):
        manual = get_object_or_404(WebManual, pk=pk)
        serializer = WebManualSerializer(manual)

        return Response(ResponseUtils.custom_response_format(serializer.data))


class EnviromentViewSet(viewsets.ViewSet):
    def list(self, request, prj=None):
        project = Project.objects.get(pk=request.user['project_id'])
        district = Region.objects.get(pk=project.admin_region_id)
        cityId = ''.join([str(project.admin_region_id)[0:4], '00'])

        try:
            city = Region.objects.get(pk=cityId, level=2)
        except ObjectDoesNotExist:
            city = district

        weather = WeatherApi.get_weather(district.weather_code)

        res_data = {
            'status': status.HTTP_200_OK,
            'data': weather,
            'project_image': ImageAPI.get_url(request, image_id=project.images),
            'device_num': project.num_of_dev_need_maintenance(),
            'city': city.name,
            'district': district.name
        }

        return Response(res_data)


class WebProjectViewSet(viewsets.ViewSet):
    def retrieve(self, request, pk=None):
        project = Project.objects.get(pk=request.user['project_id'])
        province = project.province
        city = project.city
        district = project.district

        res_data = {
            'status': status.HTTP_200_OK,
            'data': {
                'id': project.id,
                'name': project.name,
                'address': project.address,
                'en_temhum_sms': project.en_temhum_sms,
                'en_gas_sms': project.en_gas_sms,
                'project_logo': ImageAPI.get_url(request, project.logo, size='originals'),
                'slogan': ImageAPI.get_url(request, project.slogan, size='originals'),
                'image': ImageAPI.get_url(request, project.images, size='originals'),
                'province': {
                    'id': province.id,
                    'name': province.name,
                    'short_name': province.short_name
                },
                'city': {
                    'id': city.id,
                    'name': city.name,
                    'short_name': city.short_name
                },
                'district': {
                    'id': district.id,
                    'name': district.name,
                    'short_name': district.short_name
                },
                'user_manual': {
                    'id': 1,
                    'file': 'https://syense.yuque.com/rc0mt2/manual/oilizx'
                }
            }
        }

        return Response(res_data)

    def partial_update(self, request, pk=None):
        project = Project.objects.get(pk=request.user['project_id'])
        province = project.province
        city = project.city
        district = project.district

        serializer = WebProjectSerializer(project, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        # 向 admin3 更新数据
        res = f'saianadmin/intapi/projects/{project.id}'
        headers = BaseAPI.admin_intapi_header(res)
        url = f'{DOMAIN}/{res}'
        r = requests.put(url, json=request.data, headers=headers)
        if r.status_code != status.HTTP_200_OK:
            logging.error(f'向管理后台同步项目信息出错, status_code: {r.status_code}')

        # 向管理后台更新数据
        url = f'{DOMAIN}/saianapi/v1/web_projects/{request.user["project_id"]}'
        headers = {'Authorization': request.headers.get('Authorization')}
        requests.put(url, headers=headers, data=request.data)

        res_data = {
            'status': status.HTTP_200_OK,
            'data': {
                'id': project.id,
                'name': project.name,
                'address': project.address,
                'intro': project.intro,
                'en_temhum_sms': project.en_temhum_sms,
                'en_gas_sms': project.en_gas_sms,
                'project_logo': ImageAPI.get_url(request, project.logo),
                'slogan': ImageAPI.get_url(request, project.slogan),
                'image': ImageAPI.get_url(request, project.images),
                'province': {
                    'id': province.id,
                    'name': province.name,
                    'short_name': province.short_name
                },
                'city': {
                    'id': city.id,
                    'name': city.name,
                    'short_name': city.short_name
                },
                'district': {
                    'id': district.id,
                    'name': district.name,
                    'short_name': district.short_name
                }
            }
        }

        return Response(res_data)


class ProjectSettingsViewSet(viewsets.ModelViewSet):

    def get_project(self, request):
        project_id = request.query_params.get('project', None)
        if project_id is None:
            project_id = request.user['project_id']
        return Project.objects.using(f'prj{project_id}db').get(pk=project_id)

    def retrieve(self, request, *args, **kwargs):
        try:
            project = self.get_project(request)

            project_settings = json.loads(project.settings) if project.settings else ''
        except Exception as e:
            logging.error(e)
            return Response({
                'status': status.HTTP_200_OK,
                'data': {
                    'settings': None
                }
            })

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'settings': project_settings
            }
        })

    def update(self, request, *args, **kwargs):
        project = self.get_project(request)
        new_settings = request.data
        project_settings = json.loads(project.settings) if project.settings else {}

        if type(new_settings).__name__ == 'dict':
            project_settings = {**project_settings, **new_settings}

            project.settings = json.dumps(project_settings)
            project.save()

            # 向管理后台同步settings
            res = 'saianadmin/intapi/project_settings'
            headers = BaseAPI.admin_intapi_header(res)
            url = f'{DOMAIN}/{res}'
            r = requests.put(url, json={'settings': project_settings, 'project_id': project.id}, headers=headers)
            if r.status_code != status.HTTP_200_OK:
                logging.error(f'向管理后台同步 project.settings 出错')

            return Response({
                'status': 200,
                'data': project_settings
            })

        else:
            return Response({
                'status': 400,
                'data': None
            })


class ProjectSettingItemView(viewsets.ModelViewSet):
    def get_project(self, request):
        project_id = request.query_params.get('project', None)
        if project_id is None:
            project_id = request.user['project_id']
        return Project.objects.using(f'prj{project_id}db').get(pk=project_id)

    def retrieve(self, request, *args, **kwargs):
        name = kwargs['name']
        try:
            project = self.get_project(request)
            project_settings = json.loads(project.settings) if project.settings else {}

            setting = project_settings.get(name, None)
        except Exception as e:
            logging.error(e)
            return Response({
                'status': status.HTTP_200_OK,
                'data': {
                    name: None
                }
            })

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                name: setting
            }
        })


class AcRunMode(viewsets.ModelViewSet):
    renderer_classes = (SyJSONRender,)

    def list(self, request, *args, **kwargs):
        project = Project.objects.get(pk=request.user.get('project_id'))
        cs_mac = list(set(ColdSource.objects.values_list('mac', flat=True)))
        run_modes = '制冷,制暖,送风'
        ac_sources = []
        cs_dtids = list(DeviceType.objects.filter(parent_id=1).values_list('id', flat=True))
        for cs_device in Device.objects.filter(mac__in=cs_mac, project_id__isnull=False, device_type_id__in=cs_dtids):
            cs_dp = cs_device.device_prototype
            cs_terminals = Terminal.objects.filter(device__mac=cs_device.mac, show_en=True)
            cs_terminal = cs_terminals.filter(idx__isnull=True).first()
            # 冷源的运行状态 identifier 有两种：等于 RunMode 或以 SysRunMode 结尾
            cs_run_mode = TerminalAttribute.objects.filter(Q(identifier='RunMode') |
                                                           Q(identifier__endswith='SysRunMode'),
                                                           terminal=cs_terminal).first()

            host_terminals = cs_terminals.filter(prefix='Host')
            running_host_tas = TerminalAttribute.objects.filter(Q(identifier__icontains='Host') & Q(identifier__icontains='RunStatus'),
                                                                terminal_id__in=[terminal.id for terminal in host_terminals]).exclude(value='停止')

            ac_sources.append({
                'id': cs_device.id,
                'name': cs_terminal.nick_name,
                'device_id': cs_device.id,
                'custz_detail': cs_dp.content is not None,
                'device_prototype': SimpleDevicePrototypeSerializer(cs_dp).data,
                'run_mode': cs_run_mode.value if cs_run_mode is not None else None,
                'host_total': host_terminals.count(),
                'host_total_run': running_host_tas.count()
            })

        # 末端类型
        relation_list = []
        device_type_list = DeviceType.objects.filter(parent_id=2)
        for device_type_item in device_type_list:
            relation_list.append({
                'parent': {
                    'name': device_type_item.name,
                    'id': device_type_item.id
                },
                'child': list(DevicePrototype.objects.filter(device_type_id=device_type_item.id).values_list('id', flat=True))
            })
        relation_list = list(filter(lambda item: item['child'], relation_list))

        ac_terminals = []
        terminals = list(Terminal.objects.filter(terminal_type=10, show_en=True))
        for relation_item in relation_list:
            target_terminals = list(filter(lambda x: x.device_prototype_id in relation_item['child'], terminals))
            if len(target_terminals):
                target_terminals_id = [t.id for t in target_terminals]
                # use_for=20 是设置运行模式的参数，一些设备类型的设置和实质运行模式参数是分开的，所以不能在这里使用use_for=20
                target_terminal_attributes = TerminalAttribute.objects.filter(
                    Q(identifier='RunMode') | Q(identifier='SysRunMode') | Q(identifier='FCURunningMode') |
                    Q(identifier__endswith='_RunMode') | Q(identifier='CoolOrWarmSelect'),
                    terminal_id__in=target_terminals_id, terminal__show_en=True)

                # 去除不相关的参数
                target_ap_ids = target_terminal_attributes.values_list('attribute_prototype_id', flat=True)
                aps = AttributePrototype.objects.filter(pk__in=target_ap_ids)
                target_terminal_attributes = [ta for ta in target_terminal_attributes if '供冷' in aps.get(
                    pk=ta.attribute_prototype_id).options or '制冷' in aps.get(pk=ta.attribute_prototype_id).options]

                run_mode_ap = None
                if len(target_terminal_attributes) > 0:
                    run_mode_ap = AttributePrototype.objects.filter(pk=target_terminal_attributes[0].attribute_prototype_id).last()
                target_cooling = [ta for ta in target_terminal_attributes if ta.value in ['制冷', '供冷']]
                target_heating = [ta for ta in target_terminal_attributes if ta.value in ['制暖', '供暖']]
                others_count = len(target_terminal_attributes) - len(target_heating) - len(target_cooling)

                if TerminalAttribute.objects.filter(use_for=20, terminal__show_en=True, terminal_id__in=target_terminals_id).exists():
                    ac_terminals.append({
                        'label_id': relation_item['parent']['id'],
                        'terminal_type': relation_item['parent']['name'],
                        'total': len(target_terminals_id),
                        'run_mode': run_mode_ap.options if run_mode_ap else '',
                        'total_cooling': len(target_cooling),
                        'total_heating': len(target_heating),
                        'total_others': others_count
                    })

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'current_run_mode': project.run_mode,
                'run_modes': run_modes,
                'ac_sources': ac_sources,
                'ac_terminals': ac_terminals
            }
        })

    def create(self, request: Request, *args, **kwargs):
        project_id = request.user.get('project_id')
        project = Project.objects.get(pk=project_id)
        run_mode = request.data.get('run_mode', None)
        with_cs = request.data.get('with_cs', None)

        if run_mode is None:
            return Response({
                'status': status.HTTP_400_BAD_REQUEST,
                'error': 'run_mode is required.',
                'data': None
            })

        if with_cs is not None and with_cs == 1:
            cs_host_terminals = Terminal.objects.filter(terminal_type=40, prefix='Host').values_list('id', flat=True)
            running_hosts = TerminalAttribute.objects.filter(Q(identifier__startswith='Host') & Q(identifier__endswith='RunStatus'),
                                                             terminal_id__in=cs_host_terminals).exclude(value='停止')
            if running_hosts.exists():
                return Response({
                    'status': 40032,
                    'error': f'共有{running_hosts.count()}台主机没有停止。',
                    'data': None
                })

        web_user_id = request.user.get('id')
        executor = WebUser.objects.get(pk=web_user_id)

        # HandleRunMode(kwargs={'run_mode': run_mode, 'with_cs': with_cs, 'project_id': project_id, 'executor': web_user}).start()

        # with_cs判断是否需要同步冷源运行模式

        # switch_ac_run_mode.delay(run_mode, with_cs, project_id, web_user_id)

        cs_device_ids = None
        if with_cs is not None and with_cs == 1:
            cs_device_ids = Device.objects.filter(terminal__in=Terminal.objects.filter(
                terminal_type=40, idx__isnull=True)).values_list('id', flat=True).distinct()
            if cs_device_ids is not None:
                for idx, device_id in enumerate(cs_device_ids):
                    set_run_mode_by_device.delay(device_id, run_mode, project_id, web_user_id)
                    # if idx % 20 == 0:
                    #     time.sleep(1)

        # 切换所有类型为末端-10的终端的运行模式
        terminals = Terminal.objects.filter(terminal_type=10, terminalattribute__use_for=20, show_en=True)
        devices = Device.objects.filter(terminal__in=terminals).distinct()
        for device in devices:
            set_run_mode_by_device.delay(device.id, run_mode, project_id, web_user_id)

        project.run_mode = run_mode
        project.save()

        # rules = LinkageRule.objects.filter(trigger_type=40)
        # if rules.exists():
        #     [rule.execute(executor, False) for rule in rules]

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })


class HandleRunMode(Thread):
    """新线程执行项目系统总控任务"""
    __run_mode_status = {}

    def __init__(self, group=None, name=None, args=(), kwargs=None):
        Thread.__init__(self, group=group, name=name, args=args, kwargs=kwargs)

    def run(self):
        self.process_run_mode(self._kwargs)

    @classmethod
    def get_process_status(cls, project_id):
        return cls.__run_mode_status.get(project_id, None)

    @classmethod
    def process_run_mode(cls, data):
        run_mode = data.get('run_mode')
        with_cs = data.get('with_cs', False)
        project_id = data.get('project_id')
        executor = data.get('executor')
        set_global_db(project_id)
        cls.__run_mode_status[project_id] = {}

        device_count = 0
        cs_devices = None
        if with_cs is not None and with_cs == 1:
            cs_devices = Device.objects.filter(terminal__in=Terminal.objects.filter(terminal_type=40, idx__isnull=True)).distinct()
            device_count += cs_devices.count()

        terminal_devices = Device.objects.filter(terminal__in=Terminal.objects.filter(terminal_type=10, terminalattribute__use_for=20)).distinct()
        device_count += terminal_devices.count()

        cls.__run_mode_status[project_id] = {
            'total_devices': terminal_devices.count(),
            'finished': 0
        }
        try:
            if cs_devices is not None:
                for device in cs_devices:
                    loop.run_until_complete(cls.set_run_mode(device, run_mode, project_id, executor))

            for device in terminal_devices:
                loop.run_until_complete(cls.set_run_mode(device, run_mode, project_id, executor))

        except Exception as e:
            logging.error(f"后台线程执行系统总控设置时出错: {e.__str__()}")
            del cls.__run_mode_status[project_id]

    @classmethod
    async def set_run_mode(cls, device: Device, run_mode, project_id, executor):
        terminals = Terminal.objects.filter(device=device, show_en=True)
        run_mode_tas = TerminalAttribute.objects.filter(use_for=20, terminal__in=terminals)
        if run_mode_tas.exists():
            if terminals.count() != run_mode_tas.count():
                logging.error(f"系统总控：设备终端和设备运行模式的ta个数不相等, 设备: {device.id}-{device.nick_name}")
            changes = {}
            for ta in run_mode_tas:
                changes[ta.identifier] = run_mode
            device.send_ctrls(executor, changes)
        cls.__run_mode_status[project_id]['finished'] += 1
        if cls.__run_mode_status[project_id]['finished'] == cls.__run_mode_status[project_id]['total_devices']:
            del cls.__run_mode_status[project_id]

    # @classmethod
    # def set_fcu_type(cls, changes, run_mode):
    #     if run_mode == '制冷':
    #         changes['FCUType'] = '二管制单冷'
    #     elif run_mode == '制暖':
    #         changes['FCUType'] = '二管制单暖'
    #     return changes


class DashboardProjectInfoView(viewsets.ModelViewSet):
    def list(self, request, *args, **kwargs):
        project_id = request.user.get('project_id')
        project = get_object_or_404(Project, pk=project_id)

        project_info = None
        if project.settings:
            try:
                project_settings = json.loads(project.settings)
                b_img = project_settings.get('b_img', None)
                b_info = project_settings.get('b_info', None)
                if b_img is not None:
                    b_img = ImageAPI.get_url(request, b_img, 'original')
                if b_info is not None:
                    b_info = json.loads(b_info)

                project_info = {
                    'id': project.id,
                    'name': project.name,
                    'intro': project.intro,
                    'b_img': b_img,
                    'b_info': b_info
                }

            except json.JSONDecodeError:
                pass

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'project_info': project_info
            }
        })

class IsWorkdayView(viewsets.ModelViewSet):
    authentication_classes = []

    def retrieve(self, request, *args, **kwargs):
        dt = request.query_params.get('dt', None)
        if dt is None:
            dt = datetime.datetime.now()
        else:
            dt = datetime.datetime.strptime(dt, '%Y%m%d')

        result = is_workday(dt)

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'dt': dt.strftime('%Y-%m-%d'),
                'is_workday': result
            }
        })
