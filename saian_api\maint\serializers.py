from rest_framework import serializers

from saian_api.user.models import WebUser
from saian_api.utils.httpapi.image import ImageAPI
from .models import DeviceMaintenance

class DeviceMaintenanceModelSerializer(serializers.ModelSerializer):
    project_id = serializers.IntegerField()
    
    class Meta:
        model = DeviceMaintenance
        fields = ['id', 'user_id', 'device_type', 'total_device', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']

class DeviceMaintenanceSerializer(serializers.Serializer):
    device_ids = serializers.ListField(
        child=serializers.IntegerField(),
        required=False
    )

    def create(self, validated_data):
        return DeviceMaintenance.objects.create_m(validated_data)

    def to_representation(self, instance):
        user = WebUser.objects.get(pk=instance.user_id)
        avatar_url = ImageAPI.get_url(self.context['request'], image_id=user.avatar)
        return {
            'id': instance.id,
            'total_device': instance.total_device,
            'device_type': instance.device_type,
            'created_at': instance.created_at,
            'user': {
                'id': user.id,
                'name': user.name,
                'avatar_url': avatar_url
            }
        }
