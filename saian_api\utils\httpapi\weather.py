import json
import logging
import os

import numpy as np
import requests

from saian_api.utils.db.Redis import RedisHelper
from saian_api.utils.httpapi import DOMAIN

class WeatherApi:
    # 标准大气压下湿球温度列表
    wetbulb_list_101325_T1050_H20100 = [
        [2364, 3047, 3726, 4399, 5068, 5732, 6392, 7048, 7700, 8349, 8994, 9637, 10276, 10914, 11548, 12181, 12812, 13441, 14069, 14696, 15321, 15946,
         16571, 17194, 17818, 18442, 19066, 19690, 20314, 20939, 21565, 22192, 22820, 23449, 24079, 24710, 25343, 25977, 26613, 27251, 27890],
        [2473, 3162, 3846, 4525, 5199, 5870, 6536, 7198, 7857, 8512, 9164, 9814, 10460, 11105, 11747, 12387, 13026, 13663, 14299, 14934, 15568, 16201,
         16834, 17467, 18100, 18732, 19365, 19999, 20633, 21267, 21903, 22539, 23177, 23816, 24456, 25097, 25740, 26385, 27031, 27679, 28329],
        [2582, 3276, 3965, 4650, 5330, 6006, 6679, 7347, 8012, 8674, 9333, 9989, 10643, 11294, 11944, 12592, 13238, 13883, 14527, 15170, 15812, 16454,
         17095, 17736, 18378, 19019, 19661, 20304, 20947, 21591, 22236, 22882, 23529, 24178, 24827, 25479, 26131, 26786, 27442, 28100, 28760],
        [2690, 3390, 4084, 4775, 5461, 6142, 6821, 7495, 8166, 8835, 9500, 10163, 10824, 11482, 12139, 12794, 13448, 14101, 14752, 15403, 16054,
         16703, 17353, 18003, 18653, 19303, 19954, 20605, 21257, 21911, 22565, 23220, 23876, 24534, 25193, 25854, 26516, 27180, 27846, 28514, 29183],
        [2798, 3503, 4203, 4899, 5590, 6278, 6962, 7642, 8320, 8995, 9667, 10336, 11004, 11669, 12333, 12996, 13657, 14317, 14976, 15635, 16293,
         16951, 17609, 18267, 18925, 19584, 20243, 20903, 21564, 22226, 22889, 23553, 24219, 24885, 25554, 26224, 26895, 27569, 28244, 28921, 29599],
        [2906, 3616, 4321, 5022, 5719, 6412, 7102, 7789, 8473, 9153, 9832, 10508, 11182, 11855, 12526, 13195, 13864, 14531, 15198, 15864, 16530,
         17196, 17862, 18528, 19194, 19861, 20529, 21197, 21867, 22537, 23209, 23882, 24556, 25232, 25909, 26588, 27269, 27951, 28635, 29321, 30009],
        [3013, 3728, 4438, 5145, 5847, 6546, 7242, 7934, 8624, 9311, 9996, 10679, 11360, 12039, 12717, 13393, 14069, 14743, 15418, 16091, 16765,
         17439, 18112, 18786, 19461, 20136, 20812, 21488, 22166, 22845, 23525, 24206, 24889, 25574, 26260, 26947, 27636, 28328, 29020, 29715, 30412],
        [3120, 3840, 4555, 5267, 5975, 6679, 7381, 8079, 8775, 9468, 10159, 10849, 11536, 12222, 12906, 13590, 14272, 14954, 15636, 16317, 16998,
         17679, 18360, 19042, 19724, 20407, 21091, 21776, 22462, 23149, 23837, 24527, 25218, 25911, 26605, 27301, 27999, 28698, 29400, 30103, 30808],
        [3226, 3951, 4671, 5388, 6102, 6812, 7519, 8223, 8925, 9624, 10322, 11017, 11711, 12403, 13094, 13785, 14474, 15163, 15852, 16540, 17228,
         17917, 18606, 19295, 19985, 20676, 21368, 22060, 22754, 23449, 24145, 24843, 25542, 26243, 26946, 27650, 28356, 29064, 29773, 30485, 31198],
        [3332, 4062, 4787, 5509, 6228, 6944, 7656, 8366, 9074, 9779, 10483, 11184, 11885, 12584, 13281, 13978, 14675, 15370, 16066, 16762, 17457,
         18153, 18849, 19546, 20243, 20942, 21641, 22341, 23043, 23746, 24450, 25156, 25863, 26571, 27282, 27994, 28708, 29424, 30141, 30861, 31582],
        [3437, 4172, 4903, 5630, 6354, 7075, 7793, 8509, 9222, 9934, 10643, 11351, 12057, 12763, 13467, 14170, 14873, 15576, 16279, 16981, 17684,
         18387, 19090, 19794, 20499, 21205, 21911, 22619, 23328, 24039, 24751, 25464, 26179, 26895, 27614, 28334, 29055, 29779, 30504, 31231, 31960],
        [3542, 4282, 5018, 5750, 6479, 7206, 7929, 8651, 9370, 10087, 10802, 11516, 12229, 12940, 13651, 14361, 15071, 15780, 16489, 17199, 17908,
         18618, 19329, 20040, 20752, 21465, 22179, 22894, 23611, 24329, 25048, 25769, 26491, 27215, 27941, 28668, 29398, 30129, 30862, 31596, 32333],
        [3647, 4391, 5132, 5869, 6604, 7336, 8065, 8792, 9516, 10239, 10961, 11681, 12399, 13117, 13834, 14551, 15267, 15983, 16698, 17414, 18131,
         18848, 19565, 20283, 21002, 21723, 22444, 23166, 23890, 24615, 25342, 26070, 26799, 27531, 28264, 28999, 29735, 30474, 31214, 31956, 32700],
        [3751, 4500, 5246, 5988, 6728, 7465, 8199, 8932, 9662, 10391, 11118, 11844, 12569, 13292, 14016, 14738, 15461, 16183, 16906, 17628, 18352,
         19075, 19799, 20525, 21251, 21978, 22706, 23435, 24166, 24898, 25632, 26367, 27104, 27843, 28583, 29325, 30069, 30814, 31562, 32311, 33062],
        [3855, 4609, 5359, 6107, 6851, 7594, 8333, 9071, 9807, 10542, 11274, 12006, 12737, 13467, 14196, 14925, 15654, 16383, 17112, 17841, 18570,
         19301, 20032, 20763, 21496, 22230, 22965, 23701, 24439, 25178, 25919, 26661, 27405, 28151, 28898, 29647, 30398, 31150, 31904, 32661, 33418],
        [3958, 4717, 5472, 6225, 6974, 7722, 8467, 9210, 9951, 10691, 11430, 12167, 12904, 13640, 14375, 15110, 15845, 16580, 17316, 18051, 18787,
         19524, 20262, 21000, 21739, 22480, 23222, 23965, 24709, 25455, 26203, 26952, 27702, 28455, 29209, 29965, 30722, 31481, 32243, 33005, 33770],
        [4061, 4825, 5585, 6342, 7097, 7849, 8600, 9348, 10095, 10840, 11585, 12328, 13070, 13812, 14553, 15294, 16036, 16777, 17518, 18260, 19002,
         19746, 20490, 21235, 21980, 22728, 23476, 24226, 24977, 25729, 26483, 27239, 27996, 28755, 29516, 30278, 31043, 31808, 32576, 33346, 34117],
        [4164, 4932, 5697, 6459, 7219, 7976, 8732, 9485, 10238, 10989, 11738, 12487, 13235, 13983, 14730, 15477, 16224, 16971, 17719, 18467, 19216,
         19965, 20716, 21467, 22219, 22973, 23728, 24484, 25241, 26000, 26761, 27523, 28287, 29052, 29819, 30588, 31359, 32131, 32905, 33681, 34459],
        [4266, 5039, 5808, 6575, 7340, 8103, 8863, 9622, 10380, 11136, 11891, 12646, 13399, 14153, 14906, 15659, 16412, 17165, 17918, 18673, 19427,
         20183, 20940, 21697, 22456, 23216, 23977, 24739, 25503, 26268, 27035, 27804, 28574, 29346, 30119, 30894, 31671, 32450, 33230, 34012, 34796],
        [4368, 5145, 5919, 6691, 7461, 8228, 8994, 9758, 10521, 11283, 12043, 12803, 13562, 14321, 15080, 15839, 16598, 17357, 18116, 18877, 19637,
         20399, 21162, 21925, 22690, 23456, 24223, 24992, 25762, 26534, 27307, 28082, 28858, 29636, 30416, 31197, 31980, 32765, 33551, 34339, 35129],
        [4470, 5251, 6030, 6807, 7581, 8353, 9124, 9893, 10661, 11428, 12194, 12960, 13724, 14489, 15253, 16018, 16782, 17547, 18313, 19079, 19846,
         20613, 21382, 22151, 22922, 23694, 24468, 25242, 26019, 26796, 27576, 28356, 29139, 29923, 30708, 31496, 32285, 33075, 33868, 34662, 35458],
        [4571, 5357, 6140, 6922, 7701, 8478, 9254, 10028, 10801, 11573, 12345, 13115, 13886, 14656, 15426, 16196, 16966, 17736, 18508, 19279, 20052,
         20826, 21600, 22376, 23152, 23930, 24710, 25490, 26272, 27056, 27841, 28628, 29416, 30206, 30998, 31791, 32586, 33382, 34181, 34980, 35782],
        [4672, 5462, 6250, 7036, 7820, 8602, 9383, 10162, 10940, 11718, 12494, 13270, 14046, 14821, 15597, 16372, 17148, 17924, 18701, 19479, 20257,
         21036, 21816, 22598, 23380, 24164, 24949, 25736, 26524, 27313, 28104, 28897, 29691, 30487, 31284, 32083, 32884, 33686, 34490, 35295, 36102],
        [4772, 5567, 6360, 7150, 7939, 8726, 9511, 10295, 11079, 11861, 12643, 13424, 14205, 14986, 15767, 16548, 17329, 18111, 18893, 19676, 20460,
         21245, 22031, 22818, 23606, 24396, 25187, 25979, 26773, 27568, 28365, 29163, 29963, 30764, 31567, 32372, 33178, 33985, 34795, 35606, 36418],
        [4873, 5672, 6469, 7264, 8057, 8849, 9639, 10428, 11216, 12004, 12791, 13577, 14363, 15149, 15935, 16722, 17509, 18296, 19084, 19872, 20662,
         21452, 22244, 23037, 23830, 24626, 25422, 26220, 27019, 27820, 28622, 29426, 30231, 31038, 31847, 32657, 33468, 34282, 35096, 35913, 36731],
        [4972, 5776, 6577, 7377, 8175, 8971, 9766, 10560, 11353, 12146, 12938, 13729, 14520, 15312, 16103, 16895, 17687, 18480, 19273, 20067, 20862,
         21658, 22455, 23253, 24052, 24853, 25655, 26458, 27263, 28070, 28877, 29687, 30497, 31310, 32123, 32939, 33756, 34574, 35394, 36216, 37039],
        [5072, 5880, 6686, 7490, 8292, 9093, 9893, 10692, 11490, 12287, 13084, 13880, 14677, 15473, 16270, 17067, 17864, 18662, 19461, 20260, 21061,
         21862, 22664, 23468, 24273, 25079, 25886, 26695, 27505, 28317, 29130, 29944, 30760, 31578, 32397, 33218, 34040, 34864, 35689, 36516, 37344],
        [5171, 5983, 6793, 7602, 8409, 9214, 10019, 10823, 11625, 12428, 13229, 14031, 14832, 15634, 16436, 17238, 18040, 18843, 19647, 20452, 21258,
         22064, 22872, 23681, 24491, 25302, 26115, 26929, 27744, 28561, 29380, 30200, 31021, 31844, 32668, 33494, 34321, 35150, 35980, 36812, 37645],
        [5270, 6086, 6901, 7714, 8525, 9335, 10144, 10953, 11760, 12567, 13374, 14180, 14987, 15793, 16600, 17407, 18215, 19023, 19833, 20642, 21453,
         22265, 23078, 23892, 24707, 25524, 26342, 27161, 27982, 28804, 29627, 30452, 31279, 32106, 32936, 33767, 34599, 35433, 36268, 37104, 37942],
        [5368, 6189, 7008, 7825, 8641, 9456, 10269, 11082, 11895, 12706, 13518, 14329, 15141, 15952, 16764, 17576, 18389, 19202, 20016, 20831, 21647,
         22464, 23282, 24102, 24922, 25744, 26567, 27391, 28217, 29044, 29872, 30702, 31534, 32367, 33201, 34037, 34874, 35712, 36552, 37394, 38237],
        [5466, 6291, 7114, 7936, 8756, 9576, 10394, 11211, 12028, 12845, 13661, 14477, 15293, 16110, 16927, 17744, 18561, 19380, 20199, 21019, 21840,
         22662, 23485, 24309, 25135, 25962, 26790, 27619, 28450, 29282, 30115, 30950, 31786, 32624, 33463, 34304, 35146, 35989, 36834, 37680, 38527],
        [5564, 6393, 7220, 8046, 8871, 9695, 10518, 11340, 12161, 12983, 13803, 14624, 15445, 16267, 17088, 17910, 18733, 19556, 20380, 21205, 22031,
         22858, 23686, 24515, 25346, 26177, 27010, 27845, 28680, 29517, 30356, 31195, 32036, 32879, 33723, 34568, 35415, 36263, 37112, 37963, 38815],
        [5661, 6495, 7326, 8157, 8986, 9814, 10641, 11468, 12294, 13120, 13945, 14771, 15597, 16423, 17249, 18076, 18903, 19731, 20560, 21390, 22221,
         23053, 23886, 24720, 25555, 26392, 27229, 28069, 28909, 29751, 30594, 31438, 32284, 33131, 33980, 34830, 35681, 36534, 37387, 38243, 39099],
        [5759, 6596, 7432, 8266, 9100, 9932, 10764, 11595, 12425, 13256, 14086, 14916, 15747, 16577, 17409, 18240, 19072, 19905, 20739, 21574, 22409,
         23246, 24084, 24923, 25763, 26604, 27447, 28290, 29136, 29982, 30830, 31679, 32529, 33381, 34234, 35089, 35944, 36801, 37660, 38519, 39380],
        [5855, 6697, 7537, 8375, 9213, 10050, 10886, 11721, 12557, 13391, 14226, 15061, 15896, 16732, 17567, 18404, 19241, 20078, 20917, 21756, 22597,
         23438, 24280, 25124, 25969, 26815, 27662, 28510, 29360, 30211, 31064, 31917, 32772, 33629, 34486, 35345, 36205, 37067, 37929, 38793, 39658],
        [5952, 6797, 7641, 8484, 9326, 10167, 11008, 11848, 12687, 13526, 14366, 15205, 16045, 16885, 17725, 18566, 19408, 20250, 21093, 21937, 22782,
         23628, 24475, 25324, 26173, 27024, 27875, 28728, 29583, 30438, 31295, 32153, 33013, 33874, 34736, 35599, 36463, 37329, 38196, 39064, 39933],
        [6048, 6898, 7746, 8593, 9439, 10284, 11129, 11973, 12817, 13661, 14505, 15348, 16193, 17037, 17882, 18728, 19574, 20421, 21268, 22117, 22967,
         23817, 24669, 25522, 26376, 27231, 28087, 28945, 29804, 30664, 31525, 32387, 33251, 34116, 34982, 35850, 36719, 37589, 38460, 39332, 40205],
        [6144, 6997, 7850, 8701, 9551, 10400, 11249, 12098, 12946, 13794, 14643, 15491, 16340, 17189, 18038, 18888, 19739, 20590, 21442, 22296, 23150,
         24005, 24861, 25718, 26577, 27436, 28297, 29159, 30022, 30887, 31752, 32619, 33487, 34357, 35227, 36099, 36972, 37846, 38721, 39597, 40474],
        [6240, 7097, 7953, 8808, 9663, 10516, 11370, 12222, 13075, 13927, 14780, 15633, 16486, 17339, 18193, 19048, 19903, 20759, 21615, 22473, 23332,
         24191, 25052, 25913, 26776, 27640, 28505, 29372, 30239, 31108, 31978, 32849, 33721, 34595, 35469, 36345, 37222, 38100, 38979, 39860, 40741],
        [6335, 7196, 8056, 8915, 9774, 10632, 11489, 12346, 13203, 14060, 14917, 15774, 16631, 17489, 18347, 19206, 20066, 20926, 21787, 22649, 23512,
         24376, 25241, 26107, 26974, 27843, 28712, 29583, 30454, 31327, 32201, 33077, 33953, 34831, 35709, 36589, 37470, 38352, 39235, 40119, 41004],
        [6430, 7295, 8159, 9022, 9885, 10747, 11608, 12469, 13331, 14192, 15053, 15914, 16776, 17638, 18500, 19364, 20228, 21092, 21958, 22824, 23691,
         24560, 25429, 26299, 27171, 28043, 28917, 29792, 30668, 31545, 32423, 33302, 34183, 35064, 35947, 36831, 37716, 38602, 39489, 40377, 41265],
        [6524, 7393, 8261, 9129, 9995, 10861, 11727, 12592, 13457, 14323, 15188, 16054, 16920, 17786, 18653, 19520, 20389, 21257, 22127, 22998, 23869,
         24742, 25615, 26490, 27366, 28242, 29120, 29999, 30879, 31760, 32642, 33526, 34410, 35296, 36182, 37070, 37959, 38849, 39739, 40631, 41524],
        [6619, 7492, 8364, 9235, 10105, 10975, 11845, 12714, 13584, 14453, 15323, 16192, 17063, 17933, 18804, 19676, 20548, 21422, 22296, 23170,
         24046, 24923, 25801, 26679, 27559, 28440, 29322, 30205, 31089, 31974, 32860, 33747, 34636, 35525, 36416, 37307, 38200, 39093, 39988, 40883,
         41780],
        [6713, 7590, 8465, 9340, 10215, 11089, 11963, 12836, 13709, 14583, 15457, 16331, 17205, 18080, 18955, 19831, 20707, 21585, 22463, 23342,
         24222, 25103, 25985, 26867, 27751, 28636, 29522, 30409, 31297, 32186, 33076, 33967, 34859, 35753, 36647, 37542, 38438, 39336, 40234, 41133,
         42033],
        [6807, 7687, 8567, 9446, 10324, 11202, 12080, 12957, 13835, 14712, 15590, 16468, 17346, 18225, 19105, 19985, 20866, 21747, 22629, 23512,
         24396, 25281, 26167, 27054, 27942, 28831, 29720, 30611, 31503, 32396, 33290, 34185, 35081, 35978, 36876, 37775, 38675, 39576, 40478, 41380,
         42284],
        [6900, 7784, 8668, 9550, 10433, 11315, 12196, 13078, 13959, 14841, 15723, 16605, 17487, 18370, 19254, 20138, 21023, 21908, 22794, 23682,
         24570, 25458, 26348, 27239, 28131, 29024, 29917, 30812, 31708, 32605, 33502, 34401, 35301, 36202, 37103, 38006, 38909, 39814, 40719, 41625,
         42532],
        [6993, 7881, 8768, 9655, 10541, 11427, 12312, 13198, 14083, 14969, 15855, 16741, 17627, 18514, 19402, 20290, 21179, 22068, 22959, 23850,
         24742, 25635, 26528, 27423, 28319, 29215, 30113, 31011, 31911, 32811, 33713, 34615, 35519, 36423, 37328, 38234, 39141, 40049, 40958, 41868,
         42778],
        [7086, 7978, 8869, 9759, 10649, 11539, 12428, 13317, 14207, 15096, 15986, 16876, 17767, 18658, 19549, 20441, 21334, 22227, 23122, 24017,
         24913, 25809, 26707, 27606, 28505, 29405, 30307, 31209, 32112, 33017, 33922, 34828, 35735, 36643, 37551, 38461, 39372, 40283, 41195, 42108,
         43022],
        [7179, 8074, 8969, 9863, 10757, 11650, 12543, 13437, 14330, 15223, 16117, 17011, 17905, 18800, 19696, 20592, 21488, 22386, 23284, 24183,
         25083, 25983, 26885, 27787, 28690, 29594, 30499, 31405, 32312, 33220, 34129, 35038, 35949, 36860, 37773, 38686, 39600, 40514, 41430, 42346,
         43263],
        [7271, 8170, 9068, 9966, 10864, 11761, 12658, 13555, 14452, 15350, 16247, 17145, 18043, 18942, 19841, 20741, 21642, 22543, 23445, 24348,
         25251, 26156, 27061, 27967, 28874, 29782, 30690, 31600, 32511, 33422, 34334, 35247, 36161, 37076, 37992, 38908, 39826, 40744, 41662, 42582,
         43502],
        [7363, 8266, 9168, 10069, 10970, 11871, 12772, 13673, 14574, 15475, 16377, 17278, 18180, 19083, 19986, 20890, 21794, 22699, 23605, 24512,
         25419, 26327, 27236, 28146, 29056, 29968, 30880, 31793, 32707, 33622, 34538, 35455, 36372, 37290, 38209, 39129, 40050, 40971, 41893, 42816,
         43739],
        [7455, 8361, 9267, 10172, 11077, 11981, 12886, 13791, 14695, 15600, 16506, 17411, 18317, 19223, 20130, 21038, 21946, 22855, 23764, 24674,
         25585, 26497, 27410, 28323, 29237, 30152, 31068, 31985, 32903, 33821, 34740, 35660, 36581, 37502, 38425, 39348, 40272, 41196, 42121, 43047,
         43974],
        [7546, 8456, 9365, 10274, 11183, 12091, 12999, 13908, 14816, 15725, 16634, 17543, 18453, 19363, 20274, 21185, 22097, 23009, 23922, 24836,
         25751, 26666, 27583, 28499, 29417, 30336, 31255, 32175, 33096, 34018, 34941, 35864, 36788, 37713, 38639, 39565, 40492, 41420, 42348, 43277,
         44207],
        [7637, 8551, 9463, 10376, 11288, 12200, 13112, 14024, 14936, 15849, 16762, 17674, 18588, 19502, 20416, 21331, 22247, 23163, 24080, 24997,
         25915, 26834, 27754, 28675, 29596, 30518, 31441, 32364, 33289, 34214, 35140, 36066, 36994, 37922, 38851, 39780, 40710, 41641, 42572, 43505,
         44437],
        [7728, 8645, 9561, 10477, 11393, 12309, 13225, 14140, 15056, 15972, 16889, 17805, 18722, 19640, 20558, 21476, 22396, 23315, 24236, 25157,
         26079, 27001, 27925, 28848, 29773, 30699, 31625, 32552, 33480, 34408, 35337, 36267, 37198, 38129, 39061, 39993, 40927, 41861, 42795, 43730,
         44666],
        [7819, 8739, 9659, 10579, 11498, 12417, 13337, 14256, 15175, 16095, 17015, 17935, 18856, 19777, 20699, 21621, 22544, 23467, 24391, 25316,
         26241, 27167, 28094, 29021, 29949, 30878, 31808, 32738, 33669, 34601, 35533, 36466, 37400, 38334, 39269, 40205, 41141, 42078, 43016, 43954,
         44892],
        [7909, 8833, 9756, 10680, 11602, 12525, 13448, 14371, 15294, 16217, 17141, 18065, 18989, 19914, 20839, 21765, 22691, 23618, 24546, 25474,
         26402, 27332, 28262, 29193, 30124, 31056, 31989, 32923, 33857, 34792, 35728, 36664, 37601, 38538, 39476, 40415, 41354, 42294, 43235, 44175,
         45117],
        [7999, 8927, 9853, 10780, 11706, 12633, 13559, 14486, 15412, 16339, 17266, 18194, 19122, 20050, 20979, 21908, 22838, 23768, 24699, 25631,
         26563, 27496, 28429, 29363, 30298, 31233, 32170, 33106, 34044, 34982, 35921, 36860, 37800, 38740, 39681, 40623, 41565, 42508, 43451, 44395,
         45340],
        [8089, 9020, 9950, 10880, 11810, 12740, 13670, 14600, 15530, 16460, 17391, 18322, 19253, 20185, 21118, 22050, 22983, 23917, 24852, 25787,
         26722, 27658, 28595, 29533, 30471, 31409, 32349, 33289, 34229, 35170, 36112, 37054, 37997, 38941, 39885, 40830, 41775, 42720, 43667, 44613,
         45560],
        [8179, 9113, 10046, 10980, 11913, 12847, 13780, 14714, 15647, 16581, 17515, 18450, 19385, 20320, 21256, 22192, 23128, 24066, 25003, 25942,
         26881, 27820, 28760, 29701, 30642, 31584, 32526, 33469, 34413, 35357, 36302, 37248, 38194, 39140, 40087, 41035, 41983, 42931, 43880, 44830,
         45779],
        [8268, 9205, 10142, 11079, 12016, 12953, 13890, 14827, 15764, 16701, 17639, 18577, 19515, 20454, 21393, 22332, 23273, 24213, 25154, 26096,
         27038, 27981, 28924, 29868, 30812, 31757, 32703, 33649, 34596, 35543, 36491, 37439, 38388, 39338, 40287, 41238, 42189, 43140, 44092, 45044,
         45997],
        [8357, 9298, 10238, 11178, 12119, 13059, 13999, 14940, 15880, 16821, 17762, 18703, 19645, 20587, 21530, 22472, 23416, 24360, 25304, 26249,
         27194, 28140, 29087, 30034, 30981, 31930, 32878, 33827, 34777, 35727, 36678, 37629, 38581, 39534, 40486, 41439, 42393, 43347, 44302, 45257,
         46212],
        [8446, 9390, 10334, 11277, 12221, 13165, 14108, 15052, 15996, 16940, 17885, 18829, 19774, 20720, 21666, 22612, 23558, 24506, 25453, 26401,
         27350, 28299, 29249, 30199, 31149, 32101, 33052, 34005, 34957, 35910, 36864, 37818, 38773, 39728, 40684, 41640, 42596, 43553, 44510, 45467,
         46425],
        [8534, 9482, 10429, 11376, 12323, 13270, 14217, 15164, 16111, 17059, 18006, 18955, 19903, 20852, 21801, 22750, 23700, 24651, 25601, 26553,
         27504, 28457, 29409, 30363, 31316, 32271, 33225, 34180, 35136, 36092, 37049, 38006, 38963, 39921, 40879, 41838, 42797, 43757, 44717, 45677,
         46637],
        [8622, 9573, 10523, 11474, 12424, 13374, 14325, 15275, 16226, 17177, 18128, 19079, 20031, 20983, 21935, 22888, 23841, 24795, 25749, 26703,
         27658, 28613, 29569, 30526, 31482, 32439, 33397, 34355, 35314, 36273, 37232, 38192, 39152, 40113, 41074, 42035, 42997, 43959, 44921, 45884,
         46847],
        [8710, 9664, 10618, 11572, 12525, 13479, 14432, 15386, 16340, 17294, 18249, 19203, 20158, 21114, 22069, 23025, 23981, 24938, 25895, 26853,
         27811, 28769, 29728, 30687, 31647, 32607, 33568, 34529, 35490, 36452, 37414, 38377, 39340, 40303, 41267, 42231, 43195, 44160, 45125, 46090,
         47056],
        [8798, 9755, 10712, 11669, 12626, 13583, 14540, 15497, 16454, 17411, 18369, 19327, 20285, 21244, 22202, 23161, 24121, 25081, 26041, 27002,
         27963, 28924, 29886, 30848, 31811, 32774, 33737, 34701, 35665, 36630, 37595, 38560, 39526, 40492, 41458, 42425, 43392, 44359, 45327, 46295,
         47263],
        [8886, 9846, 10806, 11766, 12726, 13686, 14647, 15607, 16567, 17528, 18489, 19450, 20411, 21373, 22335, 23297, 24260, 25223, 26186, 27150,
         28114, 29078, 30043, 31008, 31973, 32939, 33906, 34872, 35839, 36807, 37774, 38742, 39711, 40679, 41648, 42617, 43587, 44557, 45527, 46497,
         47468],
        [8973, 9936, 10900, 11863, 12826, 13790, 14753, 15717, 16680, 17644, 18608, 19572, 20537, 21502, 22467, 23432, 24398, 25364, 26330, 27297,
         28264, 29231, 30199, 31167, 32135, 33104, 34073, 35042, 36012, 36982, 37952, 38923, 39894, 40865, 41837, 42809, 43781, 44753, 45726, 46698,
         47672],
        [9060, 10026, 10993, 11959, 12926, 13892, 14859, 15826, 16793, 17760, 18727, 19694, 20662, 21630, 22598, 23566, 24535, 25504, 26473, 27443,
         28413, 29383, 30354, 31325, 32296, 33267, 34239, 35211, 36184, 37156, 38129, 39103, 40076, 41050, 42024, 42998, 43973, 44948, 45923, 46898,
         47874],
        [9146, 10116, 11086, 12055, 13025, 13995, 14965, 15935, 16905, 17875, 18845, 19816, 20786, 21757, 22728, 23700, 24672, 25644, 26616, 27588,
         28561, 29534, 30508, 31481, 32455, 33430, 34404, 35379, 36354, 37329, 38305, 39281, 40257, 41233, 42210, 43187, 44164, 45141, 46119, 47096,
         48074],
        [9233, 10206, 11178, 12151, 13124, 14097, 15070, 16043, 17016, 17989, 18963, 19936, 20910, 21884, 22858, 23833, 24808, 25782, 26758, 27733,
         28709, 29685, 30661, 31637, 32614, 33591, 34568, 35546, 36523, 37501, 38480, 39458, 40437, 41416, 42395, 43374, 44353, 45333, 46313, 47293,
         48273],
        [9319, 10295, 11271, 12247, 13223, 14199, 15175, 16151, 17127, 18103, 19080, 20057, 21033, 22010, 22988, 23965, 24943, 25921, 26899, 27877,
         28855, 29834, 30813, 31792, 32772, 33751, 34731, 35711, 36692, 37672, 38653, 39634, 40615, 41596, 42578, 43560, 44541, 45524, 46506, 47488,
         48471],
        [9405, 10384, 11363, 12342, 13321, 14300, 15279, 16258, 17238, 18217, 19197, 20176, 21156, 22136, 23116, 24097, 25077, 26058, 27039, 28020,
         29001, 29983, 30964, 31946, 32929, 33911, 34893, 35876, 36859, 37842, 38825, 39809, 40792, 41776, 42760, 43744, 44728, 45713, 46697, 47682,
         48667],
        [9491, 10473, 11455, 12437, 13419, 14401, 15383, 16365, 17348, 18330, 19313, 20295, 21278, 22261, 23244, 24228, 25211, 26195, 27178, 28162,
         29146, 30130, 31115, 32100, 33084, 34069, 35054, 36040, 37025, 38011, 38996, 39982, 40968, 41954, 42941, 43927, 44914, 45900, 46887, 47874,
         48861],
        [9576, 10561, 11546, 12531, 13517, 14502, 15487, 16472, 17458, 18443, 19428, 20414, 21400, 22386, 23372, 24358, 25344, 26330, 27317, 28304,
         29290, 30277, 31264, 32252, 33239, 34227, 35214, 36202, 37190, 38178, 39166, 40155, 41143, 42132, 43120, 44109, 45098, 46087, 47076, 48065,
         49054],
        [9661, 10649, 11637, 12626, 13614, 14602, 15590, 16578, 17567, 18555, 19544, 20532, 21521, 22510, 23498, 24487, 25476, 26466, 27455, 28444,
         29434, 30423, 31413, 32403, 33393, 34383, 35373, 36364, 37354, 38344, 39335, 40326, 41317, 42307, 43298, 44290, 45281, 46272, 47263, 48255,
         49246],
        [9746, 10737, 11728, 12720, 13711, 14702, 15693, 16684, 17676, 18667, 19658, 20650, 21641, 22633, 23625, 24616, 25608, 26600, 27592, 28584,
         29576, 30569, 31561, 32553, 33546, 34539, 35531, 36524, 37517, 38510, 39503, 40496, 41489, 42482, 43475, 44469, 45462, 46456, 47449, 48443,
         49436],
        [9831, 10825, 11819, 12813, 13807, 14801, 15796, 16790, 17784, 18778, 19773, 20767, 21761, 22756, 23750, 24745, 25739, 26734, 27729, 28723,
         29718, 30713, 31708, 32703, 33698, 34693, 35688, 36683, 37679, 38674, 39669, 40665, 41660, 42656, 43651, 44647, 45643, 46638, 47634, 48630,
         49625],
        [9915, 10912, 11909, 12906, 13904, 14901, 15898, 16895, 17892, 18889, 19886, 20883, 21881, 22878, 23875, 24872, 25870, 26867, 27864, 28862,
         29859, 30857, 31854, 32852, 33849, 34847, 35844, 36842, 37840, 38837, 39835, 40833, 41830, 42828, 43826, 44824, 45822, 46819, 47817, 48815,
         49813],
        [10000, 11000, 12000, 13000, 14000, 15000, 16000, 17000, 18000, 19000, 20000, 21000, 22000, 23000, 24000, 25000, 26000, 27000, 28000, 29000,
         30000, 31000, 32000, 33000, 34000, 35000, 36000, 37000, 38000, 39000, 40000, 41000, 42000, 43000, 44000, 45000, 46000, 47000, 48000, 49000,
         50000]
    ]

    @classmethod
    def get_wetbulb_temp(cls, temp, rh, pressure_pa=101325, table_xlow=10, table_ylow=20):
        """
        根据温湿度从 wetbulb_list_101325_T1050_H20100 查询对应的湿球温度
        参数:
            temp = 温度，10~50摄氏度。不在范围内，调用 calc_wetbulb_temp 方法计算
            rh = 湿度，20-100%。不在范围内，调用 calc_wetbulb_temp 方法计算
            返回值:湿球温度摄氏度
        """

        wetbulb_list = cls.wetbulb_list_101325_T1050_H20100
        # 超过表范围判断
        # 小于Zx，Zy
        if temp < table_xlow or rh < table_ylow:
            return cls.calc_wetbulb_temp(temp, rh, pressure_pa)
        # 大于Zx，Zy
        if temp > len(wetbulb_list[0]) - 1 + table_xlow or rh > len(wetbulb_list) - 1 + table_ylow:
            return cls.calc_wetbulb_temp(temp, rh, pressure_pa)

        # 表实际索引，因为表不是从0开始，所以有偏移量
        index_x = int(temp - table_xlow)
        index_y = int(rh - table_ylow)

        # x,y用于坐标计算
        x = int(temp)
        y = int(rh)

        # 先计算x的两个插值点
        if index_x == len(wetbulb_list[0]) - 1:
            Q12 = (x + 1 - temp) / (x + 1 - x) * wetbulb_list[index_y][index_x] + (temp - x) / (x + 1 - x) * wetbulb_list[index_y][index_x]
        else:
            Q12 = (x + 1 - temp) / (x + 1 - x) * wetbulb_list[index_y][index_x] + (temp - x) / (x + 1 - x) * wetbulb_list[index_y][index_x + 1]
        # 判断索引与数组边界，xy在最边时取最边的值计算，防止数组越界
        if (index_x == len(wetbulb_list[0]) - 1) and (index_y == len(wetbulb_list) - 1):
            Q22 = (x + 1 - temp) / (x + 1 - x) * wetbulb_list[index_y][index_x] + (temp - x) / (x + 1 - x) * wetbulb_list[index_y][index_x]
        elif (index_x < len(wetbulb_list[0]) - 1) and (index_y == len(wetbulb_list) - 1):
            Q22 = (x + 1 - temp) / (x + 1 - x) * wetbulb_list[index_y][index_x] + (temp - x) / (x + 1 - x) * wetbulb_list[index_y][index_x + 1]
        elif (index_x == len(wetbulb_list[0]) - 1) and (index_y < len(wetbulb_list) - 1):
            Q22 = (x + 1 - temp) / (x + 1 - x) * wetbulb_list[index_y + 1][index_x] + (temp - x) / (x + 1 - x) * wetbulb_list[index_y + 1][index_x]
        else:
            Q22 = (x + 1 - temp) / (x + 1 - x) * wetbulb_list[index_y + 1][index_x] + (temp - x) / (x + 1 - x) * wetbulb_list[index_y + 1][
                index_x + 1]

        # 再计算y坐标的插值
        Q = (y + 1 - rh) / (y + 1 - y) * Q12 + (rh - y) / (y + 1 - y) * Q22

        # 返回计算好的插值，转换为带小数摄氏度输出,湿球温度不可小于0
        if Q / 1000 > 0:
            Q /= 1000
        else:
            Q = 0

        return Q

    @staticmethod
    def get_weather(city_id):
        weather = RedisHelper.get_hvalue(0, f'weather:{city_id}')
        if weather:
            return weather

        appsecret = os.environ['DRF_WEATHER_SECRET']
        appid = os.environ['DRF_WEATHER_APPID']
        try:
            # DOMAIN = 'https://v1.yiketianqi.com'
            url = f'{DOMAIN}/api?unescape=1&version=v62&appid={appid}&appsecret={appsecret}&cityid={city_id}'
            result = requests.get(url, timeout=(5, 10)).json()
            cache_weather = {}
            for k, v in result.items():
                if isinstance(v, str):
                    cache_weather[k] = v
            RedisHelper.set_hvalue(0, f'weather:{city_id}', cache_weather, 600)

            return result
        except Exception as e:
            from saian_api.dashboard.models import ProjectWeather

            logging.error(f'查询天气出错 {e.__str__()}')
            project_weather = ProjectWeather.objects.filter(type='hr').order_by('-created_at').first()
            temp = project_weather.temp
            humidity = project_weather.humidity
            return {
                'wea': '多云',
                "wea_img": "yun",
                "air_pm25": "15",
                'tem': temp,
                'tem1': temp,
                'tem2': temp,
                'humidity': f'{humidity}%'
            }

    @classmethod
    def calc_wetbulb_temp(cls, temp, humidity, pressure_pa=101325):
        """计算湿球温度，备用"""
        wetbulb, _, _ = WeatherApi.web_bulb(np.array([float(temp)]), np.array([float(pressure_pa)]), np.array([float(humidity)]), 1)
        if len(wetbulb) and wetbulb[0] != np.nan:
            wetbulb_temp = wetbulb[0]
            return round(float(wetbulb_temp), 2)
        return None

    @classmethod
    def QSat_2(cls, T_k, p_t):
        """
        [es_mb,rs,de_mbdT,dlnes_mbdT,rsdT,foftk,fdt]=QSat_2(T_k, p_t)

        DESCRIPTION:
            Computes saturation mixing ratio and the change in saturation
            mixing ratio with respect to temperature.  Uses Bolton eqn 10, 39.
            Davies-Jones eqns 2.3,A.1-A.10
            Reference:  Bolton: The computation of equivalent potential temperature.
                Monthly Weather Review (1980) vol. 108 (7) pp. 1046-1053
                Davies-Jones: An efficient and accurate method for computing the
                wet-bulb temperature along pseudoadiabats. Monthly Weather Review
                (2008) vol. 136 (7) pp. 2764-2785

        INPUTS:
          T_k        temperature (K)
          p_t        surface atmospheric pressure (pa)

          T_k and p_t should be arrays of identical dimensions.

        OUTPUTS:

          es_mb      vapor pressure (pa)
          rs       	 humidity (kg/kg)
          de_mbdT    d(es)/d(T)
          dlnes_mbdT dln(es)/d(T)
          rsdT     	 d(qs)/d(T)
          foftk      Davies-Jones eqn 2.3
          fdT     	 d(f)/d(T)

        Ported from HumanIndexMod by Jonathan R Buzan 08/08/13
        MATLAB port by Robert Kopp

        Last updated by Robert Kopp, robert-dot-kopp-at-rutgers-dot-edu, Wed Sep 02 22:22:25 EDT 2015
        """

        SHR_CONST_TKFRZ = 273.15

        lambd_a = 3.504  # Inverse of Heat Capacity
        alpha = 17.67  # Constant to calculate vapour pressure
        beta = 243.5  # Constant to calculate vapour pressure
        epsilon = 0.6220  # Conversion between pressure/mixing ratio
        es_C = 6.112  # Vapour Pressure at Freezing STD (mb)
        vkp = 0.2854  # Heat Capacity
        y0 = 3036  # constant
        y1 = 1.78  # constant
        y2 = 0.448  # constant
        Cf = SHR_CONST_TKFRZ  # Freezing Temp (K)
        refpres = 1000  # Reference Pressure (mb)

        # $$$  p_tmb			% Pressure (mb)
        # $$$  ndimpress		% Non-dimensional Pressure
        # $$$  prersdt			% Place Holder for derivative humidity
        # $$$  pminuse			% Vapor Pressure Difference (mb)
        # $$$  tcfbdiff		% Temp diff ref (C)
        # $$$  p0ndplam		% dimensionless pressure modified by ref pressure
        # $$$
        # $$$  rsy2rs2			% Constant function of humidity
        # $$$  oty2rs			% Constant function of humidity
        # $$$  y0tky1			% Constant function of Temp
        # $$$
        # $$$  d2e_mbdT2		% d2(es)/d(T)2
        # $$$  d2rsdT2			% d2(r)/d(T)2
        # $$$  goftk			% g(T) exponential in f(T)
        # $$$  gdT			% d(g)/d(T)
        # $$$  d2gdT2			% d2(g)/d(T)2
        # $$$
        # $$$  d2fdT2			% d2(f)/d(T)2  (K)
        #
        # -----------------------------------------------------------------------
        # Constants used to calculate es(T)
        # Clausius-Clapeyron
        p_tmb = p_t * 0.01
        tcfbdiff = T_k - Cf + beta
        es_mb = es_C * np.exp(alpha * (T_k - Cf) / (tcfbdiff))
        dlnes_mbdT = alpha * beta / ((tcfbdiff) * (tcfbdiff))
        pminuse = p_tmb - es_mb
        de_mbdT = es_mb * dlnes_mbdT
        d2e_mbdT2 = dlnes_mbdT * (de_mbdT - 2 * es_mb / (tcfbdiff))

        # Constants used to calculate rs(T)
        ndimpress = (p_tmb / refpres) ** vkp
        p0ndplam = refpres * ndimpress ** lambd_a
        rs = epsilon * es_mb / (p0ndplam - es_mb + np.spacing(1))  # eps)
        prersdt = epsilon * p_tmb / ((pminuse) * (pminuse))
        rsdT = prersdt * de_mbdT
        d2rsdT2 = prersdt * (d2e_mbdT2 - de_mbdT * de_mbdT * (2 / (pminuse)))

        # Constants used to calculate g(T)
        rsy2rs2 = rs + y2 * rs * rs
        oty2rs = 1 + 2.0 * y2 * rs
        y0tky1 = y0 / T_k - y1
        goftk = y0tky1 * (rs + y2 * rs * rs)
        gdT = - y0 * (rsy2rs2) / (T_k * T_k) + (y0tky1) * (oty2rs) * rsdT
        d2gdT2 = 2.0 * y0 * rsy2rs2 / (T_k * T_k * T_k) - 2.0 * y0 * rsy2rs2 * (
            oty2rs) * rsdT + y0tky1 * 2.0 * y2 * rsdT * rsdT + y0tky1 * oty2rs * d2rsdT2

        # Calculations for used to calculate f(T,ndimpress)
        # print('Cf/T_k = '+str(Cf/T_k)+', '+str(lambd_a))
        # print('vkp*lambd_a = '+ str(vkp)+', '+str(lambd_a))
        # print('1-es_mb/p0ndplam = '+str(1 - es_mb/p0ndplam))
        # exit()
        foftk = ((Cf / T_k) ** lambd_a) * (np.abs(1 - es_mb / p0ndplam)) ** (vkp * lambd_a) * np.exp(-lambd_a * goftk)
        fdT = -lambd_a * (1.0 / T_k + vkp * de_mbdT / pminuse + gdT)
        d2fdT2 = lambd_a * (1.0 / (T_k * T_k) - vkp * de_mbdT * de_mbdT / (pminuse * pminuse) - vkp * d2e_mbdT2 / pminuse - d2gdT2)

        # avoid bad numbers
        rs[rs > 1] = np.nan
        rs[rs < 0] = np.nan
        return es_mb, rs, de_mbdT, dlnes_mbdT, rsdT, foftk, fdT

    @classmethod
    def web_bulb(cls, temperature, pressure, humidity, humidity_mode=0):
        # temperature = 0
        # pressure = 0
        # humidity = 0
        # humidity_mode = 0
        SHR_CONST_TKFRZ = 273.15
        TemperatureK = temperature + SHR_CONST_TKFRZ

        constA = 2675  # Constant used for extreme cold temparatures (K)
        grms = 1000  # Gram per Kilogram (g/kg)
        p0 = 1000  # surface pressure (mb)

        kappad = 0.2854  # Heat Capacity

        C = SHR_CONST_TKFRZ  # Freezing Temperature
        pmb = pressure * 0.01  # pa to mb
        T1 = TemperatureK  # Use holder for T

        es_mb, rs = cls.QSat_2(TemperatureK, pressure)[0:2]  # first two returned values

        if humidity_mode == 0:
            qin = humidity  # specific humidity
            relhum = 100.0 * qin / rs  # relative humidity (%)
            vapemb = es_mb * relhum * 0.01  # vapor pressure (mb)
        elif humidity_mode == 1:
            relhum = humidity  # relative humidity (%)
            qin = rs * relhum * 0.01  # specific humidity
            vapemb = es_mb * relhum * 0.01  # vapor pressure (mb)
        # end

        mixr = qin * grms  # change specific humidity to mixing ratio (g/kg)

        #    real(r8) :: k1;		    % Quadratic Parameter (C)
        #    real(r8) :: k2;		 	% Quadratic Parameter scaled by X (C)
        #    real(r8) :: pmb;		 	% Atmospheric Surface pressure (mb)
        #    real(r8) :: D;		 	    % Linear Interpolation of X

        #   real(r8) :: hot	% Dimensionless Quantity used for changing temperature regimes
        #    real(r8) :: cold	% Dimensionless Quantity used for changing temperature regimes

        #   real(r8) :: T1     	 		% Temperature (K)
        #   real(r8) :: vapemb        	% Vapour pressure (mb)
        #   real(r8) :: mixr        	% Mixing Ratio (g/kg)

        #   real(r8) :: es_mb_teq		% saturated vapour pressure for wrt TEQ (mb)
        #   real(r8) :: de_mbdTeq		% Derivative of Saturated Vapour pressure wrt TEQ (mb/K)
        #   real(r8) :: dlnes_mbdTeq	% Log derivative of the sat. vap pressure wrt TEQ (mb/K)
        #   real(r8) :: rs_teq			% Mixing Ratio wrt TEQ (kg/kg)
        #   real(r8) :: rsdTeq			% Derivative of Mixing Ratio wrt TEQ (kg/kg/K)
        #   real(r8) :: foftk_teq		% Function of EPT wrt TEQ
        #   real(r8) :: fdTeq			% Derivative of Function of EPT wrt TEQ

        #   real(r8) :: wb_temp			    % Wet Bulb Temperature First Guess (C)
        #   real(r8) :: es_mb_wb_temp		% Vapour pressure wrt Wet Bulb Temp (mb)
        #   real(r8) :: de_mbdwb_temp		% Derivative of Sat. Vapour pressure wrt WB Temp (mb/K)
        #   real(r8) :: dlnes_mbdwb_temp	% Log Derivative of sat. vap. pressure wrt WB Temp (mb/K)
        #   real(r8) :: rs_wb_temp		    % Mixing Ratio wrt WB Temp (kg/kg)
        #   real(r8) :: rsdwb_temp		    % Derivative of Mixing Ratio wrt WB Temp (kg/kg/K)
        #   real(r8) :: foftk_wb_temp		% Function of EPT wrt WB Temp
        #   real(r8) :: fdwb_temp		    % Derivative of function of EPT wrt WB Temp

        #   real(r8) :: tl		 	        % Lifting Condensation Temperature (K)
        #   real(r8) :: theta_dl	 	    % Moist Potential Temperature (K)
        #   real(r8) :: pnd		 	        % Non dimensional pressure
        #   real(r8) :: X		 	        % Ratio of equivalent temperature to freezing scaled by Heat Capacity

        # -----------------------------------------------------------------------

        # Calculate Equivalent Pot. Temp (pmb, T, mixing ratio (g/kg), pott, epott)
        # Calculate Parameters for Wet Bulb Temp (epott, pmb)
        pnd = (pmb / p0) ** (kappad)
        D = 1.0 / (0.1859 * pmb / p0 + 0.6512)
        k1 = -38.5 * pnd * pnd + 137.81 * pnd - 53.737
        k2 = -4.392 * pnd * pnd + 56.831 * pnd - 0.384

        # Calculate lifting condensation level.  first eqn
        # uses vapor pressure (mb)
        # 2nd eqn uses relative humidity.
        # first equation: Bolton 1980 Eqn 21.
        #   tl = (2840/(3.5*log(T1) - log(vapemb) - 4.805)) + 55;
        # second equation: Bolton 1980 Eqn 22.  relhum = relative humidity
        tl = (1.0 / ((1.0 / ((T1 - 55))) - (np.log(relhum / 100.0) / 2840.0))) + 55.0

        # Theta_DL: Bolton 1980 Eqn 24.
        theta_dl = T1 * ((p0 / (pmb - vapemb)) ** kappad) * ((T1 / tl) ** (mixr * 0.00028))
        # EPT: Bolton 1980 Eqn 39.
        epott = theta_dl * np.exp(((3.036 / tl) - 0.00178) * mixr * (1 + 0.000448 * mixr))
        Teq = epott * pnd  # Equivalent Temperature at pressure
        X = (C / Teq) ** 3.504

        # Calculates the regime requirements of wet bulb equations.
        invalid = (Teq > 600) + (Teq < 200)
        hot = (Teq > 355.15)
        cold = ((X >= 1) * (X <= D))
        X[invalid == 1] = np.nan
        Teq[invalid == 1] = np.nan

        # Calculate Wet Bulb Temperature, initial guess
        # Extremely cold regime if X.gt.D then need to
        # calculate dlnesTeqdTeq

        es_mb_teq, rs_teq, de_mbdTeq, dlnes_mbdTeq, rsdTeq, foftk_teq, fdTeq = cls.QSat_2(Teq, pressure)
        wb_temp = Teq - C - ((constA * rs_teq) / (1 + (constA * rs_teq * dlnes_mbdTeq)))
        sub = np.where(X <= D)
        wb_temp[sub] = (k1[sub] - 1.21 * cold[sub] - 1.45 * hot[sub] - (k2[sub] - 1.21 * cold[sub]) * X[sub] + (0.58 / X[sub]) * hot[sub])
        wb_temp[invalid == 1] = np.nan

        # Newton-Raphson Method

        maxiter = 3
        iter = 0
        delta = 1e6 * np.ones_like(wb_temp)

        while (np.max(delta) > 0.01) and (iter <= maxiter):
            es_mb_wb_temp, rs_wb_temp, de_mbdwb_temp, dlnes_mbdwb_temp, rsdwb_temp, foftk_wb_temp, fdwb_temp = cls.QSat_2(wb_temp + C, pressure)
            delta = (foftk_wb_temp - X) / fdwb_temp  # float((foftk_wb_temp - X)/fdwb_temp)
            delta = np.where(delta < 10., delta, 10.)  # min(10,delta)
            delta = np.where(delta > -10., delta, -10.)  # max(-10,delta)
            wb_temp = wb_temp - delta
            wb_temp[invalid == 1] = np.nan
            Twb = wb_temp
            iter = iter + 1
        # end

        # ! 04-06-16: Adding iteration constraint.  Commenting out original code.
        # but in the MATLAB code, for sake of speed, we only do this for the values
        # that didn't converge

        if 1:  # ConvergenceMode:

            convergence = 0.00001
            maxiter = 20000

            es_mb_wb_temp, rs_wb_temp, de_mbdwb_temp, dlnes_mbdwb_temp, rsdwb_temp, foftk_wb_temp, fdwb_temp = cls.QSat_2(wb_temp + C, pressure)
            delta = (foftk_wb_temp - X) / fdwb_temp  # float((foftk_wb_temp - X)/fdwb_temp)
            subdo = np.where(np.abs(delta) > convergence)  # find(abs(delta)>convergence)

            iter = 0
            while (len(subdo[0]) > 0) and (iter <= maxiter):
                iter = iter + 1

                wb_temp[subdo] = wb_temp[subdo] - 0.1 * delta[subdo]

                es_mb_wb_temp, rs_wb_temp, de_mbdwb_temp, dlnes_mbdwb_temp, rsdwb_temp, foftk_wb_temp, fdwb_temp = cls.QSat_2(wb_temp[subdo] + C,
                                                                                                                              pressure[subdo])
                delta = 0 * wb_temp
                delta[subdo] = (foftk_wb_temp - X[subdo]) / fdwb_temp  # float((foftk_wb_temp - X[subdo])/fdwb_temp)
                subdo = np.where(np.abs(delta) > convergence)  # find(abs(delta)>convergence);
            # end

            Twb = wb_temp
            if any(map(len, subdo)):  # len(subdo)>0:
                print(len(subdo))
                Twb[subdo] = TemperatureK[subdo] - C
                # print(subdo)
                for www in subdo[0]:
                    #    print(www)
                    print('WARNING-Wet_Bulb failed to converge. Setting to T: WB, P, T, RH, Delta: %0.2f, %0.2f, %0.1f, %0.2g, %0.1f' % (
                        Twb[www], pressure[www], TemperatureK[www], relhum[www], delta[www]))
                # end
            # end

        # end

        # Twb=float(Twb)
        return Twb, Teq, epott

    @classmethod
    def wea_to_img(cls, wea: str):
        """根据wea返回wea_img"""
        if wea is None:
            return None

        if '雨' in wea:
            return 'yu'
        if '雷' in wea:
            return 'lei'
        if '阴' in wea:
            return 'yin'
        if '晴' in wea:
            return 'qing'
        if '云' in wea:
            return 'yun'
        if '雪' in wea:
            return 'xue'
        if '冰雹' in wea:
            return 'bingbao'
        if '雾' in wea:
            return 'wu'
        if '沙尘' in wea:
            return 'shachen'

        return None
