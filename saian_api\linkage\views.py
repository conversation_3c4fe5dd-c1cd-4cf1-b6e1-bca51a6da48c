import json
import logging

from django.core.exceptions import ObjectDoesNotExist
from django.db import transaction
from django.db.models import Q
from rest_framework import status
from rest_framework import viewsets, exceptions
from rest_framework.response import Response

from saian_api.devdefine.models import AttributePrototype
from saian_api.device.models import Device
from saian_api.group.views import HandleShortcut
from saian_api.project.models import Project
from saian_api.scheduledtask.utils import set_global_db
from saian_api.user.models import WebUser
from saian_api.group.models import Shortcut, AcStrategies
from saian_api.utils.inthttpapi.device import AdminDeviceApi
from saian_api.utils.sy_jsonrenderer import SyJSONRender
from .models import (
    LinkageRule,
    LinkageSnpVar,
    SnpVar,
    LinkageVar,
    CrossAttribute,
    CrossAttributePrototype,
    LinkageTarget,
    LinkageTrigger
)
from .serializers import (
    CrossAttributeModelSerializer,
    CrossAttributeSerializer,
    LinkageAllVarSerializer,
    LinkageRuleSerializer,
    LinkageSnpVarSerializer,
    LinkageTriggerSerializer,
    LinkageTargetSerializer,
    LinkageVarSerializer,
    SnpVarSerializer,
    ExecuteRuleSerializer
)


# Create your views here.
class LinkageRuleViewSet(viewsets.ModelViewSet):
    serializer_class = LinkageRuleSerializer
    renderer_classes = (SyJSONRender,)

    def get_queryset(self):
        search = self.request.query_params.get('search', None)
        queryset = LinkageRule.objects.filter(project_id=self.request.user['project_id'])
        if search is not None:
            queryset = queryset.filter(name__contains=search)
        return queryset

    def get_object(self):
        project = Project.objects.get_by_request(self.request)
        try:
            obj = LinkageRule.objects.get(pk=self.kwargs['pk'], project_id=project.id)
        except ObjectDoesNotExist:
            raise exceptions.NotFound('Linkage rule not found!')
        return obj

    def list(self, request, *args, **kwargs):
        rules = super(LinkageRuleViewSet, self).list(request, *args, **kwargs).data

        res_data = {
            "count": rules['count'],
            'linkage_rules': rules['results']
        }

        return Response(res_data)

    def retrieve(self, request, *args, **kwargs):
        linkage_rule = super(LinkageRuleViewSet, self).retrieve(request, *args, **kwargs)
        res_data = {
            'linkage_rule': linkage_rule.data
        }

        return Response(res_data)

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        is_shortcut = request.data.get('is_shortcut', None)

        rule = LinkageRuleSerializer(LinkageRule.objects.create_rule(request, serializer), context=serializer.context)
        if rule.data['trigger_type'] == 10 or rule.data['trigger_type'] == 20:
            AcStrategies.objects.create(
                st_type=20 if rule.data['trigger_type'] == 10 else 30,
                st_id=rule.data['id'],
                status=10,
                created_at=rule.data['created_at'],
                updated_at=rule.data['updated_at']
            )

        if is_shortcut is not None and is_shortcut == 1:
            Shortcut.objects.create(name=rule.data['name'], op_type=20, op_id=rule.data[id])

        res_data = {
            'linkage_rule': rule.data
        }

        return Response(res_data)

    def partial_update(self, request, *args, **kwargs):
        rule = super().partial_update(request, *args, **kwargs).data

        is_shortcut = request.data.get('is_shortcut', None)
        if is_shortcut is not None:
            shortcut = Shortcut.objects.filter(op_type=20, op_id=rule.get('id')).last()
            if is_shortcut == 1:
                if shortcut is None:
                    shortcut = Shortcut.objects.create(name=rule.get('name'), op_type=20, op_id=rule.get('id'))
                rule['is_shortcut'] = True
                rule['shortcut_id'] = shortcut.id
            else:
                if shortcut is not None:
                    shortcut.delete()
                rule['is_shortcut'] = False

        exe_setting = request.data.get('exe_setting', None)
        if exe_setting:
            lt_serializer = LinkageTriggerSerializer(data=exe_setting)

            # 规则只能有一个定时设置
            settings = LinkageTrigger.objects.filter(linkage_rule_id=rule['id'])
            if len(settings) != 0:
                setting = settings.first()
                lt_serializer = LinkageTriggerSerializer(setting, data=exe_setting)

            if lt_serializer.is_valid(raise_exception=True):
                lt_serializer.save(linkage_rule_id=rule['id'])

        # 触发方式不是"定时"，清除该联动的定时 trigger
        trigger_type = request.data.get('trigger_type', None)
        if trigger_type is not None:
            if trigger_type != 10:
                LinkageTrigger.objects.filter(linkage_rule_id=rule['id']).delete()

        # 修改触发方式时同时修改运行策略类型
        st = AcStrategies.objects.filter(st_id=rule['id'], st_type__in=(20, 30)).last()
        if st is not None:
            if not ((st.st_type == 20 and rule['trigger_type'] == 10) or (st.st_type == 30 and rule['trigger_type'] == 20)):
                st.st_type = 20 if rule['trigger_type'] == 10 else 30
                st.save()

        res_data = {
            'linkage_rule': rule
        }

        return Response(res_data)

    def destroy(self, request, *args, **kwargs):
        shortcuts = Shortcut.objects.filter(op_type=20, op_id=kwargs['pk'])
        shortcuts.delete()

        ac_strategies = AcStrategies.objects.filter(st_type__in=[20, 30], st_id=kwargs['pk'])
        ac_strategies.delete()

        super().destroy(request, *args, **kwargs)
        return Response()


# 用ViewSet而不用ModelViewSet是因为输出结果集是一个列表（多个模型组合），而不是单个模型，
# list不能转换为QuerySet（还没找到方法？）
class LinkageAllVarViewSet(viewsets.ViewSet):
    def get_admin_object(self, pk) -> Device | None:
        device = None
        r = AdminDeviceApi.get_device(pk)
        if r.status_code == status.HTTP_200_OK:
            data = r.json().get('row')
            # 过滤不在 Device 类声明的字段
            device_fields = {f.name if not f.is_relation else f.attname for f in Device._meta.fields}
            filtered_data = {k: v for k, v in data.items() if k in device_fields}
            device = Device(**filtered_data)
        else:
            logging.error(f'从Admin3查询设备失败，status_code: {r.status_code}, text: {r.text}')

        return device

    def list(self, request):
        from saian_api.device.models import Device

        rule_id = request.query_params.get('rule_id', None)

        # 当新增规则时，rule_id 为空
        # if rule_id is None:
        #     raise exceptions.ValidationError

        project_id = request.user['project_id']

        vars = []

        # 项目级和系统级的参数
        snp_vars = SnpVar.objects.filter(Q(project_id=project_id) | Q(project_id=None))
        if len(snp_vars) != 0:
            for snp_var in snp_vars:
                is_trigger = False
                lsnpv = LinkageSnpVar.objects.filter(snp_var_id=snp_var.id, is_trigger=True).first()
                if lsnpv is not None:
                    is_trigger = True

                device = Device.objects.filter(mac=snp_var.mac).first()
                ap_name = None
                if device is not None:
                    attr = AttributePrototype.objects.get_by_idf(device, snp_var.identifier)
                    if attr is not None:
                        ap_name = attr.name

                vars.append({
                    'id': snp_var.id,
                    'name': snp_var.name,
                    'mac': snp_var.mac,
                    'identifier': snp_var.identifier,
                    'is_trigger': is_trigger,
                    'var_type': snp_var.var_type,
                    # scope_type: 变量作用域，以snp_var.name来判断，'S_'-系统作用域-10，'P_'-项目作用域-20
                    'scope_type': 10 if snp_var.identifier.startswith('S_') else 20,
                    'device_name': Device.objects.name_by_mac(snp_var.mac),
                    'ap_name': ap_name,
                    'rule_id': rule_id
                })
        # 规则级的参数
        if rule_id is not None:
            linkage_vars = LinkageVar.objects.filter(linkage_rule_id=rule_id)
            if linkage_vars.count() != 0:
                # serializer = LinkageVarSerializer(linkage_vars, many=True)
                # for item in serializer.data:
                #     device = Device.objects.filter(mac=item.get('mac')).first()
                #     ap_name = None
                #     if device is not None:
                #         attr = AttributePrototype.objects.get_by_idf(device, item.get('identifier'))
                #         if attr is not None:
                #             ap_name = attr.name
                #     item['scope_type'] = 30
                #     item['device_name'] = device.nick_name if device is not None else None
                #     item['ap_name'] = ap_name

                for var in linkage_vars:
                    device = Device.objects.filter(mac=var.mac)
                    ap_name = None
                    device_name = None
                    attr = None

                    if device.exists():
                        device = device.first()
                        attr = AttributePrototype.objects.get_by_idf(device, var.identifier)
                        device_name = Device.objects.name_by_mac(var.mac)
                    else:
                        device = self.get_admin_object(var.mac)
                        if device is not None and device.project_id is not None:
                            attr = AttributePrototype.objects.get_by_idf(device, var.identifier, f'prj{device.project_id}db')
                            device_name = device.nick_name

                    if attr is not None:
                        ap_name = attr.name

                    vars.append({
                        'id': var.id,
                        'name': var.name,
                        'mac': var.mac,
                        'identifier': var.identifier,
                        'is_trigger': var.is_trigger,
                        'scope_type': 30,
                        'device_name': device_name,
                        'ap_name': ap_name,
                        'rule_id': rule_id,
                        'var_type': var.var_type,
                        'var_name': var.var_name,
                        'value': var.value,
                        'data_type': var.data_type,
                        'pre_cision': var.pre_cision,
                        'in_crement': var.in_crement,
                        'min_value': var.min_value,
                        'max_value': var.max_value,
                        'options': var.options,
                        'unit': var.unit,
                        'set_by_user': var.set_by_user
                    })

        # serializer = LinkageAllVarSerializer(vars, many=True)

        res_data = {
            'status': status.HTTP_200_OK,
            'total': len(vars),
            'data': {
                'linkage_vars': vars
            }
        }

        return Response(res_data)

    def create(self, request):
        serializer = LinkageAllVarSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        scope_type = request.data.get('scope_type', None)
        # 规则级变量
        if scope_type == 30:
            data = request.data
            data['linkage_rule'] = request.data['rule_id']
            serializer = LinkageVarSerializer(data=data)
            # var_serializer = LinkageVarSerializer(data={
            #     'name': request.data['name'],
            #     'mac': request.data['mac'],
            #     'identifier': request.data['identifier'],
            #     'is_trigger': request.data['is_trigger'],
            #     'linkage_rule': request.data['rule_id']
            # })

            if serializer.is_valid(raise_exception=True):
                serializer.save()

        # 项目级变量
        if scope_type == 20:
            name = request.data.get('name', None)
            # 项目级变量名必须以"P_"开头
            if name is None or not name.startswith('P_'):
                raise exceptions.ValidationError(detail={'detail': 'Name must start with P_ '})

            data = {
                'name': name,
                'mac': request.data.get('mac'),
                'identifier': request.data.get('identifier'),
                'var_type': 10,
                'project': request.user['project_id']
            }

            SnpVar.objects.create_by_rule(request.data['rule_id'], data, request.data['is_trigger'])

        res_data = {
            'status': status.HTTP_200_OK,
            'data': {
                'linkage_var': serializer.data
            }
        }

        return Response(res_data)

    def partial_update(self, request, pk=None):
        serializer = LinkageAllVarSerializer(data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)

        scope_type = request.data.get('scope_type')
        rule = LinkageRule.objects.get(pk=request.data.get('rule_id'))
        if scope_type is None:
            raise exceptions.ValidationError('Scope type must be not None!')

        # 规则级变量
        if scope_type == 30:
            lvar = LinkageVar.objects.get(pk=pk)
            serializer = LinkageVarSerializer(lvar, data=request.data, partial=True)
            if serializer.is_valid(raise_exception=True):
                serializer.save()

            return Response({
                'status': status.HTTP_200_OK,
                'linkage_var': serializer.data
            })

        # 项目级变量
        if scope_type == 20:
            try:
                snp_var = SnpVar.objects.get(pk=pk)
            except ObjectDoesNotExist:
                raise exceptions.NotFound('Snp var not found!')
            serializer = SnpVarSerializer(snp_var, data=request.data, partial=True)
            if serializer.is_valid(raise_exception=True):
                serializer.save()

            # 修改触发设置
            is_trigger = request.data.get('is_trigger', None)
            if is_trigger is not None:
                l_snp_vars = LinkageSnpVar.objects.filter(linkage_rule_id=rule.id, snp_var_id=snp_var.id)
                if len(l_snp_vars) == 0:
                    if is_trigger:
                        lsnpvar_serializer = LinkageSnpVarSerializer(data={
                            'linkage_rule': rule.id,
                            'snp_var': snp_var.id,
                            'is_trigger': True
                        }, partial=True)
                        if lsnpvar_serializer.is_valid(raise_exception=True):
                            lsnpvar_serializer.save()
                else:
                    if not is_trigger:
                        l_snp_vars.first().delete()

        res_data = {
            'status': status.HTTP_200_OK,
            'data': {
                'linkage_var': serializer.data
            }
        }

        return Response(res_data)

    def destroy(self, request, pk=None):
        scope_type = request.data.get('scope_type', None)
        if scope_type is None:
            raise exceptions.ValidationError(detail={'detail': 'Scope type must be not None!'})

        # 规则级变量
        if scope_type == 30:
            LinkageVar.objects.get(pk=pk).delete()

        # 项目级变量
        if scope_type == 20:
            SnpVar.objects.filter(pk=pk).delete()

        res_data = {
            'status': status.HTTP_200_OK,
            'data': None
        }

        return Response(res_data)


class CrossAttributeViewSet(viewsets.ModelViewSet):
    serializer_action_class = {
        'list': CrossAttributeModelSerializer,
        'create': CrossAttributeSerializer,
        'partial_update': CrossAttributeSerializer,
        'destroy': CrossAttributeModelSerializer
    }
    renderer_classes = (SyJSONRender,)

    def get_serializer_class(self):
        try:
            return self.serializer_action_class[self.action]
        except (KeyError, AttributeError):
            return super().get_serializer_class()

    def get_queryset(self):
        queryset = CrossAttribute.objects.filter(project_id=self.request.user['project_id'])

        dp_ids = self.request.query_params.get('dp_ids', None)
        if dp_ids is not None:
            aps = AttributePrototype.objects.filter(device_prototype_id__in=json.loads(dp_ids))
            ap_ids = [ap.id for ap in aps]
            queryset = queryset.filter(attribute_prototypes__id__in=ap_ids).distinct().order_by('-id')

        search = self.request.query_params.get('search', None)
        if search is not None:
            queryset = queryset.filter(Q(name__icontains=search) | Q(identifier__icontains=search))

        return queryset.order_by('id')

    def list(self, request, *args, **kwargs):
        attrs = super().list(request, *args, **kwargs).data

        res_data = {
            "count": attrs['count'],
            'cross_attributes': attrs['results']
        }

        return Response(res_data)

    def create(self, request, *args, **kwargs):
        serializer = CrossAttributeSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        name = request.data.get('name')
        identifier = request.data.get('identifier')
        data_type = request.data.get('data_type')
        default_value = request.data.get('default_value', None)
        min = request.data.get('min', None)
        max = request.data.get('max', None)
        remark = request.data.get('remark', None)
        ap_ids = request.data.get('ap_ids', None)
        project_id = request.user.get('project_id')

        with transaction.atomic(using=f'prj{project_id}db'):
            model_serializer = CrossAttributeModelSerializer(data={
                'name': name,
                'identifier': identifier,
                'data_type': data_type,
                'default_value': default_value,
                'min': min,
                'max': max,
                'remark': remark
            })
            if model_serializer.is_valid(raise_exception=True):
                cross_attribute = model_serializer.save(project_id=request.user['project_id'])

            for ap_id in ap_ids:
                ap = AttributePrototype.objects.get(pk=ap_id)
                CrossAttributePrototype.objects.create(
                    cross_attribute=cross_attribute,
                    attribute_prototype=ap
                )

        res_data = {
            'cross_attribute': {
                'id': cross_attribute.id,
                'name': cross_attribute.name,
                'identifier': cross_attribute.identifier,
                'data_type': cross_attribute.data_type,
                'remark': cross_attribute.remark
            }
        }

        return Response(res_data)

    def partial_update(self, request, *args, **kwargs):
        ap_ids = request.data.get('ap_ids', None)

        if ap_ids is not None:
            ap_ids = request.data.pop('ap_ids')

        cross_attribute = self.get_object()
        project_id = request.user.get('project_id')

        with transaction.atomic(using=f'prj{project_id}db'):
            model_serializer = CrossAttributeModelSerializer(cross_attribute, data=request.data, partial=True)
            if model_serializer.is_valid(raise_exception=True):
                model_serializer.save()

            if ap_ids is not None:
                existing_apids = cross_attribute.attribute_prototypes.values_list('id', flat=True)
                deleting_ids = [id for id in existing_apids if id not in ap_ids]
                adding_ids = [id for id in ap_ids if id not in existing_apids]

                CrossAttributePrototype.objects.filter(
                    cross_attribute=cross_attribute,
                    attribute_prototype_id__in=deleting_ids
                ).delete()

                for add_id in adding_ids:
                    CrossAttributePrototype.objects.create(
                        cross_attribute=cross_attribute,
                        attribute_prototype_id=add_id
                    )

        res_data = {
            'cross_attribute': {
                'id': cross_attribute.id,
                'name': cross_attribute.name,
                'identifier': cross_attribute.identifier,
                'data_type': cross_attribute.data_type,
                'remark': cross_attribute.remark
            }
        }

        return Response(res_data)

    def destroy(self, request, *args, **kwargs):
        cross_attribute = self.get_object()
        super().destroy(request, *args, **kwargs)

        res_data = {
            'cross_attribute': {
                'id': cross_attribute.id,
                'name': cross_attribute.name,
                'identifier': cross_attribute.identifier,
                'data_type': cross_attribute.data_type,
                'remark': cross_attribute.remark
            }
        }

        return Response(res_data)


class LinkageTargetViewSet(viewsets.ModelViewSet):
    serializer_class = LinkageTargetSerializer
    renderer_classes = (SyJSONRender,)

    def get_queryset(self):
        rule_id = self.request.query_params.get('rule_id', None)
        if rule_id is None:
            raise exceptions.ValidationError('Linkage rule must be present!')

        queryset = LinkageTarget.objects.filter(linkage_rule_id=rule_id)

        return queryset

    def get_object(self):
        try:
            obj = LinkageTarget.objects.get(pk=self.kwargs['pk'])
        except ObjectDoesNotExist:
            raise exceptions.NotFound('Linkage target not found!')
        return obj

    def list(self, request, *args, **kwargs):
        # targets = super(LinkageTargetViewSet, self).list(request, *args, **kwargs).data

        targets = []

        # 项目级和系统级的参数
        target_sets = self.get_queryset()
        if target_sets is not None:
            for target in target_sets:
                targets.append(target.target_to_json())

        res_data = {
            "count": len(targets),
            'linkage_targets': targets
        }

        return Response(res_data)

    def retrieve(self, request, *args, **kwargs):
        linkage_target = super(LinkageTargetViewSet, self).retrieve(request, *args, **kwargs)
        res_data = {
            'linkage_target': linkage_target.data
        }

        return Response(res_data)

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        rule = LinkageRule.objects.get(pk=serializer.validated_data['rule_id'])
        linkage_target = LinkageTarget.objects.create_single(rule, serializer.validated_data, request.user['project_id'])

        res_data = {
            'linkage_target': linkage_target.target_to_json()
        }

        return Response(res_data)

    def partial_update(self, request, *args, **kwargs):
        # linkage_target = super().partial_update(request, *args, **kwargs).data
        linkage_target = self.get_object()

        linkage_target.update_by_rule(linkage_target.linkage_rule, request.data)

        res_data = {
            'linkage_target': linkage_target.target_to_json()
        }

        return Response(res_data)

    def destroy(self, request, *args, **kwargs):
        linkage_target = self.get_object()
        super().destroy(request, *args, **kwargs)

        res_data = {
            'linkage_target': linkage_target.target_to_json()
        }

        return Response(res_data)


class ExecuteRuleViewSet(viewsets.GenericViewSet):
    def create(self, request):
        serializer = ExecuteRuleSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        project_id = request.user.get('project_id')
        set_global_db(project_id)

        user_id = request.user.get('id')
        user = WebUser.objects.get(pk=user_id)

        rule_id = serializer.validated_data['rule_id']
        is_trial = serializer.validated_data['is_trial']

        # 联动试执行时时不使用新线程
        if is_trial:
            rule = LinkageRule.objects.get(pk=rule_id)
            result = rule.execute(user, True)
            return Response({
                'status': status.HTTP_200_OK,
                'data': {
                    'result': result
                }
            })
        else:
            HandleShortcut(kwargs={'op_type': 20, 'op_id': rule_id, 'request': request,
                                   'project_id': project_id, 'user': user, 'is_trial': is_trial}).start()

            return Response({
                'status': status.HTTP_200_OK,
                'data': None
            })
