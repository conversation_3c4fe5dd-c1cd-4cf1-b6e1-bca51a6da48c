import datetime
import json
import logging
import re

import binascii
import traceback

from django.utils.module_loading import import_string

from saian_api.device.models import DeviceAttribute, DeviceEvent, Device, ParamRecord
from saian_api.issue.models import DeviceIssue
from saian_api.utils.db.Redis import RedisHelper
from .base import BaseDevice
from ...devdefine.models import DevicePrototype, AttributePrototype
from ...notifi.models import Notification
from ...terminal.models import TerminalAttribute, Terminal
from ...utils.inthttpapi.device import AdminDeviceApi
from ...utils.tools import is_number


class DTUMeter(BaseDevice):
    """ DTU仪表 """
    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)

        data = event.get('data', {})

        dp_uni_name = device.device_prototype.uni_name

        for key, value in data.items():
            try:
                da = None

                if dp_uni_name == "ADL400电表":
                    # ADL400电表 忽略上报的变比
                    if key == 'Meter_CT':
                        return

                # 是否应用变比
                meter_ct_affect = False
                da = DeviceAttribute.objects.get_by_idf(device, key)

                if dp_uni_name == "AMC200L" or dp_uni_name == 'PM800电表网关':
                    # AMC200L 电表 PM800电表网关，电流不需要乘变比
                    if 'Meter_W_' in key or 'Meter_Var_' in key:
                        meter_ct_affect = True
                else:
                    # 其他类型，电流，有功功率和无功功率 应用变比
                    if 'Meter_A' in key or 'Meter_W' in key or 'Meter_Var' in key or 'Meter_kVarh' in key:
                        meter_ct_affect = True

                # 电流, 有功功率, 无功功率 要乘以变比
                if meter_ct_affect:
                    if da is not None:
                        search_obj = re.search('Meter_(A|W|Var)_(A|B|C)_(\d+)', key)
                        # 子电表变比处理.
                        if search_obj is not None:
                            idf = ''.join(['Meter_CT_', search_obj.group(3)])
                            idf_value = device.get_value_by_idf(idf)
                            if idf_value is not None:
                                meter_ct = float(idf_value)
                                da.value = float(value) * meter_ct
                                da.save()
                                DeviceAttribute.objects.save_to_redis(device, da)
                        else:
                            # 电表变比处理
                            meter_ct = device.get_value_by_idf('Meter_CT')
                            if meter_ct is not None:
                                da.value = float(value) * float(meter_ct)
                                da.save()
                                DeviceAttribute.objects.save_to_redis(device, da)

                if dp_uni_name == "ADL400电表":
                    # ADL400电表 总有功电能也要乘以变比
                    if 'Meter_Power_Cons' in key:
                        meter_ct = device.get_value_by_idf('Meter_CT')
                        if meter_ct is not None:
                            meter_ct = float(meter_ct)
                            da.value = float(value) * meter_ct
                            da.save()
                            DeviceAttribute.objects.save_to_redis(device, da)
                        else:
                            msg = f'ADL400电表 找不到变比！device: {device.id}-{device.nick_name}'
                            logging.error(msg)
                            Notification.objects.send_debug_mail(device, msg)

                if ('Meter_CumHeat' in key or 'Meter_InstantHeat' in key) and device.project_id == 35:
                    da = DeviceAttribute.objects.get_by_idf(device, key)
                    if da is not None:
                        # 冷量兆焦转为kWh
                        da.value = float(value) / 3.6
                        da.save()
                        DeviceAttribute.objects.save_to_redis(device, da)

                # 市局需求，根据平均线电压值更新合闸状态
                if 'Meter_Avg_LV_' in key:
                    # search_obj = re.search('Meter_V_(A|B|C)_(\d+)', key)
                    idx = key.split('_')[-1]
                    idf = f'Meter_Brake_Status_{idx}'
                    da = DeviceAttribute.objects.query_object_by_idf(device, idf)
                    if da is not None:
                        if float(value) > 0:
                            da.value = '合闸'
                        elif float(value) < 0:
                            da.value = '分闸'
                        else:
                            da.value = '跳闸'

                        da.save()
                        DeviceAttribute.objects.save_to_redis(device, da)

                if device.device_prototype.name == "IDM30电表网关":
                    if 'Meter_CT_' in key:
                        da = DeviceAttribute.objects.get_by_idf(device, key)
                        if da is not None:
                            # 变比除以5
                            da.value = float(value) / 5
                            da.save()
                            DeviceAttribute.objects.save_to_redis(device, da)

                if device.device_prototype.name == "SPM20":
                    if 'Meter_V_A_1' in key:
                        for i in range(6):
                            idx = i + 2
                            idf = f'Meter_V_A_{idx}'
                            da = DeviceAttribute.objects.get_by_idf(device, idf)
                            if da is not None:
                                da.value = device.get_value_by_idf('Meter_V_A_1')
                                da.save()
                                DeviceAttribute.objects.save_to_redis(device, da)
                    if 'Meter_V_B_1' in key:
                        for i in range(6):
                            idx = i + 2
                            idf = f'Meter_V_B_{idx}'
                            da = DeviceAttribute.objects.get_by_idf(device, idf)
                            if da is not None:
                                da.value = device.get_value_by_idf('Meter_V_B_1')
                                da.save()
                                DeviceAttribute.objects.save_to_redis(device, da)
                    if 'Meter_V_C_1' in key:
                        for i in range(6):
                            idx = i + 2
                            idf = f'Meter_V_C_{idx}'
                            da = DeviceAttribute.objects.get_by_idf(device, idf)
                            if da is not None:
                                da.value = device.get_value_by_idf('Meter_V_C_1')
                                da.save()
                                DeviceAttribute.objects.save_to_redis(device, da)
                    if 'Meter_FRQ_1' in key:
                        for i in range(6):
                            idx = i + 2
                            idf = f'Meter_FRQ_{idx}'
                            da = DeviceAttribute.objects.get_by_idf(device, idf)
                            if da is not None:
                                da.value = device.get_value_by_idf('Meter_FRQ_1')
                                da.save()
                                DeviceAttribute.objects.save_to_redis(device, da)


                if device.device_prototype.name == "SPM20S":
                    if 'Meter_V_A' in key:
                        da_a = device.get_value_by_idf('Meter_V_A')
                        da_b = device.get_value_by_idf('Meter_V_B')
                        da_c = device.get_value_by_idf('Meter_V_C')
                        for i in range(30):
                            idx = i + 1
                            idf = f'Meter_V_{idx}'
                            da = DeviceAttribute.objects.get_by_idf(device, idf)
                            if da is not None:
                                da.value = round((float(da_a) + float(da_b) + float(da_c))/3, 2)
                                da.save()
                                DeviceAttribute.objects.save_to_redis(device, da)

                    if 'Meter_FRQ' in key:
                        for i in range(30):
                            idx = i + 1
                            idf = f'Meter_FRQ_{idx}'
                            da = DeviceAttribute.objects.get_by_idf(device, idf)
                            if da is not None:
                                da.value = device.get_value_by_idf('Meter_FRQ')
                                da.save()
                                DeviceAttribute.objects.save_to_redis(device, da)
            # 避免影响其他参数处理
            except ValueError as e:
                logging.error(f'DTUMeter.update_attres处理异常，key={key}, value={value}, error: {e.__str__()}')
                traceback.print_exc()
                pass
            except Exception as e:
                logging.error(f'DTUMeter.update_attres处理异常，key={key}, value={value}, error: {e.__str__()}')
                traceback.print_exc()
                pass

    @classmethod
    def alarm(cls, device, event):
        data = event.get('data', {})

        # 查询所有电表状态的key
        status_keys = list(filter(lambda x: x.startswith('MeterStatus_'), data.keys()))

        for key in status_keys:
            idx = key.split('_')[1]
            status = data[key]

            # 查找昵称
            nick_name = None
            names = [f'attribute_prototype:{device.device_prototype_id}_Meter_{idx}_NickName',
                     f'attribute_prototype:{device.device_prototype_id}_Meter_NickName_{idx}']
            nickname_ap = AttributePrototype.objects.query_by_names(device.project_id, names)
            if len(nickname_ap):
                nickname_ap = nickname_ap[0]
                nickname_da = DeviceAttribute.objects.query_object_by_ap(device, nickname_ap)
                if nickname_da is not None:
                    nick_name = nickname_da.value

            if status != '未连接' and status != '正常':
                # 报警处理
                if nick_name is None:
                    nick_name = f'仪表{idx}'

                content = f'{nick_name}{status}'
                DeviceIssue.objects.add_alarm(device, content)

            if status == '正常':
                # 解除报警
                if nick_name is None:
                    nick_name = f'仪表{idx}'

                content = f'{nick_name}{status}'
                DeviceIssue.objects.recover_alarm(device, content)

            # 更新仪表的在线状态
            from saian_api.terminal.models import Terminal
            terminal_idx = Terminal.objects.filter(device=device, idx=idx)
            for terminal in terminal_idx:
                if status == '未连接':
                    # '未连接'即为离线
                    if terminal.online:
                        terminal.online = False
                        terminal.save(update_fields=['online'])
                else:
                    if not terminal.online:
                        terminal.online = True
                        terminal.save()


class ADL400Meter(BaseDevice):
    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)

        data = event.get('data', {})

        for key, value in data.items():
            try:
                da = DeviceAttribute.objects.query_object_by_idf(device, key)
                if da is not None:
                    if key == "Meter_CT" and da.value:
                        continue
                    # 电流,有功功率,无功功率,总有功电能 应用变比
                    if 'Meter_A_' in key or 'Meter_W_' in key or 'Meter_Var_' in key or 'Meter_Power_Cons' in key:
                        meter_ct_da = DeviceAttribute.objects.query_object_by_idf(device, 'Meter_CT')
                        if meter_ct_da is not None and meter_ct_da.value:
                            ct = float(meter_ct_da.value)
                            da.value = round(float(value) * ct, 2)
                            da.save()
                            DeviceAttribute.objects.save_to_redis(device, da)
                        else:
                            msg = f'ADL400电表 找不到变比！device: {device.id}-{device.nick_name}'
                            logging.error(msg)
                            Notification.objects.send_debug_mail(device, msg)

            except Exception as e:
                logging.error(f'ADL400电表 update_attres 出错！err: {e.__str__()}')
                pass

    @classmethod
    def alarm(cls, device, event):
        super().alarm(device, event)


class GprsSpMeter(BaseDevice):
    """
      GPRS单相电表
    """
    @classmethod
    def alarm(cls, device, event):
        data = event.get('data', {})
        if 'MeterAlarm' in data:
            if data['MeterAlarm']:
                DeviceIssue.objects.add_alarm(device, '电表报警')
            else:
                DeviceIssue.objects.recover_alarm(device, '电表报警')


class ThrPhaMeter(BaseDevice):
    """
      三相电表网关
    """
    @classmethod
    def alarm(cls, device, event):
        data = event.get('data', {})

        # 查询所有电表状态的key
        status_keys = list(filter(lambda x: x.startswith('MeterStatus_'), data.keys()))

        for key in status_keys:
            idx = key.split('_')[1]
            status = data[key]

            # 查找昵称
            nick_name = None
            names = [f'attribute_prototype:{device.device_prototype_id}_Meter_NickName_{idx}']
            nickname_ap = AttributePrototype.objects.query_by_names(device.project_id, names)
            if len(nickname_ap):
                nickname_ap = nickname_ap[0]
                nickname_da = DeviceAttribute.objects.query_object_by_ap(device, nickname_ap)
                if nickname_da is not None:
                    nick_name = nickname_da.value

            if status != '未连接' and status != '正常':
                # 报警处理
                if nick_name is None:
                    nick_name = f'仪表{idx}'

                content = f'{nick_name}{status}'
                DeviceIssue.objects.add_alarm(device, content)

            if status == '正常':
                # 解除报警
                if nick_name is None:
                    nick_name = f'仪表{idx}'

                content = f'{nick_name}{status}'
                DeviceIssue.objects.recover_alarm(device, content)

            # 更新仪表的在线状态
            from saian_api.terminal.models import Terminal
            for terminal in Terminal.objects.filter(device=device, idx=idx):
                if status == '未连接':
                    # '未连接'即为离线
                    if terminal.online:
                        terminal.online = False
                        terminal.save(update_fields=['online'])
                else:
                    if not terminal.online:
                        terminal.online = True
                        terminal.save()


class Lxly80Meter(BaseDevice):
    """保利水表用水量解析"""

    # 下发指令的前导码为5个0xFE
    CMD_PREAMBLE_CODE = [0xFE] * 5
    # 响应的前导码为3个0xFE
    RSP_PREAMBLE_CODE = [0xFE] * 3
    # 帧头为0x68
    FRAME_HEAD = 0x68
    # 帧尾为0x16
    FRAME_TAIL = 0x16
    # 水表的类型为0x10
    METER_TYPE = 0x10
    # 控制码
    CTRL_CODE = 0x01
    # 数据标识DI0-DI1
    DATA_IDF = [0x1F, 0x90]
    # 序列号默认固定为3
    SEQ = 3

    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)

        if device.project_id != 74:
            return

        data = event.get('data', {})
        event_id = event.get('event_id', None)

        for key, value in data.items():
            if key.startswith('Meter_Water_Cons'):
                idx = key.split('_')[-1]
                # addr = DeviceAttribute.objects.get_by_idf(device, f'MeterAddr_{idx}')
                addr = DeviceAttribute.objects.query_object_by_idf(device, f'MeterAddr_{idx}')
                if addr is not None:
                    result = cls.get_water_consumption(addr.value, value)
                    if result is not None:
                        da_water_cons = DeviceAttribute.objects.get_by_idf(device, f'Meter_Water_Cons_{idx}')
                        if da_water_cons is not None:
                            da_water_cons.value = result
                            da_water_cons.save()
                            DeviceAttribute.objects.save_to_redis(device, da_water_cons)
                            # 将事件表中的上报数据更新为转换后的值
                            if event_id is not None:
                                try:
                                    device_event = DeviceEvent.objects.get(pk=event_id)
                                    device_event_data = json.loads(device_event.data)
                                    device_event_data[key] = da_water_cons.value
                                    device_event.data = json.dumps(device_event_data, ensure_ascii=False)
                                    device_event.save()
                                except DeviceEvent.DoesNotExist:
                                    pass

    @classmethod
    def get_water_consumption(cls, meter_addr: str, rsp: str) -> int:
        water_consumption = None
        meter_addr_len = len(meter_addr)
        # 水表地址需要是偶数长度且长度最多为14的十六进制字符串
        if ((meter_addr_len % 2) == 0) and (meter_addr_len <= 14):
            rsp_byte = binascii.unhexlify(''.join(rsp.split(' ')))
            while True:
                # 判断前导码是否为3个0xFE
                rsp_preamble_code = list(rsp_byte[:3])
                if rsp_preamble_code != cls.RSP_PREAMBLE_CODE:
                    logging.error("rsp preamble code error!")
                    break
                # 去掉前导码
                rsp_byte = rsp_byte[3:]
                # 判断帧头是否正确
                if rsp_byte[0] != cls.FRAME_HEAD:
                    logging.error("rsp frame head error!")
                    break
                # 判断帧尾是否正确
                if rsp_byte[-1] != cls.FRAME_TAIL:
                    logging.error("rsp frame tail error!")
                    break
                # 判断校验是否通过
                checksum_result = cls.checksum_add8(rsp_byte[:-2])
                if checksum_result != rsp_byte[-2]:
                    logging.error("rsp checksum error!")
                    break
                # 判断水表类型是否正确
                if rsp_byte[1] != cls.METER_TYPE:
                    logging.error("rsp meter type error!")
                    break
                # 将十六进制字符串的水表地址转换成对应的数值，反转成低字节在前，高字节在后，少于7个字节则补零
                addr_hex = list(binascii.unhexlify(meter_addr)[::-1])
                addr_hex.extend([0] * (7 - len(addr_hex)))
                # 判断地址是否正确
                rsp_addr = rsp_byte[2:9]
                if rsp_addr != bytearray(addr_hex):
                    logging.error("rsp meter addr error!")
                    break
                # 判断控制码是否正确，响应的控制码为下发的控制码加0x80
                if rsp_byte[9] != (cls.CTRL_CODE + 0x80):
                    logging.error("rsp ctrl code error!")
                    break
                # 判断数据域长度是否正确
                if rsp_byte[10] != len(rsp_byte[11:-2]):
                    logging.error("rsp data filed length error!")
                    break
                # 判断数据标识是否正确
                if rsp_byte[11:13] != bytearray(cls.DATA_IDF):
                    logging.error("rsp data idf error!")
                    break
                # 判断序列号是否正确
                if rsp_byte[13] != cls.SEQ:
                    logging.error("rsp seq error!")
                    break
                # 获取用水量
                water_consumption = int(binascii.hexlify(rsp_byte[17:13:-1])) // 100
                break
        return water_consumption

    @classmethod
    def checksum_add8(cls, data_byte: bytearray) -> int:
        sum = 0
        for byte in data_byte:
            sum += byte
        result = sum % 256
        return result


class PLCGateway(BaseDevice):
    """PLC网关XS"""
    @classmethod
    def update_attres(cls, device, event):
        data = event.get('data', {})
        event_id = event.get('event_id', None)

        if "dev_id" in data:
            mac = f'{device.mac}_{data["dev_id"]}'
            uni_name = data['uni_name']
            dp = DevicePrototype.objects.filter(uni_name=uni_name).order_by('id')
            if dp.count() == 0:
                logging.error(f'设备类型(uni_name): {uni_name} 不存在')
                return
            elif dp.count() != 1:
                logging.warning(f'设备类型(uni_name): {uni_name} 共有 {dp.count()} 个')
            dp = dp.last()
            sub_device = Device.objects.filter(mac=mac).last()
            if sub_device is None:
                nick_name = f'{device.mac[-5:]}#{data["dev_id"]}'
                sub_device = Device.objects.get_or_create_device(device, nick_name, mac, dp=dp)

            if sub_device is not None:
                # 更新设备及终端的状态
                if not sub_device.online:
                    sub_device.online = True
                    sub_device.status = 20
                    Terminal.objects.bulk_update_by_device(sub_device, 'online', sub_device.online)
                if not sub_device.sw_on:
                    sub_device.sw_on = True
                    Terminal.objects.bulk_update_by_device(sub_device, 'sw_on', sub_device.sw_on)
                sub_device.save()
                # sub_device.save(using='syadmindb')
                # AdminDeviceApi.update_device(sub_device.id, {})
                # 更改事件记录
                if event_id is not None:
                    device_event = DeviceEvent.objects.get(pk=event_id)
                    device_event.mac = mac
                    device_event.device_id = sub_device.id
                    device_event.device_prototype_id = sub_device.device_prototype_id
                    device_event.nick_name = sub_device.nick_name
                    device_event.save(update_fields=['mac', 'device_id', 'device_prototype_id', 'nick_name'])
                # 更新设备参数
                # 到特定的设备模块处理上报数据、故障和报警
                model_name = dp.m_name
                if model_name is not None:
                    # 动态导入对应的设备处理模块
                    try:
                        device_model = import_string('saian_api.device.domain_models.%s' % model_name)
                        if device_model is not None:
                            event['event_id'] = event_id
                            # 设备参数处理
                            device_model.update_attres(sub_device, event)
                            # 故障处理
                            device_model.fault(sub_device, event)
                            # 报警处理
                            device_model.alarm(sub_device, event)
                    except ImportError:
                        logging.error(f'找不到设备处理模块：{model_name}, 设备类型：{dp.id}-{dp.name}')
                else:  # 没有配置处理模块就默认更新
                    super().update_attres(sub_device, event)

        else:
            # 更新网关自身的数据
            super().update_attres(device, event)


class TQChaZuo(BaseDevice):
    """ 智能插座 """
    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)

        data = event.get('data', {})
        if 'SW' in data:
            sw_da = DeviceAttribute.objects.query_object_by_idf(device, 'SW')
            if sw_da is not None:
                device.sw_on = bool(sw_da.value)
            else:
                device.sw_on = bool(data['SW'])
            device.save()
            Terminal.objects.bulk_update_by_device(device, 'sw_on', device.sw_on)

    @classmethod
    def alarm(cls, device, event):
        data = event.get('data', {})
        if 'Meter_W' in data:
            w = float(data['Meter_W'])
            if w > 3:
                DeviceIssue.objects.add_alarm(device, '功率过高')
            else:
                DeviceIssue.objects.recover_alarm(device, '功率过高')


class SHYHGasMeter(BaseDevice):
    """ SHYH 燃气表 """
    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)

        data = event.get('data', {})
        if 'online' in data and isinstance(data['online'], bool):
            is_online = data['online']

            if device.online and not is_online:
                device.online = False
                device.status = 30
                device.save(update_fields=['online', 'status'])
                Terminal.objects.bulk_update_by_device(device, 'online', device.online)

            if (not device.online) and is_online:
                device.online = True
                if device.status != 40:
                    device.status = 20
                device.save(update_fields=['online', 'status'])
                Terminal.objects.bulk_update_by_device(device, 'online', device.online)


class MBUMeter(BaseDevice):
    """
    MBU 电表。
        - MBU852电表
        - MBU853B电表
        - MBU100B电表
        - MBU80电表
    """

    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)

        data = event.get('data', {})
        dp_uni_name = device.device_prototype.uni_name

        for key, value in data.items():
            try:
                da = DeviceAttribute.objects.query_object_by_idf(device, key)
                if da is not None:
                    if key.startswith('Meter_A_') or key.startswith('Meter_W') or key.startswith('Meter_Var'):
                        meter_ct_da = DeviceAttribute.objects.query_object_by_idf(device, 'Meter_CT')
                        if meter_ct_da is not None and meter_ct_da.value:
                            ct = float(meter_ct_da.value)
                            da.value = round(float(value) * ct, 2)
                            da.save()
                            DeviceAttribute.objects.save_to_redis(device, da)
                        else:
                            msg = f'MBU 电表找不到电流变比！device: {device.id}-{device.nick_name}'
                            logging.error(msg)
                            Notification.objects.send_debug_mail(device, msg)

                    # MBU852电表 的电压和功率还要乘以 Meter_PT
                    if dp_uni_name == 'MBU852电表':
                        if key.startswith('Meter_V_') or key.startswith('Meter_W') or key.startswith('Meter_Var'):
                            meter_pt_da = DeviceAttribute.objects.query_object_by_idf(device, 'Meter_PT')
                            if meter_pt_da is not None and meter_pt_da.value:
                                ct = float(meter_pt_da.value)
                                da.value = round(float(value) * ct, 2)
                                da.save()
                                DeviceAttribute.objects.save_to_redis(device, da)
                            else:
                                msg = f'MBU852电表找不到电压变比！device: {device.id}-{device.nick_name}'
                                logging.error(msg)
                                Notification.objects.send_debug_mail(device, msg)
            except Exception as e:
                logging.error(f'MBU 电表 update_attres 出错！err: {e.__str__()}')
                pass

    @classmethod
    def alarm(cls, device, event):
        super().alarm(device, event)

    @classmethod
    def fault(cls, device, event):
        super().fault(device, event)


class GasMeter(BaseDevice):
    """燃气表"""
    @classmethod
    def update_attres(cls, device, event):

        data = event.get('data', {})

        if 'Meter_Gas_Cons' in data:
            gas_cons = data['Meter_Gas_Cons']

            # 不可靠数据处理
            if 'state' in data and data['state'] == 5:
                if not is_number(gas_cons):
                    del data['Meter_Gas_Cons']
                else:
                    gas_cons = float(gas_cons)
                    da = DeviceAttribute.objects.get_by_idf(device, 'Meter_Gas_Cons')
                    if da is not None:
                        current_value = float(da.value)
                        if gas_cons < current_value:
                            del data['Meter_Gas_Cons']
                        else:
                            abs_value = gas_cons - current_value
                            if abs_value > 300:
                                pr = ParamRecord.objects.filter(device_id=device.id, identifier='Meter_Gas_Cons'
                                                                ).exclude(created_at__minute=5, created_at__second=0).order_by('-created_at').first()
                                if pr is not None:
                                    span_minutes = (datetime.datetime.now() - pr.created_at).seconds // 60
                                    if float(abs_value) > span_minutes * 10:
                                        del data['Meter_Gas_Cons']

        if 'Meter_Gas_Cons' in data:
            value = data['Meter_Gas_Cons']
            # 机械表通常以"0"开头，并且带有小数点
            if isinstance(value, str) and value.startswith('0'):
                gas_cons = float(value)

                # 如果没有识别到小数点，则主动除以10
                if '.' not in value:
                    gas_cons = gas_cons / 10

                data['Meter_Gas_Cons'] = gas_cons

        super().update_attres(device, event)

        if 'online' in data and isinstance(data['online'], bool):
            is_online = data['online']

            if device.online and not is_online:
                device.online = False
                device.status = 30
                device.save(update_fields=['online', 'status'])
                Terminal.objects.bulk_update_by_device(device, 'online', device.online)

            if (not device.online) and is_online:
                device.online = True
                if device.status != 40:
                    device.status = 20
                device.save(update_fields=['online', 'status'])
                Terminal.objects.bulk_update_by_device(device, 'online', device.online)

    @classmethod
    def alarm(cls, device, event):
        super().alarm(device, event)

    @classmethod
    def fault(cls, device, event):
        super().fault(device, event)


class LTAMeter(BaseDevice):
    """图像识别抄表"""
    @classmethod
    def update_attres(cls, device, event):

        data = event.get('data', {})

        if 'Meter_Cons' in data:
            meter_cons = data['Meter_Cons']

            # 校验状态为人工校正或已确认时，可认为数据一定正确
            #  0-未处理 1-人工矫正 2-已确认 3-可靠数据 4-自动矫正 5-不可靠数据
            if 'state' in data and data['state'] in (0, 3, 4, 5):
                # 不可靠数据处理
                if not is_number(meter_cons) or data['state'] in (0, 5):
                    del data['Meter_Cons']
                else:
                    meter_cons = float(meter_cons)
                    da = DeviceAttribute.objects.get_by_idf(device, 'Meter_Cons')
                    ct = DeviceAttribute.objects.query_object_by_idf(device, 'Meter_CT')
                    if ct is not None and ct.value:
                        meter_cons = meter_cons * float(ct.value)
                    if da is not None:
                        current_value = float(da.value)

                        if meter_cons < current_value:
                            del data['Meter_Cons']
                        else:
                            abs_value = meter_cons - current_value
                            if abs_value > 300:
                                message = (f'{device.id}-{device.nick_name}: Meter_Cons, 仪表用量过大, 当前值: {current_value}, '
                                           f'上报值: {data["Meter_Cons"]}。')
                                last_event = DeviceEvent.objects.filter(device_id=device.id).order_by('-id').first()
                                if last_event is not None:
                                    span_minutes = (datetime.datetime.now() - last_event.created_at).seconds // 60
                                    if float(abs_value) > span_minutes * 0.5:
                                        message += '数据异常，不更新。'
                                        del data['Meter_Cons']
                                    else:
                                        message += '正常更新'
                                    Notification.objects.send_debug_mail(device, message)

        if 'Meter_Cons' in data:
            value = data['Meter_Cons']
            # 机械表通常以"0"开头，不一定带有小数点
            if isinstance(value, str) and value.startswith('0'):
                meter_cons = float(value)

                if '.' not in value:
                    # 存在小数位，除以10
                    has_decimal = DeviceAttribute.objects.query_object_by_idf(device, 'Has_Decimal')
                    if has_decimal and int(has_decimal.value):
                        meter_cons = meter_cons / 10

                data['Meter_Cons'] = meter_cons

        super().update_attres(device, event)

        if 'online' in data and isinstance(data['online'], bool):
            is_online = data['online']

            if device.online and not is_online:
                device.online = False
                device.status = 30
                device.save(update_fields=['online', 'status'])
                Terminal.objects.bulk_update_by_device(device, 'online', device.online)

            if (not device.online) and is_online:
                device.online = True
                if device.status != 40:
                    device.status = 20
                device.save(update_fields=['online', 'status'])
                Terminal.objects.bulk_update_by_device(device, 'online', device.online)

        # 变比
        if 'Meter_Cons' in data:
            meter_ct = DeviceAttribute.objects.query_object_by_idf(device, 'Meter_CT')
            meter_cons = DeviceAttribute.objects.query_object_by_idf(device, 'Meter_Cons')
            if meter_ct and meter_cons:
                ct = float(meter_ct.value)
                cons = float(meter_cons.value)

                meter_cons.value = round(ct * cons, 2)
                meter_cons.save()
                DeviceAttribute.objects.save_to_redis(device, meter_cons)

    @classmethod
    def alarm(cls, device, event):
        super().alarm(device, event)

    @classmethod
    def fault(cls, device, event):
        super().fault(device, event)


class GL3012Meter(BaseDevice):
    # 广安配电房
    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)
        data = event.get('data', {})

        meter_ct = DeviceAttribute.objects.query_object_by_idf(device, 'Meter_CT')
        if meter_ct is None:
            logging.error(f'找不到GL3012电表变比')
        else:
            ct = float(meter_ct.value)

            # 功率和电量要乘以变比
            for idf, value in data.items():
                da = DeviceAttribute.objects.query_object_by_idf(device, idf)
                if idf.startswith('Meter_W') or idf == 'Meter_Power_Cons':
                    da.value = round(float(value) * ct, 2)
                    da.save()
                    DeviceAttribute.objects.save_to_redis(device, da)

    @classmethod
    def alarm(cls, device, event):
        super().alarm(device, event)

    @classmethod
    def fault(cls, device, event):
        super().fault(device, event)


class YNTECHMeter(BaseDevice):
    # 人医电表
    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)
        data = event.get('data', {})
        now = datetime.datetime.now()
        ctda = None
        if 'Meter_ACT' in data:
            ctda = DeviceAttribute.objects.get_by_idf(device, 'Meter_ACT')
            if ctda is not None:
                # 变比除以5
                ctda.value = float(data['Meter_ACT']) / 5
                ctda.save()
                DeviceAttribute.objects.save_to_redis(device, ctda)
                RedisHelper.push_list(device.project_id, f'device_event_{now.strftime("%Y%m%d%H")}:{device.id}_Meter_ACT', ctda.value, True, 7200)
        meter_ct = ctda if ctda else DeviceAttribute.objects.query_object_by_idf(device, 'Meter_ACT')
        if meter_ct is None:
            logging.error(f'找不到YNTECH电表变比')
        else:
            ct = float(meter_ct.value)
            # 电流、有功功率、无功功率要乘以变比
            for idf, value in data.items():
                da = DeviceAttribute.objects.query_object_by_idf(device, idf)
                if idf.startswith('Meter_A_') or idf.startswith('Meter_W') or idf.startswith('Meter_Var'):
                    da.value = round(float(value) * ct, 2)
                    da.save()
                    DeviceAttribute.objects.save_to_redis(device, da)
                    RedisHelper.push_list(device.project_id, f'device_event_{now.strftime("%Y%m%d%H")}:{device.id}_{idf}', da.value, True, 7200)

    @classmethod
    def alarm(cls, device, event):
        super().alarm(device, event)

    @classmethod
    def fault(cls, device, event):
        super().fault(device, event)


class CHMeter(BaseDevice):
    # 美仪1158S冷热量计

    # 累积消耗属性配置: attr_name, int_key, float_key, 是否取绝对值, 小数部分倍数
    CONSUMPTION_ATTRS = [
        ('FlowCons', 'FlowConsInt', 'FlowConsFloat', False, 1),
        ('HeatCons', 'HeatConsInt', 'HeatConsFloat', False, 1),
        ('CoolCons', 'CoolConsInt', 'CoolConsFloat', True, 1),
    ]

    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)
        data = event.get('data', {})

        # 更新瞬时功率
        if 'InstantPower' in data:
            cls._update_instant_power(device, data['InstantPower'])

        # 批量更新累积消耗值
        cls._update_consumptions(device, event, data)

    @staticmethod
    def _update_instant_power(device, value):
        da = DeviceAttribute.objects.query_object_by_idf(device, 'InstantPower')
        da.value = round(abs(float(value)) * 277.78, 2)
        da.save()
        DeviceAttribute.objects.save_to_redis(device, da)

    @classmethod
    def _update_consumptions(cls, device, event, data):
        now = datetime.datetime.now()
        for attr_name, int_key, float_key, abs_value, float_scale in cls.CONSUMPTION_ATTRS:
            # 确保整数和小数部分同时上报
            if int_key in data and float_key in data:
                int_value = float(data[int_key])
                float_value = float(data[float_key]) * float_scale
                total_value = int_value + float_value
                if abs_value:
                    total_value = abs(total_value)

                da = DeviceAttribute.objects.query_object_by_idf(device, attr_name)
                # 累积值不能比原值小
                if float(da.value) <= total_value:
                    da.value = total_value
                    da.save()
                    DeviceAttribute.objects.save_to_redis(device, da)

                    # 更新事件 data
                    de = DeviceEvent.objects.get(id=event['event_id'])
                    data[attr_name] = da.value
                    de.data = json.dumps(data, ensure_ascii=False)
                    de.save(update_fields=['data'])

                    # 保存属性 id 到 Redis
                    RedisHelper.add_set(
                        device.project_id,
                        f'recently_updated_{now.minute:02}:{device.device_prototype_id}',
                        da.attribute_prototype_id,
                        True,
                        120
                    )

    @classmethod
    def alarm(cls, device, event):
        super().alarm(device, event)

    @classmethod
    def fault(cls, device, event):
        super().fault(device, event)


class MikoLDGRMeter(CHMeter):
    # 米科LDGR冷热量计
    # 累积消耗属性配置: attr_name, int_key, float_key, 是否取绝对值, 小数部分倍数
    CONSUMPTION_ATTRS = [
        ('FlowCons', 'FlowConsInt', 'FlowConsFloat', False, 0.001),
        ('HeatCons', 'HeatConsInt', 'HeatConsFloat', False, 0.001),
        ('CoolCons', 'CoolConsInt', 'CoolConsFloat', False, 0.001),
    ]

    @staticmethod
    def _update_instant_power(device, value):
        da = DeviceAttribute.objects.query_object_by_idf(device, 'InstantPower')
        da.value = round(abs(float(value)), 2)
        da.save()
        DeviceAttribute.objects.save_to_redis(device, da)


class IMETER6(BaseDevice):
    # 大坦沙电表 IMETER6
    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)
        data = event.get('data', {})
        # 将上报的整数和小数部分，拼接成用电量，更新到"Meter_Power_Cons"
        if 'Meter_Power_Cons_I' in data and 'Meter_Power_Cons_D' in data:
            value = f'{data["Meter_Power_Cons_I"]}.{data["Meter_Power_Cons_D"]}'
            data['Meter_Power_Cons'] = value

            ap = AttributePrototype.objects.query_by_idf(device, 'Meter_Power_Cons')
            da = DeviceAttribute.objects.query_object_by_ap(device, ap)
            da.value = value
            DeviceAttribute.objects.save_to_redis(device, da)

            now = datetime.datetime.now()
            RedisHelper.add_set(device.project_id, f'recently_updated_{now.minute:02}:{device.device_prototype_id}',
                                ap.id, True, 120)

            event_obj = DeviceEvent.objects.get(pk=event['event_id'])
            event_obj.data = json.dumps(data, ensure_ascii=False)
            event_obj.save(update_fields=['data'])

    @classmethod
    def alarm(cls, device, event):
        super().alarm(device, event)

    @classmethod
    def fault(cls, device, event):
        super().fault(device, event)
