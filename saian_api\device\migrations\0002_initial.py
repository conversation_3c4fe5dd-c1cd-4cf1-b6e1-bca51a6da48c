# Generated by Django 3.2.8 on 2021-12-30 15:16

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('device', '0001_initial'),
        ('project', '0001_initial'),
        ('building', '0002_initial'),
        ('issue', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='waterleakage',
            name='project',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='project.project'),
        ),
        migrations.AddField(
            model_name='roomdevice',
            name='active_room',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='building.activeroom'),
        ),
        migrations.AddField(
            model_name='roomdevice',
            name='device',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='device.device'),
        ),
        migrations.AddField(
            model_name='paramrecord',
            name='device',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='device.device'),
        ),
        migrations.AddField(
            model_name='livingdetection',
            name='device',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='device.device'),
        ),
        migrations.AddField(
            model_name='devicetimer',
            name='device',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='device.device'),
        ),
        migrations.AddIndex(
            model_name='deviceeventhistory',
            index=models.Index(fields=['project_id', 'nick_name'], name='device_even_project_36b620_idx'),
        ),
        migrations.AddIndex(
            model_name='deviceeventhistory',
            index=models.Index(fields=['nick_name', 'device_prototype_id'], name='device_even_nick_na_81dd43_idx'),
        ),
        migrations.AddIndex(
            model_name='deviceeventhistory',
            index=models.Index(fields=['nick_name', 'created_at'], name='device_even_nick_na_a38b41_idx'),
        ),
        migrations.AddIndex(
            model_name='deviceeventhistory',
            index=models.Index(fields=['device_prototype_id', 'created_at'], name='device_even_device__bb46a5_idx'),
        ),
        migrations.AddIndex(
            model_name='deviceeventhistory',
            index=models.Index(fields=['project_id', 'created_at'], name='device_even_project_cb6df2_idx'),
        ),
        migrations.AddIndex(
            model_name='deviceeventhistory',
            index=models.Index(fields=['nick_name', 'project_id', 'device_prototype_id'], name='device_even_nick_na_8fdca6_idx'),
        ),
        migrations.AddIndex(
            model_name='deviceeventhistory',
            index=models.Index(fields=['nick_name', 'device_prototype_id', 'created_at'], name='device_even_nick_na_90ea39_idx'),
        ),
        migrations.AddIndex(
            model_name='deviceeventhistory',
            index=models.Index(fields=['project_id', 'device_prototype_id', 'created_at'], name='device_even_project_070c64_idx'),
        ),
        migrations.AddIndex(
            model_name='deviceeventhistory',
            index=models.Index(fields=['nick_name', 'project_id', 'created_at'], name='device_even_nick_na_3e4ea2_idx'),
        ),
        migrations.AddIndex(
            model_name='deviceeventhistory',
            index=models.Index(fields=['nick_name', 'project_id', 'device_prototype_id', 'created_at'], name='device_even_nick_na_451a23_idx'),
        ),
        migrations.AddField(
            model_name='deviceevent',
            name='device',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='device.device'),
        ),
        migrations.AddField(
            model_name='deviceevent',
            name='device_issue',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='issue.deviceissue'),
        ),
        migrations.AddField(
            model_name='deviceevent',
            name='project',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='project.project'),
        ),
        migrations.AddField(
            model_name='devicectrllog',
            name='device',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='device.device'),
        ),
        migrations.AddField(
            model_name='devicectrllog',
            name='project',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='project.project'),
        ),
        migrations.AddField(
            model_name='deviceattribute',
            name='device',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='device.device'),
        ),
        migrations.AddField(
            model_name='device',
            name='project',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='project.project'),
        ),
        migrations.AddIndex(
            model_name='paramrecord',
            index=models.Index(fields=['mac', 'identifier'], name='param_recor_mac_e079b7_idx'),
        ),
        migrations.AddIndex(
            model_name='paramrecord',
            index=models.Index(fields=['device_id', 'identifier'], name='param_recor_device__0c4bc4_idx'),
        ),
        migrations.AddIndex(
            model_name='paramrecord',
            index=models.Index(fields=['device_id', 'identifier', 'created_at'], name='param_recor_device__0ad85c_idx'),
        ),
        migrations.AddIndex(
            model_name='paramrecord',
            index=models.Index(fields=['mac', 'identifier', 'created_at'], name='param_recor_mac_8d5530_idx'),
        ),
        migrations.AddIndex(
            model_name='deviceevent',
            index=models.Index(fields=['project_id', 'nick_name'], name='device_even_project_32ad63_idx'),
        ),
        migrations.AddIndex(
            model_name='deviceevent',
            index=models.Index(fields=['nick_name', 'device_prototype_id'], name='device_even_nick_na_f09caf_idx'),
        ),
        migrations.AddIndex(
            model_name='deviceevent',
            index=models.Index(fields=['nick_name', 'created_at'], name='device_even_nick_na_319203_idx'),
        ),
        migrations.AddIndex(
            model_name='deviceevent',
            index=models.Index(fields=['device_prototype_id', 'created_at'], name='device_even_device__1df14d_idx'),
        ),
        migrations.AddIndex(
            model_name='deviceevent',
            index=models.Index(fields=['project_id', 'created_at'], name='device_even_project_2df4d0_idx'),
        ),
        migrations.AddIndex(
            model_name='deviceevent',
            index=models.Index(fields=['nick_name', 'project_id', 'device_prototype_id'], name='device_even_nick_na_726dc1_idx'),
        ),
        migrations.AddIndex(
            model_name='deviceevent',
            index=models.Index(fields=['nick_name', 'device_prototype_id', 'created_at'], name='device_even_nick_na_d8b2e3_idx'),
        ),
        migrations.AddIndex(
            model_name='deviceevent',
            index=models.Index(fields=['project_id', 'device_prototype_id', 'created_at'], name='device_even_project_a841f4_idx'),
        ),
        migrations.AddIndex(
            model_name='deviceevent',
            index=models.Index(fields=['nick_name', 'project_id', 'created_at'], name='device_even_nick_na_271949_idx'),
        ),
        migrations.AddIndex(
            model_name='deviceevent',
            index=models.Index(fields=['nick_name', 'project_id', 'device_prototype_id', 'created_at'], name='device_even_nick_na_4cc888_idx'),
        ),
        migrations.AddIndex(
            model_name='devicectrllog',
            index=models.Index(fields=['executor_id', 'executor_type'], name='device_ctrl_executo_08e24b_idx'),
        ),
        migrations.AddIndex(
            model_name='devicectrllog',
            index=models.Index(fields=['project_id', 'executor_type'], name='device_ctrl_project_69e851_idx'),
        ),
        migrations.AddIndex(
            model_name='devicectrllog',
            index=models.Index(fields=['device_id', 'executor_type'], name='device_ctrl_device__0592f6_idx'),
        ),
        migrations.AddIndex(
            model_name='devicectrllog',
            index=models.Index(fields=['mac', 'executor_type'], name='device_ctrl_mac_9c72cc_idx'),
        ),
        migrations.AddIndex(
            model_name='devicectrllog',
            index=models.Index(fields=['executor_id', 'executor_type', 'errcode'], name='device_ctrl_executo_8990b6_idx'),
        ),
        migrations.AddIndex(
            model_name='devicectrllog',
            index=models.Index(fields=['project_id', 'executor_type', 'errcode'], name='device_ctrl_project_0c8c8c_idx'),
        ),
        migrations.AddIndex(
            model_name='devicectrllog',
            index=models.Index(fields=['device_id', 'executor_type', 'errcode'], name='device_ctrl_device__f222bd_idx'),
        ),
        migrations.AddIndex(
            model_name='devicectrllog',
            index=models.Index(fields=['mac', 'executor_type', 'errcode'], name='device_ctrl_mac_b4ac6a_idx'),
        ),
        migrations.AddIndex(
            model_name='deviceattribute',
            index=models.Index(fields=['device_id', 'show_in_list'], name='device_attr_device__8cfd69_idx'),
        ),
        migrations.AddIndex(
            model_name='device',
            index=models.Index(fields=['project_id', 'nick_name'], name='devices_project_27cb04_idx'),
        ),
        migrations.AddIndex(
            model_name='device',
            index=models.Index(fields=['project_id', 'device_type_id'], name='devices_project_92b14b_idx'),
        ),
        migrations.AddIndex(
            model_name='device',
            index=models.Index(fields=['project_id', 'device_type_id', 'status'], name='devices_project_70612f_idx'),
        ),
        migrations.AddIndex(
            model_name='device',
            index=models.Index(fields=['project_id', 'device_type_id', 'in_fault'], name='devices_project_aa67ae_idx'),
        ),
        migrations.AddIndex(
            model_name='device',
            index=models.Index(fields=['project_id', 'device_type_id', 'in_alarm'], name='devices_project_36401b_idx'),
        ),
    ]
