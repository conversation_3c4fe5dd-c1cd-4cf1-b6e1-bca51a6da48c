import os
import re
import json
import calendar
import datetime
import logging
from collections import defaultdict
from typing import List

from django.contrib.contenttypes.models import ContentType
from django.db import transaction
from django.db.models import Q, Case, When, F, Avg, Sum, OuterRef, Exists
from django.shortcuts import get_object_or_404
from openpyxl.reader.excel import load_workbook
from rest_framework import viewsets, status, exceptions
from rest_framework.decorators import action
from rest_framework.response import Response

from saian_api import settings
from saian_api.building.models import ActiveRoom
from saian_api.coldsource.models import ColdSource, ManualMeterReading, EcMeter
from saian_api.device.views import DeviceAttributeViewV5
from saian_api.report.tasks import sys_ec_report
from saian_api.report.models import DeviceHourlyStat, DlTask, DeviceDailyStat, DeviceMonthlyStat, ManualRecord
from saian_api.dashboard.models import ProjectWeather, EcCriteria, EcTranscription
from saian_api.devdefine.models import AttributePrototype, DevicePrototype, DeviceType
from saian_api.device.models import ParamRecord, ParamRecordHistory, <PERSON>ce, <PERSON>ceAttribute, DeviceEventHistory, RoomDevice
from saian_api.dimension.models import (Dimension, DimensionTerminal, DimensionAttribute, DimensionHourlyStat,
                                        DimensionMonthlyStat, DimensionDailyStat, PpvConfig, EsrStat, DimensionYearlyStat, DimensionUser)
from saian_api.dimension.serializers import (DimensionSerializer, DimensionTerminalSerializer,
                                             DimensionAttributeSerializer, SimpleDimensionAttributeSerializer, PpvConfigSerializer)
from saian_api.project.models import Project, WebChart
from saian_api.regions.models import Region
from saian_api.terminal.models import TerminalAttribute, Terminal
from saian_api.terminal.serializers import SimpleTerminalSerializer
from saian_api.user.models import WebUser
from saian_api.user.serializers import SimpleWebUserSerializer
from saian_api.utils.httpapi.weather import WeatherApi
from saian_api.utils.intapi_auth import IntapiAuth
from saian_api.utils.legacy_auth import LegacyAuth
from saian_api.utils.sy_jsonrenderer import SyJSONRender
from saian_api.utils.tools import get_day_range, get_month_range, get_year_range, is_number, ec_type_to_unit
from saian_api.utils.utils import ExcelUtils

# Create your views here.

class DimensionView(viewsets.ModelViewSet):
    serializer_class = DimensionSerializer
    renderer_classes = (SyJSONRender,)

    @action(detail=True, methods=['get', 'post', 'put', 'delete'])
    def get_authenticators(self):
        if 'intapi' in self.request.path:
            return [IntapiAuth(), ]
        return [LegacyAuth(), ]

    @classmethod
    def dimension_sort_key(cls, dim):
        name = dim['name']
        ec_types = ['总用电', '总用水', '总用冷', '总用气', '电', '水', '冷', '汽', '油']
        for i, v in enumerate(ec_types):
            if v in name:
                return i, name
        return 5, name

    def get_queryset(self):
        queryset = Dimension.objects.filter(project_id=self.request.user['project_id'])

        types = self.request.query_params.get('types', None)
        if types is not None:
            queryset = queryset.filter(type_name__in=[t.strip() for t in types.split(',')])
            if types == '单位':
                queryset = queryset.filter(Q(parent_id__isnull=True) | Q(parent_id__isnull=False, ec_persons__isnull=False, unit_area__isnull=False))

        dim_type = self.request.query_params.get('type', None)
        if dim_type is not None:
            queryset = queryset.filter(type_name=dim_type)
            if dim_type == '单位':
                queryset = queryset.filter(ec_persons__isnull=False, unit_area__isnull=False)
        else:
            pid = self.request.query_params.get('pid', None)
            if pid is not None:
                queryset = queryset.filter(parent_id=pid)

        search = self.request.query_params.get('search', None)
        if search is not None:
            queryset = queryset.filter(name__icontains=search)

        return queryset

    def get_object(self):
        return get_object_or_404(Dimension, pk=self.kwargs['pk'])

    def get_children(self, dimension_id, dimensions):
        children = [dim for dim in dimensions if dim['parent'] == dimension_id]
        for child in children:
            child['children'] = self.get_children(child['id'], dimensions)
        return children

    def list(self, request, *args, **kwargs):
        data = super().list(request, *args, **kwargs).data

        dimensions = data['results']
        root_dimensions = [dim for dim in dimensions if dim['parent'] is None]
        if len(root_dimensions):
            for dim in root_dimensions:
                dim['children'] = self.get_children(dim['id'], dimensions)
        else:
            root_dimensions = dimensions
        root_dimensions = sorted(root_dimensions, key=self.dimension_sort_key)

        return Response({
            'status': status.HTTP_200_OK,
            'dimensions': root_dimensions,
            'count': data['count']
        })

    def create(self, request, *args, **kwargs):
        data = request.data.copy()
        data['project'] = request.user['project_id']
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        return Response({
            'status': status.HTTP_200_OK,
            'data': serializer.data
        })

    def retrieve(self, request, *args, **kwargs):
        data = super().retrieve(request, *args, **kwargs).data
        return Response({
            'status': status.HTTP_200_OK,
            'dimension': data
        })

    def partial_update(self, request, *args, **kwargs):
        data = super().partial_update(request, *args, **kwargs).data

        return Response({
            'status': status.HTTP_200_OK,
            'dimension': data
        })

    def destroy(self, request, *args, **kwargs):
        dimension_id = kwargs.get('pk')
        children = Dimension.objects.filter(parent_id=dimension_id)
        if children.exists():
            return Response({
                'status': 40040,
                'error': '请先删除子维度',
                'data': None
            })

        super().destroy(request, *args, **kwargs)
        return Response({
            'status': status.HTTP_200_OK,
            'data': None,
        })


class DimensionUserView(viewsets.ModelViewSet):

    def get_queryset(self):
        queryset = DimensionUser.objects.all()

        dimension_id = self.request.query_params.get('dimension_id', None)
        if dimension_id is not None:
            queryset = queryset.filter(dimension_id=dimension_id)

        user_id = self.request.query_params.get('user_id', None)
        if user_id is not None:
            queryset = queryset.filter(web_user_id=user_id)

        return queryset.order_by('dimension_id')

    def list(self, request, *args, **kwargs):
        dimension_id = self.request.query_params.get('dimension_id', None)
        queryset = self.get_queryset()

        dimension_ids = [dimension_id] if dimension_id is not None else queryset.values_list('dimension_id', flat=True)

        result = []

        for d_id in dimension_ids:
            dimension = Dimension.objects.get(pk=d_id)
            user_ids = list(DimensionUser.objects.filter(dimension_id=d_id).values_list('web_user_id', flat=True))
            web_users = WebUser.objects.filter(id__in=user_ids)

            result.append({
                "id": dimension.id,
                "name": dimension.name,
                "web_users": SimpleWebUserSerializer(web_users, many=True).data,
                "count": web_users.count()
            })

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'dimension_users': result,
            }
        })

    def create(self, request, *args, **kwargs):
        project_id = request.user.get('project_id')
        dimension_id = request.data.get('dimension_id', None)
        if dimension_id is None:
            raise exceptions.ValidationError(detail={'detail': 'dimension_id is required!'})

        user_ids = request.data.get('user_ids', None)
        if user_ids is None:
            raise exceptions.ValidationError(detail={'detail': 'user_ids is required!'})
        if isinstance(user_ids, int):
            user_ids = [user_ids]

        current_user_ids = list(DimensionUser.objects.filter(dimension_id=dimension_id).values_list('web_user_id', flat=True))
        new_user = set(user_ids) - set(current_user_ids)
        del_user = set(current_user_ids) - set(user_ids)

        with transaction.atomic(using=f"prj{project_id}db"):
            for user_id in new_user:
                DimensionUser.objects.create(
                    dimension_id=dimension_id,
                    web_user_id=user_id
                )

            DimensionUser.objects.filter(dimension_id=dimension_id, web_user_id__in=del_user).delete()

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

class DimensionTerminalView(viewsets.ModelViewSet):
    serializer_class = DimensionTerminalSerializer
    renderer_classes = (SyJSONRender,)

    @action(detail=True, methods=['get', 'post', 'put', 'delete'])
    def get_authenticators(self):
        if 'intapi' in self.request.path:
            return [IntapiAuth(), ]
        return [LegacyAuth(), ]

    def get_queryset(self):
        queryset = DimensionTerminal.objects.all()

        dimension_id = self.request.query_params.get('dimension_id', None)
        if dimension_id is not None:
            queryset = queryset.filter(dimension_id=dimension_id)

        terminal_id = self.request.query_params.get('terminal_id', None)
        if terminal_id is not None:
            queryset = queryset.filter(terminal_id=terminal_id)

        return queryset.order_by('dimension_id')

    def list(self, request, *args, **kwargs):
        dimension_id = self.request.query_params.get('dimension_id', None)
        queryset = self.get_queryset()

        dimension_ids = [dimension_id] if dimension_id is not None else queryset.values_list('dimension_id', flat=True)

        result = []

        for d_id in dimension_ids:
            dimension = Dimension.objects.get(pk=d_id)
            terminals = dimension.terminals
            terminal_dps = set(terminals.values_list('device_prototype', flat=True))

            # 子设备终端的 device_prototype_id 是子设备类型，前端绑定终端时需要对应父设备类型
            # dp_ids 找出所有终端的父级设备类型id集合
            dp_ids = set()
            for dp in terminal_dps:
                dp = DevicePrototype.objects.get(pk=dp)
                dp_ids.add(dp.parent_id if dp.parent_id is not None else dp.id)

            result.append({
                "id": dimension.id,
                "name": dimension.name,
                "terminals": SimpleTerminalSerializer(terminals, many=True).data,
                "dp_ids": dp_ids,
                "count": terminals.count()
            })

        return Response({
            'status': status.HTTP_200_OK,
            'dimension_terminals': result,
        })

    def create(self, request, *args, **kwargs):
        project_id = request.user.get('project_id')
        dimension_id = request.data.get('dimension_id', None)
        if dimension_id is None:
            raise exceptions.ValidationError(detail={'detail': 'dimension_id is required!'})

        terminal_ids = request.data.get('terminal_ids', None)
        if terminal_ids is None:
            raise exceptions.ValidationError(detail={'detail': 'terminal_id is required!'})
        if isinstance(terminal_ids, str):
            terminal_ids = terminal_ids.split(',')

        attributes = request.data.get('attributes', [])
        if attributes is None:
            raise exceptions.ValidationError(detail={'detail': 'attributes is required!'})

        with transaction.atomic(using=f"prj{project_id}db"):
            # terminal_ids = terminal_ids.split(',')
            old_terminals = [str(t) for t in DimensionTerminal.objects.filter(dimension_id=dimension_id).values_list('terminal_id', flat=True)]

            # 需要新绑定的终端id
            c_terminals = list(set(terminal_ids).difference(set(old_terminals)))
            # 需要移除的终端id
            d_terminals = list(set(old_terminals).difference(terminal_ids))

            # 批量绑定
            dimension_terminals = []
            for terminal_id in c_terminals:
                dimension_terminals.append(DimensionTerminal(terminal_id=terminal_id, dimension_id=dimension_id))
            DimensionTerminal.objects.bulk_create(dimension_terminals)

            # 批量解除绑定
            DimensionTerminal.objects.filter(dimension_id=dimension_id, terminal_id__in=d_terminals).delete()

            # 创建维度属性
            for attr in attributes:

                # identifier 或 formula 至少要有一个
                formula = attr.get('formula', None)
                identifier = attr.get('identifier', None)
                if identifier is None and formula is None:
                    raise exceptions.ValidationError(detail={'detail': 'attributes data field identifier is required!'})

                ta_ids = attr.get('ta_ids', None)
                if ta_ids is None:
                    raise exceptions.ValidationError(detail={'detail': 'attributes data field ta_ids is required!'})

                # 检查维度属性是否已经存在
                has_dimension_attribute = False
                for da in DimensionAttribute.objects.filter(dimension_id=dimension_id, identifier=attr.get('identifier'), ta_ids__isnull=False):
                    # 因为 ta_ids 是终端属性的id字符串，终端属性相同可能顺序不同，故不能直接对比
                    if set(ta_ids.split(',')) - set(da.ta_ids.split(',')) == set(da.ta_ids.split(',')) - set(ta_ids.split(',')):
                        has_dimension_attribute = True
                        break

                if has_dimension_attribute:
                    continue

                ta_id = ta_ids.split(',')[0]
                ta = get_object_or_404(TerminalAttribute, pk=ta_id)
                ap = get_object_or_404(AttributePrototype, pk=ta.attribute_prototype_id)
                attr['is_cum'] = ap.is_cum
                attr['dimension'] = dimension_id

                serializer = DimensionAttributeSerializer(data=attr)
                serializer.is_valid(raise_exception=True)
                serializer.save()

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def destroy(self, request, *args, **kwargs):
        super().destroy(request, *args, **kwargs)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })


class DimensionAttributeView(viewsets.ModelViewSet):
    serializer_class = DimensionAttributeSerializer
    renderer_classes = (SyJSONRender,)

    @action(detail=True, methods=['get', 'post', 'put', 'delete'])
    def get_authenticators(self):
        if 'intapi' in self.request.path:
            return [IntapiAuth(), ]
        return [LegacyAuth(), ]

    def get_queryset(self):
        queryset = DimensionAttribute.objects.all()

        dimension_id = self.request.query_params.get('dimension_id', None)
        if dimension_id is not None:
            queryset = queryset.filter(dimension_id__in=dimension_id.split(','))

        search = self.request.query_params.get('search', None)
        if search is not None:
            queryset = queryset.filter(name__icontains=search)

        identifier = self.request.query_params.get('identifier', None)
        if identifier is not None:
            queryset = queryset.filter(identifier__icontains=identifier)

        return queryset

    def list(self, request, *args, **kwargs):
        if request.query_params.get('dimension_id', None) is None:
            return Response(status=status.HTTP_400_BAD_REQUEST, data={"error": "query field dimension_id is required.", "data": None})

        data = super().list(request, *args, **kwargs).data

        return Response({
            'status': status.HTTP_200_OK,
            'dimension_attributes': data['results'],
            'count': data['count']
        })

    def create(self, request, *args, **kwargs):
        data = request.data.copy()

        identifier = data.get('identifier', None)
        formula = data.get('formula', None)

        # identifier 或 formula 至少要有一个
        if identifier is None and formula is None:
            return Response(status=status.HTTP_400_BAD_REQUEST,
                            data={"error": 'data field identifier is required.',
                                  'status': status.HTTP_400_BAD_REQUEST,
                                  'data': None})

        ta_ids = data.get('ta_ids', None)
        if not ta_ids:
            return Response(status=status.HTTP_400_BAD_REQUEST,
                            data={"error": 'data field ta_ids is required.',
                                  'status': status.HTTP_400_BAD_REQUEST,
                                  'data': None})
        else:
            ta_id = ta_ids.split(',')[0]
            ta = get_object_or_404(TerminalAttribute, pk=ta_id)
            ap = get_object_or_404(AttributePrototype, pk=ta.attribute_prototype_id)
            data['is_cum'] = ap.is_cum

        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)

        return Response({
            'status': status.HTTP_200_OK,
            'dimension_attribute': serializer.data
        })

    def partial_update(self, request, *args, **kwargs):
        data = super().partial_update(request, *args, **kwargs).data

        return Response({
            'status': status.HTTP_200_OK,
            'dimension_attribute': data
        })

    def retrieve(self, request, *args, **kwargs):
        data = super().retrieve(request, *args, **kwargs).data
        return Response({
            'status': status.HTTP_200_OK,
            'dimension_attribute': data
        })

    def destroy(self, request, *args, **kwargs):
        super().destroy(request, *args, **kwargs)
        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })


class DimensionTypeView(viewsets.ModelViewSet):
    renderer_classes = (SyJSONRender,)

    @action(detail=True, methods=['get', 'post', 'put', 'delete'])
    def get_authenticators(self):
        if 'intapi' in self.request.path:
            return [IntapiAuth(), ]
        return [LegacyAuth(), ]

    def list(self, request, *args, **kwargs):
        dimension_types = set(Dimension.objects.all().values_list('type_name', flat=True))

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'dimension_types': dimension_types,
            },
            'count': len(dimension_types)
        })


class DimensionAttributeEcItem(viewsets.ModelViewSet):
    @action(detail=True, methods=['get'])
    def get_authenticators(self):
        if 'intapi' in self.request.path:
            return [IntapiAuth(), ]
        return [LegacyAuth(), ]
        
    def get_root_dimension(self, dimensions, dimension):
        if dimension.parent_id is None:
            return dimension
        else:
            dimension = next((d for d in dimensions if d.id == dimension.parent_id), None)
            return self.get_root_dimension(dimensions, dimension)

    @classmethod
    def custom_dimension_sort(cls, dim):
        name = dim["name"]
        # 定义排序优先级
        if "空调" in name and "总" in name:
            return 0, name  # 排在前面
        if "用水" in name and "总" in name:
            return 0, name  # 排在前面
        if "用气" in name and "总" in name:
            return 0, name  # 排在前面
        if "总" in name:
            return 0, name  # 排在前面
        elif "水" in name:
            return 2, name  # 排在后面
        elif "单位" in name:
            return 3, name  # 单位排在最后
        else:
            return 1, name  # 中间排序

    def list(self, request, *args, **kwargs):
        ec_result = {}
        dimensions = list(Dimension.objects.all())

        ec_das = DimensionAttribute.objects.filter(ec_type__isnull=False).order_by('ec_type', 'dimension_id')

        ec_type = request.query_params.get('ec_type', None)
        if ec_type is not None:
            ec_das = ec_das.filter(ec_type=ec_type)

        dimension_type = request.query_params.get('dimension_type', None)
        if dimension_type is not None:
            ec_das = ec_das.filter(dimension__type_name=dimension_type)

        for da in ec_das:
            dimension = next((d for d in dimensions if d.id == da.dimension_id), None)
            root_dimension = self.get_root_dimension(dimensions, dimension)
            if ec_result.get(root_dimension.id) is None:
                ec_result[root_dimension.id] = [SimpleDimensionAttributeSerializer(da).data]
            else:
                if dimension.parent_id is None:
                    ec_result[root_dimension.id].insert(0, SimpleDimensionAttributeSerializer(da).data)
                elif '总' in da.name:
                    ec_result[root_dimension.id].insert(0, SimpleDimensionAttributeSerializer(da).data)
                else:
                    ec_result[root_dimension.id].append(SimpleDimensionAttributeSerializer(da).data)

        result = []
        for k, v in ec_result.items():
            dimension = next((d for d in dimensions if d.id == k), None)
            item = {
                "name": dimension.name,
                "type_name": dimension.type_name,
                "dimension_id": dimension.id,
                "ec_items": v
            }
            if dimension.parent_id is None:
                result.insert(0, item)
            else:
                result.append(item)

        result = sorted(result, key=self.custom_dimension_sort)

        return Response({
            'status': status.HTTP_200_OK,
            'data': result
        })

class DimensionEcTypeView(viewsets.ModelViewSet):
    EC_TYPES = {
        10: {'name': '用电量', 'unit': 'kWh'},
        20: {'name': '用水量', 'unit': 'm³'},
        30: {'name': '用冷量', 'unit': 'kWh'},
        40: {'name': '燃气量', 'unit': 'm³'},
        50: {'name': '燃油量', 'unit': 'L'},
        60: {'name': '用热量', 'unit': 'kWh'}
    }

    @action(detail=True, methods=['get'])
    def get_authenticators(self):
        if 'intapi' in self.request.path:
            return [IntapiAuth(), ]
        return [LegacyAuth(), ]
        
    def list(self, request, *args, **kwargs):
        ec_types = []
        for ec_type in set(DimensionAttribute.objects.filter(ec_type__isnull=False).values_list('ec_type', flat=True)):
            ec_types.append({'id': ec_type,
                             'ec_type': ec_type,
                             'name': self.EC_TYPES.get(ec_type, {}).get('name', '--'),
                             'unit': self.EC_TYPES.get(ec_type, {}).get('unit', '--')})

        ec_types = sorted(ec_types, key=lambda item: item['ec_type'])
        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'ec_types': ec_types
            }
        })

class DimensionEcStats(viewsets.ModelViewSet):
    @action(detail=True, methods=['get'])
    def get_authenticators(self):
        if 'intapi' in self.request.path:
            return [IntapiAuth(), ]
        return [LegacyAuth(), ]

    def calc_single(self, time_item, dimension_attribute_id):
        project_id = self.request.user.get('project_id')
        now = datetime.datetime.now()
        dt = self.request.query_params.get('dt', None)
        dt = datetime.datetime.strptime(dt, '%Y%m%d%H%M%S') if dt is not None else now
        time_type = self.request.query_params.get('time_type', 'di')
        item = {
            "peak": 0,
            "flat": 0,
            "valley": 0,
            "sharp": 0
        }
        dimension_attribute = DimensionAttribute.objects.get(pk=dimension_attribute_id)
        tas = TerminalAttribute.objects.filter(id__in=dimension_attribute.ta_ids.split(',')) if dimension_attribute.ta_ids else []

        device_cache = {}
        for ta in tas:
            device_id = ta.terminal.device_id
            device = device_cache.get(device_id, None)
            if device is None:
                device = Device.objects.get(pk=device_id)
                device_cache[device_id] = device

            if time_type == 'yr':
                begin, end = get_year_range(dt)
                queryset = DeviceHourlyStat.objects.filter(device_id=device_id, created_at__range=[begin, end], created_at__month=time_item, )
            elif time_type == 'mo':
                begin, end = get_month_range(dt)
                queryset = DeviceHourlyStat.objects.filter(device_id=device_id, created_at__range=[begin, end], created_at__day=time_item, )
            else:
                begin, end = get_day_range(dt)
                queryset = DeviceHourlyStat.objects.filter(device_id=device_id, created_at__range=[begin, end], created_at__hour=time_item, )

            # 峰平谷尖 identifier 有两种形式
            # TODO 优化判断效率
            peak_idf = f'{ta.identifier}_P'
            flat_idf = f'{ta.identifier}_F'
            valley_idf = f'{ta.identifier}_V'
            sharp_idf = f'{ta.identifier}_S'
            if not AttributePrototype.objects.filter(device_prototype_id=device.device_prototype_id, identifier=peak_idf).exists():
                idf_comp = ta.identifier.split('_')
                idf_comp.insert(-1, 'P')
                peak_idf = '_'.join(idf_comp)
                idf_comp[-2] = 'F'
                flat_idf = '_'.join(idf_comp)
                idf_comp[-2] = 'V'
                valley_idf = '_'.join(idf_comp)
                idf_comp[-2] = 'S'
                sharp_idf = '_'.join(idf_comp)

            peak = queryset.filter(identifier=peak_idf).order_by('created_at').aggregate(Sum('avg'))['avg__sum']
            flat = queryset.filter(identifier=flat_idf).order_by('created_at').aggregate(Sum('avg'))['avg__sum']
            valley = queryset.filter(identifier=valley_idf).order_by('created_at').aggregate(Sum('avg'))['avg__sum']
            sharp = queryset.filter(identifier=sharp_idf).order_by('created_at').aggregate(Sum('avg'))['avg__sum']

            item['peak'] += peak if peak is not None else 0
            item['flat'] += flat if flat is not None else 0
            item['valley'] += valley if valley is not None else 0
            item['sharp'] += sharp if sharp is not None else 0

        if (time_type == 'di' and now.date() == dt.date() and time_item == now.hour) or (
                time_type == 'mo' and dt.year == now.year and dt.month == now.month and time_item == now.day) or (
                time_type == 'yr' and dt.year == now.year and time_item == now.month):

            current_hour = now.strftime('%Y-%m-%d %H:05:00')
            current_hour_begin_records = ParamRecord.objects.filter(created_at=current_hour)

            # 峰
            # 当前时各个终端属性值的和
            peak_tas = []
            # 平
            # 当前时各个终端属性值的和
            flat_tas = []
            # 谷
            # 当前时各个终端属性值的和
            valley_tas = []
            # 尖
            # 当前时各个终端属性值的和
            sharp_tas = []

            current_hour_begin_cons_peak = 0
            current_hour_begin_cons_flat = 0
            current_hour_begin_cons_valley = 0
            current_hour_begin_cons_sharp = 0

            for ta in tas:
                device_id = ta.terminal.device_id
                device = device_cache.get(device_id, None)
                if device is None:
                    device = Device.objects.get(pk=device_id)
                    device_cache[device_id] = device
                peak_idf = f'{ta.identifier}_P'
                flat_idf = f'{ta.identifier}_F'
                valley_idf = f'{ta.identifier}_V'
                sharp_idf = f'{ta.identifier}_S'
                if not AttributePrototype.objects.filter(device_prototype_id=device.device_prototype_id, identifier=peak_idf).exists():
                    idf_comp = ta.identifier.split('_')
                    idf_comp.insert(-1, 'P')
                    peak_idf = '_'.join(idf_comp)
                    idf_comp[-2] = 'F'
                    flat_idf = '_'.join(idf_comp)
                    idf_comp[-2] = 'V'
                    valley_idf = '_'.join(idf_comp)
                    idf_comp[-2] = 'S'
                    sharp_idf = '_'.join(idf_comp)

                peak_ta = TerminalAttribute.objects.filter(terminal_id=ta.terminal_id, identifier=peak_idf).last()
                if peak_ta is not None:
                    peak_tas.append(peak_ta)

                flat_ta = TerminalAttribute.objects.filter(terminal_id=ta.terminal_id, identifier=flat_idf).last()
                if flat_ta is not None:
                    flat_tas.append(flat_ta)

                valley_ta = TerminalAttribute.objects.filter(terminal_id=ta.terminal_id, identifier=valley_idf).last()
                if valley_ta is not None:
                    valley_tas.append(valley_ta)

                sharp_ta = TerminalAttribute.objects.filter(terminal_id=ta.terminal_id, identifier=sharp_idf).last()
                if sharp_ta is not None:
                    sharp_tas.append(sharp_ta)

                record = current_hour_begin_records.filter(device_id=device_id, identifier=peak_idf).last()
                if record is not None:
                    current_hour_begin_cons_peak += float(record.value)

                record = current_hour_begin_records.filter(device_id=device_id, identifier=flat_idf).last()
                if record is not None:
                    current_hour_begin_cons_flat += float(record.value)

                record = current_hour_begin_records.filter(device_id=device_id, identifier=valley_idf).last()
                if record is not None:
                    current_hour_begin_cons_valley += float(record.value)

                record = current_hour_begin_records.filter(device_id=device_id, identifier=sharp_idf).last()
                if record is not None:
                    current_hour_begin_cons_sharp += float(record.value)

            now_cons_peak = sum([float(TerminalAttribute.objects.query_object_by_ta(ta, project_id).value) for ta in peak_tas])

            now_cons_flat = sum([float(TerminalAttribute.objects.query_object_by_ta(ta, project_id).value) for ta in flat_tas])

            now_cons_valley = sum([float(TerminalAttribute.objects.query_object_by_ta(ta, project_id).value) for ta in valley_tas])

            now_cons_sharp = sum([float(TerminalAttribute.objects.query_object_by_ta(ta, project_id).value) for ta in sharp_tas])

            item['peak'] += now_cons_peak - current_hour_begin_cons_peak
            item['flat'] += now_cons_flat - current_hour_begin_cons_flat
            item['valley'] += now_cons_valley - current_hour_begin_cons_valley
            item['sharp'] += now_cons_sharp - current_hour_begin_cons_sharp

        return item

    def calc_ppv_data(self, da, count):

        ppv_data = []

        # 请求维度属性信息
        dimension_attribute = da
        # 小时、日、月份
        time_type = self.request.query_params.get('time_type', 'di')
        if time_type == 'di':
            time = [i for i in range(0, count)]
        else:
            time = [i for i in range(1, count + 1)]
        # 是否需要进行公式计算
        if dimension_attribute.formula:
            for time_item in time:
                peak = 0
                flat = 0
                valley = 0
                sharp = 0
                # 提取公式内的维度属性
                dimension_attribute_list = re.findall(r'{{(.*?)}}', dimension_attribute.formula)
                # 临时存储公式各值
                da_values = []

                for da_id in dimension_attribute_list:
                    da_values.append(self.calc_single(time_item, da_id))

                eval_str_peak = dimension_attribute.formula
                eval_str_flat = dimension_attribute.formula
                eval_str_valley = dimension_attribute.formula
                eval_str_sharp = dimension_attribute.formula

                for v in da_values:
                    eval_str_peak = re.sub(r'{{(.*?)}}', f'{v["peak"]}', eval_str_peak, 1)
                    eval_str_flat = re.sub(r'{{(.*?)}}', f'{v["flat"]}', eval_str_flat, 1)
                    eval_str_valley = re.sub(r'{{(.*?)}}', f'{v["valley"]}', eval_str_valley, 1)
                    eval_str_sharp = re.sub(r'{{(.*?)}}', f'{v["sharp"]}', eval_str_sharp, 1)

                peak = eval(eval_str_peak)
                flat = eval(eval_str_flat)
                valley = eval(eval_str_valley)
                sharp = eval(eval_str_sharp)

                ppv_data_item = {
                    'peak': round(peak, 2),
                    'flat': round(flat, 2),
                    'valley': round(valley, 2),
                    'sharp': round(sharp, 2)
                }
                ppv_data.append(ppv_data_item)
        else:
            for time_item in time:
                item = self.calc_single(time_item, dimension_attribute.id)
                ppv_data_item = {
                    'peak': round(item['peak'], 2),
                    'flat': round(item['flat'], 2),
                    'valley': round(item['valley'], 2),
                    'sharp': round(item['sharp'], 2)
                }
                ppv_data.append(ppv_data_item)

        return ppv_data

    def list(self, request, *args, **kwargs):
        project_id = request.user['project_id']

        project = Project.objects.get(pk=project_id)
        project_settings = json.loads(project.settings)
        project_support = project_settings.get('en_ppv', False)

        now = datetime.datetime.now()
        time_type = request.query_params.get('time_type', 'di')
        da_ids = request.query_params.get('item_ids', None)

        time_range = []
        stats = []
        current_queryset = None
        previous_queryset = None
        prev_month_range = None

        current_name = ''
        diff_name = ''
        previous_name = ''

        # 指定范围
        from_at = request.query_params.get('from', None)
        till_at = request.query_params.get('till', None)
        if from_at is not None and till_at is not None:
            from_at = datetime.datetime.strptime(from_at, '%Y%m%d%H%M%S').replace(minute=0, second=0)
            till_at = datetime.datetime.strptime(till_at, '%Y%m%d%H%M%S').replace(minute=59, second=59)

            if from_at > till_at:
                from_at, till_at = till_at, from_at

        if da_ids is None:
            raise exceptions.ValidationError(detail={'detail': 'item_ids is required!'})

        if time_type == 'di':
            current_name = '当日用量'
            diff_name = '昨日同比'
            previous_name = '昨日同期'

            dt = request.query_params.get('dt', datetime.datetime.strftime(now, '%Y%m%d000000'))
            dt_begin = datetime.datetime.strptime(dt[:8], '%Y%m%d')
            dt_end = dt_begin + datetime.timedelta(days=1) - datetime.timedelta(seconds=1)

            if from_at is not None:
                dt_begin = from_at
                dt_end = till_at

            time_range = []
            begin = dt_begin
            while begin <= dt_end:
                time_range.append('%02d时' % begin.hour)
                begin = begin + datetime.timedelta(hours=1)

            # for h in range(24):
            #     time_range.append('%02d时' % h)
            current_queryset = DimensionHourlyStat.objects.filter(created_at__range=(dt_begin, dt_end),
                                                                  dimension_attribute_id__in=da_ids.split(',')).order_by('created_at')

            previous_queryset = DimensionHourlyStat.objects.filter(created_at__range=(dt_begin - datetime.timedelta(days=1),
                                                                                      dt_end - datetime.timedelta(days=1)),
                                                                   dimension_attribute_id__in=da_ids.split(',')).order_by('created_at')

        elif time_type == 'wk':
            current_name = '当周用量'
            diff_name = '上周同比'
            previous_name = '上周同期用量'

            dt = request.query_params.get('dt', datetime.datetime.strftime(now, '%Y%m%d000000'))
            dt_begin = datetime.datetime.strptime(dt[:8], '%Y%m%d')

            dt_begin = dt_begin - datetime.timedelta(days=dt_begin.weekday())
            dt_end = dt_begin + datetime.timedelta(days=7) - datetime.timedelta(seconds=1)
            prev_dt_begin = dt_begin - datetime.timedelta(days=7)

            if from_at is not None:
                dt_begin = from_at.replace(hour=0)
                dt_end = till_at.replace(hour=23)

            time_range = []
            begin = dt_begin
            while begin <= dt_end:
                time_range.append('%02d日' % begin.day)
                begin = begin + datetime.timedelta(days=1)

            current_queryset = DimensionDailyStat.objects.filter(created_at__range=(dt_begin, dt_end),
                                                                 dimension_attribute_id__in=da_ids.split(',')).order_by('created_at')

            previous_queryset = DimensionDailyStat.objects.filter(created_at__range=(prev_dt_begin, dt_begin),
                                                                  dimension_attribute_id__in=da_ids.split(',')).order_by('created_at')

        elif time_type == 'mo':
            current_name = '当月用量'
            diff_name = '上月同比'
            previous_name = '上月同期用量'

            dt = request.query_params.get('dt', datetime.datetime.strftime(now, '%Y%m01000000'))
            dt_begin = datetime.datetime.strptime(dt[:6], '%Y%m')
            month_range = calendar.monthrange(dt_begin.year, dt_begin.month)[1]
            prev_month = dt_begin - datetime.timedelta(days=1)
            prev_month_range = calendar.monthrange(prev_month.year, prev_month.month)[1]
            # for d in range(month_range):
            #     time_range.append('%02d日' % (d + 1))
            dt_end = dt_begin + datetime.timedelta(days=month_range) - datetime.timedelta(seconds=1)
            if from_at is not None:
                dt_begin = from_at.replace(hour=0)
                dt_end = till_at.replace(hour=23)
            time_range = []
            begin = dt_begin
            span_day = (dt_end - dt_begin).days + 1
            while begin <= dt_end:
                time_range.append('%02d日' % begin.day)
                begin = begin + datetime.timedelta(days=1)

            current_queryset = DimensionDailyStat.objects.filter(created_at__range=(dt_begin, dt_end),
                                                                 dimension_attribute_id__in=da_ids.split(',')).order_by('created_at')

            # 广安项目要求使用上一年的数据
            if int(project_id) == 91:
                diff_name = '去年同比'
                previous_name = '去年同期用量'
                prev_begin_dt = dt_begin - datetime.timedelta(days=365)
                prev_end_dt = prev_begin_dt + datetime.timedelta(days=span_day)
                prev_month_range = span_day
                previous_queryset = DimensionDailyStat.objects.filter(created_at__range=(prev_begin_dt, prev_end_dt),
                                                                      dimension_attribute_id__in=da_ids.split(',')).order_by('created_at')
            else:
                previous_queryset = DimensionDailyStat.objects.filter(created_at__range=(dt_begin - datetime.timedelta(days=prev_month_range),
                                                                                         dt_begin - datetime.timedelta(seconds=1)),
                                                                      dimension_attribute_id__in=da_ids.split(',')).order_by('created_at')
        elif time_type == 'yr':
            current_name = '当年用量'
            diff_name = '上年同比'
            previous_name = '上年同期用量'

            dt = request.query_params.get('dt', datetime.datetime.strftime(now, '%Y0101000000'))
            dt_begin = datetime.datetime.strptime(dt[:4], '%Y')

            dt_end = datetime.datetime(dt_begin.year, 12, 31, 23, 59, 59)
            if from_at is not None:
                monthrange = calendar.monthrange(till_at.year, till_at.month)[1]
                dt_begin = from_at.replace(hour=0)
                dt_end = till_at.replace(day=monthrange, hour=23)
            time_range = []
            begin_month = dt_begin.month
            while begin_month <= dt_end.month:
                time_range.append('%02d月' % begin_month)
                begin_month += 1

            # for m in range(12):
            #     time_range.append('%02d月' % (m + 1))
            try:
                prev_begin = dt_begin.replace(year=dt_begin.year - 1)
                prev_end = dt_end.replace(year=dt_begin.year - 1)
            except ValueError:
                monthrange = calendar.monthrange(dt_begin.year - 1, dt_begin.month)[1]
                prev_begin = dt_begin.replace(year=dt_begin.year - 1, month=dt_begin.month, day=monthrange)
                monthrange = calendar.monthrange(dt_end.year - 1, dt_end.month)[1]
                prev_end = dt_end.replace(year=dt_end.year - 1, month=dt_end.month, day=monthrange)

            current_queryset = DimensionMonthlyStat.objects.filter(created_at__range=(dt_begin, dt_end),
                                                                   dimension_attribute_id__in=da_ids.split(',')).order_by('created_at')
            previous_queryset = DimensionMonthlyStat.objects.filter(created_at__range=(prev_begin, prev_end),
                                                                    dimension_attribute_id__in=da_ids.split(',')).order_by('created_at')

        elif time_type == 'annual':
            current_name = '每年用量'
            diff_name = ''
            previous_name = ''

            current_queryset = DimensionYearlyStat.objects.filter(dimension_attribute_id__in=da_ids.split(',')).order_by('created_at')
            previous_queryset = None

            if from_at is not None:
                current_queryset = current_queryset.filter(created_at__gte=from_at,
                                                           created_at__lte=till_at.replace(month=12, day=31, hour=23, minute=59, second=59))

            time_range = [f'{y.created_at.year}年' for y in current_queryset]

        if current_queryset is None and previous_queryset is None:
            return Response({
                'status': status.HTTP_200_OK,
                'data': {
                    'stats': stats,
                    'time_range': time_range
                }
            })

        dt = request.query_params.get('dt', None)
        dt = datetime.datetime.strptime(dt, '%Y%m%d%H%M%S') if dt is not None else now

        for da in DimensionAttribute.objects.filter(id__in=da_ids.split(',')).order_by('-formula'):
            item_id = da.id
            # for item_id in da_ids.split(','):
            da = DimensionAttribute.objects.get(pk=item_id)
            if da.ta_ids:
                # ap_id = TerminalAttribute.objects.get(pk=da.ta_ids.split(',')[0]).attribute_prototype_id
                # ap = AttributePrototype.objects.get(pk=ap_id)
                # 维度属性关联的终端属性
                tas = TerminalAttribute.objects.filter(id__in=da.ta_ids.split(','))
            else:
                # ap = None
                tas = []
            current_data = [round(float(v), 2) for v in current_queryset.filter(dimension_attribute_id=item_id).values_list('avg', flat=True)]
            if previous_queryset is not None:
                previous_data = [round(float(v), 2) for v in previous_queryset.filter(dimension_attribute_id=item_id).values_list('avg', flat=True)]
            else:
                previous_data = []

            current_hour_cons = 0
            # 当前时的用量, 上一小时统计未完成时不计算当前小时用量
            if (time_type == 'yr' and now.minute >= 5) or (dt.date() == now.date() and len(current_data) >= now.hour):
                current_hour_cons = da.calc_current_hour_cons(project_id)

            # 初始日，月，年数据对齐
            if time_type == 'di':
                # 将当前小时的用量加入 current_data
                if till_at is None and len(current_data) < now.hour + 1:  # 查询往日的小时统计不应该加上当前小时的用量
                    current_data.append(current_hour_cons)
                elif till_at and till_at.date() == now.date() and till_at.hour == now.hour:
                    current_data.append(current_hour_cons)
                # 初始日数据对齐
                # 数据差异，正常情况，数据量与当前的时是相等的，但有两种特殊情况例外
                # 1. 当前日期是项目初始化的当日
                # 2. 当前时的维度统计还未完成，统计任务在05分开始
                data_discrepancy = now.hour - len(current_data)
                if len(current_data) < 24 and from_at is None:
                    current_data = [0.0] * (now.hour - len(current_data) - data_discrepancy) + current_data
                if len(previous_data) < 24 and from_at is None:
                    previous_data = [0.0] * (24 - len(previous_data) - data_discrepancy) + previous_data

            elif time_type == 'mo':
                # 当日的用量
                if dt.year == now.year and dt.month == now.month:
                    today_hour = now.strftime('%Y-%m-%d 00:05:00')

                    if da.formula:
                        now_cons = da.calc_formula_value()
                        today_begin_cons = da.calc_formula_value(today_hour)
                    else:
                        # 当前时各个终端属性值的和
                        ta_values = [TerminalAttribute.objects.query_object_by_ta(ta, project_id).value for ta in tas]
                        now_cons = sum([float(v) for v in ta_values if v])
                        # 从 ParamRecord 查询当日开始时的记录
                        today_begin_records = ParamRecord.objects.filter(created_at=today_hour)
                        today_begin_cons = 0
                        for ta in tas:
                            record = today_begin_records.filter(identifier=ta.identifier, device__terminal=ta.terminal).last()
                            if record is not None:
                                today_begin_cons += float(record.value)

                    # 将当日用量加入 current_data
                    if till_at is None:
                        current_data.append(round(now_cons - today_begin_cons, 2))
                    elif till_at and till_at.date() == now.date():
                        current_data.append(round(now_cons - today_begin_cons, 2))
                # 初始月数据对齐
                day = now.day if dt.month == now.month else calendar.monthrange(dt.year, dt.month)[1]
                if len(current_data) + 1 < day and from_at is None:
                    current_data = [0.0] * (day - len(current_data)) + current_data
                if from_at is not None and till_at is not None:
                    delta = till_at - from_at
                    if delta.days + 1 > len(current_data):
                        current_data = [0.0] * (delta.days + 1 - len(current_data)) + current_data
                if prev_month_range is not None and len(previous_data) < prev_month_range and from_at is None:
                    previous_data = [0.0] * (prev_month_range - len(previous_data)) + previous_data
            elif time_type == 'yr':
                # 当月的用量
                if dt.year == now.year:
                    # 当前时各个终端属性值的和
                    # 计算当月用电量
                    current_month_begin = datetime.datetime.strftime(now, '%Y-%m-01 00:00:00')
                    current_month_cons = current_hour_cons
                    for day in DimensionDailyStat.objects.filter(dimension_attribute_id=item_id, created_at__gte=current_month_begin):
                        current_month_cons += float(day.avg)

                    # 计算当日用电量
                    current_day_begin = datetime.datetime.strftime(now, '%Y-%m-%d 00:00:00')
                    for hour in DimensionHourlyStat.objects.filter(dimension_attribute_id=item_id, created_at__gte=current_day_begin):
                        current_month_cons += float(hour.avg)
                    # 将当月用量加入 current_data
                    if till_at is None:
                        current_data.append(round(current_month_cons, 2))
                    elif till_at and till_at.year == now.year and till_at.month == now.month:
                        current_data.append(round(current_month_cons, 2))

                if len(current_data) < 12 and from_at is None:
                    if dt.year == now.year:
                        compensate_count = now.month - len(current_data)
                    else:
                        compensate_count = 12 - len(current_data)
                    current_data = [0.0] * compensate_count + current_data
                if from_at is not None and till_at is not None:
                    if till_at.month - from_at.month + 1 > len(current_data):
                        current_data = [0.0] * (till_at.month - from_at.month + 1 - len(current_data)) + current_data
                if len(previous_data) < 12 and from_at is None:
                    previous_data = [0.0] * (12 - len(previous_data)) + previous_data

            elif time_type == 'annual':
                if till_at is None or till_at.year >= now.year:
                    # 计算当年的用量
                    time_range.append(f'{now.year}年')

                    # 计算当月用电量
                    current_month_begin = datetime.datetime.strftime(now, '%Y-%m-01 00:00:00')
                    current_month_cons = current_hour_cons
                    days_cons = DimensionDailyStat.objects.filter(dimension_attribute_id=item_id,
                                                                  created_at__gte=current_month_begin).aggregate(sum=Sum('avg'))
                    if days_cons['sum'] is not None:  # 每月1号，没有当月的日统计
                        current_month_cons += float(days_cons['sum'])

                    # 计算当日用电量
                    current_day_begin = datetime.datetime.strftime(now, '%Y-%m-%d 00:00:00')
                    hours_cons = DimensionHourlyStat.objects.filter(dimension_attribute_id=item_id,
                                                                    created_at__gte=current_day_begin).aggregate(sum=Sum('avg'))
                    if hours_cons['sum'] is not None:  # 每日01点前，没有当日的小时统计，这时hours_cons['sum'] == None
                        current_month_cons += float(hours_cons['sum'])

                    monthly_cons = DimensionMonthlyStat.objects.filter(dimension_attribute_id=item_id,
                                                                       created_at__year=now.year).aggregate(sum=Sum('avg'))
                    if monthly_cons is not None:
                        current_data.append(round(float(monthly_cons['sum']) + current_month_cons, 2))
                    previous_data = []

            if len(current_data) > len(previous_data):
                diff_data = [current_data[idx] - previous for idx, previous in enumerate(previous_data)]
            else:
                diff_data = [current - previous_data[idx] for idx, current in enumerate(current_data)]

            ppv_data = self.calc_ppv_data(da, len(current_data)) if project_support else []

            stats.append({
                "id": da.id,
                "name": da.name,
                "unit": ec_type_to_unit(da.ec_type),
                "current": {
                    "data": current_data,
                    "name": current_name,
                    "ppv_data": ppv_data
                },
                "diff": {
                    "data": diff_data,
                    "name": diff_name
                },
                "previous": {
                    "data": previous_data,
                    "name": previous_name
                }
            })

        # 排序，总用电量在前面
        sorted_ids = list(DimensionAttribute.objects.order_by('dimension__parent_id', 'dimension_id').values_list('id', flat=True))
        sorted_ids = list(filter(lambda x: f'{x}' in da_ids.split(','), sorted_ids))
        stats = sorted(stats, key=lambda obj: sorted_ids.index(obj["id"]))

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'stats': stats,
                'time_range': time_range
            }
        })

class DimensionEcRankingView(viewsets.ModelViewSet):
    @action(detail=True, methods=['get'])
    def get_authenticators(self):
        if 'intapi' in self.request.path:
            return [IntapiAuth(), ]
        return [LegacyAuth(), ]
    def get_da_queryset(self):
        # TODO 区分单体和总 (formula)
        queryset = DimensionAttribute.objects.all()
        # 能耗类型， 10-电，20-水, 30-冷, 40-燃气, 50-燃油
        ec_type = self.request.query_params.get('ec_type', None)
        # 维度类型，对应 Dimension 的 name 字段
        target = self.request.query_params.get('target', None)
        # 维度属性 id
        target_ids = self.request.query_params.get('target_ids', None)

        if ec_type is not None:
            queryset = queryset.filter(ec_type=ec_type)

        if target is not None:
            queryset = queryset.filter(dimension__type_name=target)

        if target_ids is not None:
            queryset = queryset.filter(id__in=target_ids.split(','))
        else:
            # 排除非能耗维度属性
            queryset = queryset.filter(ec_type__isnull=False)
            # 排除总用电以及热水用电,identifier是null
            queryset = queryset.exclude(Q(name__icontains='总用电') | Q(name__contains='热水用电') | Q(identifier__isnull=True))
            # 如果不指定维度属性，就排除单位
            queryset = queryset.exclude(dimension__type_name='单位')

        return queryset

    def list(self, request, *args, **kwargs):
        now = datetime.datetime.now()
        project_id = request.user.get('project_id')
        # 时间日期
        dt = request.query_params.get('dt', now)
        # 指定范围
        from_at = request.query_params.get('from', None)
        till_at = request.query_params.get('till', None)
        if from_at is not None and till_at is not None:
            from_at = datetime.datetime.strptime(from_at, '%Y%m%d%H%M%S')
            till_at = datetime.datetime.strptime(till_at, '%Y%m%d%H%M%S')

        # 时间类型，默认小时统计
        time_type = self.request.query_params.get('type', 'hr')

        da_queryset = self.get_da_queryset()
        da_ids = da_queryset.values_list('id', flat=True)
        queryset = DimensionHourlyStat.objects.filter(dimension_attribute_id__in=da_ids)

        if time_type == 'hr' or time_type == 'di':
            # queryset = DimensionHourlyStat.objects.filter(dimension_attribute_id__in=da_ids)
            dt_begin, dt_end = get_day_range(dt)
        elif time_type == 'mo':
            # queryset = DimensionMonthlyStat.objects.filter(dimension_attribute_id__in=da_ids)
            dt_begin, dt_end = get_month_range(dt)
        else:
            # queryset = DimensionDailyStat.objects.filter(dimension_attribute_id__in=da_ids)
            dt_begin, dt_end = get_year_range(dt)

        if from_at is not None and till_at is not None:
            queryset = queryset.filter(created_at__range=(from_at, till_at))
        else:
            queryset = queryset.filter(created_at__range=(dt_begin, dt_end))

        items = []
        values = []
        pur_values = []
        units = []
        total = 0

        result = []

        # 当前时用量
        current_hour_cons = 0
        current_hour = now.strftime('%Y-%m-%d %H:05:00')
        for da in da_queryset.order_by('dimension__parent_id', 'dimension_id'):
            # value = sum([float(v) for v in queryset.filter(dimension_attribute_id=da.id).values_list('avg', flat=True)])
            value = queryset.filter(dimension_attribute_id=da.id).aggregate(total=Sum('avg'))['total']
            if value is not None:
                value = round(value, 2)
            else:
                value = 0

            # 加上当前时的用量
            if till_at is None or (till_at is not None and till_at.date() == now.date() and till_at.hour == now.hour):
                current_hour_cons = da.calc_current_hour_cons(project_id)
                value += current_hour_cons

            total += value
            result.append({'value': round(value, 2), 'item': da.name, 'unit': ec_type_to_unit(da.ec_type)})

        result = sorted(result, key=lambda obj: obj['value'], reverse=True)
        for item in result:
            items.append(item['item'])
            pur_values.append(item['value'])
            values.append(round((item['value'] / total) * 100, 2) if total else 0)
            units.append(item['unit'])

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                "items": items,
                "pur_values": pur_values,
                "values": values,
                "units": units
            }
        })

class DimensionEcAnalysisPrediction(viewsets.ModelViewSet):
    def retrieve(self, request, *args, **kwargs):
        project_id = request.user['project_id']
        ec_type = request.query_params.get('ec_type', 10)
        dim_id = request.query_params.get('dim_id', None)  # 指定维度（单位）

        # 时间
        now = datetime.datetime.now()
        today_begin, today_end = get_day_range()
        yesterday_begin, yesterday_end = get_day_range(today_begin - datetime.timedelta(days=1))
        current_month_begin, current_month_end = get_month_range()
        prev_month_begin, prev_month_end = get_month_range(current_month_begin - datetime.timedelta(days=1))
        current_year_begin, current_year_end = get_year_range()
        prev_year_begin, prev_year_end = get_year_range(current_year_begin - datetime.timedelta(days=1))
        # 去年这个月的天数
        prev_year_month_days = calendar.monthrange(now.year - 1, now.month)[1]

        if dim_id is not None:
            dimension = Dimension.objects.get(id=dim_id)
            das = list(DimensionAttribute.objects.filter(dimension_id=dim_id, ec_type=10, is_cum=True).filter(
                Q(name__contains=f'{dimension.name}用电量') |
                Q(name__contains=f'{dimension.name}槽用电量') |
                Q(name__endswith=f'{dimension.name}设备用电量')))
        else:
            # 默认根维度的维度属性是总用电量，排除热水用量
            root_dimensions = Dimension.objects.filter(parent__isnull=True, type_name='分项').exclude(name__contains='热水')
            das = list(DimensionAttribute.objects.filter(dimension__in=root_dimensions, ec_type=ec_type, name__contains='总用'))

        # 当前用量
        current_day = 0
        current_hour_cons = 0
        current_month = 0
        current_year = 0
        # 预测用量
        predict_day = 0
        predict_month = 0
        predict_year = 0
        # 环比计算，昨天，上个月，上一年
        prev_day = 0
        prev_month = 0
        prev_year = 0
        # 去年
        last_year_criteria = list(EcCriteria.objects.filter(year_month__startswith=f'{now.year - 1}', ec_type=10))
        today_last_year = 0
        month_last_year = 0
        year_last_year = 0
        # 基准
        criteria = list(EcCriteria.objects.filter(is_refer=True, ec_type=10))
        benchmark_day = 0
        benchmark_month = 0
        benchmark_year = 0

        for da in das:
            # 当日
            current_day_query = DimensionHourlyStat.objects.filter(dimension_attribute_id=da.id,
                                                                   created_at__range=(today_begin, today_end)
                                                                   ).aggregate(total_avg=Sum('avg'))['total_avg']
            if current_day_query is not None:
                current_day += current_day_query
            # - 当前时的用量, 上一小时统计未完成时不计算当前小时用量
            # - clac_cum 定时任务05分开始
            if now.minute > 5:
                current_hour_cons = da.calc_current_hour_cons(project_id)
                current_day += current_hour_cons

            # - 昨日
            prev_day_query = DimensionHourlyStat.objects.filter(dimension_attribute_id=da.id,
                                                                created_at__range=(yesterday_begin,
                                                                                   now - datetime.timedelta(days=1) + datetime.timedelta(hours=1))
                                                                ).aggregate(total_avg=Sum('avg'))['total_avg']
            if prev_day_query is not None:
                prev_day += prev_day_query

            # - 去年今日, 拿去年的今日的前、后各15天，共31天耗电量的平均值
            try:
                tdly_date = today_begin.replace(year=now.year - 1)
                today_last_year_query = DimensionDailyStat.objects.filter(dimension_attribute_id=da.id,
                                                                          created_at__range=(tdly_date - datetime.timedelta(days=15),
                                                                                             tdly_date + datetime.timedelta(days=15))
                                                                          ).aggregate(total_avg=Avg('avg'))['total_avg']
            except ValueError:
                today_last_year_query = None
            if today_last_year_query is not None:
                today_last_year += today_last_year_query

            # - 基准
            month_criteria = next(filter(lambda x: x.year_month.endswith(now.strftime("%m")), criteria), None)

            if month_criteria is not None:
                # 获取当前月份的天数
                days_in_month = calendar.monthrange(now.year, now.month)[1]
                benchmark_day = round(float(month_criteria.value) / days_in_month, 2)

            # 当月
            current_month_query = DimensionDailyStat.objects.filter(dimension_attribute_id=da.id,
                                                                    created_at__range=(current_month_begin, current_month_end)
                                                                    ).aggregate(total_avg=Sum('avg'))['total_avg']
            if current_month_query is not None:
                current_month += current_month_query
                # current_year += current_month_query
            # - 上一个月
            # -- 上一个月的今天
            prev_month_day = prev_month_begin + datetime.timedelta(days=now.day - 1)
            prev_month_query = DimensionDailyStat.objects.filter(dimension_attribute_id=da.id,
                                                                 created_at__range=(prev_month_begin, prev_month_day)
                                                                 ).aggregate(total_avg=Sum('avg'))['total_avg']
            if prev_month_query is not None:
                prev_month += prev_month_query

            prev_month_query = DimensionHourlyStat.objects.filter(dimension_attribute_id=da.id,
                                                                  created_at__range=(prev_month_day,
                                                                                     prev_month_day + datetime.timedelta(hours=now.hour + 1))
                                                                  ).aggregate(total_avg=Sum('avg'))['total_avg']
            if prev_month_query is not None:
                prev_month += prev_month_query

            # - 去年今月
            try:
                mthly_date = current_month_begin.replace(year=now.year - 1)
                month_last_year_query = DimensionDailyStat.objects.filter(dimension_attribute_id=da.id,
                                                                          created_at__range=(mthly_date, mthly_date + datetime.timedelta(
                                                                              days=prev_year_month_days))
                                                                          ).aggregate(total_avg=Sum('avg'))['total_avg']
            except ValueError:
                month_last_year_query = None
            if month_last_year_query is not None:
                month_last_year += month_last_year_query

            # - 月基准
            if month_criteria is not None:
                benchmark_month = float(month_criteria.value)

            # 当年
            current_year_query = DimensionMonthlyStat.objects.filter(dimension_attribute_id=da.id,
                                                                     created_at__range=(current_year_begin, current_month_begin)
                                                                     ).aggregate(total_avg=Sum('avg'))['total_avg']
            if current_year_query is not None:
                current_year += current_year_query
            # - 上一年
            # -- 上一年的当月
            prev_year_month = current_month_begin.replace(year=now.year - 1)
            prev_year_query = DimensionMonthlyStat.objects.filter(dimension_attribute_id=da.id,
                                                                  created_at__range=(prev_year_begin, prev_year_month)
                                                                  ).aggregate(total_avg=Sum('avg'))['total_avg']
            if prev_year_query is not None:
                prev_year += prev_year_query
            prev_year_query = DimensionDailyStat.objects.filter(dimension_attribute_id=da.id,
                                                                created_at__range=(prev_year_month,
                                                                                   prev_year_month + datetime.timedelta(days=now.day))
                                                                ).aggregate(total_avg=Sum('avg'))['total_avg']
            if prev_year_query is not None:
                prev_year += prev_year_query

            # - 年基准
            for mth in range(1, 13):
                mth_criteria = next(filter(lambda x: x.year_month.endswith(f'{mth}'), criteria), None)
                if mth_criteria is not None:
                    benchmark_year += float(mth_criteria.value)

        # 预测
        project = Project.objects.get(pk=request.user['project_id'])
        region = Region.objects.get(pk=project.admin_region_id)
        weather_result = WeatherApi.get_weather(region.weather_code)
        # - 今日最高温
        target_temp = float(weather_result.get('tem1'))
        # - 记录中一天最高温最接近今天的记录
        closest_record = ProjectWeather.objects.filter(type='di').annotate(diff=Case(
            When(max_temp__gt=target_temp, then=F('max_temp') - target_temp),
            default=target_temp - F('max_temp'),
        )).order_by('diff', '-created_at').first()
        created_at = closest_record.created_at.replace(hour=now.hour, minute=59, second=59, microsecond=0)

        # - 计算
        # -- 当日当前小时的预测
        today_predict = current_day - current_hour_cons
        # -- 当日全日的预测
        full_day_predict = today_predict
        # -- 当月预测
        month_predict = current_month
        # -- 全月预测
        full_month_predict = current_month
        current_month = current_month + current_day

        # year_predict = current_year
        current_year = current_year + current_month
        for da in das:
            predict_hour = DimensionHourlyStat.objects.filter(dimension_attribute_id=da.id, created_at=created_at).last()
            if predict_hour is not None:
                today_predict += float(predict_hour.avg)

            predict_hours_total = DimensionHourlyStat.objects.filter(dimension_attribute_id=da.id,
                                                                     created_at__range=(created_at, created_at.replace(hour=23))
                                                                     ).aggregate(total_avg=Sum('avg'))['total_avg']
            if predict_hours_total is not None:
                full_day_predict += predict_hours_total

            predict_day = DimensionDailyStat.objects.filter(dimension_attribute_id=da.id, created_at=created_at.replace(hour=23)).last()
            if predict_day is not None:
                month_predict += float(predict_day.avg)
            days_in_month = calendar.monthrange(created_at.year, created_at.month)[1]
            predict_days_total = DimensionDailyStat.objects.filter(dimension_attribute_id=da.id,
                                                                   created_at__range=(created_at.replace(hour=23),
                                                                                      created_at.replace(hour=23, day=days_in_month))
                                                                   ).aggregate(total_avg=Sum('avg'))['total_avg']
            if predict_days_total is not None:
                full_month_predict += predict_days_total

            # predict_month = DimensionMonthlyStat.objects.filter(dimension_attribute_id=da.id,
            #                                                     created_at=created_at.replace(year=now.year - 1, month=now.month, hour=23)
            #                                                     ).last()
            # if predict_month is not None:
            #     year_predict += float(predict_month.avg)
            # else:
            #     year_predict = 0

        # 当年预测
        year_predict = 0
        for mth in range(1, 13):
            if mth == now.month:
                mth_data = full_month_predict
            else:
                cri = next(filter(lambda x: x.year_month.endswith('%02d' % mth), last_year_criteria), None)
                mth_data = 0
                if cri is None:
                    for da in das:
                        dms = DimensionMonthlyStat.objects.filter(created_at__year=now.year - 1, created_at__month=mth,
                                                                  dimension_attribute_id=da.id).last()
                        if dms is not None and is_number(dms.avg):
                            mth_data += float(dms.avg)
                else:
                    mth_data = float(cri.value)
            year_predict += mth_data

        # qoq 环比
        day_qoq = round(((today_predict - prev_day) / prev_day) * 100, 2) if prev_day else 0
        month_qoq = round(((month_predict - prev_month) / prev_month) * 100, 2) if prev_month else 0
        year_qoq = round(((year_predict - prev_year) / prev_year) * 100, 2) if prev_year else 0

        # yoy 同比
        # 去年维度统计无数据就用去年基准的数据
        last_year_month_criteria = next(filter(lambda x: x.year_month.endswith('%02d' % now.month), last_year_criteria), None)
        if not today_last_year and last_year_month_criteria:
            today_last_year = float(last_year_month_criteria.value) / prev_year_month_days
        if not month_last_year and last_year_month_criteria:
            month_last_year = float(last_year_month_criteria.value)
        if len(last_year_criteria):
            year_last_year = sum([float(i.value) for i in last_year_criteria])

        day_yoy = round(((full_day_predict - today_last_year) / today_last_year) * 100, 2) if today_last_year else 0
        month_yoy = round(((full_month_predict - month_last_year) / month_last_year) * 100, 2) if month_last_year else 0
        year_yoy = round(((year_predict - year_last_year) / year_last_year) * 100, 2) if year_last_year else 0

        # yoy 基准同比
        benchmark_day_yoy = round(((full_day_predict - benchmark_day) / benchmark_day) * 100, 2) if benchmark_day else 0
        benchmark_month_yoy = round(((full_month_predict - benchmark_month) / benchmark_month) * 100, 2) if benchmark_month else 0
        benchmark_year_yoy = 0
        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'day': {
                    'current': round(current_day, 2),
                    'predict': round(today_predict, 2),
                    'previous': round(prev_day, 2),
                    'qoq': round(day_qoq, 2),
                    'last_year': round(today_last_year, 2),
                    'yoy': round(day_yoy, 2),
                    'benchmark': round(benchmark_day, 2),
                    'benchmark_yoy': round(benchmark_day_yoy, 2)
                },
                'month': {
                    'current': round(current_month, 2),
                    'predict': round(month_predict, 2),
                    'previous': round(prev_month, 2),
                    'qoq': round(month_qoq, 2),
                    'last_year': round(month_last_year, 2),
                    'yoy': round(month_yoy, 2),
                    'benchmark': round(benchmark_month, 2),
                    'benchmark_yoy': round(benchmark_month_yoy, 2)
                },
                'year': {
                    'current': round(current_year, 2),
                    'predict': round(year_predict, 2),
                    'previous': round(prev_year, 2),
                    'qoq': round(year_qoq, 2),
                    'last_year': round(year_last_year, 2),
                    'yoy': round(year_yoy, 2),
                    'benchmark': round(benchmark_year, 2),
                    'benchmark_yoy': round(benchmark_year_yoy, 2)
                }
            }
        })


class DimensionQoqStatsView(viewsets.ModelViewSet):
    @action(detail=True, methods=['get'])
    def get_authenticators(self):
        if 'intapi' in self.request.path:
            return [IntapiAuth(), ]
        return [LegacyAuth(), ]

    @classmethod
    def get_qoq_status(cls, project_id, das: list[DimensionAttribute], ec_type=10):
        now = datetime.datetime.now()
        today_begin, today_end = get_day_range()
        yesterday_begin, yesterday_end = get_day_range(today_begin - datetime.timedelta(days=1))
        current_month_begin, current_month_end = get_month_range()
        prev_month_begin, prev_month_end = get_month_range(current_month_begin - datetime.timedelta(days=1))
        current_year_begin, current_year_end = get_year_range()
        prev_year_begin, prev_year_end = get_year_range(current_year_begin - datetime.timedelta(days=1))

        da_ids = [da.id for da in das]

        today = 0
        yesterday = 0
        current_hour_cons = 0
        for da in das:
            # 今日及昨日
            today_records = DimensionHourlyStat.objects.filter(dimension_attribute_id=da.id,
                                                               created_at__range=(today_begin, today_end)).values_list('avg', flat=True)
            today += round(sum([float(v) for v in today_records]), 2)
            # 当前时的用量, 上一小时统计未完成时不计算当前小时用量

            if now.date() == now.date() and len(today_records) >= now.hour:
                current_hour_cons = da.calc_current_hour_cons(project_id)
                today += current_hour_cons

            yesterday_records = DimensionHourlyStat.objects.filter(dimension_attribute_id=da.id, created_at__range=(
                yesterday_begin, (now - datetime.timedelta(days=1)))).values_list('avg', flat=True)
            yesterday = +round(sum([float(v) for v in yesterday_records]), 2)

        # -日环比
        day_qoq = round(((today - yesterday) / yesterday) * 100, 2) if yesterday else 0

        # 当前月及上一月
        current_month = DimensionDailyStat.objects.filter(dimension_attribute_id__in=da_ids,
                                                          created_at__range=(current_month_begin, current_month_end)).values_list('avg', flat=True)
        current_month = sum([float(v) for v in current_month])
        current_month += today
        current_month = round(current_month, 2)
        last_month = DimensionDailyStat.objects.filter(dimension_attribute_id__in=da_ids,
                                                       created_at__range=(prev_month_begin,
                                                                          (prev_month_begin + datetime.timedelta(days=now.day - 1)))
                                                       ).values_list('avg', flat=True)
        # 上个月的当日当前小时用电量
        last_month_day_begin = prev_month_begin + datetime.timedelta(days=now.day - 1)
        last_month_day = DimensionHourlyStat.objects.filter(dimension_attribute_id__in=da_ids,
                                                            created_at__range=(last_month_day_begin,
                                                                               last_month_day_begin + datetime.timedelta(hours=now.hour))
                                                            ).values_list('avg', flat=True)
        last_month = round(sum([float(v) for v in last_month]), 2)
        last_month += round(sum([float(v) for v in last_month_day]), 2)
        # -月环比
        month_qoq = round(((current_month - last_month) / last_month) * 100, 2) if last_month else 0

        # 当前年及上一年
        current_year = DimensionMonthlyStat.objects.filter(dimension_attribute_id__in=da_ids,
                                                           created_at__range=(current_year_begin, current_year_end)).values_list('avg', flat=True)
        current_year = sum([float(v) for v in current_year])
        current_year += current_month
        current_year = round(current_year, 2)
        last_year = DimensionMonthlyStat.objects.filter(dimension_attribute_id__in=da_ids,
                                                        created_at__range=(prev_year_begin,
                                                                           (prev_year_begin +
                                                                            datetime.timedelta(days=(now - current_year_begin).days)))
                                                        ).values_list('avg', flat=True)
        last_year = round(sum([float(v) for v in last_year]), 2)

        # -年环比
        year_qoq = round(((current_year - last_year) / last_year) * 100, 2) if last_year else 0

        # 单位
        unit = DimensionEcTypeView.EC_TYPES.get(int(ec_type), {}).get('unit', '')

        return {
            "today": today,
            "yesterday": yesterday,
            "day_qoq": day_qoq,
            "current_month": current_month,
            "last_month": last_month,
            "month_qoq": month_qoq,
            "current_year": current_year,
            "last_year": last_year,
            "year_qoq": year_qoq,
            "unit": unit,
            "current_hour_cons": current_hour_cons
        }

    def list(self, request, *args, **kwargs):
        ec_type = request.query_params.get('ec_type', 10)
        project_id = request.user['project_id']

        # 默认根维度的维度属性是总用电量，排除热水用电
        root_dimensions = Dimension.objects.filter(parent__isnull=True).exclude(name__contains='热水')
        das = list(DimensionAttribute.objects.filter(dimension__in=root_dimensions, ec_type=ec_type))
        total_status = self.get_qoq_status(project_id, das, ec_type)
        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                "total_status": total_status,
                "unit": total_status['unit']
            }
        })


class DimensionEtQoqStatsView(viewsets.ModelViewSet):
    @action(detail=True, methods=['get'])
    def get_authenticators(self):
        if 'intapi' in self.request.path:
            return [IntapiAuth(), ]
        return [LegacyAuth(), ]

    def list(self, request, *args, **kwargs):
        project_id = request.user.get('project_id')
        ec_type = request.query_params.get('ec_type', 10)
        time_type = request.query_params.get('time_type', 'hr')

        das = DimensionAttribute.objects.filter(ec_type=ec_type)
        now = datetime.datetime.now()

        stats = []
        now = datetime.datetime.now()
        for da in das:
            if time_type == 'hr':
                today_begin, today_end = get_day_range()
                yesterday_begin, yesterday_end = get_day_range(today_begin - datetime.timedelta(days=1))
                try:
                    yoy_today_begin = today_begin.replace(year=today_begin.year - 1)
                    yoy_today_end = today_end.replace(year=today_end.year - 1)
                    yoy_queryset = DimensionHourlyStat.objects.filter(dimension_id=da.dimension_id,
                                                                      dimension_attribute_id=da.id,
                                                                      created_at__range=(yoy_today_begin, yoy_today_end))
                except ValueError as e:
                    logging.error(f'yoy datetime error: {e.__str__()}')
                    yoy_queryset = None
                queryset = DimensionHourlyStat.objects.filter(dimension_id=da.dimension_id,
                                                              dimension_attribute_id=da.id,
                                                              created_at__range=(today_begin, today_end))
                prev_queryset = DimensionHourlyStat.objects.filter(dimension_id=da.dimension_id,
                                                                   dimension_attribute_id=da.id,
                                                                   created_at__range=(yesterday_begin, yesterday_end.replace(hour=now.hour)))

            elif time_type == 'mo':
                current_month_begin, current_month_end = get_month_range()
                prev_month = current_month_begin - datetime.timedelta(days=1)
                prev_month_range = calendar.monthrange(prev_month.year, prev_month.month)[1]
                prev_month_begin = datetime.datetime(prev_month.year, prev_month.month, 1)
                prev_day = now.day if now.day <= prev_month_range else prev_month_range
                prev_month_end = datetime.datetime(prev_month.year, prev_month.month, prev_day, 23, 59, 59)
                try:
                    yoy_current_month_begin = current_month_begin.replace(year=current_month_begin.year - 1)
                    yoy_current_month_end = current_month_end.replace(year=current_month_end.year - 1)
                    yoy_queryset = DimensionDailyStat.objects.filter(dimension_id=da.dimension_id,
                                                                     dimension_attribute_id=da.id,
                                                                     created_at__range=(yoy_current_month_begin, yoy_current_month_end))
                except ValueError as e:
                    logging.error(f'yoy datetime error: {e.__str__()}')
                    yoy_queryset = None

                queryset = DimensionDailyStat.objects.filter(dimension_id=da.dimension_id,
                                                             dimension_attribute_id=da.id,
                                                             created_at__range=(current_month_begin, current_month_end))
                prev_queryset = DimensionDailyStat.objects.filter(dimension_id=da.dimension_id,
                                                                  dimension_attribute_id=da.id,
                                                                  created_at__range=(prev_month_begin, prev_month_end))

            else:
                current_year_begin, current_year_end = get_year_range()
                prev_year_begin, prev_year_end = get_year_range(current_year_begin - datetime.timedelta(days=1))

                queryset = DimensionMonthlyStat.objects.filter(dimension_id=da.dimension_id,
                                                               dimension_attribute_id=da.id,
                                                               created_at__range=(current_year_begin, current_year_end))
                prev_queryset = DimensionMonthlyStat.objects.filter(dimension_id=da.dimension_id,
                                                                    dimension_attribute_id=da.id,
                                                                    created_at__range=(prev_year_begin, prev_year_end.replace(day=now.day)))
                yoy_queryset = None

            values = queryset.values_list('avg', flat=True)
            value = round(sum([float(v) for v in values]), 2)

            # 加上当前时/日/月的用量
            if (time_type == 'hr' and len(values) >= now.hour) or time_type != 'hr':
                current_hour_cons = da.calc_current_hour_cons()
                value += current_hour_cons

                if time_type == 'mo':
                    today_begin, today_end = get_day_range()
                    current_day_cons = DimensionHourlyStat.objects.filter(dimension_id=da.dimension_id,
                                                                          dimension_attribute_id=da.id,
                                                                          created_at__range=(today_begin, today_end)
                                                                          ).aggregate(total=Sum('avg'))['total']
                    if current_day_cons is not None:
                        value += current_day_cons

                if time_type == 'yr':
                    current_month_begin, current_month_end = get_month_range()
                    current_month_cons = DimensionDailyStat.objects.filter(dimension_id=da.dimension_id,
                                                                           dimension_attribute_id=da.id,
                                                                           created_at__range=(current_month_begin, current_month_end)
                                                                           ).aggregate(total=Sum('avg'))['total']
                    if current_month_cons is not None:
                        value += current_month_cons

            prev_values = prev_queryset.values_list('avg', flat=True)
            prev_value = round(sum([float(v) for v in prev_values]), 2)

            qoq_value = round(((value - prev_value) / prev_value) * 100, 2) if prev_value else '--'
            yoy_value = '--'

            if yoy_queryset is not None:
                last_year_values = yoy_queryset.values_list('avg', flat=True)
                last_year_value = round(sum([float(v) for v in last_year_values]), 2)
                yoy_value = round(((value - last_year_value) / last_year_value) * 100, 2) if last_year_value else '--'

            stats.append({
                "name": da.name,
                "value": value,
                "qoq": qoq_value,
                "yoy": yoy_value
            })

        stats = sorted(stats, key=lambda d: d['value'])

        unit = ec_type_to_unit(int(ec_type))

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'stats': stats,
                'unit': unit
            }
        })


class DimensionStatView(viewsets.ModelViewSet):
    @classmethod
    def query_stats(cls, item_ids, stat_type, from_at, till_at, start_hour=None, end_hour=None):
        if item_ids is None:
            raise exceptions.ValidationError(detail={'detail': 'item_ids is required!'})
        else:
            item_ids = item_ids.split(',')

        if stat_type is None:
            raise exceptions.ValidationError(detail={'detail': 'time_type is required!'})

        if from_at is None or till_at is None:
            if stat_type == 'di':
                from_at = datetime.datetime.now().strftime('%Y-%m-%d 00:00:00')
                till_at = datetime.datetime.now().strftime('%Y-%m-%d 23:59:59')
            else:
                from_at = datetime.datetime.now().strftime('%Y-%m-%d 00:00:00')
                till_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 格式化前端传来的时间字符串 “20220310000000” =》 "2022-03-10 00:00:00"
        if from_at:
            from_at = datetime.datetime.strptime(from_at, '%Y%m%d%H%M%S')
        if till_at:
            till_at = datetime.datetime.strptime(till_at, '%Y%m%d%H%M%S')
            latest_time = datetime.time(23, 59, 59, 999999)
            if stat_type == 'yr':
                monthrange = calendar.monthrange(till_at.year, till_at.month)[1]
                till_at = datetime.datetime.combine(till_at.date().replace(day=monthrange), latest_time)
            elif stat_type == 'mo':
                till_at = datetime.datetime.combine(till_at.date(), latest_time)

        result = []
        if start_hour is not None and end_hour is not None:
            for item_id in item_ids:
                values = []
                da = DimensionAttribute.objects.get(pk=item_id)
                das = DimensionHourlyStat.objects.filter(dimension_attribute_id=da.id,
                                                         created_at__gte=from_at,
                                                         created_at__lte=till_at,
                                                         created_at__hour__gte=start_hour,
                                                         created_at__hour__lte=end_hour)
                if stat_type == 'yr':
                    times = list(DimensionMonthlyStat.objects.filter(dimension_attribute_id=da.id, created_at__range=[from_at, till_at]
                                                                     ).order_by('created_at').values_list('created_at', flat=True))
                    for idx, created_at, in enumerate(times):
                        begin = created_at.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
                        if idx == 0:
                            begin = from_at
                        end = created_at
                        item_value = das.filter(created_at__range=[begin, end]).aggregate(total=Sum('avg'))['total']
                        values.append({
                            'time': datetime.datetime.strftime(created_at, '%Y-%m-%d %H:%M:%S'),
                            'value': round(float(item_value), 2) if item_value else '--',
                            'min': '--',
                            'max': '--',
                        })

                elif stat_type == 'mo':
                    times = list(DimensionDailyStat.objects.filter(dimension_attribute_id=da.id, created_at__range=[from_at, till_at]
                                                                   ).order_by('created_at').values_list('created_at', flat=True))
                    for idx, created_at, in enumerate(times):
                        begin = created_at.replace(hour=0, minute=0, second=0, microsecond=0)
                        if idx == 0:
                            begin = from_at
                        end = created_at
                        item_value = das.filter(created_at__range=[begin, end]).aggregate(total=Sum('avg'))['total']
                        print(created_at, begin, end)
                        values.append({
                            'time': datetime.datetime.strftime(created_at, '%Y-%m-%d %H:%M:%S'),
                            'value': round(float(item_value), 2) if item_value else '--',
                            'min': '--',
                            'max': '--',
                        })
                else:
                    for item in das:
                        values.append({
                            'time': datetime.datetime.strftime(item.created_at, '%Y-%m-%d %H:%M:%S'),
                            'value': round(float(item.avg), 2) if item.avg != '--' else '--',
                            'min': round(float(item.min), 2) if item.min != '--' else '--',
                            'max': round(float(item.max), 2) if item.max != '--' else '--',
                        })

                result.append({
                    'name': da.name,
                    'label': da.name,
                    'unit': ec_type_to_unit(da.ec_type),
                    'values': values,
                    'identifier': da.identifier
                })

        else:
            if stat_type == 'yr':
                data_from = DimensionMonthlyStat.objects
            elif stat_type == 'mo':
                data_from = DimensionDailyStat.objects
            else:
                data_from = DimensionHourlyStat.objects

            for item_id in item_ids:
                da = DimensionAttribute.objects.get(pk=item_id)
                values = data_from.get_report_stat(item_id, from_at, till_at)
                result.append({
                    'name': da.name,
                    'label': da.name,
                    'unit': ec_type_to_unit(da.ec_type),
                    'values': values,
                    'identifier': da.identifier
                })

        return result

    def create(self, request, *args, **kwargs):
        item_ids = request.data.get('item_ids', None)
        stat_type = request.data.get('time_type', None)
        from_at = request.data.get('from', None)
        till_at = request.data.get('till', None)
        weathers = request.data.get('weathers', None)
        # 仅导出指定时间段的用电数据，例如上班期间的用电
        start_hour = request.data.get('start_hour', None)
        end_hour = request.data.get('end_hour', None)

        result = self.query_stats(item_ids, stat_type, from_at, till_at, start_hour, end_hour)
        if weathers is not None:
            weather_result = ProjectWeather.objects.query_stats(weathers, stat_type, from_at, till_at)
            result += weather_result

        path = ''
        filename = ''
        if len(result):
            path, filename = ExcelUtils.generate_work_book(result, filename)

        return Response({
            "status": status.HTTP_200_OK,
            'data': {
                'file_url': path,
                'filename': filename
            }
        })


class DimensionEcReportView(viewsets.ModelViewSet):

    @classmethod
    def get_child_circle_stats(cls, dim, stats):
        children = []
        for c_dim in Dimension.objects.filter(parent_id=dim.id):
            child_stats = cls.get_circle_stat(c_dim, stats)
            if Dimension.objects.filter(parent_id=c_dim.id).exists():
                child_stats['children'] = cls.get_child_circle_stats(c_dim, stats)
            children.append(child_stats)

        return children

    @classmethod
    def sum_data_list(cls, *data_list):
        length = len(data_list[0])
        result = []

        for i in range(length):
            # 获取当前索引处的所有元素
            current_elements = [lst[i] for lst in data_list]

            # 检查是否所有元素都是字符串 '--'
            if all(element == '--' for element in current_elements):
                result.append('--')
            else:
                # 如果不是 '--'，进行求和，注意跳过 '--'
                result.append(sum(element for element in current_elements if element != '--'))

        return result

    @classmethod
    def get_circle_stat(cls, dim, stats):
        da = DimensionAttribute.objects.filter(dimension_id=dim.id).first()

        if da is not None and da.ta_ids is not None:
            ta_ids = da.ta_ids.split(',')

            dim_stats = list(filter(lambda x: str(x['id']) in ta_ids, stats))
            data_list = [stat['data'] for stat in dim_stats]
            pr_data_list = [stat['pr_data'] for stat in dim_stats]
            data = cls.sum_data_list(*data_list)
            pr_data = cls.sum_data_list(*pr_data_list)
            children = []

            if not Dimension.objects.filter(parent_id=dim.id).exists():
                children = dim_stats

            return {
                'id': dim.id,
                'name': dim.name,
                'data': data,
                'pr_data': pr_data,
                'children': children
            }

        else:
            return None

    @classmethod
    def resemble_data(cls, da_list, timer_type, dt):
        if timer_type == 'hr':
            begin, end = get_day_range(dt)
            queryset = DimensionHourlyStat.objects.filter(dimension_attribute_id__in=da_list, created_at__range=(begin, end))
            count = 24
            attr = 'hour'
        elif timer_type == 'di':
            begin, end = get_month_range(dt)
            queryset = DimensionDailyStat.objects.filter(dimension_attribute_id__in=da_list, created_at__range=(begin, end))
            # dt 的当月共有多少天
            count = end.day
            attr = 'day'
        else:
            begin, end = get_year_range()
            queryset = DimensionMonthlyStat.objects.filter(dimension_attribute_id__in=da_list, created_at__range=(begin, end))
            count = 12
            attr = 'month'

        pr_queryset = ParamRecord.objects.filter(Q(created_at__range=(begin, end)) & Q(created_at__minute=5) & Q(created_at__second=0))
        if not pr_queryset.exists():
            pr_queryset = ParamRecordHistory.objects.filter(Q(created_at__range=(begin, end)) & Q(created_at__minute=5) & Q(created_at__second=0))

        ta_did_cache = {}
        stats = []

        for da in da_list:
            values = ['--'] * count
            pr_values = ['--'] * count

            query = Q()
            for ta in TerminalAttribute.objects.filter(id__in=da.ta_ids.split(',')):
                did = ta_did_cache.get(ta.id, None)
                if did is None:
                    did = ta.terminal.device_id
                    ta_did_cache[ta.id] = did
                query |= Q(identifier=ta.identifier, device_id=did)

            pr_records = list(pr_queryset.filter(query).values('created_at', 'value'))

            for item in queryset.filter(dimension_attribute_id=da.id).order_by('created_at'):
                idx = getattr(item.created_at, attr)
                if timer_type != 'hr':
                    idx = idx - 1
                # DimensionHourlyStat 每日记录从零时开始，故 idx 可直取 created_at.hour
                values[idx] = item.avg
                pr_created_at = item.created_at.replace(minute=5, second=0, microsecond=0)
                record_value = sum(map(lambda x: float(x['value']),
                                       filter(lambda x: x['created_at'] == pr_created_at and is_number(x['value']), pr_records)))
                pr_values[idx] = str(record_value)

            da_stat = {
                "ec_type": da.ec_type,
                "id": da.id,
                "name": da.name,
                "data": [round(float(v), 2) if v != '--' else v for v in values],
                "pr_data": [round(float(v), 2) if v != '--' else v for v in pr_values]
            }

            stats.append(da_stat)
        return stats

    def list(self, request, *args, **kwargs):
        project = Project.objects.get(pk=request.user['project_id'])

        time_type = request.query_params.get('type', 'hr')
        dt = request.query_params.get('dt', datetime.datetime.now())
        meter_type = request.query_params.get('meter_type', None)
        search = request.query_params.get('search', None)

        if time_type == 'hr':
            begin, end = get_day_range(dt)
            count = 24

            begin_created_at = begin.replace(minute=5)
            created_at_list = [begin_created_at.replace(hour=h) for h in range(count)]
            created_at_list.append(created_at_list[-1] + datetime.timedelta(hours=1))

        elif time_type == 'di':
            begin, end = get_month_range(dt)
            # dt 的当月共有多少天
            count = end.day

            begin_created_at = begin.replace(minute=5)
            created_at_list = [begin_created_at.replace(day=d + 1) for d in range(count)]
            created_at_list.append(created_at_list[-1] + datetime.timedelta(days=1))

        else:
            begin, end = get_year_range(dt)
            count = 12

            begin_created_at = begin.replace(minute=5)
            created_at_list = [begin_created_at.replace(month=m + 1) for m in range(count)]
            last_month = created_at_list[-1]
            created_at_list.append(last_month.replace(year=last_month.year + 1, month=1, day=1))

        now = datetime.datetime.now().date()
        yesterday = now - datetime.timedelta(days=1)

        if time_type == 'hr' and (begin.date() == now or begin.date() == yesterday):
            queryset_from = ParamRecord.objects
        else:
            queryset_from = ParamRecordHistory.objects

        pr_queryset = queryset_from.filter(created_at__in=created_at_list)

        # ParamRecord 只保留一天，也就是说昨天的数据也在 ParamRecord 表里，当 time_type == 'di' 且 dt 是当月时，昨天的表读数要从 ParamRecord 中查询
        yesterday_pr_queryset = None
        if time_type == 'di' and now.year == begin.date().year and now.month == begin.date().month:
            yesterday_pr_queryset = ParamRecord.objects.filter(created_at__in=created_at_list)

        # 缓存终端属性对应的设备id
        ta_did_cache = {}
        stats = []

        # 排除峰平谷用电
        ppv_query = (Q(identifier__endswith='_P') | Q(identifier__endswith='_F') | Q(identifier__endswith='_V') | Q(identifier__endswith='_S') |
                     Q(identifier__startswith="Meter_Power_Cons_V") | Q(identifier__startswith="Meter_Power_Cons_F") |
                     Q(identifier__startswith="Meter_Power_Cons_P") | Q(identifier__startswith="Meter_Power_Cons_S") |
                     Q(identifier__startswith="Meter_Power_Cons_C") | Q(identifier__startswith="Meter_Power_Cons_B") |
                     Q(identifier__startswith="Meter_Power_Cons_A"))

        meter_cons = TerminalAttribute.objects.filter(use_for=60).exclude(ppv_query).order_by('terminal__nick_name')
        if meter_type:
            meter_cons = meter_cons.filter(terminal__terminal_label=meter_type)

        if search is not None:
            key_words = search.split(' ')
            query = Q()
            for key_word in key_words:
                query |= Q(terminal__nick_name__icontains=key_word)
            meter_cons = meter_cons.filter(Q(query))

        # 项目是否启用峰平谷
        project_settings = json.loads(project.settings)
        enable_ppv = project_settings.get('en_ppv', False)

        # 回路维度判断
        circle_dimension_ids = list(Dimension.objects.filter(type_name='回路').values_list('id', flat=True))
        circle_ta_ids = ','.join(DimensionAttribute.objects.filter(dimension_id__in=circle_dimension_ids).values_list('ta_ids', flat=True))
        if circle_ta_ids:
            circle_ta_ids = circle_ta_ids.split(',')
        # 当回路维度的终端绑定有电表的终端属性时，只返回回路的终端属性
        handle_circle = meter_cons.filter(id__in=circle_ta_ids).exists()
        if handle_circle:
            meter_cons = meter_cons.filter(id__in=circle_ta_ids)

        total = meter_cons.count()
        meter_cons = self.paginate_queryset(meter_cons)

        for cons_ta in meter_cons:

            terminal = cons_ta.terminal

            values: List[str | float] = ['--'] * count
            pr_values: List[str | float] = ['--'] * (count + 1)

            ppv_data = [{
                'ppv_type': 10,
                'pr_data': ['--'] * (count + 1),
                'data': ['--'] * count
            }, {
                'ppv_type': 20,
                'pr_data': ['--'] * (count + 1),
                'data': ['--'] * count
            }, {
                'ppv_type': 30,
                'pr_data': ['--'] * (count + 1),
                'data': ['--'] * count
            }, {
                'ppv_type': 40,
                'pr_data': ['--'] * (count + 1),
                'data': ['--'] * count
            }]

            idx = terminal.idx

            did = ta_did_cache.get(cons_ta.id, None)
            if did is None:
                did = terminal.device_id
                ta_did_cache[cons_ta.id] = did
            pr_records = list(pr_queryset.filter(identifier=cons_ta.identifier, device_id=did).values('created_at', 'value'))
            # 峰
            peak_query = Q(identifier=f"{cons_ta.identifier}_P")
            if idx:
                peak_query |= Q(identifier=f"Meter_Power_Cons_P_{idx}")
            pr_records_peak = list(pr_queryset.filter(peak_query, device_id=did).values('created_at', 'value'))
            # 平
            flat_query = Q(identifier=f"{cons_ta.identifier}_F")
            if idx:
                flat_query |= Q(identifier=f"Meter_Power_Cons_F_{idx}")
            pr_records_flat = list(pr_queryset.filter(flat_query, device_id=did).values('created_at', 'value'))
            # 谷
            valley_query = Q(identifier=f"{cons_ta.identifier}_V")
            if idx:
                valley_query |= Q(identifier=f"Meter_Power_Cons_V_{idx}")
            pr_records_valley = list(pr_queryset.filter(valley_query, device_id=did).values('created_at', 'value'))
            # 尖
            sharp_query = Q(identifier=f"{cons_ta.identifier}_S")
            if idx:
                sharp_query |= Q(identifier=f"Meter_Power_Cons_S_{idx}")
            pr_records_sharp = list(pr_queryset.filter(sharp_query, device_id=did).values('created_at', 'value'))

            yesterday_pr_records = None
            if yesterday_pr_queryset is not None:
                yesterday_pr_records = list(yesterday_pr_queryset.filter(identifier=cons_ta.identifier, device_id=did).values('created_at', 'value'))
                # 峰
                yesterday_pr_records_peak = list(yesterday_pr_queryset.filter(peak_query, device_id=did).values('created_at', 'value'))
                # 平
                yesterday_pr_records_flat = list(yesterday_pr_queryset.filter(flat_query, device_id=did).values('created_at', 'value'))
                # 谷
                yesterday_pr_records_valley = list(yesterday_pr_queryset.filter(valley_query, device_id=did).values('created_at', 'value'))
                # 尖
                yesterday_pr_records_sharp = list(yesterday_pr_queryset.filter(sharp_query, device_id=did).values('created_at', 'value'))

            # 表读数
            for idx, created_at in enumerate(created_at_list):

                record_values = list(filter(lambda x: x['created_at'] == created_at and is_number(x['value']), pr_records))
                if len(record_values):
                    record_value = sum(map(lambda x: float(x['value']), record_values))
                    pr_values[idx] = record_value
                # 峰
                record_value_peak = sum(map(lambda x: float(x['value']),
                                            filter(lambda x: x['created_at'] == created_at and is_number(x['value']), pr_records_peak)))
                # 平
                record_value_flat = sum(map(lambda x: float(x['value']),
                                            filter(lambda x: x['created_at'] == created_at and is_number(x['value']), pr_records_flat)))
                # 谷
                record_value_valley = sum(map(lambda x: float(x['value']),
                                              filter(lambda x: x['created_at'] == created_at and is_number(x['value']), pr_records_valley)))
                # 尖
                record_value_sharp = sum(map(lambda x: float(x['value']),
                                             filter(lambda x: x['created_at'] == created_at and is_number(x['value']), pr_records_sharp)))


                # 峰
                if record_value_peak:
                    ppv_data[0]['pr_data'][idx] = round(float(record_value_peak), 2)

                # 平
                if record_value_flat:
                    ppv_data[1]['pr_data'][idx] = round(float(record_value_flat), 2)

                # 谷
                if record_value_valley:
                    ppv_data[2]['pr_data'][idx] = round(float(record_value_valley), 2)

                # 尖
                if record_value_sharp:
                    ppv_data[3]['pr_data'][idx] = round(float(record_value_sharp), 2)

                # 处理昨日表读数
                if (idx == now.day - 2 or idx == now.day - 1) and yesterday_pr_records is not None:
                    record_value = sum(map(lambda x: float(x['value']),
                                           filter(lambda x: x['created_at'] == created_at and is_number(x['value']), yesterday_pr_records)))
                    pr_values[idx] = record_value

                    if yesterday_pr_records_peak is not None:
                        record_value_peak = sum(map(lambda x: float(x['value']),
                                                    filter(lambda x: x['created_at'] == created_at and is_number(x['value']),
                                                           yesterday_pr_records_peak)))
                        ppv_data[0]['pr_data'][idx] = round(float(record_value_peak), 2)

                    if yesterday_pr_records_flat is not None:
                        record_value_flat = sum(map(lambda x: float(x['value']),
                                                    filter(lambda x: x['created_at'] == created_at and is_number(x['value']),
                                                           yesterday_pr_records_flat)))
                        ppv_data[1]['pr_data'][idx] = round(float(record_value_flat), 2)

                    if yesterday_pr_records_valley is not None:
                        record_value_valley = sum(map(lambda x: float(x['value']),
                                                      filter(lambda x: x['created_at'] == created_at and is_number(x['value']),
                                                             yesterday_pr_records_valley)))
                        ppv_data[2]['pr_data'][idx] = round(float(record_value_valley), 2)

                    if yesterday_pr_records_sharp is not None:
                        record_value_sharp = sum(map(lambda x: float(x['value']),
                                                     filter(lambda x: x['created_at'] == created_at and is_number(x['value']),
                                                            yesterday_pr_records_sharp)))
                        ppv_data[3]['pr_data'][idx] = round(float(record_value_sharp), 2)

                if idx != 0:
                    try:
                        values[idx - 1] = pr_values[idx] - pr_values[idx - 1]
                    except Exception as e:
                        pass

                    try:
                        ppv_data[0]['data'][idx - 1] = round(ppv_data[0]['pr_data'][idx] - ppv_data[0]['pr_data'][idx - 1], 2)
                    except Exception as e:
                        pass

                    try:
                        ppv_data[1]['data'][idx - 1] = round(ppv_data[1]['pr_data'][idx] - ppv_data[1]['pr_data'][idx - 1], 2)
                    except Exception as e:
                        pass

                    try:
                        ppv_data[2]['data'][idx - 1] = round(ppv_data[2]['pr_data'][idx] - ppv_data[2]['pr_data'][idx - 1], 2)
                    except Exception as e:
                        pass

                    try:
                        ppv_data[3]['data'][idx - 1] = round(ppv_data[3]['pr_data'][idx] - ppv_data[3]['pr_data'][idx - 1], 2)
                    except Exception as e:
                        pass

            pr_values.pop()
            ppv_data[0]['pr_data'].pop()
            ppv_data[1]['pr_data'].pop()
            ppv_data[2]['pr_data'].pop()
            ppv_data[3]['pr_data'].pop()

            da_stat = {
                "id": cons_ta.id,
                "meter_type": terminal.terminal_label,
                "name": f'{terminal.nick_name}',
                "identifier": cons_ta.identifier,
                "data": [round(float(v), 2) if v != '--' else v for v in values],
                "pr_data": [round(float(v), 2) if v != '--' else v for v in pr_values],
                'ppv_data': ppv_data
            }

            stats.append(da_stat)

        # 按照回路维度的层级显示
        if handle_circle:
            circle_stats = []
            circle_dimensions = list(Dimension.objects.filter(type_name='回路').order_by('id'))
            circle_dimension_ids = [d.id for d in circle_dimensions]

            # 从根回路开始
            for dim in list(filter(lambda x: x.parent_id not in circle_dimension_ids, circle_dimensions)):
                dim_stat = self.get_circle_stat(dim, stats)
                if dim_stat:
                    if Dimension.objects.filter(parent_id=dim.id).exists():
                        dim_stat['children'] = self.get_child_circle_stats(dim, stats)
                    elif dim.ta_ids:
                        ta_ids = dim.ta_ids.split(',')
                        dim_stat['children'] = list(filter(lambda x: str(x['id']) in ta_ids, stats))
                    else:
                        dim_stat['children'] = []
                    circle_stats.append(dim_stat)

            stats = circle_stats

        # 构建 time_range
        time_range = []
        temp_str = f'%02d{"时" if time_type == "hr" else "日" if time_type == "di" else "月"}'
        for h in range(count):
            time_range.append(temp_str % (h if time_type == 'hr' else h + 1))

        meter_types = Terminal.objects.filter(terminal_type=20, terminal_label__isnull=False).values_list('terminal_label', flat=True)

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                "stats": stats,
                "time_range": time_range,
                "meter_types": set(meter_types)
            },
            'total': total
        })

    def create(self, request, *args, **kwargs):
        result = self.list(request, *args, **kwargs)
        time_range = result.data['data']['time_range']
        stats = result.data['data']['stats']

        sum_cost = []

        for idx in range(len(time_range)):
            sum_cost.append(sum([float(stat['data'][idx]) for stat in stats if is_number(stat['data'][idx])]))

        stats.append({
            'name': '总能耗',
            'data': sum_cost
        })

        path, filename = ExcelUtils.generate_ec_report(stats, time_range, "汇总导出")

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'file_url': path,
                'filename': filename
            }
        })


class EcReportView(viewsets.ModelViewSet):

    @classmethod
    def sum_data_list(cls, *data_list):
        length = len(data_list[0])
        result = []

        for i in range(length):
            # 获取当前索引处的所有元素
            current_elements = [lst[i] for lst in data_list]

            # 检查是否所有元素都是字符串 '--'
            if all(element == '--' for element in current_elements):
                result.append('--')
            else:
                # 如果不是 '--'，进行求和，注意跳过 '--'
                result.append(round(sum(element for element in current_elements if element != '--'), 2))

        return result

    @classmethod
    def get_child_circle_stats(cls, dim, stats):
        children = []
        for c_dim in Dimension.objects.filter(parent_id=dim.id):
            child_stats = cls.get_circle_stat(c_dim, stats)
            if Dimension.objects.filter(parent_id=c_dim.id).exists():
                child_stats['children'] = cls.get_child_circle_stats(c_dim, stats)
            children.append(child_stats)

        return children

    @classmethod
    def get_circle_stat(cls, dim, stats):
        da = DimensionAttribute.objects.filter(dimension_id=dim.id).first()

        if da is not None and da.ta_ids is not None:
            ta_ids = da.ta_ids.split(',')

            dim_stats = list(filter(lambda x: str(x['id']) in ta_ids, stats))
            data_list = [stat['data'] for stat in dim_stats]
            pr_data_list = [stat['pr_data'] for stat in dim_stats]
            data = cls.sum_data_list(*data_list)
            pr_data = cls.sum_data_list(*pr_data_list)
            children = []

            if not Dimension.objects.filter(parent_id=dim.id).exists():
                children = dim_stats

            return {
                'id': dim.id,
                'name': dim.name,
                'data': data,
                'pr_data': pr_data,
                'children': children
            }

        else:
            return None

    def list(self, request, *args, **kwargs):
        now = datetime.datetime.now()
        day_begin = now.replace(hour=0, minute=0, second=0, microsecond=0)
        yesterday_begin = day_begin - datetime.timedelta(days=1)

        time_type = request.query_params.get('type', 'hr')
        # dt = request.query_params.get('dt', datetime.datetime.now())
        meter_type = request.query_params.get('meter_type', None)
        search = request.query_params.get('search', None)
        from_at = request.query_params.get('from', None)
        till_at = request.query_params.get('till', None)
        dt = request.query_params.get('dt', None)
        if dt is not None:
            dt = datetime.datetime.strptime(dt, '%Y%m%d%H%M%S')

        stats = []

        if from_at is None:
            if dt is not None:
                from_at = dt
            else:
                from_at = day_begin
        else:
            from_at = datetime.datetime.strptime(from_at, '%Y%m%d%H%M%S')

        if till_at is None:
            if dt is not None:
                till_at = dt.replace(hour=23, minute=59, second=59)
            else:
                till_at = day_begin + datetime.timedelta(days=1)
        else:
            till_at = datetime.datetime.strptime(till_at, '%Y%m%d%H%M%S')

        # 构建时间
        created_at_list = []
        if time_type == 'hr':
            current_time = from_at.replace(minute=5)
            while current_time <= till_at:
                created_at_list.append(current_time)
                current_time = current_time + datetime.timedelta(hours=1)
            created_at_list.append(created_at_list[-1] + datetime.timedelta(hours=1))

        elif time_type == 'di':
            current_time = from_at.replace(hour=0, minute=5)
            while current_time <= till_at:
                created_at_list.append(current_time)
                current_time = current_time + datetime.timedelta(days=1)
            created_at_list.append(created_at_list[-1] + datetime.timedelta(days=1))

        else:
            current_time = from_at.replace(day=1, hour=0, minute=5)
            while current_time <= till_at:
                created_at_list.append(current_time)
                monthrange = calendar.monthrange(current_time.year, current_time.month)[1]
                current_time = current_time + datetime.timedelta(days=monthrange)
            last_created_at = created_at_list[-1]
            if last_created_at.month == 12:
                created_at_list.append(last_created_at + datetime.timedelta(days=31))
            else:
                created_at_list.append(last_created_at.replace(month=last_created_at.month + 1))

        # 个数必须大于 1 才有意义
        count = len(created_at_list)
        if count <= 1:
            return Response({
                'status': status.HTTP_200_OK,
                'data': {
                    "stats": stats,
                    "time_range": [],
                    "meter_types": []
                },
                'total': 0
            })

        # 排除峰平谷用电
        ppv_query = (Q(identifier__endswith='_P') | Q(identifier__endswith='_F') | Q(identifier__endswith='_V') | Q(identifier__endswith='_S') |
                     Q(identifier__startswith="Meter_Power_Cons_V") | Q(identifier__startswith="Meter_Power_Cons_F") |
                     Q(identifier__startswith="Meter_Power_Cons_P") | Q(identifier__startswith="Meter_Power_Cons_S") |
                     Q(identifier__startswith="Meter_Power_Cons_C") | Q(identifier__startswith="Meter_Power_Cons_B") |
                     Q(identifier__startswith="Meter_Power_Cons_A"))

        # 能耗值的终端属性
        meter_cons = TerminalAttribute.objects.filter(use_for=60).exclude(ppv_query).select_related('terminal')
        if meter_type:
            meter_cons = meter_cons.filter(terminal__terminal_label=meter_type)

        if search is not None:
            key_words = search.split(' ')
            query = Q()
            for key_word in key_words:
                query |= Q(terminal__nick_name__icontains=key_word)
            meter_cons = meter_cons.filter(Q(query))

        # 回路终端
        circle_dimension_ids = list(Dimension.objects.filter(type_name='回路').values_list('id', flat=True))
        circle_ta_ids = ','.join(DimensionAttribute.objects.filter(dimension_id__in=circle_dimension_ids).values_list('ta_ids', flat=True))
        if circle_ta_ids:
            circle_ta_ids = circle_ta_ids.split(',')
        # 当回路维度的终端绑定有电表的终端属性时，只返回回路的终端属性
        handle_circle = meter_cons.filter(id__in=circle_ta_ids).exists()
        if handle_circle:
            meter_cons = meter_cons.filter(id__in=circle_ta_ids)

        # 项目是否启用峰平谷
        project = Project.objects.get(pk=request.user['project_id'])
        project_settings = json.loads(project.settings)
        enable_ppv = project_settings.get('en_ppv', False)

        meter_cons = meter_cons.order_by('terminal__nick_name')
        total = meter_cons.count()
        meter_cons = self.paginate_queryset(meter_cons)

        device_ids = list({m.terminal.device_id for m in meter_cons})
        identifiers = list({m.identifier for m in meter_cons})

        if enable_ppv:
            notes = ['P', 'F', 'S', 'V']
            identifiers.extend([f'Meter_Power_Cons_{note}' for note in notes])
            terminals = [m.terminal for m in meter_cons]
            max_idx = 0
            terminal_idx_list = [terminal.idx for terminal in terminals if terminal.idx]
            if len(terminal_idx_list):
                max_idx = max(terminal_idx_list)
            for idx in range(1, max_idx + 1):
                identifiers.extend([f'Meter_Power_Cons_{idx}_{note}' for note in notes])
                identifiers.extend([f'Meter_Power_Cons_{note}_{idx}' for note in notes])

        queryset = ParamRecord.objects.filter(device_id__in=device_ids,
                                              identifier__in=identifiers,
                                              created_at__in=created_at_list)
        pr_record_map = {
            (device_id, identifier, created_at): value
            for device_id, identifier, created_at, value in queryset.values_list('device_id', 'identifier', 'created_at', 'value')
        }

        if from_at < yesterday_begin:
            history_queryset = ParamRecordHistory.objects.filter(device_id__in=device_ids,
                                                                 identifier__in=identifiers,
                                                                 created_at__in=created_at_list)
            history_pr_record_map = {
                (device_id, identifier, created_at): value
                for device_id, identifier, created_at, value in history_queryset.values_list('device_id', 'identifier', 'created_at', 'value')
            }
            pr_record_map.update(history_pr_record_map)

        for meter_con in meter_cons:
            terminal = meter_con.terminal
            values: List[str | float] = ['--'] * count
            pr_values: List[str | float] = ['--'] * (count + 1)
            ppv_data = [
                {
                    'ppv_type': ppv_type,
                    'pr_data': ['--'] * count,
                    'data': ['--'] * (count + 1)
                }
                for ppv_type in (10, 20, 30, 40)
            ]

            device_id = meter_con.terminal.device_id
            identifier = meter_con.identifier

            peak_identifier = ['Meter_Power_Cons_P', f'Meter_Power_Cons_P_{terminal.idx}', f'Meter_Power_Cons_{terminal.idx}_P']
            flat_identifier = ['Meter_Power_Cons_F', f'Meter_Power_Cons_F_{terminal.idx}', f'Meter_Power_Cons_{terminal.idx}_F']
            valley_identifier = ['Meter_Power_Cons_V', f'Meter_Power_Cons_V_{terminal.idx}', f'Meter_Power_Cons_{terminal.idx}_V']
            sharp_identifier = ['Meter_Power_Cons_S', f'Meter_Power_Cons_S_{terminal.idx}', f'Meter_Power_Cons_{terminal.idx}_S']

            for idx, created_at in enumerate(created_at_list):
                value = pr_record_map.get((device_id, identifier, created_at))
                pr_values[idx] = float(value) if value is not None and value != '--' else '--'

                if idx != 0:
                    try:
                        values[idx - 1] = round(pr_values[idx] - pr_values[idx - 1], 2)
                    except (TypeError, ValueError):
                        pass

                if enable_ppv and meter_con.identifier.startswith('Meter_Power_Cons'):
                    peak_pr_data = ppv_data[0]['pr_data']
                    peak_data = ppv_data[0]['data']

                    flat_pr_data = ppv_data[1]['pr_data']
                    flat_data = ppv_data[1]['data']

                    valley_pr_data = ppv_data[2]['pr_data']
                    valley_data = ppv_data[2]['data']

                    sharp_pr_data = ppv_data[3]['pr_data']
                    sharp_data = ppv_data[3]['data']
                    # 峰
                    peak_value = next((pr_record_map.get((device_id, identifier, created_at)) for identifier in peak_identifier), None)
                    peak_pr_data[idx] = float(peak_value) if peak_value is not None and peak_value != '--' else '--'
                    # 平
                    flat_value = next((pr_record_map.get((device_id, identifier, created_at)) for identifier in flat_identifier), None)
                    flat_pr_data[idx] = float(flat_value) if flat_value is not None and flat_value != '--' else '--'
                    # 谷
                    valley_value = next((pr_record_map.get((device_id, identifier, created_at)) for identifier in valley_identifier), None)
                    valley_pr_data[idx] = float(valley_value) if valley_value is not None and valley_value != '--' else '--'
                    # 尖
                    sharp_value = next((pr_record_map.get((device_id, identifier, created_at)) for identifier in sharp_identifier), None)
                    sharp_pr_data[idx] = float(sharp_value) if sharp_value is not None and sharp_value != '--' else '--'

                    if idx != 0:
                        try:
                            peak_data[idx - 1] = round(peak_pr_data[idx] - peak_pr_data[idx - 1], 2)
                            flat_data[idx - 1] = round(flat_pr_data[idx] - flat_pr_data[idx - 1], 2)
                            valley_data[idx - 1] = round(valley_pr_data[idx] - valley_pr_data[idx - 1], 2)
                            sharp_data[idx - 1] = round(sharp_pr_data[idx] - sharp_pr_data[idx - 1], 2)
                        except (TypeError, ValueError):
                            pass

            values.pop()
            pr_values.pop()

            stats.append({
                "id": meter_con.id,
                "meter_type": terminal.terminal_type,
                "name": f'{terminal.nick_name}',
                "identifier": meter_con.identifier,
                "data": values,
                "pr_data": pr_values,
                "ppv_data": ppv_data
            })

        # 按照回路维度的层级显示
        if handle_circle:
            circle_stats = []
            circle_dimensions = list(Dimension.objects.filter(type_name='回路').order_by('id'))
            circle_dimension_ids = [d.id for d in circle_dimensions]

            # 从根回路开始
            for dim in list(filter(lambda x: x.parent_id not in circle_dimension_ids, circle_dimensions)):
                dim_stat = self.get_circle_stat(dim, stats)
                if dim_stat:
                    if Dimension.objects.filter(parent_id=dim.id).exists():
                        dim_stat['children'] = self.get_child_circle_stats(dim, stats)
                    elif dim.ta_ids:
                        ta_ids = dim.ta_ids.split(',')
                        dim_stat['children'] = list(filter(lambda x: str(x['id']) in ta_ids, stats))
                    else:
                        dim_stat['children'] = []
                    circle_stats.append(dim_stat)

            stats = circle_stats

        # 构建 time_range
        created_at_list.pop()
        if time_type == 'hr':
            if (created_at_list[0].date() == created_at_list[-1].date() or
                    (created_at_list[-1] > now and (created_at_list[-1] - created_at_list[0]).days == 1)):
                time_range = [f"{dt.hour:02d}时" for dt in created_at_list]
            else:
                time_range = [f"{dt.day:02d}日{dt.hour:02d}时" for dt in created_at_list]
        elif time_type == 'di':
            start, end = created_at_list[0], created_at_list[-1]
            if start.year == end.year and start.month == end.month:
                time_range = [f"{dt.day:02d}日" for dt in created_at_list]
            else:
                time_range = [f"{dt.month:02d}月{dt.day:02d}日" for dt in created_at_list]
        else:
            if created_at_list[0].year == created_at_list[-1].year:
                time_range = [f'{dt.month:02d}月' for dt in created_at_list]
            else:
                time_range = [f'{dt.year}年{dt.month:02d}月' for dt in created_at_list]

        attribute_qs = TerminalAttribute.objects.filter(
            terminal=OuterRef('pk'),
            use_for=60
        )
        meter_types = list(
            Terminal.objects.filter(terminal_type=20, terminal_label__isnull=False).annotate(has_use_for_60=Exists(attribute_qs)).filter(
                has_use_for_60=True).values_list('terminal_label', flat=True))

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                "stats": stats,
                "time_range": time_range,
                "meter_types": set(meter_types)
            },
            'total': total
        })

    def create(self, request, *args, **kwargs):
        result = self.list(request, *args, **kwargs)
        time_range = result.data['data']['time_range']
        stats = result.data['data']['stats']

        sum_cost = []

        for idx in range(len(time_range)):
            sum_cost.append(sum([float(stat['data'][idx]) for stat in stats if is_number(stat['data'][idx])]))

        stats.append({
            'name': '总能耗',
            'data': sum_cost
        })

        path, filename = ExcelUtils.generate_ec_report(stats, time_range, "汇总导出")

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'file_url': path,
                'filename': filename
            }
        })


class PpvConfigView(viewsets.ModelViewSet):
    serializer_class = PpvConfigSerializer

    def get_queryset(self):
        project_id = self.request.user.get('project_id')
        return PpvConfig.objects.filter(project_id=project_id).order_by('ec_type', 'begin_at')

    def list(self, request, *args, **kwargs):
        data = super().list(request, *args, **kwargs).data
        return Response({
            'status': status.HTTP_200_OK,
            'ppv_configs': data['results'],
            'total': data['count']
        })

    def create(self, request, *args, **kwargs):
        data = request.data
        project_id = request.user.get('project_id')
        data['project'] = project_id
        serializer = PpvConfigSerializer(data=data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        return Response({
            'status': status.HTTP_200_OK,
            'data': serializer.data
        })

    def partial_update(self, request, *args, **kwargs):
        data = super().partial_update(request, *args, **kwargs).data
        return Response({
            'status': status.HTTP_200_OK,
            'data': data
        })

    def destroy(self, request, *args, **kwargs):
        super().destroy(request, *args, **kwargs)
        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

class EcTotalStatsView(viewsets.ModelViewSet):
    @action(detail=True, methods=['get'])
    def get_authenticators(self):
        if 'intapi' in self.request.path:
            return [IntapiAuth(), ]
        return [LegacyAuth(), ]

    def list(self, request, *args, **kwargs):
        """能耗汇总统计信息，能耗统计值为当年能耗用量"""
        now = datetime.datetime.now()
        project_id = request.user.get('project_id')
        root_dimension = Dimension.objects.filter(parent_id__isnull=True, type_name='分项')
        root_da = DimensionAttribute.objects.filter(dimension__in=root_dimension, ec_type__isnull=False, name__contains='总用')

        total_stats = []

        from_at = request.query_params.get('from', None)
        if from_at is not None:
            from_at = datetime.datetime.strptime(from_at, '%Y%m%d%H%M%S').replace(minute=0, second=0)

        till_at = request.query_params.get('till', None)
        if till_at is not None:
            till_at = datetime.datetime.strptime(till_at, '%Y%m%d%H%M%S').replace(minute=59, second=59)

        for da in root_da:
            cost = 0

            if from_at and till_at:
                cost = DimensionHourlyStat.objects.filter(dimension_attribute_id=da.id, created_at__range=[from_at, till_at]
                                                          ).aggregate(sum=Sum('avg')).get('sum')
                if cost is None:
                    cost = 0
                if till_at >= now.replace(minute=0, second=0, microsecond=0):
                    cost += da.calc_current_hour_cons(project_id)
            else:
                m_cost = DimensionMonthlyStat.objects.filter(created_at__range=get_year_range(),
                                                             dimension_attribute_id=da.id).aggregate(sum=Sum('avg')).get('sum')
                if m_cost is not None:
                    cost += m_cost
                d_cost = DimensionDailyStat.objects.filter(created_at__range=get_month_range(),
                                                           dimension_attribute_id=da.id).aggregate(sum=Sum('avg')).get('sum')
                if d_cost is not None:
                    cost += d_cost
                h_cost = DimensionHourlyStat.objects.filter(created_at__range=get_day_range(),
                                                            dimension_attribute_id=da.id).aggregate(sum=Sum('avg')).get('sum')
                if h_cost is not None:
                    cost += h_cost

                # 当前时的用量
                current_hour_cons = da.calc_current_hour_cons(project_id)
                cost += current_hour_cons

            total_stats.append({
                'name': da.name,
                'identifier': da.identifier,
                'value': round(cost, 2) if cost else 0,
                'fee': None,
                'unit': DimensionEcTypeView.EC_TYPES.get(da.ec_type, {}).get('unit', None)
            })

        # 如果维度没有用水量，用气量，就从手工录入数据检查是否有相关数据
        # 手工录入用水量
        if not any(('用水量' in stat['name']) for stat in total_stats):
            water_records = ManualRecord.objects.filter(gran=50, name__icontains='用水量')
            if water_records.exists():
                water_cons = 0
                for water_record in water_records:
                    data = json.loads(water_record.data)
                    for monthly_data in data:
                        year, month = monthly_data.get('time').split('-')
                        value = monthly_data.get('value')
                        if value:
                            if from_at is not None and till_at is not None:
                                if till_at.year >= int(year) >= from_at.year and till_at.month >= int(month) >= from_at.month:
                                    water_cons += float(value)
                            else:
                                water_cons += float(value)
                total_stats.append({
                    'name': '用水量',
                    'identifier': 'Meter_Water_Cons',
                    'value': round(water_cons, 2),
                    'fee': None,
                    'unit': 'm³'
                })

        # 手工录入用气量
        if not any(('用气量' in stat['name']) for stat in total_stats):
            gas_records = ManualRecord.objects.filter(gran=50, name__icontains='用气量')
            if gas_records.exists():
                gas_cons = 0
                for gas_record in gas_records:
                    data = json.loads(gas_record.data)
                    for monthly_data in data:
                        year, month = monthly_data.get('time').split('-')
                        value = monthly_data.get('value')
                        if value:
                            if from_at is not None and till_at is not None:
                                if till_at.year >= int(year) >= from_at.year and till_at.month >= int(month) >= from_at.month:
                                    gas_cons += float(value)
                            else:
                                gas_cons += float(value)
                total_stats.append({
                    'name': '用气量',
                    'identifier': 'Meter_Gas_Cons',
                    'value': round(gas_cons, 2),
                    'fee': None,
                    'unit': 'm³'
                })

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'total_stats': total_stats
            }
        })

class EcComparisonView(viewsets.ModelViewSet):
    def list(self, request, *args, **kwargs):
        project_id = request.user.get('project_id', None)
        now = datetime.datetime.now()
        da_ids = request.query_params.get('da_ids', None)
        from_at = self.request.query_params.get('from', None)
        till_at = self.request.query_params.get('till', None)
        if da_ids is None or from_at is None or till_at is None:
            return Response(status=status.HTTP_400_BAD_REQUEST)
        else:
            da_ids = da_ids.split(',')

        time_type = self.request.query_params.get('type', 'hr')

        queryset = DimensionHourlyStat.objects.filter(dimension_attribute_id__in=da_ids)

        from_at = datetime.datetime.strptime(from_at, '%Y%m%d%H%M%S')
        till_at = datetime.datetime.strptime(till_at, '%Y%m%d%H%M%S')
        if from_at > till_at:
            from_at, till_at = till_at, from_at
        # queryset = queryset.filter(created_at__gte=from_at).filter(created_at__lte=till_at)

        query_year = till_at.year
        now = datetime.datetime.now()

        # 求根维度的总用电量
        root_dim = list(Dimension.objects.filter(parent__isnull=True, type_name__icontains='分项').values_list('id', flat=True))
        root_das = DimensionAttribute.objects.filter(Q(name__icontains='总') & Q(name__icontains='用电量'), dimension_id__in=root_dim, ec_type=10)
        root_da = None
        if root_das.count() > 1:
            logging.error(f'查询根总用电量失败，存在多个总用电量分项')
        else:
            root_da = root_das.last()

        years = [now.year]
        if query_year == now.year:
            years += [now.year - 1, now.year - 2]
        else:
            if query_year == now.year - 1:
                years += [query_year, now.year - 2]
            else:
                years += [now.year - 1, query_year]

        years.reverse()
        result = []
        for da_id in da_ids:
            da = DimensionAttribute.objects.get(pk=da_id)
            da_result = {
                'id': da_id,
                'name': da.name,
                'ec_type': da.ec_type,
                'data': []
            }

            for idx, year in enumerate(years):
                try:
                    begin = from_at.replace(year=year)
                except ValueError:
                    monthrange = calendar.monthrange(year, now.month)[1]
                    begin = from_at.replace(year=year, month=now.month, day=monthrange)
                if from_at.year != till_at.year:
                    end = from_at.replace(month=12, day=31, hour=23, minute=59, second=0)
                else:
                    try:
                        end = till_at.replace(year=year)
                    except ValueError:
                        monthrange = calendar.monthrange(year, now.month)[1]
                        end = till_at.replace(year=year, month=now.month, day=monthrange)

                # 优先使用手工录入的数据
                script_cost = None
                if year != now.year and root_da is not None and root_da.id == int(da_id):
                    script_cost = EcTranscription.objects.filter(ec_type=10, year_month__startswith=f'{year}').aggregate(total=Sum('value'))['total']
                if script_cost:
                    cost = script_cost
                else:
                    cost = queryset.filter(dimension_attribute_id=da_id, created_at__range=[begin, end]).aggregate(total=Sum('avg'))['total']
                    if cost is None:
                        cost = 0
                    if now.date() == end.date() and now.hour == end.hour:
                        cost += da.calc_current_hour_cons(project_id)
                if time_type == 'di':
                    if begin.day == 1:
                        qoq_begin_date = begin - datetime.timedelta(days=1)
                        qoq_end_date = end - datetime.timedelta(days=1)
                        qoq_begin = begin.replace(year=qoq_begin_date.year, month=qoq_begin_date.month, day=qoq_begin_date.day)
                        qoq_end = end.replace(year=qoq_end_date.year, month=qoq_end_date.month, day=qoq_end_date.day)
                    else:
                        qoq_begin = begin - datetime.timedelta(days=1)
                        qoq_end = end - datetime.timedelta(days=1)
                    qoq_cost = queryset.filter(dimension_attribute_id=da_id, created_at__range=[qoq_begin, qoq_end]
                                               ).aggregate(total=Sum('avg'))['total']

                elif time_type == 'mo':
                    if begin.month == 1:
                        qoq_begin = begin.replace(year=begin.year - 1, month=12)
                    else:
                        qoq_begin = begin.replace(month=begin.month - 1)
                    qoq_end = begin - datetime.timedelta(seconds=1)
                    # if end.month == 1:
                    #     qoq_end = end.replace(year=end.year - 1, month=12)
                    # else:
                    #     qoq_end = end.replace(month=end.month - 1)
                    qoq_cost = queryset.filter(dimension_attribute_id=da_id, created_at__range=[qoq_begin, qoq_end]
                                               ).aggregate(total=Sum('avg'))['total']
                else:
                    qoq_cost = None

                if cost is None:
                    cost = 0

                qoq_cost = round(qoq_cost, 2) if qoq_cost else qoq_cost
                qoq = round((cost - qoq_cost) / qoq_cost * 100, 2) if qoq_cost else '--'

                data = {
                    'year': '当年' if now.year == year else f'{year}',
                    'cost': round(cost, 2) if cost else cost,
                    'qoq_cost': qoq_cost,
                    'qoq': qoq,
                    'yoy': '--'
                }

                if idx != 0:
                    prev_cost = da_result['data'][idx - 1]['cost']
                    data['yoy'] = round(((cost - prev_cost) / prev_cost) * 100, 2) if prev_cost else '--'

                da_result['data'].append(data)

            da_result['data'].reverse()

            # 基准，只计算总用电量
            criteria_cost = '--'
            qoq_criteria_cost = '--'
            if da.dimension.parent is None and '总用电量' in da.name:
                ec_criteria = list(EcCriteria.objects.filter(is_refer=True, ec_type=10))
                if len(ec_criteria):
                    criteria_cost = 0
                    qoq_criteria_cost = 0
                    from_month = from_at.month
                    till_month = till_at.month
                    criteria = next(filter(lambda x: x.year_month.endswith('%02d' % from_month), ec_criteria), None)
                    if criteria is not None:
                        qoq_criteria_value = 0
                        year_month = criteria.year_month
                        qoq_year = int(year_month[:4])
                        month = year_month[4:]
                        qoq_criteria = EcCriteria.objects.filter(year_month=f'{qoq_year - 1}{month}', ec_type=criteria.ec_type).last()
                        if qoq_criteria is not None:
                            qoq_criteria_value = qoq_criteria.value

                        if time_type == 'hr':
                            span_hour = (till_at - from_at).seconds / 60 / 60
                            hour_avg = float(criteria.value) / 30 / 24
                            if qoq_criteria_value:
                                qoq_hour_avg = float(qoq_criteria_value) / 30 / 24
                                qoq_criteria_cost = qoq_hour_avg * span_hour
                            criteria_cost = hour_avg * span_hour
                        elif time_type == 'di':
                            span_day = (till_at - from_at).days + 1
                            day_avg = float(criteria.value) / 30
                            if qoq_criteria_value:
                                qoq_day_avg = float(qoq_criteria_value) / 30
                                qoq_criteria_cost = qoq_day_avg * span_day
                            criteria_cost = day_avg * span_day
                        else:
                            criteria_cost += float(criteria.value)
                            qoq_criteria_cost += float(qoq_criteria_value)
                            from_month += 1
                            while from_month <= till_month:
                                criteria = next(filter(lambda x: x.year_month.endswith('%02d' % from_month), ec_criteria), None)
                                qoq_criteria = EcCriteria.objects.filter(year_month=f'{qoq_year}{"%02d" % from_month}', ec_type=10).last()
                                if criteria is not None:
                                    criteria_cost += float(criteria.value)
                                if qoq_criteria is not None:
                                    qoq_criteria_cost += float(qoq_criteria.value)
                                from_month += 1

            data = {
                'year': '基准年',
                'cost': round(criteria_cost, 2) if is_number(criteria_cost) else '--',
                'yoy': '--',
                'qoq': round((criteria_cost - qoq_criteria_cost) / qoq_criteria_cost * 100, 2
                             ) if qoq_criteria_cost and is_number(qoq_criteria_cost) and is_number(criteria_cost) else '--'
            }
            da_result['data'].append(data)

            result.append(da_result)

        return Response({
            'status': status.HTTP_200_OK,
            'data': result
        })

class EsrStatView(viewsets.ModelViewSet):
    def retrieve(self, request, *args, **kwargs):
        year_month = request.query_params.get('mo', None)
        if year_month is None:
            raise exceptions.ValidationError(detail={'detail': 'mo is required!'})
        year, month = year_month[:4], year_month[4:]
        month_range = calendar.monthrange(int(year), int(month))[1]
        project_id = request.user['project_id']

        esr_stats = EsrStat.objects.filter(project_id=project_id, type='di')

        # 统计每日节能率
        stats = []
        daily_stats = list(esr_stats.filter(created_at__year=year, created_at__month=month))
        for day in range(1, month_range + 1):
            stat = next(filter(lambda x: x.created_at.day == day, daily_stats), None)
            stats.append({
                'id': stat.id if stat else None,
                'current': stat.cur_value if stat else None,
                'refer': stat.refer_value if stat else None,
                'esr': stat.esr_value if stat else None,
                'data_at': f'{year}-{"%02s" % month}-{"%02d" % day}'
            })

        # 月统计数据
        monthly_stats = esr_stats.filter(created_at__year=year, created_at__month=month)
        p_days = monthly_stats.filter(esr_value__gt=0).count()
        np_days = monthly_stats.filter(esr_value__lt=0).count()
        month_stat = {
            'p_days': p_days,
            'np_days': np_days,
            'pr': round(p_days / monthly_stats.count() * 100, 2) if monthly_stats.count() else 0
        }

        # 年统计数据
        yearly_stats = esr_stats.filter(created_at__year=year)
        p_days = yearly_stats.filter(esr_value__gt=0).count()
        np_days = yearly_stats.filter(esr_value__lt=0).count()
        year_stat = {
            'p_days': p_days,
            'np_days': np_days,
            'pr': round(p_days / yearly_stats.count() * 100, 2) if yearly_stats.count() else 0
        }

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'stats': stats,
                'month_stat': month_stat,
                'year_stat': year_stat
            }
        })

class SysEcReportView(viewsets.ModelViewSet):
    """
    项目setting配置是否开启用电数据统计导出功能
    用电时段根据ppv_configs解析
    只统计冷源运行状态非本地时段
    """

    def create(self, request, *args, **kwargs):
        project_id = request.user['project_id']
        time_type = request.data.get('type', 'di')

        # 指定范围
        from_at = request.data.get('from', None)
        till_at = request.data.get('till', None)

        mac = request.data.get('mac', None)
        if mac is None:
            device = None
            cs_mac = list(ColdSource.objects.values_list('mac', flat=True).distinct())
            cs_devices = Device.objects.filter(mac__in=cs_mac)
            da = None
            for cs_device in cs_devices:
                da = DeviceAttribute.objects.query_object_by_idf(cs_device, 'CSSWStatus')
                if da is not None:
                    device = cs_device
                    break
        else:
            device = Device.objects.get(mac=mac)
            da = DeviceAttribute.objects.query_object_by_idf(device, 'CSSWStatus')

        if da is None:
            return Response({
                'status': status.HTTP_500_INTERNAL_SERVER_ERROR,
                'data': None,
                'error': '找不到合适的冷源!'
            })

        dlt = DlTask.objects.create(
            web_user_id=request.user['id'],
            project_id=request.user['project_id'],
            device_id=device.id,
            data_at=datetime.datetime.strptime(from_at, '%Y%m%d%H%M%S'),
            status=10,
            name=None,
            file_path=None,
        )
        # self.export_sys_stats(self, project_id, time_type, from_at, till_at, da)
        sys_ec_report.delay(project_id, time_type, from_at, till_at, da.id, dlt.id)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })


class EcConsumeCalculationView(viewsets.ModelViewSet):
    """地大未来城，能耗用量计算"""

    def list(self, request, *args, **kwargs):
        time_type = request.query_params.get('type', 'hr')
        if time_type == 'hr':
            data_from = DeviceHourlyStat.objects
        elif time_type == 'di':
            data_from = DeviceDailyStat.objects
        else:
            data_from = DeviceMonthlyStat.objects

        from_at = request.query_params.get('from', None)
        till_at = request.query_params.get('till', None)
        if from_at is None or till_at is None:
            raise exceptions.ValidationError(detail={'detail': 'time range is required!'})
        if time_type == 'mo':
            from_dt = datetime.datetime.strptime(from_at, '%Y%m%d%H%M%S')
            monthrange = calendar.monthrange(from_dt.year, from_dt.month)[1]
            till_dt = from_dt + datetime.timedelta(days=monthrange)
        else:
            from_dt = datetime.datetime.strptime(from_at, '%Y%m%d%H%M%S')
            till_dt = datetime.datetime.strptime(till_at, '%Y%m%d%H%M%S')

        queryset = data_from.filter(created_at__gte=from_dt, created_at__lte=till_dt)

        pool_flow = Device.objects.get(mac='865328064237018')
        boiler_meter = Device.objects.get(mac='867435053535753')
        gym_flow = Device.objects.get(mac='869476058094683')
        cs_flow = Device.objects.get(mac='869476058089287')
        cs_meter = Device.objects.get(mac='866714046340443')

        # 计算冷量计算
        pool_cool_query = (Q(device_id=cs_flow.id, identifier='Meter_CumHeat_1') |
                           Q(device_id=pool_flow.id, identifier__in=['Meter_CumHeat_1', 'Meter_CumHeat_2', 'Meter_CumHeat_3']))
        pool_cool_usage = queryset.filter(pool_cool_query).aggregate(sum=Sum('avg'))['sum']

        gym_cool_query = Q(device_id=gym_flow.id, identifier__in=['Meter_CumHeat_1', 'Meter_CumHeat_2'])
        gym_cool_usage = queryset.filter(gym_cool_query).aggregate(sum=Sum('avg'))['sum']

        sys_cool_query = Q(device_id=cs_flow.id, identifier__in=['Meter_CumHeat_2', 'Meter_CumHeat_3'])
        sys_cool_usage = queryset.filter(sys_cool_query).aggregate(sum=Sum('avg'))['sum']

        # 分摊比例计算
        pool_ratio = pool_cool_usage / sys_cool_usage if sys_cool_usage else 0  # 游泳池分摊比例
        gym_ratio = gym_cool_usage / sys_cool_usage if sys_cool_usage else 0  # 健身中心分摊比例

        # 用电量计算
        sys_power_query = (Q(device_id=cs_meter.id, identifier__in=['Meter_Power_Cons_1', 'Meter_Power_Cons_2', 'Meter_Power_Cons_3']) |
                           Q(device_id=boiler_meter.id, identifier__in=['Meter_Power_Cons_3', 'Meter_Power_Cons_4']))
        sys_power_usage = queryset.filter(sys_power_query).aggregate(sum=Sum('avg'))['sum']

        pool_power_query = Q(device_id=boiler_meter.id, identifier__in=['Meter_Power_Cons_1', 'Meter_Power_Cons_2'])
        pool_power_usage = queryset.filter(pool_power_query).aggregate(sum=Sum('avg'))['sum'] + pool_ratio * sys_power_usage

        gym_power_usage = gym_ratio * sys_power_usage

        # 用气量计算
        pool_gas_usage = 0
        gym_gas_usage = 0
        sys_gas_usage = 0
        ec_meter = EcMeter.objects.get(name='天然气总用量')

        while from_dt < till_dt:
            month_gas = ManualMeterReading.objects.filter(ec_meter_id=ec_meter.id,
                                                          reading_time__year=from_dt.year,
                                                          reading_time__month=from_dt.month).last()
            total_gas = 0
            if month_gas is not None:
                if month_gas.value:
                    days_in_month = calendar.monthrange(from_dt.year, from_dt.month)[1]
                    hours_in_month = days_in_month * 24
                    end_at = from_dt.replace(day=days_in_month, hour=23, minute=59, second=59, microsecond=999999)
                    if end_at > till_dt:
                        end_at = till_dt
                    total_gas = ((from_dt - end_at).total_seconds() / 3600) / hours_in_month * month_gas.value

            a = queryset.filter(device_id=boiler_meter.id, identifier__in=['Meter_Power_Cons_3',
                                                                           'Meter_Power_Cons_4']).aggregate(sum=Sum('avg'))['sum']
            b = queryset.filter(device_id=boiler_meter.id, identifier__in=['Meter_Power_Cons_1', 'Meter_Power_Cons_2', 'Meter_Power_Cons_3',
                                                                           'Meter_Power_Cons_4']).aggregate(sum=Sum('avg'))['sum']
            sys_gas_usage_month = a / b * total_gas
            sys_gas_usage += sys_gas_usage_month

            pool_gas_usage += (total_gas - sys_gas_usage_month) + (pool_ratio * sys_gas_usage_month)

            gym_gas_usage += gym_ratio * sys_gas_usage_month

            if from_dt.month == 12:
                from_dt = from_dt.replace(year=from_dt.year + 1, month=1, day=1, hour=0, second=0, microsecond=0)
            else:
                from_dt = from_dt.replace(month=from_dt.month + 1, day=1, hour=0, second=0, microsecond=0)

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'ratio': {
                    'pool': round(pool_ratio * 100, 4),
                    'gym': round(gym_ratio * 100, 4)
                },
                'cool_stats': {
                    'pool': pool_cool_usage,
                    'gym': gym_cool_usage,
                    'sys': sys_cool_usage
                },
                'power_stats': {
                    'pool': pool_power_usage,
                    'gym': gym_power_usage,
                    'sys': sys_power_usage
                },
                'gas_stats': {
                    'pool': pool_gas_usage,
                    'gym': gym_gas_usage,
                    'sys': sys_gas_usage
                }

            }
        })


class UnitRankingView(viewsets.ModelViewSet):
    """单位面积电耗排名"""

    def list(self, request, *args, **kwargs):
        now = datetime.datetime.now()
        project_id = request.user['project_id']

        from_dt = request.query_params.get('from', None)
        till_dt = request.query_params.get('till', None)

        if from_dt is not None:
            from_dt = datetime.datetime.strptime(from_dt, '%Y%m%d%H%M%S')
        else:
            from_dt = now - datetime.timedelta(days=365)

        if till_dt is not None:
            till_dt = datetime.datetime.strptime(till_dt, '%Y%m%d%H%M%S')
        else:
            till_dt = now

        name = request.query_params.get('name', '单位建面能耗')
        if name not in ['人均综合能耗', '单位建面能耗', '人均水耗']:
            raise exceptions.ValidationError(detail={'detail': '无效定额!'})

        units = Dimension.objects.filter(type_name='单位', parent__isnull=False, unit_area__isnull=False, ec_persons__isnull=False)
        unit_count = units.count()

        unit_name = request.query_params.get('unit_name', None)
        if unit_name is not None:
            units = units.filter(name=unit_name)

        results = []
        for unit in units:
            if name == '人均综合能耗' or name == '单位建面能耗':
                # 用电量
                power_das = DimensionAttribute.objects.filter(dimension_id=unit.id, name__contains=f'{unit.name}用电量', ec_type=10, is_cum=True)
                da_ids = [da.id for da in power_das]

                # 用电量
                power_cons = 0
                power_monthly_cons = DimensionMonthlyStat.objects.filter(dimension_id=unit.id, dimension_attribute_id__in=da_ids,
                                                                         created_at__range=[from_dt, till_dt]).aggregate(total=Sum('avg'))['total']
                if power_monthly_cons:
                    power_cons += power_monthly_cons

                if till_dt.year == now.year and till_dt.month == now.month:
                    power_daily_cons = DimensionDailyStat.objects.filter(dimension_id=unit.id, dimension_attribute_id__in=da_ids,
                                                                         created_at__year=now.year,
                                                                         created_at__month=now.month).aggregate(total=Sum('avg'))['total']
                    if power_daily_cons:
                        power_cons += power_daily_cons
                if till_dt.date() == now.date():
                    power_hourly_cons = DimensionHourlyStat.objects.filter(dimension_id=unit.id, dimension_attribute_id__in=da_ids,
                                                                           created_at__year=now.year,
                                                                           created_at__month=now.month,
                                                                           created_at__day=now.day).aggregate(total=Sum('avg'))['total']
                    if power_hourly_cons:
                        power_cons += power_hourly_cons
                if till_dt.date() == now.date() and till_dt.hour == now.hour:
                    for power_da in power_das:
                        power_cons += power_da.calc_current_hour_cons(project_id)

                # 用气量
                gas_cons = 0
                gas_da = DimensionAttribute.objects.filter(dimension__type_name='分项', dimension__parent__isnull=True,
                                                           name__contains='总用气量', ec_type=40).first()
                if gas_da:
                    # 当年用气量
                    gas_monthly_cons = DimensionMonthlyStat.objects.filter(dimension_attribute_id=gas_da.id,
                                                                           created_at__range=[from_dt, till_dt]).aggregate(total=Sum('avg'))['total']
                    if gas_monthly_cons:
                        gas_cons += gas_monthly_cons
                    if till_dt.year == now.year and till_dt.month == now.month:
                        gas_daily_cons = DimensionDailyStat.objects.filter(dimension_attribute_id=gas_da.id,
                                                                           created_at__year=now.year,
                                                                           created_at__month=now.month).aggregate(total=Sum('avg'))['total']
                        if gas_daily_cons:
                            gas_cons += gas_daily_cons
                    if till_dt.date() == now.date():
                        today = now.replace(hour=0, minute=0, second=0, microsecond=0)
                        gas_hourly_cons = DimensionHourlyStat.objects.filter(dimension_attribute_id=gas_da.id,
                                                                             created_at__gte=today).aggregate(total=Sum('avg'))['total']
                        if gas_hourly_cons:
                            gas_cons += gas_hourly_cons
                    if till_dt.date() == now.date() and till_dt.hour == now.hour:
                        gas_cons += gas_da.calc_current_hour_cons(project_id)

                else:
                    gas_cons = 0
                    gas_record = ManualRecord.objects.filter(Q(name__contains=now.year) & Q(name__contains='用气量'), gran=50).first()
                    if gas_record:
                        data = json.loads(gas_record.data)
                        for monthly_data in data:
                            year, month = monthly_data.get('time').split('-')
                            value = monthly_data.get('value')
                            if value and int(year) == now.year:
                                gas_cons += float(value)
                    prev_gas_record = ManualRecord.objects.filter(Q(name__contains=(now.year - 1)) & Q(name__contains='用气量'), gran=50).first()
                    if prev_gas_record:
                        data = json.loads(prev_gas_record.data)
                        for monthly_data in data:
                            year, month = monthly_data.get('time').split('-')
                            value = monthly_data.get('value')
                            if value and int(year) == (now.year - 1) and int(month) >= now.month:
                                gas_cons += float(value)
                gas_cons = gas_cons / unit_count

                # 能耗计算
                kgce = power_cons * 0.1229 + gas_cons * 1.33
                if name == '人均综合能耗':
                    value = kgce / unit.ec_persons
                    constraint = 542
                    base = 299
                    guide = 156
                    kgce_unit = 'kgce/p'
                else:
                    value = kgce / unit.unit_area
                    constraint = 13
                    base = 7
                    guide = 4
                    kgce_unit = 'kgce/m²'

                if value <= guide:
                    level = 'guide'
                elif value <= base:
                    level = 'base'
                else:
                    level = 'constraint'

                results.append({
                    'id': unit.id,
                    'unit_name': unit.name,
                    'name': name,
                    'kgce': round(kgce, 2),
                    'power_cons': round(power_cons, 2),
                    'gas_cons': round(gas_cons, 2),
                    'value': round(value, 2),
                    'level': level,
                    'unit': kgce_unit
                })

            elif name == '人均水耗':
                # 用水量
                water_cons = 0
                water_da = DimensionAttribute.objects.filter(dimension__type_name='单位', name=f'{unit.name}用水量', ec_type=20).first()

                if water_da:
                    # 当年用水量
                    water_monthly_cons = DimensionMonthlyStat.objects.filter(dimension_attribute_id=water_da.id,
                                                                             created_at__year=now.year).aggregate(total=Sum('avg'))['total']
                    if water_monthly_cons:
                        water_cons += water_monthly_cons
                    if till_dt.year == now.year and till_dt.month == now.month:
                        water_daily_cons = DimensionDailyStat.objects.filter(dimension_attribute_id=water_da.id,
                                                                             created_at__year=now.year,
                                                                             created_at__month=now.month).aggregate(total=Sum('avg'))['total']
                        if water_daily_cons:
                            water_cons += water_daily_cons
                    if till_dt.date() == now.date():
                        today = now.replace(hour=0, minute=0, second=0, microsecond=0)
                        water_hourly_cons = DimensionHourlyStat.objects.filter(dimension_attribute_id=water_da.id,
                                                                               created_at__gte=today).aggregate(total=Sum('avg'))['total']
                        if water_hourly_cons:
                            water_cons += water_hourly_cons
                    if till_dt.date() == now.date() and till_dt.hour == now.hour:
                        water_cons += water_da.calc_current_hour_cons(project_id)

                value = water_cons / unit.ec_persons

                general = 38
                advanced = 15

                if value <= advanced:
                    level = 'advanced'
                else:
                    level = 'general'

                results.append({
                    'id': unit.id,
                    'unit_name': unit.name,
                    'name': name,
                    'water_cons': round(water_cons, 2),
                    'value': round(value, 2),
                    'level': level,
                    'unit': 'm³/人'
                })

        results.sort(key=lambda x: -x['value'])

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'rankings': results
            }
        })

class UnitConsView(viewsets.ModelViewSet):
    def list(self, request, *args, **kwargs):
        now = datetime.datetime.now()
        project_id = request.user.get('project_id')

        dimension_id = request.query_params.get('dimension_id', None)

        from_at = request.query_params.get('from', None)
        if from_at is not None:
            from_at = datetime.datetime.strptime(from_at, '%Y%m%d%H%M%S')
        else:
            from_at = now - datetime.timedelta(hours=24)

        till_at = request.query_params.get('till', None)
        if till_at is not None:
            till_at = datetime.datetime.strptime(till_at, '%Y%m%d%H%M%S')
        else:
            till_at = now

        dt = request.query_params.get('dt', None)
        if dt is not None:
            dt = datetime.datetime.strptime(dt, '%Y%m%d%H%M%S')
            from_at = dt.replace(hour=0, minute=0, second=0, microsecond=0)
            till_at = from_at + datetime.timedelta(days=1)

        # 从小时统计表中查找数据
        # if (from_at - till_at).days >= 7:
        #     data_from = DimensionDailyStat.objects
        # else:
        data_from = DimensionHourlyStat.objects

        results = []
        if dimension_id is None:
            prev_from_at = from_at - datetime.timedelta(days=365)
            prev_till_at = till_at - datetime.timedelta(days=365)

            units = Dimension.objects.filter(type_name='单位', parent__isnull=False, ec_persons__isnull=False, unit_area__isnull=False)
            unit_name = request.query_params.get('unit_name', None)
            if unit_name is not None:
                units = units.filter(name__contains=unit_name)
            name = request.query_params.get('name', '用电排名')

            unit_count = Dimension.objects.filter(type_name='单位', parent__isnull=False, ec_persons__isnull=False,
                                                  unit_area__isnull=False).count()

            # 用水量
            water_cons = 0
            prev_water_cons = 0
            # 用气量
            gas_cons = 0
            prev_gas_cons = 0

            if name != '用电排名':
                # TODO 计算用水及用气
                pass

            for unit in units:
                da = DimensionAttribute.objects.filter(dimension_id=unit.id, ec_type=10, is_cum=True, name__contains=f'{unit.name}用电量').first()
                if not da:
                    logging.error(f"cannot find '{unit.name}用电量'")
                    continue
                # 用电量
                # - 计算当前时的用量
                current_hour_cons = da.calc_current_hour_cons(project_id)

                power_value = data_from.filter(dimension_attribute_id=da.id, created_at__range=[from_at, till_at]).aggregate(sum=Sum('avg'))['sum']
                if power_value is None:
                    power_value = 0

                power_value += current_hour_cons
                prev_power_value = data_from.filter(dimension_attribute_id=da.id, created_at__range=[prev_from_at, prev_till_at]
                                                    ).aggregate(sum=Sum('avg'))['sum']
                if prev_power_value is None:
                    prev_power_value = 0

                if name == '用电排名':
                    results.append({
                        'id': unit.id,
                        'name': unit.name,
                        'value': round(power_value, 2),
                        'prev_value': round(prev_power_value, 2),
                        'yoy': round((power_value - prev_power_value) / prev_power_value * 1000, 2) if prev_power_value else '--',
                        'unit': 'kWh'
                    })
                elif name == '人均水耗':
                    results.append({
                        'id': unit.id,
                        'name': unit.name,
                        'value': round(water_cons, 2),
                        'prev_value': round(prev_water_cons, 2),
                        'yoy': round((water_cons - prev_water_cons / prev_water_cons * 1000), 2) if prev_water_cons else '--',
                        'unit': 'm³'
                    })
                elif name == '单位建面能耗' or name == '人均综合能耗':
                    kgce = power_value * 0.1229 + gas_cons * 1.33
                    prev_kgce = prev_power_value * 0.1229 + prev_gas_cons * 1.33

                    if name == '单位建面能耗':
                        value = round(kgce / unit.unit_area, 2)
                        prev_value = round(prev_kgce / unit.unit_area, 2)
                        results.append({
                            'id': unit.id,
                            'name': unit.name,
                            'value': round(value, 2),
                            'prev_value': round(prev_value, 2),
                            'yoy': round((value - prev_value / prev_value * 100), 2) if prev_value else '--',
                            'unit': 'kgce/m²'
                        })
                    elif name == '人均综合能耗':
                        value = round(kgce / unit.ec_persons, 2)
                        prev_value = round(prev_kgce / unit.ec_persons, 2)
                        results.append({
                            'id': unit.id,
                            'name': unit.name,
                            'value': round(value, 2),
                            'prev_value': round(prev_value, 2),
                            'yoy': round((value - prev_value / prev_value * 100), 2) if prev_value else '--',
                            'unit': 'kgce/p'
                        })

        else:
            das: list[DimensionAttribute] = list(DimensionAttribute.objects.filter(dimension_id=dimension_id,
                                                                                   is_cum=True, formula__isnull=True))
            for da in das:
                value = data_from.filter(dimension_attribute_id=da.id, created_at__range=[from_at, till_at]).aggregate(sum=Sum('avg'))['sum']
                if value is None:
                    value = 0
                if till_at.date() == now.date() and till_at.hour == now.hour:
                    value += da.calc_current_hour_cons(project_id)
                results.append({
                    'name': da.name,
                    'value': round(value, 2),
                    'unit': 'kWh'
                })

        results.sort(key=lambda x: -x['value'])

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'unit_cons': results
            }
        })


class UnitDimensionRoomRanking(viewsets.ModelViewSet):
    def list(self, request, *args, **kwargs):
        results = []
        terminal_type = ContentType.objects.get_for_model(Terminal)
        unit_id = request.query_params.get('unit_id', None)

        from_at = request.query_params.get('from', None)
        till_at = request.query_params.get('till', None)
        if from_at is not None:
            from_at = datetime.datetime.strptime(from_at, '%Y%m%d%H%M%S')
        if till_at is not None:
            till_at = datetime.datetime.strptime(till_at, '%Y%m%d%H%M%S')

        if unit_id is not None:
            terminal_ids = list(DimensionTerminal.objects.filter(dimension_id=unit_id).values_list('terminal_id', flat=True))
            room_ids = list(RoomDevice.objects.filter(object_id__in=terminal_ids, content_type_id=terminal_type.id
                                                      ).values_list('active_room_id', flat=True))
            unit_rooms = ActiveRoom.objects.filter(id__in=room_ids)
            for room in unit_rooms:
                terminal_ids = list(RoomDevice.objects.filter(active_room_id=room.id, content_type_id=terminal_type.id
                                                              ).values_list('object_id', flat=True))
                device_ids = list(Terminal.objects.filter(id__in=terminal_ids).values_list('device_id', flat=True))
                room_device_queryset = DeviceHourlyStat.objects.filter(device_id__in=device_ids,
                                                                       identifier__in=['Meter_Power', 'power_cons', 'Meter_Power_Cons'])
                if from_at is not None:
                    room_device_queryset = room_device_queryset.filter(created_at__gte=from_at)
                if till_at is not None:
                    room_device_queryset = room_device_queryset.filter(created_at__lte=till_at)

                consumption = room_device_queryset.aggregate(sum=Sum('avg'))['sum']
                results.append({
                    'id': room.id,
                    'name': room.name,
                    'consumption': round(consumption, 2) if consumption else 0
                })

            results.sort(key=lambda x: -x['consumption'])

        return Response({
            'status': status.HTTP_200_OK,
            'data': results
        })

class DimensionEcSum(viewsets.ModelViewSet):

    def list(self, request, *args, **kwargs):
        now = datetime.datetime.now()
        project_id = request.user.get('project_id')
        last_now = now - datetime.timedelta(days=365)  # 上一年的今天
        unit_name = request.query_params.get('unit_name', None)

        unit_count = 1
        if unit_name is not None:
            unit_count = Dimension.objects.filter(type_name='单位', parent__isnull=False, ec_persons__isnull=False, unit_area__isnull=False).count()

        # 计算用电量
        power_cons = 0
        last_power_cons = 0
        if unit_name:
            power_dim = Dimension.objects.filter(type_name='单位', name=unit_name, parent_id__isnull=False).first()
            power_da = DimensionAttribute.objects.filter(dimension_id=power_dim.id, name__contains=f'{power_dim.name}用电量').first()
        else:
            root_dimension_ids = Dimension.objects.filter(type_name='分项', parent_id__isnull=True).values_list('id', flat=True)
            power_da = DimensionAttribute.objects.filter(dimension_id__in=root_dimension_ids, ec_type__isnull=False,
                                                         name__contains=f'总用电量').first()
            if power_da is None:
                power_dim = Dimension.objects.filter(type_name='单位', parent_id__isnull=True).first()
                power_da = DimensionAttribute.objects.filter(dimension_id=power_dim.id, ec_type__isnull=False, name__contains=f'总用电量').first()

        if power_da:
            # 当年用电量
            power_monthly_cons = DimensionMonthlyStat.objects.filter(dimension_attribute_id=power_da.id,
                                                                     created_at__year=now.year).aggregate(total=Sum('avg'))['total']
            if power_monthly_cons:
                power_cons += power_monthly_cons

            power_daily_cons = DimensionDailyStat.objects.filter(dimension_attribute_id=power_da.id,
                                                                 created_at__year=now.year,
                                                                 created_at__month=now.month).aggregate(total=Sum('avg'))['total']
            if power_daily_cons:
                power_cons += power_daily_cons

            power_hourly_cons = DimensionHourlyStat.objects.filter(dimension_attribute_id=power_da.id,
                                                                   created_at__year=now.year,
                                                                   created_at__month=now.month,
                                                                   created_at__day=now.day).aggregate(total=Sum('avg'))['total']
            if power_hourly_cons:
                power_cons += power_hourly_cons

            power_cons += power_da.calc_current_hour_cons(project_id)

            # 上一年用电量
            last_power_monthly_cons = DimensionMonthlyStat.objects.filter(dimension_attribute_id=power_da.id,
                                                                          created_at__lt=last_now).aggregate(total=Sum('avg'))['total']
            if last_power_monthly_cons:
                last_power_cons += last_power_monthly_cons

            last_power_daily_cons = DimensionDailyStat.objects.filter(dimension_attribute_id=power_da.id,
                                                                      created_at__year=(now.year - 1),
                                                                      created_at__month=now.month,
                                                                      created_at__lte=last_now).aggregate(total=Sum('avg'))['total']
            if last_power_daily_cons:
                last_power_cons += last_power_daily_cons

        # 计算当年用水量。如果维度没有配置，就从手工录入查找
        water_cons = 0
        # if unit_name:
        #     water_dim = Dimension.objects.filter(type_name='单位', name=unit_name, parent_id__isnull=False).first()
        #     water_da = DimensionAttribute.objects.filter(dimension_id=water_dim.id, name__contains='用水').first()
        # else:
        #     water_dim = Dimension.objects.filter(type_name='单位', parent_id__isnull=True).first()
        #     water_da = DimensionAttribute.objects.filter(dimension_id=water_dim.id, name__contains='用水').first()
        water_da = DimensionAttribute.objects.filter(dimension__type_name='分项', dimension__parent__isnull=True,
                                                     name__contains='总用水量', ec_type=20).first()
        if water_da:
            water_monthly_cons = DimensionMonthlyStat.objects.filter(dimension_attribute_id=water_da.id,
                                                                     created_at__year=now.year).aggregate(total=Sum('avg'))['total']
            if water_monthly_cons:
                water_cons += water_monthly_cons

            water_daily_cons = DimensionDailyStat.objects.filter(dimension_attribute_id=water_da.id,
                                                                 created_at__year=now.year,
                                                                 created_at__month=now.month).aggregate(total=Sum('avg'))['total']
            if water_daily_cons:
                water_cons += water_daily_cons

            today = now.replace(hour=0, minute=0, second=0, microsecond=0)
            water_hourly_cons = DimensionHourlyStat.objects.filter(dimension_attribute_id=water_da.id,
                                                                   created_at__gte=today).aggregate(total=Sum('avg'))['total']
            if water_hourly_cons:
                water_cons += water_hourly_cons
            water_cons += water_da.calc_current_hour_cons(project_id)

        else:
            water_record = ManualRecord.objects.filter(Q(name__contains=now.year) & Q(name__contains='用水量'), gran=50).first()
            if water_record:
                data = json.loads(water_record.data)
                for monthly_data in data:
                    year, month = monthly_data.get('time').split('-')
                    value = monthly_data.get('value')
                    if value and int(year) == now.year:
                        water_cons += float(value)
        water_cons = water_cons / unit_count

        # 计算用气量。如果维度没有配置，就从手工录入查找
        gas_cons = 0
        last_gas_cons = 0
        gas_da = DimensionAttribute.objects.filter(dimension__type_name='分项', dimension__parent__isnull=True,
                                                   name__contains='总用气量', ec_type=40).first()
        if gas_da:
            # 当年用气量
            gas_monthly_cons = DimensionMonthlyStat.objects.filter(dimension_attribute_id=gas_da.id,
                                                                   created_at__year=now.year).aggregate(total=Sum('avg'))['total']
            if gas_monthly_cons:
                gas_cons += gas_monthly_cons

            gas_daily_cons = DimensionDailyStat.objects.filter(dimension_attribute_id=gas_da.id,
                                                               created_at__year=now.year,
                                                               created_at__month=now.month).aggregate(total=Sum('avg'))['total']
            if gas_daily_cons:
                gas_cons += gas_daily_cons
            today_begin, today_end = get_day_range()
            today_cons = DimensionHourlyStat.objects.filter(dimension_attribute_id=gas_da.id,
                                                            created_at__range=(today_begin, today_end)).aggregate(total=Sum('avg'))['total']
            if today_cons:
                gas_cons += today_cons
            gas_cons += gas_da.calc_current_hour_cons(project_id)
            # 上一年用气量
            last_gas_monthly_cons = DimensionMonthlyStat.objects.filter(dimension_attribute_id=gas_da.id,
                                                                        created_at__lt=last_now).aggregate(total=Sum('avg'))['total']
            if last_gas_monthly_cons:
                last_gas_cons += last_gas_monthly_cons

            last_gas_daily_cons = DimensionDailyStat.objects.filter(dimension_attribute_id=gas_da.id,
                                                                    created_at__year=(now.year - 1),
                                                                    created_at__month=now.month,
                                                                    created_at__lte=last_now).aggregate(total=Sum('avg'))['total']
            if last_gas_daily_cons:
                last_gas_cons += last_gas_daily_cons
        else:
            # 当年用气量
            gas_record = ManualRecord.objects.filter(Q(name__contains=now.year) & Q(name__contains='用气量'), gran=50).first()
            if gas_record:
                data = json.loads(gas_record.data)
                for monthly_data in data:
                    year, month = monthly_data.get('time').split('-')
                    value = monthly_data.get('value')
                    if value and int(year) == now.year:
                        gas_cons += float(value)
            # 上一年用气量
            last_gas_record = ManualRecord.objects.filter(Q(name__contains=(now.year - 1)) & Q(name__contains='用气量'), gran=50).first()
            if last_gas_record:
                data = json.loads(last_gas_record.data)
                for monthly_data in data:
                    year, month = monthly_data.get('time').split('-')
                    value = monthly_data.get('value')
                    if value and int(year) == now.year:
                        last_gas_cons += float(value)
        gas_cons = gas_cons / unit_count
        last_gas_cons = last_gas_cons / unit_count

        curr_kgce = power_cons * 0.1229 + gas_cons * 1.33
        last_kgce = last_power_cons * 0.1229 + last_gas_cons * 1.33
        curr_econs = power_cons * 0.5366
        curr_wcons = water_cons * 0.91
        curr_gcons = gas_cons * 1.96

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                "curr_kgce": round(curr_kgce, 2),
                "last_kgce": round(last_kgce, 2),
                "curr_econs": round(curr_econs, 2),
                "curr_wcons": round(curr_wcons, 2),
                "curr_gcons": round(curr_gcons, 2),
                "power_cons": round(power_cons, 2),
                "water_cons": round(water_cons, 2),
                "gas_cons": round(gas_cons, 2)
            }
        })

class DimensionEcQuota(viewsets.ModelViewSet):
    def list(self, request, *args, **kwargs):

        def get_next_month_start(y: int, m: int) -> datetime.datetime:
            if m == 12:
                return datetime.datetime(y + 1, 1, 1)
            else:
                return datetime.datetime(y, m + 1, 1)

        now = datetime.datetime.now()
        project_id = request.user.get('project_id')
        unit_name = request.query_params.get('unit_name', None)
        year = request.query_params.get('year', None)
        if year is not None:
            year = int(year)
        else:
            year = now.year

        month = request.query_params.get('month', None)
        if month is not None:
            month = int(month)
        else:
            month = now.month

        begin_dt = datetime.datetime(year, 1, 1)
        end_dt = get_next_month_start(year, month)

        prev_begin_dt = datetime.datetime(year - 1, 1, 1)
        prev_end_dt = get_next_month_start(year - 1, month)

        area = None
        persons = None
        power_cons = 0
        prev_power_cons = 0
        gas_cons = 0
        prev_gas_cons = 0
        water_cons = 0
        prev_water_cons = 0

        # 所有单位
        units = Dimension.objects.filter(type_name='单位', parent__isnull=False, ec_persons__isnull=False, unit_area__isnull=False)
        unit_count = 1
        if unit_name:
            unit_dim = Dimension.objects.filter(type_name="单位", name=unit_name).first()
            area = unit_dim.unit_area
            persons = unit_dim.ec_persons
            unit_count = units.count()
        else:
            area = units.filter(unit_area__isnull=False).aggregate(total=Sum('unit_area'))['total']
            persons = units.filter(ec_persons__isnull=False).aggregate(total=Sum('ec_persons'))['total']

        gas_da = DimensionAttribute.objects.filter(dimension__type_name='分项', dimension__parent__isnull=True,
                                                   name__contains='总用气量', ec_type=40).first()
        if gas_da:
            # 当年用气量
            gas_monthly_cons = DimensionMonthlyStat.objects.filter(dimension_attribute_id=gas_da.id,
                                                                   created_at__gte=begin_dt,
                                                                   created_at__lt=end_dt).aggregate(total=Sum('avg'))['total']
            if gas_monthly_cons:
                gas_cons += gas_monthly_cons

            if now.year == now.year and now.month == month:
                gas_daily_cons = DimensionDailyStat.objects.filter(dimension_attribute_id=gas_da.id,
                                                                   created_at__year=year,
                                                                   created_at__month=month).aggregate(total=Sum('avg'))['total']
                if gas_daily_cons:
                    gas_cons += gas_daily_cons

                today = now.replace(hour=0, minute=0, second=0, microsecond=0)
                gas_hourly_cons = DimensionHourlyStat.objects.filter(dimension_attribute_id=gas_da.id,
                                                                     created_at__gte=today).aggregate(total=Sum('avg'))['total']
                if gas_hourly_cons:
                    gas_cons += gas_hourly_cons
                gas_cons += gas_da.calc_current_hour_cons(project_id)

            # 上一年用气量
            prev_gas_monthly_cons = DimensionMonthlyStat.objects.filter(dimension_attribute_id=gas_da.id,
                                                                        created_at__gte=prev_begin_dt,
                                                                        created_at__lt=prev_end_dt
                                                                        ).aggregate(total=Sum('avg'))['total']
            if prev_gas_monthly_cons:
                prev_gas_cons += prev_gas_monthly_cons

            # # 今年剩余月份数据从去年拿
            # prev_remain_monthly_cons = DimensionMonthlyStat.objects.filter(dimension_attribute_id=gas_da.id,
            #                                                                created_at__year=(year - 1),
            #                                                                created_at__month__gt=month).aggregate(total=Sum('avg'))['total']
            # if prev_remain_monthly_cons:
            #     gas_cons += prev_remain_monthly_cons
        else:
            # 当年用气量
            gas_record = ManualRecord.objects.filter(Q(name__contains=year) & Q(name__contains='用气量'), gran=50).first()
            if gas_record:
                data = json.loads(gas_record.data)
                for monthly_data in data:
                    record_year, record_month = monthly_data.get('time').split('-')
                    value = monthly_data.get('value')
                    if value and int(record_year) == year:
                        gas_cons += float(value)

            # 上一年用气量
            prev_gas_record = ManualRecord.objects.filter(Q(name__contains=(year - 1)) & Q(name__contains='用气量'), gran=50).first()
            if prev_gas_record:
                data = json.loads(prev_gas_record.data)
                for monthly_data in data:
                    record_year, record_month = monthly_data.get('time').split('-')
                    value = monthly_data.get('value')
                    if value and int(record_year) == year:
                        prev_gas_cons += float(value)
                    # 今年剩余月份数据从去年拿
                    if year == now.year and int(record_month) > month and value:
                        gas_cons += float(value)

        gas_cons = gas_cons / unit_count
        prev_gas_cons = prev_gas_cons / unit_count

        if unit_name is None:
            water_da = DimensionAttribute.objects.filter(dimension__type_name='分项', dimension__parent__isnull=True,
                                                         name__contains='总用水量', ec_type=20).first()
        else:
            water_da = DimensionAttribute.objects.filter(dimension__type_name='单位', name=f'{unit_name}用水量', ec_type=20).first()
        if water_da:
            # 当年用水量
            water_monthly_cons = DimensionMonthlyStat.objects.filter(dimension_attribute_id=water_da.id,
                                                                     created_at__gte=begin_dt,
                                                                     created_at__lt=end_dt).aggregate(total=Sum('avg'))['total']
            if water_monthly_cons:
                water_cons += water_monthly_cons

            if now.year == year and now.month == month:
                water_daily_cons = DimensionDailyStat.objects.filter(dimension_attribute_id=water_da.id,
                                                                     created_at__year=year,
                                                                     created_at__month=month).aggregate(total=Sum('avg'))['total']
                if water_daily_cons:
                    water_cons += water_daily_cons

                today = now.replace(hour=0, minute=0, second=0, microsecond=0)
                water_hourly_cons = DimensionHourlyStat.objects.filter(dimension_attribute_id=water_da.id,
                                                                       created_at__gte=today).aggregate(total=Sum('avg'))['total']
                if water_hourly_cons:
                    water_cons += water_hourly_cons
                water_cons += water_da.calc_current_hour_cons(project_id)

            # 上一年用水量
            prev_water_monthly_cons = DimensionMonthlyStat.objects.filter(dimension_attribute_id=water_da.id,
                                                                          created_at__gte=prev_begin_dt,
                                                                          created_at__lt=prev_end_dt).aggregate(total=Sum('avg'))['total']
            if prev_water_monthly_cons:
                prev_water_cons += prev_water_monthly_cons

            # # 今年剩余月份数据从去年拿
            # prev_remain_monthly_cons = DimensionMonthlyStat.objects.filter(dimension_attribute_id=water_da.id,
            #                                                                created_at__year=(year - 1),
            #                                                                created_at__month__gt=now.month).aggregate(total=Sum('avg'))['total']
            # if prev_remain_monthly_cons:
            #     water_cons += prev_remain_monthly_cons
        else:
            # 当年用水量
            water_record = ManualRecord.objects.filter(Q(name__contains=year) & Q(name__contains='用水量'), gran=50).first()
            if water_record:
                data = json.loads(water_record.data)
                for monthly_data in data:
                    record_year, month = monthly_data.get('time').split('-')
                    value = monthly_data.get('value')
                    if value and int(record_year) == year:
                        water_cons += float(value)

            # 上一年用水量
            prev_water_record = ManualRecord.objects.filter(Q(name__contains=(year - 1)) & Q(name__contains='用水量'), gran=50).first()
            if prev_water_record:
                data = json.loads(prev_water_record.data)
                for monthly_data in data:
                    record_year, record_month = monthly_data.get('time').split('-')
                    value = monthly_data.get('value')
                    if value and int(record_year) == year:
                        prev_water_cons += float(value)
                    # 今年剩余月份数据从去年拿
                    if year == now.year and int(record_month) > month and value:
                        water_cons += float(value)
        # water_cons = water_cons / unit_count
        # prev_water_cons = prev_water_cons / unit_count

        # 用电量
        if unit_name:
            power_dim = Dimension.objects.filter(type_name='单位', name=unit_name, parent_id__isnull=False).first()
            power_da = DimensionAttribute.objects.filter(dimension_id=power_dim.id, name__contains=f'{power_dim.name}用电量').first()
        else:
            root_dimension_ids = Dimension.objects.filter(type_name='分项', parent_id__isnull=True).values_list('id', flat=True)
            power_da = DimensionAttribute.objects.filter(dimension_id__in=root_dimension_ids, ec_type__isnull=False,
                                                         name__contains=f'总用电量').first()
            if power_da is None:
                power_dim = Dimension.objects.filter(type_name='单位', parent_id__isnull=True).first()
                power_da = DimensionAttribute.objects.filter(dimension_id=power_dim.id, ec_type__isnull=False, name__contains=f'总用电量').first()

        if power_da:
            # 当年用电量
            power_monthly_cons = DimensionMonthlyStat.objects.filter(dimension_attribute_id=power_da.id,
                                                                     created_at__gte=begin_dt,
                                                                     created_at__lt=end_dt).aggregate(total=Sum('avg'))['total']
            if power_monthly_cons:
                power_cons += power_monthly_cons

            if now.year == year and now.month == month:
                power_daily_cons = DimensionDailyStat.objects.filter(dimension_attribute_id=power_da.id,
                                                                     created_at__year=now.year,
                                                                     created_at__month=now.month).aggregate(total=Sum('avg'))['total']
                if power_daily_cons:
                    power_cons += power_daily_cons

                power_hourly_cons = DimensionHourlyStat.objects.filter(dimension_attribute_id=power_da.id,
                                                                       created_at__year=now.year,
                                                                       created_at__month=now.month,
                                                                       created_at__day=now.day).aggregate(total=Sum('avg'))['total']
                if power_hourly_cons:
                    power_cons += power_hourly_cons

                power_cons += power_da.calc_current_hour_cons(project_id)

            # # -没有到的月份用电量从往年取
            # comp_stats = DimensionMonthlyStat.objects.filter(dimension_attribute_id=power_da.id, created_at__year=(year - 1),
            #                                                  created_at__month__gt=now.month)
            # comp_power_cons = comp_stats.aggregate(total=Sum('avg'))['total']
            # if comp_power_cons:
            #     power_cons += comp_power_cons

            # 上一年用电量
            last_power_monthly_cons = DimensionMonthlyStat.objects.filter(dimension_attribute_id=power_da.id,
                                                                          created_at__gte=prev_begin_dt,
                                                                          created_at__lt=prev_end_dt).aggregate(total=Sum('avg'))['total']
            if last_power_monthly_cons:
                prev_power_cons += last_power_monthly_cons

        # 标准煤
        kgce = power_cons * 0.1229 + gas_cons * 1.33
        # 单位面积标准煤
        unit_sq_kgce = kgce / area

        # 上一年标准煤
        prev_kgce = prev_power_cons * 0.1229 + prev_gas_cons * 1.33
        # 上一年单位面积标准煤
        prev_unit_sq_kgce = prev_kgce / area

        # 单位面积能耗同比
        yoy_unit_sq_kgce = (unit_sq_kgce - prev_unit_sq_kgce) / prev_unit_sq_kgce * 100 if prev_unit_sq_kgce else '--'

        # 人均
        p_avg_kgce = kgce / persons
        # 上一年人均
        prev_p_avg_kgce = prev_kgce / persons
        # 人均同比
        yoy_p_avg_kgce = (p_avg_kgce - prev_p_avg_kgce) / prev_p_avg_kgce * 100 if prev_p_avg_kgce else '--'

        # 人均水耗
        p_avg_wcons = water_cons / persons
        # 上一年人均水耗
        prev_p_avg_wcons = prev_water_cons / persons
        # 人均水耗同比
        yoy_p_avg_wcons = (p_avg_wcons - prev_p_avg_wcons) / prev_p_avg_wcons * 100 if prev_p_avg_wcons else '--'

        # 碳排放
        kgco2 = power_cons * 0.5366 + gas_cons * 1.96
        # 上一年碳排放
        prev_kgco2 = prev_power_cons * 0.5366 + prev_gas_cons * 1.96
        # 人均碳排放
        unit_sq_kgco2 = kgco2 / persons
        # 上一年人均碳排放
        prev_unit_sq_kgco2 = prev_kgco2 / persons
        # 人均碳排放同比
        yoy_unit_sq_kgco2 = (unit_sq_kgco2 - prev_unit_sq_kgco2) / prev_unit_sq_kgco2 * 100 if prev_unit_sq_kgco2 else '--'

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'unit_sq_kgce': round(unit_sq_kgce, 2),
                'yoy_unit_sq_kgce': round(yoy_unit_sq_kgce, 2) if not isinstance(yoy_unit_sq_kgce, str) else yoy_unit_sq_kgce,
                'p_avg_kgce': round(p_avg_kgce, 2),
                'yoy_p_avg_kgce': round(yoy_p_avg_kgce, 2) if not isinstance(yoy_p_avg_kgce, str) else yoy_p_avg_kgce,
                'p_avg_wcons': round(p_avg_wcons, 2),
                'yoy_p_avg_wcons': round(yoy_p_avg_wcons, 2) if not isinstance(yoy_p_avg_wcons, str) else yoy_p_avg_wcons,
                'unit_sq_kgco2': round(unit_sq_kgco2, 2),
                'yoy_unit_sq_kgco2': round(yoy_unit_sq_kgco2, 2) if not isinstance(yoy_unit_sq_kgco2, str) else yoy_unit_sq_kgco2,
            }
        })

class DimensionEcTrend(viewsets.ModelViewSet):
    def list(self, request, *args, **kwargs):
        """
        能耗趋势，只保留水电气数据，要按年，按月，按日有不同的显示
        """
        now = datetime.datetime.now()
        project_id = request.user.get('project_id')
        time_type = request.query_params.get('time_type', 'hr')

        unit_name = request.query_params.get('unit_name', None)
        unit_count = 1
        root_dimension_ids = Dimension.objects.filter(type_name='分项', parent_id__isnull=True).values_list('id', flat=True)

        if unit_name is not None:
            power_da = DimensionAttribute.objects.filter(dimension__name=unit_name, name__contains=f'{unit_name}用电量', ec_type=10).first()
            unit_count = Dimension.objects.filter(type_name='单位', parent_id__isnull=True, ec_persons__isnull=False, unit_area__isnull=False).count
        else:
            power_da = DimensionAttribute.objects.filter(dimension_id__in=root_dimension_ids, ec_type__isnull=False,
                                                         name__contains=f'总用电量').first()
            if power_da is None:
                power_dim = Dimension.objects.filter(type_name='单位', parent_id__isnull=True).first()
                power_da = DimensionAttribute.objects.filter(dimension_id=power_dim.id, ec_type__isnull=False, name__contains=f'总用电量').first()

        water_da = DimensionAttribute.objects.filter(dimension_id__in=root_dimension_ids, ec_type=20, name__contains=f'总用水量').first()
        gas_da = DimensionAttribute.objects.filter(dimension_id__in=root_dimension_ids, ec_type=40, name__contains=f'总用气量').first()

        if time_type == 'yr':
            # 年。 从2024一直到2034十年托管期
            from_year = int(request.query_params.get('from_year', 2024))
            years = []
            power_cons = []
            water_cons = []
            gas_cons = []
            for idx in range(10):
                # 年份
                year = from_year + idx
                years.append(f'{year}年')
                # 总用电量
                year_power = DimensionMonthlyStat.objects.filter(dimension_attribute_id=power_da.id,
                                                                 created_at__year=year).aggregate(total=Sum('avg'))['total']
                if year_power is None:
                    year_power = 0

                if year == now.year:
                    current_month_power = DimensionDailyStat.objects.filter(dimension_attribute_id=power_da.id, created_at__year=year,
                                                                            created_at__month=now.month).aggregate(total=Sum('avg'))['total']
                    if current_month_power is not None:
                        year_power += current_month_power

                    # - 今日用电量
                    today_begin = now.replace(hour=0, minute=0, second=0, microsecond=0)
                    today_power_cons = power_da.calc_current_hour_cons(project_id)
                    today_power_cons_query = DimensionHourlyStat.objects.filter(dimension_attribute_id=power_da.id,
                                                                                created_at__gte=today_begin).aggregate(total=Sum('avg'))['total']
                    if today_power_cons_query:
                        today_power_cons += today_power_cons_query
                    year_power += today_power_cons

                power_cons.append(f'{year_power}')
                # 总用水量
                year_water = 0
                if water_da is None:
                    water_record = ManualRecord.objects.filter(Q(name__contains=year) & Q(name__contains='用水量'), gran=50).first()
                    if water_record:
                        data = json.loads(water_record.data)
                        for monthly_data in data:
                            record_year, month = monthly_data.get('time').split('-')
                            value = monthly_data.get('value')
                            if value and int(record_year) == year:
                                year_water += float(value)
                    water_cons.append(year_water)
                else:
                    year_water = DimensionMonthlyStat.objects.filter(dimension_attribute_id=water_da.id,
                                                                     created_at__year=year).aggregate(total=Sum('avg'))['total']
                    if year_water is None:
                        year_water = 0

                    if year == now.year:
                        current_month_water = DimensionDailyStat.objects.filter(dimension_attribute_id=water_da.id, created_at__year=year,
                                                                                created_at__month=now.month).aggregate(total=Sum('avg'))['total']
                        if current_month_water is not None:
                            year_water += current_month_water

                        # - 今日用水量
                        today_begin = now.replace(hour=0, minute=0, second=0, microsecond=0)
                        today_water_cons = water_da.calc_current_hour_cons(project_id)
                        today_water_cons_query = DimensionHourlyStat.objects.filter(dimension_attribute_id=water_da.id,
                                                                                    created_at__gte=today_begin).aggregate(total=Sum('avg'))['total']
                        if today_water_cons_query:
                            today_water_cons += today_water_cons_query
                        year_water += today_water_cons
                    water_cons.append(f'{year_water}')
                # 总用气量
                year_gas = 0
                if gas_da is None:
                    gas_record = ManualRecord.objects.filter(Q(name__contains=year) & Q(name__contains='用气量'), gran=50).first()
                    if gas_record:
                        data = json.loads(gas_record.data)
                        for monthly_data in data:
                            record_year, month = monthly_data.get('time').split('-')
                            value = monthly_data.get('value')
                            if value and int(record_year) == year:
                                year_gas += float(value)
                else:
                    year_gas = DimensionMonthlyStat.objects.filter(dimension_attribute_id=gas_da.id,
                                                                   created_at__year=year).aggregate(total=Sum('avg'))['total']
                    if year_gas is None:
                        year_gas = 0

                    if year == now.year:
                        current_month_gas = DimensionDailyStat.objects.filter(dimension_attribute_id=gas_da.id, created_at__year=year,
                                                                              created_at__month=now.month).aggregate(total=Sum('avg'))['total']
                        if current_month_gas is not None:
                            year_gas += current_month_gas

                        # - 今日用气量
                        today_begin = now.replace(hour=0, minute=0, second=0, microsecond=0)
                        today_gas_cons = gas_da.calc_current_hour_cons(project_id)
                        today_gas_cons_query = DimensionHourlyStat.objects.filter(dimension_attribute_id=gas_da.id,
                                                                                  created_at__gte=today_begin).aggregate(total=Sum('avg'))['total']
                        if today_gas_cons_query:
                            today_gas_cons += today_gas_cons_query
                        year_gas += today_gas_cons
                    gas_cons.append(f'{year_gas}')

            return Response({
                'status': status.HTTP_200_OK,
                'data': {
                    "data_series": [
                        {
                            "name": "用电量",
                            "ec_type": 10,
                            "stats": power_cons,
                            "unit": 'kWh',
                            "time_range": years
                        },
                        {
                            "name": "用水量",
                            "ec_type": 20,
                            "stats": water_cons,
                            "unit": 't',
                            "time_range": years
                        },
                        {
                            "name": "用气量",
                            "ec_type": 40,
                            "stats": gas_cons,
                            "unit": 'm³',
                            "time_range": years
                        },
                    ]
                }
            })
        elif time_type == 'mo' or time_type == 'mth':
            # 月。今年和去年的电，水，气每月用量
            power_cons = []
            prev_power_cons = []
            water_cons = []
            prev_water_cons = []
            gas_cons = []
            prev_gas_cons = []

            # 用电
            power_monthly_cons = list(DimensionMonthlyStat.objects.filter(dimension_attribute_id=power_da.id, created_at__year=now.year))
            power_monthly_cons_dict = {}
            for dms in power_monthly_cons:
                power_monthly_cons_dict[dms.created_at.month] = dms.avg

            prev_monthly_cons = list(DimensionMonthlyStat.objects.filter(dimension_attribute_id=power_da.id, created_at__year=(now.year - 1)))
            prev_power_monthly_cons_dict = {}
            for pdms in prev_monthly_cons:
                prev_power_monthly_cons_dict[pdms.created_at.month] = pdms.avg

            # 用水
            if water_da is None:
                water_record = ManualRecord.objects.filter(Q(name__contains=now.year) & Q(name__contains='用水量'), gran=50).first()
                water_record_dict = {}
                if water_record:
                    data = json.loads(water_record.data)
                    for monthly_data in data:
                        record_year, month = monthly_data.get('time').split('-')
                        value = monthly_data.get('value')
                        if int(record_year) == now.year:
                            water_record_dict[int(month)] = float(value) / unit_count if value else 0

                prev_water_record = ManualRecord.objects.filter(Q(name__contains=(now.year - 1)) & Q(name__contains='用水量'), gran=50).first()
                prev_water_record_dict = {}
                if prev_water_record:
                    data = json.loads(prev_water_record.data)
                    for monthly_data in data:
                        record_year, month = monthly_data.get('time').split('-')
                        value = monthly_data.get('value')
                        if int(record_year) == now.year - 1:
                            prev_water_record_dict[int(month)] = float(value) / unit_count if value else 0
            else:
                water_monthly_cons = list(DimensionMonthlyStat.objects.filter(dimension_attribute_id=water_da.id, created_at__year=now.year))
                water_record_dict = {}
                for dms in water_monthly_cons:
                    water_record_dict[dms.created_at.month] = dms.avg

                prev_monthly_cons = list(DimensionMonthlyStat.objects.filter(dimension_attribute_id=water_da.id, created_at__year=(now.year - 1)))
                prev_water_record_dict = {}
                for pdms in prev_monthly_cons:
                    prev_water_record_dict[pdms.created_at.month] = pdms.avg

            # 用气
            if gas_da is None:
                gas_record = ManualRecord.objects.filter(Q(name__contains=now.year) & Q(name__contains='用气量'), gran=50).first()
                gas_record_dict = {}
                if gas_record:
                    data = json.loads(gas_record.data)
                    for monthly_data in data:
                        record_year, month = monthly_data.get('time').split('-')
                        value = monthly_data.get('value')
                        if int(record_year) == now.year:
                            gas_record_dict[int(month)] = float(value) / unit_count if value else 0

                prev_gas_record = ManualRecord.objects.filter(Q(name__contains=(now.year - 1)) & Q(name__contains='用气量'), gran=50).first()
                prev_gas_record_dict = {}
                if prev_gas_record:
                    data = json.loads(prev_gas_record.data)
                    for monthly_data in data:
                        record_year, month = monthly_data.get('time').split('-')
                        value = monthly_data.get('value')
                        if int(record_year) == now.year - 1:
                            prev_gas_record_dict[int(month)] = float(value) / unit_count if value else 0
            else:
                gas_monthly_cons = list(DimensionMonthlyStat.objects.filter(dimension_attribute_id=gas_da.id, created_at__year=now.year))
                gas_record_dict = {}
                for dms in gas_monthly_cons:
                    gas_record_dict[dms.created_at.month] = dms.avg

                prev_monthly_cons = list(DimensionMonthlyStat.objects.filter(dimension_attribute_id=gas_da.id, created_at__year=(now.year - 1)))
                prev_gas_record_dict = {}
                for pdms in prev_monthly_cons:
                    prev_gas_record_dict[pdms.created_at.month] = pdms.avg

            time_range = []
            for month in range(1, 13):
                time_range.append(f'{month:02}月')
                # 当年用电量
                if month == now.month:
                    # 当月用电量
                    current_month_cons = DimensionDailyStat.objects.filter(dimension_attribute_id=power_da.id,
                                                                           created_at__year=now.year,
                                                                           created_at__month=now.month).aggregate(total=Sum('avg'))['total']
                    if current_month_cons is None:
                        current_month_cons = 0
                    # - 今日用电量
                    today_begin = now.replace(hour=0, minute=0, second=0, microsecond=0)
                    today_power_cons = power_da.calc_current_hour_cons(project_id)
                    today_power_cons_query = DimensionHourlyStat.objects.filter(dimension_attribute_id=power_da.id,
                                                                                created_at__gte=today_begin).aggregate(total=Sum('avg'))['total']
                    if today_power_cons_query:
                        today_power_cons += today_power_cons_query
                    current_month_cons += today_power_cons
                    power_cons.append(f'{round(current_month_cons, 2)}')
                elif month > now.month:
                    # power_cons.append(None)
                    pass
                else:
                    power_cons.append(power_monthly_cons_dict.get(month, 0))
                # 上一年用电量
                prev_power_cons.append(prev_power_monthly_cons_dict.get(month, 0))

                # 当年用水量
                if month == now.month:
                    # 当月用水量
                    current_month_cons = DimensionDailyStat.objects.filter(dimension_attribute_id=water_da.id,
                                                                           created_at__year=now.year,
                                                                           created_at__month=now.month).aggregate(total=Sum('avg'))['total']
                    if current_month_cons is None:
                        current_month_cons = 0
                    # - 今日用水量
                    today_begin = now.replace(hour=0, minute=0, second=0, microsecond=0)
                    today_water_cons = water_da.calc_current_hour_cons(project_id)
                    today_water_cons_query = DimensionHourlyStat.objects.filter(dimension_attribute_id=water_da.id,
                                                                                created_at__gte=today_begin).aggregate(total=Sum('avg'))['total']
                    if today_water_cons_query:
                        today_water_cons += today_water_cons_query
                    current_month_cons += today_water_cons
                    water_cons.append(f'{round(current_month_cons, 2)}')
                elif month > now.month:
                    # water_cons.append(0)
                    pass
                else:
                    water_cons.append(water_record_dict.get(month, 0))
                # 上一年用水量
                prev_water_cons.append(prev_water_record_dict.get(month, 0))

                # 当年用气量
                if month == now.month:
                    # 当月用气量
                    current_month_cons = DimensionDailyStat.objects.filter(dimension_attribute_id=gas_da.id,
                                                                           created_at__year=now.year,
                                                                           created_at__month=now.month).aggregate(total=Sum('avg'))['total']
                    if current_month_cons is None:
                        current_month_cons = 0
                    # - 今日用气量
                    today_begin = now.replace(hour=0, minute=0, second=0, microsecond=0)
                    today_gas_cons = gas_da.calc_current_hour_cons(project_id)
                    today_gas_cons_query = DimensionHourlyStat.objects.filter(dimension_attribute_id=gas_da.id,
                                                                              created_at__gte=today_begin).aggregate(total=Sum('avg'))['total']
                    if today_gas_cons_query:
                        today_gas_cons += today_gas_cons_query
                    current_month_cons += today_gas_cons
                    gas_cons.append(f'{round(current_month_cons, 2)}')
                elif month > now.month:
                    # gas_cons.append(None)
                    pass
                else:
                    gas_cons.append(gas_record_dict.get(month, 0))
                # 上一年用气量
                prev_gas_cons.append(prev_gas_record_dict.get(month, 0))

            return Response({
                'status': status.HTTP_200_OK,
                'data': {
                    'data_series': [
                        {
                            'name': '用电量',
                            'stats': power_cons,
                            'yoy_stats': prev_power_cons,
                            'time_range': time_range,
                            'unit': 'kWh'
                        },
                        {
                            'name': '用水量',
                            'stats': water_cons,
                            'yoy_stats': prev_water_cons,
                            'time_range': time_range,
                            'unit': 't'
                        },
                        {
                            'name': '用气量',
                            'stats': gas_cons,
                            'yoy_stats': prev_gas_cons,
                            'time_range': time_range,
                            'unit': 'm³'
                        }
                    ]
                }
            })
        elif time_type == 'di':
            power_cons = []
            prev_power_cons = []
            water_cons = []
            prev_water_cons = []
            gas_cons = []
            prev_gas_cons = []

            # 用电
            power_daily_cons = list(DimensionDailyStat.objects.filter(dimension_attribute_id=power_da.id,
                                                                      created_at__year=now.year,
                                                                      created_at__month=now.month))
            power_daily_cons_dict = {}
            for dms in power_daily_cons:
                power_daily_cons_dict[dms.created_at.day] = dms.avg

            prev_power_daily_cons = list(DimensionDailyStat.objects.filter(dimension_attribute_id=power_da.id,
                                                                           created_at__year=(now.year - 1),
                                                                           created_at__month=now.month))
            prev_power_daily_cons_dict = {}
            for pdms in prev_power_daily_cons:
                prev_power_daily_cons_dict[pdms.created_at.day] = pdms.avg

            # 用水
            if water_da is None:
                water_record = ManualRecord.objects.filter(Q(name__contains=now.year) & Q(name__contains='用水量'), gran=50).first()
                water_record_dict = {}
                if water_record:
                    data = json.loads(water_record.data)
                    for monthly_data in data:
                        record_year, month = monthly_data.get('time').split('-')
                        value = monthly_data.get('value')
                        if int(record_year) == now.year and int(month) == now.month:
                            month_range = calendar.monthrange(now.year, now.month)[1]
                            avg = float(value) / month_range
                            for day in range(month_range):
                                water_record_dict[int(day) + 1] = float(avg) / unit_count if avg else 0

                prev_water_record = ManualRecord.objects.filter(Q(name__contains=(now.year - 1)) & Q(name__contains='用水量'), gran=50).first()
                prev_water_record_dict = {}
                if prev_water_record:
                    data = json.loads(prev_water_record.data)
                    for monthly_data in data:
                        record_year, month = monthly_data.get('time').split('-')
                        value = monthly_data.get('value')
                        if int(record_year) == now.year - 1 and int(month) == now.month:
                            month_range = calendar.monthrange(now.year - 1, now.month)[1]
                            avg = float(value) / month_range
                            for day in range(month_range):
                                prev_water_record_dict[int(day) + 1] = float(avg) / unit_count if avg else 0
            else:
                water_daily_cons = list(DimensionDailyStat.objects.filter(dimension_attribute_id=water_da.id,
                                                                          created_at__year=now.year,
                                                                          created_at__month=now.month))
                water_record_dict = {}
                for dms in water_daily_cons:
                    water_record_dict[dms.created_at.day] = dms.avg

                prev_daily_cons = list(DimensionDailyStat.objects.filter(dimension_attribute_id=water_da.id,
                                                                         created_at__year=(now.year - 1),
                                                                         created_at__month=now.month))
                prev_water_record_dict = {}
                for pdms in prev_daily_cons:
                    prev_water_record_dict[pdms.created_at.day] = pdms.avg

            # 用气
            if gas_da is None:
                gas_record = ManualRecord.objects.filter(Q(name__contains=now.year) & Q(name__contains='用气量'), gran=50).first()
                gas_record_dict = {}
                if gas_record:
                    data = json.loads(gas_record.data)
                    for monthly_data in data:
                        record_year, month = monthly_data.get('time').split('-')
                        value = monthly_data.get('value')
                        if int(record_year) == now.year and int(month) == now.month:
                            month_range = calendar.monthrange(now.year - 1, now.month)[1]
                            avg = float(value) / month_range
                            for day in range(month_range):
                                gas_record_dict[int(day) + 1] = float(avg) / unit_count if avg else 0

                prev_gas_record = ManualRecord.objects.filter(Q(name__contains=(now.year - 1)) & Q(name__contains='用气量'), gran=50).first()
                prev_gas_record_dict = {}
                if prev_gas_record:
                    data = json.loads(prev_gas_record.data)
                    for monthly_data in data:
                        record_year, month = monthly_data.get('time').split('-')
                        value = monthly_data.get('value')
                        if int(record_year) == now.year - 1 and int(month) == now.month:
                            month_range = calendar.monthrange(now.year - 1, now.month)[1]
                            avg = float(value) / month_range
                            for day in range(month_range):
                                prev_gas_record_dict[int(day) + 1] = float(avg) / unit_count if avg else 0
            else:
                gas_daily_cons = list(DimensionDailyStat.objects.filter(dimension_attribute_id=gas_da.id,
                                                                        created_at__year=now.year,
                                                                        created_at__month=now.month))
                gas_record_dict = {}
                for dms in gas_daily_cons:
                    gas_record_dict[dms.created_at.day] = dms.avg

                prev_daily_cons = list(DimensionDailyStat.objects.filter(dimension_attribute_id=gas_da.id,
                                                                         created_at__year=(now.year - 1),
                                                                         created_at__month=now.month))
                prev_gas_record_dict = {}
                for pdms in prev_daily_cons:
                    prev_gas_record_dict[pdms.created_at.day] = pdms.avg

            time_range = []
            month_range = calendar.monthrange(now.year - 1, now.month)[1]
            for day in range(month_range):
                time_range.append(f'{day:02}日')
                # 当年用电量
                if day + 1 == now.day:
                    # - 今日用电量
                    today_begin = now.replace(hour=0, minute=0, second=0, microsecond=0)
                    today_power_cons = power_da.calc_current_hour_cons(project_id)
                    today_power_cons_query = DimensionHourlyStat.objects.filter(dimension_attribute_id=power_da.id,
                                                                                created_at__gte=today_begin).aggregate(total=Sum('avg'))['total']
                    if today_power_cons_query:
                        today_power_cons += today_power_cons_query
                    power_cons.append(f'{round(today_power_cons, 2)}')
                elif day + 1 > now.day:
                    # power_cons.append(0)
                    pass
                else:
                    power_cons.append(power_daily_cons_dict.get(day + 1, 0))
                # 上一年用电量
                prev_power_cons.append(prev_power_daily_cons_dict.get(day + 1, 0))

                # 当年用水量
                if day + 1 == now.day:
                    # - 今日用水量
                    today_begin = now.replace(hour=0, minute=0, second=0, microsecond=0)
                    today_water_cons = water_da.calc_current_hour_cons(project_id)
                    today_water_cons_query = DimensionHourlyStat.objects.filter(dimension_attribute_id=water_da.id,
                                                                                created_at__gte=today_begin).aggregate(total=Sum('avg'))['total']
                    if today_water_cons_query:
                        today_water_cons += today_water_cons_query
                    water_cons.append(f'{round(today_water_cons, 2)}')
                elif day + 1 > now.day:
                    # water_cons.append(0)
                    pass
                else:
                    water_cons.append(water_record_dict.get(day + 1, 0))
                # 上一年用水量
                prev_water_cons.append(prev_water_record_dict.get(day + 1, 0))

                # 当年用气量
                if day + 1 == now.day:
                    # - 今日用水量
                    today_begin = now.replace(hour=0, minute=0, second=0, microsecond=0)
                    today_gas_cons = gas_da.calc_current_hour_cons(project_id)
                    today_gas_cons_query = DimensionHourlyStat.objects.filter(dimension_attribute_id=gas_da.id,
                                                                              created_at__gte=today_begin).aggregate(total=Sum('avg'))['total']
                    if today_gas_cons_query:
                        today_gas_cons += today_gas_cons_query
                    gas_cons.append(f'{round(today_gas_cons, 2)}')
                elif day + 1 > now.day:
                    pass
                    # gas_cons.append(0)
                else:
                    gas_cons.append(gas_record_dict.get(day + 1, 0))
                # 上一年用气量
                prev_gas_cons.append(prev_gas_record_dict.get(day + 1, 0))

            return Response({
                'status': status.HTTP_200_OK,
                'data': {
                    'data_series': [
                        {
                            'name': '用电量',
                            'stats': power_cons,
                            'yoy_stats': prev_power_cons,
                            'time_range': time_range,
                            'unit': 'kWh'
                        },
                        {
                            'name': '用水量',
                            'stats': water_cons,
                            'yoy_stats': prev_water_cons,
                            'time_range': time_range,
                            'unit': 't'
                        },
                        {
                            'name': '用气量',
                            'stats': gas_cons,
                            'yoy_stats': prev_gas_cons,
                            'time_range': time_range,
                            'unit': 'm³'
                        }
                    ]
                }
            })

        elif time_type == 'hr':
            begin, end = get_day_range(now)

            power_cons = []
            water_cons = []
            gas_cons = []

            power_dhs = DimensionHourlyStat.objects.filter(dimension_attribute_id=power_da.id, created_at__range=(begin, end))
            power_dhs_dict = {stat.created_at.hour: stat.avg for stat in power_dhs}
            # 插入当前时用电量
            current_hour_cons = power_da.calc_current_hour_cons(project_id)
            power_dhs_dict[now.hour] = f'{current_hour_cons}'

            # 用水
            if water_da is None:
                water_record = ManualRecord.objects.filter(Q(name__contains=now.year) & Q(name__contains='用水量'), gran=50).first()
                water_record_dict = {}
                if water_record:
                    value = 0
                    data = json.loads(water_record.data)
                    monthly_data = data.get(f'{now.year}-{now.month:02}', None)
                    if monthly_data:
                        value = monthly_data.get('value', None)
                        if value:
                            monthrange = calendar.monthrange(now.year, now.month)[1]
                            value = round(value / monthrange / 24 / unit_count, 2)
                    for hour in range(0, now.hour):
                        water_record_dict[hour] = value
            else:
                water_dhs = DimensionHourlyStat.objects.filter(dimension_attribute_id=water_da.id, created_at__range=(begin, end))
                water_record_dict = {stat.created_at.hour: stat.avg for stat in water_dhs}
                # 插入当前时用水量
                current_hour_cons = water_da.calc_current_hour_cons(project_id)
                water_record_dict[now.hour] = f'{current_hour_cons}'

            # 用气
            if gas_da is None:
                gas_record = ManualRecord.objects.filter(Q(name__contains=now.year) & Q(name__contains='用气量'), gran=50).first()
                gas_record_dict = {}
                if gas_record:
                    value = 0
                    data = json.loads(gas_record.data)
                    monthly_data = data.get(f'{now.year}-{now.month:02}', None)
                    if monthly_data:
                        value = monthly_data.get('value', None)
                        if value:
                            monthrange = calendar.monthrange(now.year, now.month)[1]
                            value = round(value / monthrange / 24 / unit_count, 2)
                    for hour in range(0, now.hour):
                        gas_record_dict[hour] = value
            else:
                gas_dhs = DimensionHourlyStat.objects.filter(dimension_attribute_id=gas_da.id, created_at__range=(begin, end))
                gas_record_dict = {stat.created_at.hour: stat.avg for stat in gas_dhs}
                # 插入当前时用水量
                current_hour_cons = gas_da.calc_current_hour_cons(project_id)
                gas_record_dict[now.hour] = f'{current_hour_cons}'

            time_range = []
            for hour in range(0, 24):
                time_range.append(f'{hour}时')
                if hour > now.hour:
                    continue
                power_cons.append(power_dhs_dict.get(hour, 0))
                water_cons.append(water_record_dict.get(hour, 0))
                gas_cons.append(gas_record_dict.get(hour, 0))

            return Response({
                'status': status.HTTP_200_OK,
                'data': {
                    'data_series': [
                        {
                            'name': '用电量',
                            'stats': power_cons,
                            'time_range': time_range,
                            'unit': 'kWh'
                        },
                        # {
                        #     'name': '用水量',
                        #     'stats': water_cons,
                        #     'time_range': time_range,
                        #     'unit': 't'
                        # },
                        # {
                        #     'name': '用气量',
                        #     'stats': gas_cons,
                        #     'time_range': time_range,
                        #     'unit': 'm³'
                        # }
                    ]
                }
            })

        return Response({
            'status': status.HTTP_200_OK,
            'data': {

            }
        })


class EcTotalView(viewsets.ModelViewSet):
    def list(self, request, *args, **kwargs):
        now = datetime.datetime.now()
        project_id = request.user.get('project_id')
        unit_name = request.query_params.get('unit_name', None)
        unit_count = 1
        if unit_name is not None:
            unit_dimension = Dimension.objects.get(name=unit_name, type_name='单位')
            power_da = DimensionAttribute.objects.filter(dimension__name=unit_name, name__contains=f'{unit_dimension.name}用电量', ec_type=10).first()
            unit_count = Dimension.objects.filter(type_name='单位', parent_id__isnull=True, ec_persons__isnull=False, unit_area__isnull=False).count
            building_area = unit_dimension.unit_area
            ec_nop = unit_dimension.ec_persons
        else:
            # 总用电使用配电房电表读数
            power_da = DimensionAttribute.objects.filter(dimension__type_name='分项', dimension__parent__isnull=True,
                                                         name__contains='总用电量', ec_type=10).first()
            building_area = Dimension.objects.filter(type_name='单位', unit_area__isnull=False).aggregate(total=Sum('unit_area'))['total']
            ec_nop = Dimension.objects.filter(type_name='单位', ec_persons__isnull=False).aggregate(total=Sum('ec_persons'))['total']

        power_cons = 0
        history_month_cons = DimensionMonthlyStat.objects.filter(dimension_attribute_id=power_da.id).aggregate(total=Sum('avg'))['total']
        if history_month_cons:
            power_cons += history_month_cons
        current_month_cons = DimensionDailyStat.objects.filter(dimension_attribute_id=power_da.id, created_at__year=now.year,
                                                               created_at__month=now.month).aggregate(total=Sum('avg'))['total']
        if current_month_cons:
            power_cons += current_month_cons
        today_begin, today_end = get_day_range()
        today_cons = DimensionHourlyStat.objects.filter(dimension_attribute_id=power_da.id,
                                                        created_at__range=(today_begin, today_end)).aggregate(total=Sum('avg'))['total']
        if today_cons:
            power_cons += today_cons
        power_cons += power_da.calc_current_hour_cons(project_id)
        power_cons = round(power_cons / unit_count, 2)

        water_cons = 0
        water_da = DimensionAttribute.objects.filter(dimension__type_name='分项', dimension__parent__isnull=True,
                                                     name__contains='总用水量', ec_type=20).first()
        if water_da is None:
            for water_record in ManualRecord.objects.filter(name__endswith='用水量', gran=50):
                data = json.loads(water_record.data)
                for monthly_data in data:
                    value = monthly_data.get('value')
                    if value:
                        water_cons += float(value)
            water_cons = round(water_cons / unit_count, 2)
        else:
            history_month_cons = DimensionMonthlyStat.objects.filter(dimension_attribute_id=water_da.id).aggregate(total=Sum('avg'))['total']
            if history_month_cons:
                water_cons += history_month_cons
            current_month_cons = DimensionDailyStat.objects.filter(dimension_attribute_id=water_da.id, created_at__year=now.year,
                                                                   created_at__month=now.month).aggregate(total=Sum('avg'))['total']
            if current_month_cons:
                water_cons += current_month_cons
            today_begin, today_end = get_day_range()
            today_cons = DimensionHourlyStat.objects.filter(dimension_attribute_id=water_da.id,
                                                            created_at__range=(today_begin, today_end)).aggregate(total=Sum('avg'))['total']
            if today_cons:
                water_cons += today_cons
            water_cons += water_da.calc_current_hour_cons(project_id)
            water_cons = round(water_cons / unit_count, 2)

        gas_cons = 0
        gas_da = DimensionAttribute.objects.filter(dimension__type_name='分项', dimension__parent__isnull=True,
                                                   name__contains='总用气量', ec_type=40).first()
        if gas_da is None:
            for gas_record in ManualRecord.objects.filter(name__endswith='用气量', gran=50):
                data = json.loads(gas_record.data)
                for monthly_data in data:
                    value = monthly_data.get('value')
                    if value:
                        gas_cons += float(value)
            gas_cons = round(gas_cons / unit_count, 2)
        else:
            history_month_cons = DimensionMonthlyStat.objects.filter(dimension_attribute_id=gas_da.id).aggregate(total=Sum('avg'))['total']
            if history_month_cons:
                gas_cons += history_month_cons
            current_month_cons = DimensionDailyStat.objects.filter(dimension_attribute_id=gas_da.id, created_at__year=now.year,
                                                                   created_at__month=now.month).aggregate(total=Sum('avg'))['total']
            if current_month_cons:
                gas_cons += current_month_cons
            today_begin, today_end = get_day_range()
            today_cons = DimensionHourlyStat.objects.filter(dimension_attribute_id=gas_da.id,
                                                            created_at__range=(today_begin, today_end)).aggregate(total=Sum('avg'))['total']
            if today_cons:
                gas_cons += today_cons
            gas_cons += gas_da.calc_current_hour_cons(project_id)
            gas_cons = round(gas_cons / unit_count, 2)

        kgce = round(power_cons * 0.1229 + gas_cons * 1.33, 2)
        kgco2 = round(power_cons * 0.5366 + gas_cons * 1.96, 2)

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'building_area': 27000,
                'ec_nop': ec_nop,
                'cu_pcons': power_cons,
                'cu_wcons': water_cons,
                'cu_gcons': gas_cons,
                'cu_kgce': kgce,
                'cu_kgco2': kgco2
            }
        })

class UnitEcRankingView(viewsets.ModelViewSet):
    def list(self, request, *args, **kwargs):
        project_id = request.user.get('project_id')
        # 广安的分项和设备个数
        names = ['空调', '办公', '照明', '电梯', '其他']
        counts_91 = [327, 850, 134, 6, 10]
        now = datetime.datetime.now()

        unit_count = Dimension.objects.filter(type_name='单位', ec_persons__isnull=False, unit_area__isnull=False, parent__isnull=False).count()

        dt = request.query_params.get('dt', None)
        if dt is not None:
            dt = datetime.datetime.strptime(dt, '%Y%m%d%H%M%S')
        else:
            dt = now
        dt = dt.replace(hour=0, minute=0, second=0, microsecond=0)
        created_at = dt.strftime('%Y-%m-%d 23:59:59')

        unit_name = request.query_params.get('unit_name', None)

        items = []
        values = []
        device_counts = []
        dimensions = list(Dimension.objects.filter(name__in=names, type_name='分项'))
        dimensions = sorted(dimensions, key=lambda dim: names.index(dim.name))
        for dimension in dimensions:
            avg_to_units = False
            if unit_name is None:
                da = DimensionAttribute.objects.filter(dimension_id=dimension.id, name=f'{dimension.name}用电量').first()
                if da is None:
                    da = DimensionAttribute.objects.filter(dimension_id=dimension.id, name=f'{dimension.name}槽用电量').first()
                if da is None:
                    da = DimensionAttribute.objects.filter(dimension_id=dimension.id, name__endswith=f'{dimension.name}设备用电量').first()

                if dimension.children.exists():
                    sub_dims = dimension.children.values_list('id', flat=True)
                else:
                    sub_dims = [dimension.id]
                device_count = DimensionTerminal.objects.filter(dimension_id__in=sub_dims).count()
            else:
                if not dimension.children.exists():
                    da = DimensionAttribute.objects.filter(dimension_id=dimension.id, name=f'{dimension.name}用电量').first()
                    if da is None:
                        da = DimensionAttribute.objects.filter(dimension_id=dimension.id, name=f'{dimension.name}槽用电量').first()
                    if da is None:
                        da = DimensionAttribute.objects.filter(dimension_id=dimension.id, name__endswith=f'{dimension.name}设备用电量').first()

                    avg_to_units = True
                    device_count = DimensionTerminal.objects.filter(dimension_id=dimension.id).count()
                else:
                    dimension = dimension.children.get(name=f"{unit_name}{dimension.name}")
                    da = DimensionAttribute.objects.filter(dimension_id=dimension.id, name=f"{dimension.name}用电量").first()
                    device_count = DimensionTerminal.objects.filter(dimension_id=dimension.id).count()
            if da:
                if dt.date() == now.date():
                    power_cons = DimensionHourlyStat.objects.filter(dimension_attribute_id=da.id, created_at__gte=dt
                                                                    ).aggregate(total=Sum('avg'))['total']
                    if power_cons is None:
                        power_cons = 0
                else:
                    dds = DimensionDailyStat.objects.filter(dimension_attribute_id=da.id, created_at=created_at).first()
                    power_cons = float(dds.avg) if dds else 0
            else:
                power_cons = 0
            values.append(round(power_cons, 2) if not avg_to_units else round(power_cons / unit_count, 2))
            if dimension.name == '办公':
                items.append('办公设备')
            else:
                items.append(dimension.name)
            device_counts.append(device_count)

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                "device_cons": {
                    "name": unit_name if unit_name else None,
                    "items": items,
                    "values": values,
                    "device_counts": counts_91 if int(project_id) == 91 else device_counts,
                    'unit': 'kWh'
                }
            }
        })

class DimensionAttrTreeView(viewsets.ModelViewSet):

    @classmethod
    def custom_dimension_sort(cls, dim):
        if isinstance(dim, object) and hasattr(dim, 'name'):
            name = dim.name
        else:
            name = dim["name"]
        # 定义排序优先级
        if "空调" in name and "总" in name:
            return 0, name  # 排在前面
        if "用水" in name and "总" in name:
            return 0, name  # 排在前面
        if "用气" in name and "总" in name:
            return 0, name  # 排在前面
        if "总" in name:
            return 0, name  # 排在前面
        if "已安装" in name:
            return 0, name  # 排在前面
        elif "水" in name:
            return 2, name  # 排在后面
        elif "单位" in name:
            return 3, name  # 单位排在最后
        else:
            return 1, name  # 中间排序

    @classmethod
    def process_dimensions(cls, root_dimensions, ec_type=None, dimension_type=None, depth=0):
        if dimension_type is not None:
            root_dimensions = list(root_dimensions.filter(type_name=dimension_type))

        root_dimensions = sorted(root_dimensions, key=cls.custom_dimension_sort)

        results = []

        for root_dimension in root_dimensions:
            children = []
            item = {
                'dimension_id': root_dimension.id,
                'name': root_dimension.name,
                'type_name': root_dimension.type_name,
                'children': children
            }
            root_das = DimensionAttribute.objects.filter(dimension_id=root_dimension.id)
            if ec_type is not None:
                root_das = root_das.filter(ec_type=ec_type)

            formula_da_ids = None
            if root_das.exists():
                formula_das = root_das.filter(formula__isnull=False)
                if formula_das.exists() and depth != 0:
                    formula_da = formula_das.first()
                    da_ids = re.findall(r'{{(.*?)}}', formula_da.formula)
                    formula_da_ids = da_ids
                    formula_children = []
                    children_das = DimensionAttribute.objects.filter(id__in=da_ids)
                    if ec_type is not None:
                        children_das = children_das.filter(ec_type=ec_type)
                    for child in children_das:
                        formula_children.append({
                            'id': child.id,
                            'name': child.name,
                            'identifier': child.identifier,
                            'ec_type': child.ec_type,
                            'dimension': child.dimension_id
                        })

                    children.append({
                        'id': formula_da.id,
                        'name': formula_da.name,
                        'identifier': formula_da.identifier,
                        'ec_type': formula_da.ec_type,
                        'dimension': formula_da.dimension_id,
                        'children': formula_children
                    })
                else:
                    for root_da in root_das:
                        children.append({
                            'id': root_da.id,
                            'name': root_da.name,
                            'identifier': root_da.identifier,
                            'ec_type': root_da.ec_type,
                            'dimension': root_da.dimension_id,
                            'children': []
                        })

            dimension_children = Dimension.objects.filter(parent_id=root_dimension.id)
            if formula_da_ids and depth != 0:
                not_formula_dimension = DimensionAttribute.objects.exclude(id__in=formula_da_ids).values_list('dimension_id', flat=True)
                dimension_children = dimension_children.filter(id__in=not_formula_dimension)
            children += cls.process_dimensions(dimension_children, ec_type, dimension_type, depth + 1)
            # 有多个"总"分项，不排序，默认
            if len(list(filter(lambda x: '总' in x['name'], children))) <= 1:
                item['children'] = sorted(children, key=cls.custom_dimension_sort)

            if depth == 0:
                results.append(item)
            else:
                results += children

        # results = sorted(results, key=cls.custom_dimension_sort)

        return results

    def list(self, request, *args, **kwargs):
        root_dimensions = Dimension.objects.filter(parent_id__isnull=True)

        ec_type = request.query_params.get('ec_type', None)
        dimension_type = request.query_params.get('dimension_type', None)

        results = self.process_dimensions(root_dimensions, ec_type, dimension_type)

        tree = []

        for result in results:
            if result.get('children', None) and len(result['children']):
                tree.append(result)

        return Response({
            'status': status.HTTP_200_OK,
            'data': tree
        })
