import logging
import traceback

from django.core.management import BaseCommand, CommandError

from saian_api.devdefine.models import DeviceType, DevicePrototype, AttributeType, AttributePrototype
from saian_api.device.models import <PERSON>y<PERSON><PERSON>, Device, DeviceAttribute
from saian_api.group.models import Shortcut, GroupAction
from saian_api.linkage.models import LinkageRule
from saian_api.project.models import Project, WebMenu
from saian_api.scheduledtask.utils import get_projects, set_global_db
from saian_api.terminal.models import TerminalAttribute, Terminal
from saian_api.user.models import WebRole, UserProject, WebUser

class Command(BaseCommand):
    help = "新项目初始化"

    # def add_arguments(self, parser):
    #     parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        try:
            # 同步基础数据到新项目
            target_project_id = 43

            admin_db = 'syadmindb'
            project_db = f'prj{target_project_id}db'

            set_global_db(admin_db)

            # sql console
            # insert into prj43db.device_types select * from syadmindb.device_types;
            # insert into prj43db.device_prototypes select * from syadmindb.device_prototypes;
            # insert into prj43db.attribute_types select * from syadmindb.attribute_types;
            # insert into prj43db.attribute_prototypes select  * from syadmindb.attribute_prototypes;
            # insert into prj43db.report_configurers select * from syadmindb.report_configurers;

            # # project
            # project = Project.objects.get(pk=target_project_id)
            # project.save(using=project_db)
            #
            # # web_role
            # for role in WebRole.objects.filter(project_id=target_project_id):
            #     role.save(using=project_db)

            # web_user
            # user_ids = list(UserProject.objects.filter(project_id=target_project_id).values_list('web_user_id', flat=True))
            # web_us = WebUser.objects.filter(id__in=user_ids)
            # print(web_us)
            # for web_user in web_us:
            #     setattr(web_user, 'online_time', 0)
            #     web_user.save(using=project_db)

            # # web menus
            # for web_menu in WebMenu.objects.order_by('-parent'):
            #     web_menu.save(using=project_db)
            #
            # # device
            # devices = list(Device.objects.filter(project_id=target_project_id))
            # set_global_db(target_project_id)
            # for device in devices:
            #     device.save(using=project_db)
            #     DeviceAttribute.objects.create_by_device(device)
            #     Terminal.objects.create_by_device(device)
            #     TerminalAttribute.objects.create_by_device(device)



        except CommandError:
            self.stderr.write(f"运行'新项目初始化'任务失败，项目ID：，命令参数不合法！", ending='\n')
            # continue
        except Exception as e:
            self.stderr.write(f"运行'新项目初始化'任务失败，项目ID：", ending='\n')
            self.stderr.write(traceback.format_exc(), ending='')
            # continue
