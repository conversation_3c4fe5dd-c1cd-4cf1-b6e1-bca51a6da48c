from django.urls import path

from .views import (ProjectPanelWeather, ProjectPanelEsrApiView, EnergySummaryApiView, ProjectWeatherAPIView,
                    RealtimeWeather, ProjectPanelEcApiView, DeviceOfflineRate, DeviceFault30d, DeviceFaultReasonStats,
                    DeviceAlarmSummary, DeviceFollowerTop10, DeviceFollowingTop10, EcCriteriaViewSet,
                    EcTranscriptionViewSet, PanelSettingsViewSet, WebDashboardChartView, EcTranscriptionV5View, MiniStatsInfo)

# router = routers.DefaultRouter()

# router.register('saianapi/v1/project_weather', ProjectWeatherViewSet)

# 能耗基准
ec_criteria_list = EcCriteriaViewSet.as_view({
    'get': 'list',
    'post': 'create'
})
ec_criteria_detail = EcCriteriaViewSet.as_view({
    'put': 'update',
    'delete': 'destroy',
    'get': 'retrieve',
    'patch': 'partial_update'
})

# 能耗手动录入
ec_transcription_list = EcTranscriptionViewSet.as_view({
    'get': 'list',
    'post': 'create'
})
ec_transcription_detail = EcTranscriptionViewSet.as_view({
    'put': 'update',
    'delete': 'destroy',
    'get': 'retrieve',
})

ec_transcription_v5 = EcTranscriptionV5View.as_view({
    'get': 'list'
})

# web Dashboard Chart
web_dashboard_chart = WebDashboardChartView.as_view({
    'get': 'list'
})

mini_stats_info = MiniStatsInfo.as_view({
    'get': 'list'
})

urlpatterns = [
    path('saianapi/v1/panel_settings', PanelSettingsViewSet.as_view()),
    path('saianapi/v1/panel_weather', ProjectPanelWeather.as_view()),
    path('saianapi/v1/project_weather', ProjectWeatherAPIView.as_view()),
    path('saianapi/v1/weather', RealtimeWeather.as_view()),
    path('saianapi/v1/weather/<int:pk>', RealtimeWeather.as_view()),
    path('saianapi/v1/ec_power', ProjectPanelEcApiView.as_view()),
    path('saianapi/v1/ec_cold', ProjectPanelEcApiView.as_view()),
    path('saianapi/v1/esr_power', ProjectPanelEsrApiView.as_view()),
    path('saianapi/v1/esr_cold', ProjectPanelEsrApiView.as_view()),
    path('saianapi/v1/panel_summary', EnergySummaryApiView.as_view()),
    path('saianapi/v1/offline_rate', DeviceOfflineRate.as_view()),
    path('saianapi/v1/fault_rate_30d', DeviceFault30d.as_view()),
    path('saianapi/v1/fault_reason_stats', DeviceFaultReasonStats.as_view()),
    path('saianapi/v1/panel_alarm_summary', DeviceAlarmSummary.as_view()),
    path('saianapi/v1/device_follower', DeviceFollowerTop10.as_view()),
    path('saianapi/v1/device_following', DeviceFollowingTop10.as_view()),

    path('saianapi/v1/ec_criteria', ec_criteria_list),
    path('saianapi/v1/ec_criteria/<int:pk>', ec_criteria_detail),

    path('saianapi/v1/ec_transcription', ec_transcription_list),
    path('saianapi/v1/ec_transcription/<int:pk>', ec_transcription_detail),
    path('saianapi/v5/ec_transcription', ec_transcription_v5, name='ec_transcription_v5'),

    path('saianapi/v2/dashboards/web', web_dashboard_chart, name='web-dashboard-chart'),

    path('saianapi/v5/mini_stats_info', mini_stats_info, name="mini_stats_info"),

    # path('saianapi/v2/issue_stats', IssueStatsView.as_view())
]
