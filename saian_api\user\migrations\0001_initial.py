# Generated by Django 3.2.8 on 2021-12-30 15:16

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('project', '0001_initial'),
        ('device', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='WebRole',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('en_name', models.CharField(max_length=255, null=True)),
                ('permissions', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='project.project')),
                ('web_menus', models.ManyToManyField(to='project.WebMenu')),
            ],
            options={
                'db_table': 'web_roles',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='WebUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('username', models.CharField(max_length=255, null=True)),
                ('password', models.CharField(max_length=255)),
                ('name', models.CharField(max_length=255, null=True)),
                ('mobile', models.CharField(max_length=255, null=True)),
                ('status', models.IntegerField()),
                ('avatar', models.CharField(max_length=255, null=True)),
                ('unionid', models.CharField(max_length=255, null=True)),
                ('openid', models.CharField(max_length=255, null=True)),
                ('access_token', models.CharField(max_length=255, null=True)),
                ('refresh_token', models.CharField(max_length=255, null=True)),
                ('permissions', models.CharField(max_length=255, null=True)),
                ('ec_token', models.CharField(max_length=255, null=True)),
                ('is_super', models.BooleanField(default=False)),
                ('last_login', models.DateTimeField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('web_roles', models.ManyToManyField(to='user.WebRole')),
            ],
            options={
                'db_table': 'web_users',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='UserProject',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='project.project')),
                ('web_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='user.webuser')),
            ],
            options={
                'db_table': 'user_projects',
            },
        ),
        migrations.CreateModel(
            name='UserDevice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('unit_idx', models.IntegerField(null=True)),
                ('unit_prefix', models.CharField(max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('device', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='device.device')),
                ('web_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='user.webuser')),
            ],
            options={
                'db_table': 'user_devices',
            },
        ),
    ]
