import calendar
import datetime
import logging
import traceback

from django.core.management import BaseCommand
from django.db.models import Sum

from saian_api.dashboard.models import EcCriteria, ProjectWeather
from saian_api.dimension.models import Dimension, DimensionAttribute, DimensionDailyStat, EsrStat
from saian_api.scheduledtask.utils import get_projects, set_global_db

class Command(BaseCommand):
    help = '每日节能率计算'

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目列表
        projects = get_projects(self, options)

        now = datetime.datetime.now()
        yesterday = now - datetime.timedelta(days=1)
        month = yesterday.month
        day = yesterday.day

        for project_id in projects:
            try:
                # 设置全局数据库
                set_global_db(project_id)
                logging.info(f'每日节能率计算-{project_id}')
                month_criteria = EcCriteria.objects.filter(is_refer=True, year_month__endswith='%02d' % month).last()
                if month_criteria is None:
                    logging.info(f'\t项目{project_id}无配置{month}月基准')
                    continue

                # 查询总用电的 dimension_attribute，要求维度属性的name包含“总用电”
                # - 根维度，排除用水维度
                root_dimensions = list(Dimension.objects.filter(parent__isnull=True).exclude(name__contains='水').values_list('id', flat=True))
                # - 查询总用电的维度属性
                ec_da = DimensionAttribute.objects.filter(name__contains='总用电', dimension_id__in=root_dimensions).last()
                if ec_da is None:
                    logging.warning(f'\t项目没有配置总用电量的维度属性')
                    continue
                # 查询昨日的总用电量数据
                das = DimensionDailyStat.objects.filter(created_at__year=yesterday.year, created_at__month=yesterday.month, created_at__day=day,
                                                        dimension_attribute_id=ec_da.id).last()
                if das is None:
                    logging.warning(f'\t维度日统计缺失{day}日数据')
                    continue
                cur_value = float(das.avg)

                year = int(month_criteria.year_month[:4])
                # 查询当月有多少天
                month_range = calendar.monthrange(year, month)[1]
                # 查询基准年月的温度历史记录
                monthly_weathers = ProjectWeather.objects.filter(project_id=project_id, type='di', created_at__year=year, created_at__month=month)
                weather_count = monthly_weathers.count()

                if month_range != weather_count:
                    logging.warning(f'\t基准{year}年{month}月的共有{month_range}天，历史天气数据有{weather_count}条!')
                if weather_count == 0:
                    continue

                # 求基准同期用电量
                yesterday_weather = monthly_weathers.filter(created_at__day=day).last()
                if yesterday_weather is None:
                    logging.warning(f'\t历史天气缺失{day}日数据，使用基准平均值计算')
                    refer_value = float(month_criteria.value) / float(month_range)
                else:
                    monthly_temp = monthly_weathers.aggregate(sum=Sum('temp'))['sum']
                    refer_value = float(month_criteria.value) * (float(yesterday_weather.temp) / monthly_temp)
                if refer_value == 0:
                    logging.warning(f'\t 基准同期用电量为零')
                    continue

                # 求节能率
                esr_value = round((1 - cur_value / refer_value) * 100, 2)
                logging.info(f'\t 当期用电量: {cur_value}, 基准同期用电量: {refer_value}, 节能率: {esr_value}')

                created_at = yesterday.strftime('%Y-%m-%d 23:59:59')
                if not EsrStat.objects.filter(project_id=project_id, type='di', created_at=created_at).exists():
                    EsrStat.objects.create(
                        project_id=project_id,
                        type='di',
                        esr_value=esr_value,
                        cur_value=round(cur_value, 2),
                        refer_value=round(refer_value, 2),
                        created_at=created_at
                    )

            except Exception as e:
                self.stderr.write(
                    f"执行每日节能率计算任务出错，project_id = {project_id}, err = {e.__str__()}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
                continue

