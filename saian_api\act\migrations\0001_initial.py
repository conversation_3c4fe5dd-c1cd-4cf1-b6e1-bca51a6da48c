# Generated by Django 3.2.8 on 2021-12-30 15:16

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('devdefine', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ActColdStat',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('base_load', models.CharField(max_length=255, null=True)),
                ('current_load', models.CharField(max_length=255, null=True)),
                ('load_rate', models.CharField(max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'act_cold_stats',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='AcTerminal',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cooling_cap', models.CharField(max_length=255, null=True)),
                ('load_rate', models.CharField(max_length=255, null=True)),
                ('device_prototype', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='devdefine.deviceprototype')),
            ],
            options={
                'db_table': 'ac_terminals',
            },
        ),
    ]
