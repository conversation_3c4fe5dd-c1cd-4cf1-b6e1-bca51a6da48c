# Generated by Django 3.2.8 on 2022-06-07 12:03

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ('user', '0006_webuser_msg_types'),
        ('project', '0004_project_user_panel_settings'),
    ]

    operations = [
        migrations.CreateModel(
            name='Message',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('updated_at', models.DateTimeField(auto_created=True)),
                ('msg_type', models.IntegerField()),
                ('content', models.TextField(blank=True, null=True)),
                ('uid', models.CharField(blank=True, max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='project.project')),
            ],
            options={
                'db_table': 'messages',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='UserMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('read', models.BooleanField(default=False)),
                ('useful', models.IntegerField(default=9)),
                ('message', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='message.message')),
                ('web_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='user.webuser')),
            ],
            options={
                'db_table': 'user_messages',
            },
        ),
    ]
