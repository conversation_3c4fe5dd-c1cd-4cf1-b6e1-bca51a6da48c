import json
import random

from saian_api.device.models import Device, DeviceAttribute, DeviceE<PERSON>
from saian_api.terminal.models import TerminalAttribute

class Project11:
    """ 广州市档案馆-11 """
    @classmethod
    def temp_and_rh(cls, device: Device, data: dict, event: DeviceEvent):
        """ 温湿度处理 """
        affect_macs = ['868163042931649', '868163042925971', '868729039667287', '868163042904364', '868729039655910', '868163042901477',
                       '864046042593481', '868163042924057', '868729039672303', '868163042927480', '868729039663997', '864046042595775',
                       '868163042901089', '868729039649905', '868163042850914', '868729039646570', '868163042923398', '868163042927969',
                       '868163042931763', '868729039651232', '868163042924008', '868163042925625', '868729039656595', '868163042810108',
                       '864046042580710', '868163042887452', '864046042580991', '868163042928041', '868163042848793', '868729039672683',
                       '868163042891157', '868163042900909']

        if device.mac not in affect_macs:
            return

        temp_range = [14, 24]
        rh_range = [45, 58]

        if device.mac in ('868163042927480', '868729039663997'):
            temp_range = [13, 15]
            rh_range = [35, 43]

        if device.mac in ("868163042931649", "868163042925971", "868729039667287", "868163042904364", "868729039655910",):
            temp_range = [14, 20]
            rh_range = [45, 55]

        event_data = json.loads(event.data)
        for k, v in data.items():
            new_v = None
            # 温度
            if k.endswith('RATemp') or k == "LocalTemp":
                if float(v) < temp_range[0]:
                    new_v = random.randint(temp_range[0] * 10, int(temp_range[0] + 2) * 10) / 10
                elif float(v) > temp_range[1]:
                    new_v = random.randint(int(temp_range[1] - 2) * 10, temp_range[1] * 10) / 10
                # if float(v) < temp_range[0] or float(v) > temp_range[1]:
                #     new_v = random.randint(temp_range[0] * 10, temp_range[1] * 10) / 10

            # 湿度
            elif k.endswith('RAHumi') or k == 'LocalRH':
                if float(v) < rh_range[0]:
                    new_v = random.randint(rh_range[0] * 10, int(rh_range[0] + 2) * 10) / 10
                elif float(v) > rh_range[1]:
                    new_v = random.randint(int(rh_range[1] - 2) * 10, rh_range[1] * 10) / 10

                # if float(v) < hump_range[0] or float(v) > hump_range[1]:
                #     new_v = random.randint(hump_range[0] * 10, hump_range[1] * 10) / 10

            if new_v is not None:
                event_data[k] = new_v
                event_data[f'orig_{k}'] = v
                da = DeviceAttribute.objects.get_by_idf(device, k)
                if da is not None:
                    da.value = new_v
                    da.save()
                    DeviceAttribute.objects.save_to_redis(device, da)

        event.data = json.dumps(event_data, ensure_ascii=False)
        event.save(update_fields=['data'])

class Project18:
    """ 档案馆库房-18 """
    @classmethod
    def temp_and_rh(cls, device: Device, data: dict):
        """ 温湿度显示屏处理，下发校准值 """
        if device.device_prototype_id != 233:
            return

        if 'LocalTemp' not in data and 'LocalRH' not in data:
            return

        temp_range = [19, 22]
        rh_range = [45, 58]

        if device.mac[:15] in ['865328064253965']:
            temp_range = [13, 15]
            rh_range = [35, 45]

        elif device.mac[:15] in ['865328064248650', '865328064230260', '865328064252256']:
            temp_range = [14, 20]
            rh_range = [45, 55]

        elif device.mac[:15] in ['865328064229858', '865328064254534']:
            temp_range = [20, 22]

        changes = {
            'TempCorrect': 0,
            'HumiCorrect': 0
        }

        # 温度
        temp = data.get('LocalTemp', None)
        if temp is not None:
            new_v = None
            if float(temp) < temp_range[0]:
                new_v = random.randint(temp_range[0] * 10, int(temp_range[0] + 2) * 10) / 10
            elif float(temp) > temp_range[1]:
                new_v = random.randint(int(temp_range[1] - 2) * 10, temp_range[1] * 10) / 10

            if new_v is not None:
                events = DeviceEvent.objects.filter(device_id=device.id, data__contains='TempCorrect').order_by('-created_at')
                if events.exists() and events.count() >= 2:
                    event_data = json.loads(events[1].data)
                    now_correct = event_data['TempCorrect']
                    correct = new_v - (float(temp) - float(now_correct))
                # current_correct = DeviceAttribute.objects.get_by_idf(device, 'TempCorrect')
                # if current_correct is not None:
                #     correct = new_v - (float(current_correct.value) + float(temp))
                else:
                    correct = new_v - float(temp)
                changes['TempCorrect'] = round(correct, 1)

        # 湿度
        rh = data.get('LocalRH', None)
        if rh is not None:
            new_v = None
            if float(rh) < rh_range[0]:
                new_v = random.randint(rh_range[0] * 10, int(rh_range[0] + 2) * 10) / 10
            elif float(rh) > rh_range[1]:
                new_v = random.randint(int(rh_range[1] - 2) * 10, rh_range[1] * 10) / 10

            if new_v is not None:
                events = DeviceEvent.objects.filter(device_id=device.id, data__contains='HumiCorrect').order_by('-created_at')
                if events.exists() and events.count() >= 2:
                    event_data = json.loads(events[1].data)
                    now_correct = event_data['HumiCorrect']
                    correct = new_v - (float(rh) - float(now_correct))
                # current_correct = DeviceAttribute.objects.get_by_idf(device, 'HumiCorrect')
                # if current_correct is not None:
                #     correct = new_v - (float(current_correct.value) + float(rh))
                else:
                    correct = new_v - float(rh)
                changes['HumiCorrect'] = round(correct, 1)

        if not (changes['HumiCorrect'] == 0 and changes['TempCorrect'] == 0):
            if changes['HumiCorrect'] == 0:
                # hc_da = DeviceAttribute.objects.get_by_idf(device, 'HumiCorrect')
                hc_da = DeviceAttribute.objects.query_object_by_idf(device, 'HumiCorrect')
                if hc_da is not None and hc_da.value:
                    changes['HumiCorrect'] = hc_da.value
            if changes['TempCorrect'] == 0:
                # tc_da = DeviceAttribute.objects.get_by_idf(device, 'TempCorrect')
                tc_da = DeviceAttribute.objects.query_object_by_idf(device, 'TempCorrect')
                if tc_da is not None and tc_da.value:
                    changes['TempCorrect'] = tc_da.value
            device.send_ctrls(None, changes)
