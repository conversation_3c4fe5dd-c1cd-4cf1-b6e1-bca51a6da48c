"""
    维度月统计
"""
import logging
import re
import traceback

from django.core.management import BaseCommand, CommandError

from saian_api.dimension.models import DimensionMonthlyStat, DimensionDailyStat, DimensionAttribute
from saian_api.project.models import Project
from saian_api.scheduledtask.utils import get_projects, set_global_db
from saian_api.utils.report import do_dimension_stat_cce, last_month_time_range
from saian_api.utils.tools import is_number

def cus_create(dimension_id, dimension_attribute_id, last_month_end, value, min_value: [float, str] = '--', max_value: [float, str] = '--'):
    value = round(value, 3)
    if is_number(min_value):
        min_value = round(min_value, 3)
    if is_number(max_value):
        max_value = round(max_value, 3)

    dms = DimensionMonthlyStat.objects.filter(dimension_id=dimension_id,
                                              dimension_attribute_id=dimension_attribute_id,
                                              created_at=last_month_end).last()
    if dms is None:
        DimensionMonthlyStat.objects.create(
            dimension_id=dimension_id,
            dimension_attribute_id=dimension_attribute_id,
            avg=value,
            min=min_value,
            max=max_value,
            created_at=last_month_end
        )
    else:
        if dms.avg != str(value):
            logging.info(f'{dimension_attribute_id}, {last_month_end}, old-{dms.avg}, new-{value}')
            dms.avg = value
            dms.min = min_value
            dms.max = max_value
            # dms.save()


class Command(BaseCommand):
    help = '每月统计维度的数据'

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目ID列表
        projects = get_projects(self, options)

        for project_id in projects:
            try:
                # 设置全局数据库
                set_global_db(project_id)
                project = Project.objects.get(pk=project_id)

                self.stdout.write(f"维度月统计开始: {project.name}", ending='\n')

                begin, end = last_month_time_range()

                do_dimension_stat_cce(cus_create, DimensionDailyStat.objects, begin, end)

                # 最后计算有公式的维度属性值
                for da in DimensionAttribute.objects.filter(formula__isnull=False):
                    da_ids = re.findall(r'{{(.*?)}}', da.formula)
                    da_values = []
                    for da_id in da_ids:
                        dms = DimensionMonthlyStat.objects.filter(created_at=end, dimension_attribute_id=da_id).last()
                        # da = DimensionAttribute.objects.get(pk=da_id)
                        da_values.append(float(dms.avg) if dms is not None and is_number(dms.avg) else 0)
                    # 构建计算表达式
                    eval_str = da.formula
                    for v in da_values:
                        eval_str = re.sub(r'{{(.*?)}}', f'{v}', eval_str, 1)

                    try:
                        value = eval(eval_str)
                        cus_create(da.dimension_id, da.id, end, value)

                    except Exception as e:
                        self.stdout.write(f'计算维度属性 {da.id}-{da.name} 公式时出错，表达式为: {eval_str}; ERROR: {e.__str__()}')

                self.stdout.write(f"维度月统计任务完成: {project.name}", ending='\n')

            except CommandError:
                self.stderr.write(f"运行'维度月统计'任务失败，项目ID：{project_id}，命令参数不合法！", ending='\n')
                continue
            except Exception as e:
                self.stderr.write(f"运行'维度月统计'任务失败，项目ID：{project_id}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
                continue
