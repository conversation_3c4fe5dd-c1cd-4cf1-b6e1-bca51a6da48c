import json

from django.db import models

# Create your models here.
from saian_api.project.models import Project
from saian_api.user.models import WebUser

class MessageManager(models.Manager):
    def fault(self, device, glossary):
        content = f'「{device.nick_name}-{device.mac}」发生故障：{glossary}'
        self.cus_create(8, device.project_id, content)

    def fault_recover(self, device, glossary):
        content = f'「{device.nick_name}-{device.mac}」：{glossary} 故障恢复'
        self.cus_create(8, device.project_id, content)

    def alarm(self, device, glossary):
        content = f'「{device.nick_name}-{device.mac}」报警：{glossary}'
        self.cus_create(9, device.project_id, content)

    def alarm_recover(self, device, glossary):
        content = f'「{device.nick_name}-{device.mac}」：{glossary} 报警解除'
        self.cus_create(9, device.project_id, content)

    # 由定时任务创建，所以需要db
    def maintain(self, device, db=None):
        content = f'「{device.nick_name}-{device.mac}」需要保养'
        self.cus_create(10, device.project_id, content, db=db)

    def maintain_complete(self, device):
        content = f'「{device.nick_name}-{device.mac}」已完成保养'
        self.cus_create(10, device.project_id, content)

    def system_message(self, msg_type, project_id, content, uid):
        self.cus_create(msg_type, project_id, content, uid)

    # 由定时任务创建，所以需要db
    def device_offline(self, device, db=None):
        content = f'「{device.nick_name}-{device.mac}」离线！'
        self.cus_create(4, device.project_id, content, db=db)

    def device_online(self, device):
        content = f'「{device.nick_name}-{device.mac}」恢复在线！'
        self.cus_create(4, device.project_id, content)

    def cus_create(self, msg_type, project_id, content, uid='', db=None, users=None):
        if db is not None:
            message = self.using(db).create(msg_type=msg_type, project_id=project_id, content=content, uid=uid)
            UserMessage.objects.message_distributor(message, msg_type, db=db)
        else:
            message = self.create(msg_type=msg_type, project_id=project_id, content=content, uid=uid)
            UserMessage.objects.message_distributor(message, msg_type, users=users)
        # message.save(using=db)


class Message(models.Model):
    """消息中心"""

    # 项目
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    # 消息类别, 1-系统通知，2-冷源系统，3-末端系统，4-设备离线，5-快捷操作，6-设备联动，7-新设备接入，8-设备故障，9-设备报警，10-设备保养，11-参数超限，
    # ####### 12-日统计报告，13-周统计报告，14-月统计报告，15-年统计报告, 99-其他
    msg_type = models.IntegerField()
    # 消息内容
    content = models.TextField(blank=True, null=True)
    # uid, 后台管理定位消息
    uid = models.CharField(max_length=255, blank=True, null=True)
    # 创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 更新时间
    updated_at = models.DateTimeField(auto_now=True)
    # user
    web_user = models.ManyToManyField(WebUser, through='message.UserMessage', through_fields=('message', 'web_user'))

    class Meta:
        db_table = 'messages'
        ordering = ['-created_at']

    objects = MessageManager()


class UserMessageManager(models.Manager):
    def useful_count(self, message_id):
        """统计某个消息的有用无用数"""
        messages = self.filter(message_id=message_id)
        useful = messages.filter(useful=1).count()
        useless = messages.filter(useful=0).count()
        return useful, useless

    def message_distributor(self, message, msg_type, db=None, users=None):
        if users is None:
            web_users = WebUser.objects.filter(msg_types__isnull=False) if db is None else WebUser.objects.using(db).filter(msg_types__isnull=False)
        else:
            web_users = users
        user_messages = []
        for user in web_users:
            if user.msg_types and users is None:
                msg_configs = json.loads(user.msg_types)
                if len([config for config in msg_configs if config.get('msg_type', None) == msg_type and config.get('blocked', True) is False]):
                    user_messages.append(UserMessage(message=message, web_user=user))
            else:
                user_messages.append(UserMessage(message=message, web_user=user))

        if db is None:
            self.bulk_create(user_messages)
        else:
            self.using(db).bulk_create(user_messages)

class UserMessage(models.Model):
    """用户消息中间表"""

    # 消息
    message = models.ForeignKey(Message, on_delete=models.CASCADE)
    # 用户
    web_user = models.ForeignKey(WebUser, on_delete=models.CASCADE)
    # 是否已读
    read = models.BooleanField(default=False)
    # 该消息是否有用，0-无用，1-有用，9-未选择
    useful = models.IntegerField(default=9)

    class Meta:
        db_table = 'user_messages'

    objects = UserMessageManager()
