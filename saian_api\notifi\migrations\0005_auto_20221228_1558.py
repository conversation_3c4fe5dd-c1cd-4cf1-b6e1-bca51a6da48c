# Generated by Django 3.2.8 on 2022-12-28 15:58

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('device', '0010_devicectrllog_action_log'),
        ('notifi', '0004_auto_20221227_1356'),
    ]

    operations = [
        migrations.AlterField(
            model_name='notification',
            name='device',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='device.device'),
        ),
        migrations.AlterField(
            model_name='notifyconfig',
            name='concern_type',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype'),
        ),
        migrations.AlterField(
            model_name='notifylimit',
            name='concern_type',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype'),
        ),
    ]
