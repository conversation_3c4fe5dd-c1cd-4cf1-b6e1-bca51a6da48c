# Generated by Django 3.2.8 on 2021-12-30 15:16

import builtins
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('building', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Device',
            fields=[
                ('id', models.BigIntegerField(primary_key=builtins.id, serialize=False)),
                ('device_type_id', models.BigIntegerField(db_index=True)),
                ('device_prototype_id', models.BigIntegerField(db_index=True)),
                ('nick_name', models.CharField(blank=True, db_index=True, max_length=255, null=True)),
                ('mac', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('did', models.CharField(blank=True, max_length=255, null=True)),
                ('batch_no', models.CharField(blank=True, max_length=255, null=True)),
                ('address', models.Char<PERSON><PERSON>(blank=True, max_length=255, null=True)),
                ('status', models.Integer<PERSON>ield(blank=True, null=True)),
                ('remark', models.Char<PERSON><PERSON>(blank=True, max_length=255, null=True)),
                ('in_alarm', models.BooleanField(default=False)),
                ('wudid', models.CharField(blank=True, max_length=255, null=True)),
                ('platform', models.IntegerField(blank=True, null=True)),
                ('prd_key', models.CharField(blank=True, max_length=255, null=True)),
                ('in_fault', models.BooleanField(default=False)),
                ('online', models.BooleanField(default=False)),
                ('sw_on', models.BooleanField(default=False)),
                ('gw_end_point', models.CharField(blank=True, max_length=255, null=True)),
                ('agent_id', models.BigIntegerField(null=True)),
                ('in_acc', models.BooleanField(default=False)),
                ('ali_secret', models.CharField(blank=True, max_length=255, null=True)),
                ('ali_region', models.CharField(blank=True, max_length=255, null=True)),
                ('ali_device_name', models.CharField(blank=True, max_length=255, null=True)),
                ('needs_m', models.BooleanField(default=False)),
                ('live_update', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'devices',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DeviceAttribute',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('attribute_prototype_id', models.BigIntegerField(db_index=True)),
                ('value', models.CharField(max_length=255, null=True)),
                ('show_in_list', models.BooleanField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'device_attributes',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DeviceCtrlLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('executor_id', models.BigIntegerField()),
                ('executor_type', models.CharField(max_length=255)),
                ('mac', models.CharField(max_length=255)),
                ('data', models.TextField()),
                ('errcode', models.IntegerField()),
                ('result', models.CharField(max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'device_ctrl_logs',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DeviceCtrlLogManager',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
        ),
        migrations.CreateModel(
            name='DeviceEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('device_prototype_id', models.BigIntegerField(db_index=True)),
                ('cmd', models.CharField(max_length=255, null=True)),
                ('event_type', models.CharField(max_length=255, null=True)),
                ('event_id', models.CharField(max_length=255, null=True)),
                ('delivery_id', models.CharField(max_length=255, null=True)),
                ('product_key', models.CharField(max_length=255, null=True)),
                ('timestamp', models.FloatField(null=True)),
                ('ip', models.CharField(max_length=255, null=True)),
                ('latitude', models.DecimalField(decimal_places=6, max_digits=10, null=True)),
                ('longitude', models.DecimalField(decimal_places=6, max_digits=10, null=True)),
                ('country', models.CharField(max_length=255, null=True)),
                ('region', models.CharField(max_length=255, null=True)),
                ('data', models.TextField(null=True)),
                ('did', models.CharField(max_length=255, null=True)),
                ('mac', models.CharField(max_length=255, null=True)),
                ('nick_name', models.CharField(blank=True, db_index=True, max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'device_events',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DeviceEventHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('project_id', models.BigIntegerField(db_index=True)),
                ('device_issue_id', models.BigIntegerField(db_index=True)),
                ('device_id', models.BigIntegerField(db_index=True)),
                ('device_prototype_id', models.BigIntegerField(db_index=True)),
                ('cmd', models.CharField(max_length=255, null=True)),
                ('event_type', models.CharField(max_length=255, null=True)),
                ('event_id', models.CharField(max_length=255, null=True)),
                ('delivery_id', models.CharField(max_length=255, null=True)),
                ('product_key', models.CharField(max_length=255, null=True)),
                ('timestamp', models.FloatField(null=True)),
                ('ip', models.CharField(max_length=255, null=True)),
                ('latitude', models.DecimalField(decimal_places=6, max_digits=10, null=True)),
                ('longitude', models.DecimalField(decimal_places=6, max_digits=10, null=True)),
                ('country', models.CharField(max_length=255, null=True)),
                ('region', models.CharField(max_length=255, null=True)),
                ('data', models.TextField(null=True)),
                ('did', models.CharField(max_length=255, null=True)),
                ('mac', models.CharField(max_length=255, null=True)),
                ('nick_name', models.CharField(blank=True, db_index=True, max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'device_event_histories',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DeviceTimer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('identifiers', models.TextField()),
                ('repeat', models.BooleanField(default=False)),
                ('run_date', models.TimeField()),
                ('wdays', models.CharField(max_length=255, null=True)),
                ('enabled', models.BooleanField(default=False)),
                ('is_finished', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'device_timers',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='LivingDetection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('mac', models.CharField(db_index=True, max_length=255)),
                ('identifier', models.CharField(max_length=255)),
                ('value', models.CharField(max_length=255, null=True)),
                ('desc', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'living_detections',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ParamRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('mac', models.CharField(db_index=True, max_length=255)),
                ('identifier', models.CharField(max_length=255)),
                ('value', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'param_records',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='RoomDevice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
            options={
                'db_table': 'room_devices',
            },
        ),
        migrations.CreateModel(
            name='WaterLeakage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('identifier', models.CharField(max_length=255)),
                ('status', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('active_room', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='building.activeroom')),
                ('device', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='device.device')),
            ],
            options={
                'db_table': 'water_leakages',
                'ordering': ['-created_at'],
            },
        ),
    ]
