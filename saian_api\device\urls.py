from django.urls import path

from .views import (
    DeviceLiveStatViewSet,
    DeviceViewSet,
    DeviceValuesViewSet,
    DeviceAttributeViewSet,
    DeviceTimerViewSet,
    DeviceCtrlLogViewSet,
    DeviceDebugViewSet,
    DeviceEventViewSet,
    WebDeviceViewSet,
    WebUnbindDeviceViewSet,
    DeviceV2ViewSet,
    DeviceV3ViewSet,
    DeviceChangesViewSet,
    LivingDetectionsViewSet,
    WebDeviceV2ViewSet,
    DeviceLogsViewSet,
    DeviceLimitViewSet,
    DeviceRuntimeViewSet,
    DeviceLogMeaning,
    FcuAttachDeviceView,
    NewFcubdStatusView,
    DataRouteView,
    DeviceAttributeViewV5,
    SySimView, SpacVariantView,
    InfraredCodeView, AttributeValueView, NNADeviceView, LTAMeterCollectionView
)

device_list = DeviceViewSet.as_view({
    'get': 'list',
    'post': 'create'
})

device_detail = DeviceViewSet.as_view({
    'get': 'retrieve',
    'put': 'partial_update',
    # 'patch': 'partial_update',
    'delete': 'destroy'
})

device_values = DeviceValuesViewSet.as_view({
    'get': 'list',
    'post': 'create'
})

device_attributes = DeviceAttributeViewSet.as_view({
    'get': 'list'
})
# device_attribute = DeviceAttributeViewSet.as_view({
#     'get': 'retrieve'
# })

device_attributes_v5 = DeviceAttributeViewV5.as_view({
    'post': 'create',
    'get': 'list'
})
device_attribute_detail_v5 = DeviceAttributeViewV5.as_view({
    'get': 'retrieve'
})

device_timers = DeviceTimerViewSet.as_view({
    'get': 'list',
    'post': 'create'
})

device_timer = DeviceTimerViewSet.as_view({
    'put': 'partial_update',
    'get': 'retrieve',
    'delete': 'destroy'
})

device_ctrl_logs = DeviceCtrlLogViewSet.as_view({
    'get': 'list'
})

device_ctrl_log = DeviceCtrlLogViewSet.as_view({
    'get': 'retrieve',
    'put': 'partial_update'
})

device_debugs = DeviceDebugViewSet.as_view({
    'get': 'list'
})

device_events = DeviceEventViewSet.as_view({
    'get': 'list'
})

device_event = DeviceEventViewSet.as_view({
    'get': 'retrieve'
})

device_live_stats = DeviceLiveStatViewSet.as_view({
    'get': 'list'
})

web_devices = WebDeviceViewSet.as_view({
    'get': 'list'
})

web_device = WebDeviceViewSet.as_view({
    'get': 'retrieve',
    'put': 'partial_update'
})

web_unbind_devices = WebUnbindDeviceViewSet.as_view({
    'get': 'list'
})

device_changes = DeviceChangesViewSet.as_view({
    'post': 'update'
})

living_detections = LivingDetectionsViewSet.as_view({
    'get': 'list'
})

device_v2_list = DeviceV2ViewSet.as_view({
    'get': 'list'
})

device_v2_detail = DeviceV2ViewSet.as_view({
    'get': 'retrieve'
})

web_device_v2_list = WebDeviceV2ViewSet.as_view({
    'get': 'list'
})

device_v3_detail = DeviceV3ViewSet.as_view({
    'get': 'retrieve'
})

device_logs = DeviceLogsViewSet.as_view({
    'get': 'list'
})
device_logs_details = DeviceLogsViewSet.as_view({
    'get': 'retrieve'
})

device_log_meaning = DeviceLogMeaning.as_view({
    'get': 'retrieve'
})

device_limits = DeviceLimitViewSet.as_view({
    'get': 'list',
    'post': 'create'
})

device_limit = DeviceLimitViewSet.as_view({
    'put': 'partial_update',
    'get': 'retrieve',
    'delete': 'destroy'
})

device_runtime_list = DeviceRuntimeViewSet.as_view({
    'get': 'list',
})
device_runtime_detail = DeviceRuntimeViewSet.as_view({
    'get': 'retrieve',
})

# 温控器附加设备
fcubd_list = FcuAttachDeviceView.as_view({
    'post': 'create',
    'get': 'list'
})
fcubd_status = NewFcubdStatusView.as_view({
    'get': 'retrieve'
})

# 设备数据流转
data_routes = DataRouteView.as_view({
    'post': 'create',
    'get': 'list'
})
data_route_detail = DataRouteView.as_view({
    'put': 'partial_update',
    'delete': 'destroy'
})

# sim 卡
# sy_sims = SySimView.as_view({
#     'get': 'list',
#     'post': 'create'
# })
sy_sim_detail = SySimView.as_view({
    'get': 'retrieve',
})

# 分体空调品牌
spac_variants = SpacVariantView.as_view({
    'get': 'list'
})

infrared_codes = InfraredCodeView.as_view({
    'get': 'list'
})

# 直接修改设备的多个参数
attribute_values = AttributeValueView.as_view({
    'post': 'update'
})

# 智能插座单页接口
nna_device = NNADeviceView.as_view({
    'get': 'retrieve',
})
nna_device_ctrl = NNADeviceView.as_view({
    'post': 'create'
})

lta_meters = LTAMeterCollectionView.as_view({
    'get': 'list',
    'put': 'update'
})

urlpatterns = [
    path('saianapi/v1/devices', device_list, name='device-list'),
    path('saianapi/v1/projects/<int:prj>/devices', device_list, name='device-list'),
    path('saianapi/v1/devices/<str:str>', device_detail, name='device-detail'),
    path('saianapi/v1/device_values', device_values, name='device-values'),
    # device attribute
    path('saianapi/v1/attribute_types/<int:pk>/device_attributes', device_attributes, name='at-device-attributes'),
    path('saianapi/v1/device_attributes', device_attributes, name='device-attributes'),
    path('saianapi/v1/web_device_attributes', device_attributes, name='web-device-attributes'),
    # device attribute v5
    path('saianapi/v5/device_attributes/<int:pk>', device_attribute_detail_v5, name='device-attribute-v5'),
    path('saianapi/v5/device_attributes', device_attributes_v5, name='device-attributes-v5'),
    # device timer
    path('saianapi/v1/device_timers', device_timers, name='device_timers'),
    path('saianapi/v1/device_timers/<int:pk>', device_timer, name='device-timer'),
    # device ctrl log
    path('saianapi/v1/device_ctrl_logs', device_ctrl_logs, name='device-ctrl-logs'),
    path('saianapi/v1/device_ctrl_logs/<int:pk>', device_ctrl_log, name='device-ctrl-logs'),
    path('saianapi/v1/device_debugs', device_debugs, name='device_debugs'),
    path('saianapi/v1/device_events', device_events, name='device-events'),
    path('saianapi/v1/device_events/<int:pk>', device_event, name='device-events'),
    path('saianapi/v1/device_live_stats', device_live_stats, name='device-live_stats'),
    path('saianapi/v1/web_devices', web_devices, name='web-devices'),
    path('saianapi/v1/web_devices/<str:pk>', web_device, name='web-device'),
    path('saianapi/v1/web_unbind_devices', web_unbind_devices, name='web-unbind-devices'),
    path('saianapi/v1/device_changes', device_changes, name='device-changes'),
    path('saianapi/v1/web_device_changes', device_changes, name='web-device-changes'),
    path('saianapi/v1/living_detections/<int:device_id>', living_detections, name='living-detections'),
    path('saianapi/v2/devices', device_v2_list, name='device-v2-list'),
    path('saianapi/v2/devices/<int:pk>', device_v2_detail, name='device-v2-detail'),
    path('saianapi/v3/devices/<str:str>', device_v3_detail, name='device-v3-detail'),
    path('saianapi/v2/web_devices', web_device_v2_list, name='web-device-v2-list'),
    path('saianapi/v1/device_logs', device_logs, name='device-logs'),
    path('saianapi/v1/device_logs/<int:pk>', device_logs_details, name='device-logs'),
    path('saianapi/v5/device_log_meaning', device_log_meaning, name='device-log-meaning'),

    path('saianapi/v5/device_limits', device_limits, name='device-limits'),
    path('saianapi/v5/device_limits/<int:pk>', device_limit, name="device-limit"),

    # 运行时间
    path('saianapi/v5/device_runtime', device_runtime_list, name='device-runtime-list'),
    path('saianapi/v5/device_runtime/<int:pk>', device_runtime_detail, name="device-runtime-detail"),

    # 温控器附加设备
    path('saianapi/v5/fcubd', fcubd_list, name='fcu-attach-devices'),
    path('saianapi/v5/fcubd_status', fcubd_status, name='fcu-attach-status'),

    # sim 卡
    # path('saianapi/v5/sy_sims', sy_sims, name='sy-sims'),
    path('saianapi/v5/sy_sims/<str:id>', sy_sim_detail, name='sy-sim-detail'),

    # 分体空调品牌
    path('saianapi/v5/spac_variants', spac_variants, name='spac-variants'),
    path('saianapi/v1/infrared_codes', infrared_codes, name='infrared-codes'),
    # 直接修改设备的多个参数值
    path('saianapi/v1/attribute_values', attribute_values, name='attribute-value'),

    # 智能插座
    path('saianapi/v5/nna_devices/<str:mac>', nna_device, name="nna_device"),
    path('saianapi/v5/nna_device_ctrls', nna_device_ctrl, name="nna_device_ctrl"),
    # LTA 仪表
    path('saianapi/v5/lta_meters', lta_meters, name="lta_meters"),
]
