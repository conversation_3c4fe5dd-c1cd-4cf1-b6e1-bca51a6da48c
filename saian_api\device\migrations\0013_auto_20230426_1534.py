# Generated by Django 3.2.8 on 2023-04-26 15:34

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('device', '0012_dyattribute'),
    ]

    operations = [
        migrations.CreateModel(
            name='ParamRecordHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('mac', models.CharField(db_index=True, max_length=255)),
                ('identifier', models.CharField(max_length=255)),
                ('value', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('device', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='device.device')),
            ],
            options={
                'db_table': 'param_record_histories',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='paramrecordhistory',
            index=models.Index(fields=['mac', 'identifier'], name='param_recor_mac_62f011_idx'),
        ),
        migrations.AddIndex(
            model_name='paramrecordhistory',
            index=models.Index(fields=['device_id', 'identifier'], name='param_recor_device__d9530e_idx'),
        ),
        migrations.AddIndex(
            model_name='paramrecordhistory',
            index=models.Index(fields=['device_id', 'identifier', 'created_at'], name='param_recor_device__c39cf9_idx'),
        ),
        migrations.AddIndex(
            model_name='paramrecordhistory',
            index=models.Index(fields=['mac', 'identifier', 'created_at'], name='param_recor_mac_33d10b_idx'),
        ),
    ]
