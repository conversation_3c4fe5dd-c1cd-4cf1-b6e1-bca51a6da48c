"""
项目仅保留一个月最多 20000 条的消息
"""
import datetime
import traceback

from django.core.management import BaseCommand, CommandError

from saian_api.message.models import Message
from saian_api.project.models import Project
from saian_api.scheduledtask.utils import get_projects, set_global_db

class Command(BaseCommand):
    help = "消息删除任务"

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目列表
        projects = get_projects(self, options)

        for project_id in projects:
            set_global_db(project_id)
            project = Project.objects.get(pk=project_id)

            try:
                self.stdout.write(f"删除消息任务: {project.name}", ending='\n')
                days = 10

                while Message.objects.count() >= 20000:
                    days -= 1
                    month_ago = datetime.datetime.now() - datetime.timedelta(days=days)

                    messages = Message.objects.filter(project=project_id, msg_type__gt=1, created_at__lt=month_ago)
                    delete_count = messages.count()
                    if delete_count:
                        messages.delete()
                        self.stdout.write(f'\t删除{days}天前的消息{delete_count}条。', ending='\n')

            except CommandError:
                self.stderr.write(f"运行'消息删除'任务失败，项目ID：{project_id}，命令参数不合法！", ending='\n')
                continue
            except Exception as e:
                self.stderr.write(f"运行'消息删除'任务失败，项目ID：{project_id}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
                continue
