"""
    维度日统计
"""
import datetime
import logging
import re
import traceback

from django.core.management import BaseCommand, CommandError

from saian_api.dimension.models import DimensionDailyStat, DimensionHourlyStat, DimensionAttribute
from saian_api.project.models import Project
from saian_api.scheduledtask.utils import get_projects, set_global_db
from saian_api.utils.report import do_dimension_stat_cce
from saian_api.utils.tools import is_number

def cus_create(dimension_id, dimension_attribute_id, yesterday, value, min_value: [float, str] = '--', max_value: [float, str] = '--'):
    value = round(value, 3)
    if is_number(min_value):
        min_value = round(min_value, 3)
    if is_number(max_value):
        max_value = round(max_value, 3)

    dds = DimensionDailyStat.objects.filter(dimension_id=dimension_id,
                                            dimension_attribute_id=dimension_attribute_id,
                                            created_at=yesterday).last()
    if dds is None:
        DimensionDailyStat.objects.create(
            dimension_id=dimension_id,
            dimension_attribute_id=dimension_attribute_id,
            avg=value,
            min=min_value,
            max=max_value,
            created_at=yesterday
        )
    else:
        if dds.avg != str(value):
            logging.info(f'{dimension_attribute_id}, {yesterday}, old-{dds.avg}, new-{value}')
            dds.avg = value
            dds.min = min_value
            dds.max = max_value
            # dds.save()

class Command(BaseCommand):
    help = '每日统计维度的数据'

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目ID列表
        projects = get_projects(self, options)
        now = datetime.datetime.now()
        # now = datetime.datetime.strptime('20240513010500', '%Y%m%d%H%M%S')

        for project_id in projects:
            try:
                # 设置全局数据库
                set_global_db(project_id)
                project = Project.objects.get(pk=project_id)

                self.stdout.write(f"维度日统计开始: {project.name}", ending='\n')

                yesterday = now - datetime.timedelta(days=1)
                begin = yesterday.strftime('%Y-%m-%d 00:00:00')
                end = yesterday.strftime('%Y-%m-%d 23:59:59')

                do_dimension_stat_cce(cus_create, DimensionHourlyStat.objects, begin, end)

                # 最后计算有公式的维度属性值
                for da in DimensionAttribute.objects.filter(formula__isnull=False):
                    da_ids = re.findall(r'{{(.*?)}}', da.formula)
                    da_values = []
                    for da_id in da_ids:
                        dds = DimensionDailyStat.objects.filter(created_at=end, dimension_attribute_id=da_id).last()
                        # da = DimensionAttribute.objects.get(pk=da_id)
                        da_values.append(float(dds.avg) if dds is not None and is_number(dds.avg) else 0)
                    # 构建计算表达式
                    eval_str = da.formula
                    for v in da_values:
                        eval_str = re.sub(r'{{(.*?)}}', f'{v}', eval_str, 1)

                    try:
                        value = eval(eval_str)
                        cus_create(da.dimension_id, da.id, end, value)

                    except Exception as e:
                        self.stdout.write(f'计算维度属性 {da.id}-{da.name} 公式时出错，表达式为: {eval_str}; ERROR: {e.__str__()}')

                self.stdout.write(f"维度日统计任务完成: {project.name}", ending='\n')

            except CommandError:
                self.stderr.write(f"运行'维度日统计'任务失败，项目ID：{project_id}，命令参数不合法！", ending='\n')
                continue
            except Exception as e:
                self.stderr.write(f"运行'维度日统计'任务失败，项目ID：{project_id}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
                continue
