"""
  设备定时任务
"""
from django.core.management.base import BaseCommand, CommandError

from saian_api.utils.tools import replenishment_time
from saian_api.group.models import ActionTimer, GroupAction, AcStrategies
from saian_api.device.models import DeviceTimer
from saian_api.project.models import Project
from saian_api.scheduledtask.utils import get_projects, hit_time, set_global_db

import datetime
import traceback
import json


class Command(BaseCommand):
    help = '设备定时任务'

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目列表
        projects = get_projects(self, options)

        now = datetime.datetime.now()

        for project_id in projects:
            try:
                # 存储满足条件的定时任务
                hit_timers = []

                # 设置全局数据库
                set_global_db(project_id)
                project = Project.objects.get(pk=project_id)

                self.stdout.write(f"执行设备定时任务: {project.name}", ending='\n')

                timers = DeviceTimer.objects.filter(device__in=project.device_set.all(), enabled=True, is_finished=False)
                for timer in timers:
                    try:

                        is_effective = False
                        if timer.time_ranges:
                            time_period_list = replenishment_time(now, json.loads(timer.time_ranges))
                            for item in time_period_list:
                                if item['from_at'] <= now < item['till_at']:
                                    is_effective = True
                                    break
                        else:
                            is_effective = True

                        last_run_at = int(timer.updated_at.timestamp())
                        if hit_time(self, timer.run_date, timer.run_time, last_run_at, timer.wdays, None, timer.repeat, now) and is_effective:
                            # 更新时间和状态，避免重复执行
                            timer.updated_at = datetime.datetime.now()
                            if not timer.repeat:
                                timer.is_finished = True
                                ac_strategy = AcStrategies.objects.filter(st_type=40, st_id=timer.id).last()
                                if ac_strategy is not None:
                                    ac_strategy.status = 20
                                    ac_strategy.save()
                            timer.save()

                            hit_timers.append(timer)
                    except Exception:
                        self.stderr.write(traceback.format_exc())
                        continue

                for timer in hit_timers:
                    try:
                        self.stdout.write(f"execute device timer job, id = {timer.id}")
                        timer.execute_job()

                        # 重复任务不会结束
                        # if timer.repeat:
                        #     timer.is_finished = False
                        #     timer.save()
                        #
                        #     ac_strategy = AcStrategies.objects.get(st_type=40, st_id=timer.id)
                        #     ac_strategy.status = 10
                        #     ac_strategy.save()

                    except Exception as e:
                        self.stderr.write(f"运行设备定时任务出错，action_timer_id = {timer.id}, err: {e.__str__()}", ending='\n')
                        self.stderr.write(traceback.format_exc(), ending='')

                        # 重复任务异常也要恢复原样
                        # if timer.repeat:
                        #     timer.is_finished = False
                        #     timer.save()
                        #
                        #     ac_strategy = AcStrategies.objects.get(st_type=40, st_id=timer.id)
                        #     ac_strategy.status = 10
                        #     ac_strategy.save()
                        continue
            except Exception:
                self.stderr.write(
                    f"执行设备定时任务出错，project_id = {project_id}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
                continue
