# from aliyunsdkcore.client import AcsClient
# from aliyunsdkdysmsapi.request.v20170525 import SendSmsRequest
# from aliyunsdkcore.acs_exception.exceptions import ClientException
# from aliyunsdkcore.acs_exception.exceptions import ServerException
import logging

import requests
from django.http import JsonResponse
import os

from saian_api.utils.inthttpapi import DOMAIN

class AliSms:
    @staticmethod
    def send(mobile, templateCode, templateParam):
        # # 你的AccessKey ID和AccessKey Secret
        # access_key_id = APP_KEY
        # access_key_secret = APP_SECRET
        # # 初始化AcsClient
        # client = AcsClient(access_key_id, access_key_secret, 'cn-hangzhou')
        #
        # # 初始化SendSmsRequest实例用于设置API的输入参数
        # request = SendSmsRequest.SendSmsRequest()
        # request.set_PhoneNumbers(mobile)  # 请替换为实际手机号码，多个号码使用英文逗号分隔
        # request.set_SignName("塞安科技")  # 请替换为你的签名名称
        # request.set_TemplateCode(templateCode)  # 请替换为你的模板CODE
        # request.set_TemplateParam(templateParam)  # 请根据实际情况替换模板中的变量，例如验证码等
        url = f'{DOMAIN}/alisms/send'
        data = {
            'mobile': mobile,
            'templateCode': templateCode,
            'templateParam': templateParam
        }

        try:
            # 发起请求并得到结果
            response = requests.post(url, json=data)
            # 加载返回结果
            logging.info(f'send sms result: {response.status_code} - {response.text}')
            return JsonResponse({"status": "success", "message": "短信发送成功"})
        except Exception as e:
            logging.error(f'fail to send sms, error: {e.__str__()}')
            return JsonResponse({"status": "error", "message": f"请求错误: {e.__str__()}"}, status=500)

