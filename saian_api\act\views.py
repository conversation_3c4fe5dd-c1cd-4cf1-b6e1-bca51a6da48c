from datetime import datetime, timedelta

from django.shortcuts import get_object_or_404
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

# Create your views here.
from saian_api.act.models import AcTerminal, ActColdStat
from saian_api.act.serializers import AcTerminalReadSerializer, AcTerminalWriteSerializer, ActColdStatSerializer
from saian_api.utils.sy_jsonrenderer import ResponseUtils
from saian_api.utils.utils import DatetimeUtils

class ActListView(APIView):

    def get(self, request):
        acts = AcTerminal.objects.filter(project__id=request.user['project_id'])
        serializer = AcTerminalReadSerializer(acts, many=True)

        return Response(ResponseUtils.custom_response_format(serializer.data, 'ac_terminals', True))

    def post(self, request):
        data = request.data
        data['device_prototype'] = data.get('dpid')
        serializer = AcTerminalWriteSerializer(data=data)

        if 'project' not in request.data:
            request.data['project'] = request.user['project_id']

        if serializer.is_valid():
            serializer.save()
            return Response(ResponseUtils.custom_response_format(serializer.data, 'ac_terminals', True))
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class ActDetailView(APIView):

    def get(self, request, pk):
        act = get_object_or_404(AcTerminal, pk=pk)
        serializer = AcTerminalReadSerializer(act)
        return Response(ResponseUtils.custom_response_format(serializer.data, 'ac_terminal', True))

    def put(self, request, pk):
        act = get_object_or_404(AcTerminal, pk=pk)
        data = request.data
        data['device_prototype'] = act.device_prototype_id
        serializer = AcTerminalWriteSerializer(instance=act, data=data)

        if 'project' not in request.data:
            request.data['project'] = request.user['project_id']

        if serializer.is_valid():
            serializer.save()
            return Response(ResponseUtils.custom_response_format(serializer.data, 'ac_terminal', True))

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk):
        act = get_object_or_404(AcTerminal, pk=pk)
        serializer = AcTerminalReadSerializer(act)
        act.delete()
        return Response(ResponseUtils.custom_response_format(serializer.data, 'ac_terminal', True))

class ActColdStatsView(APIView):

    def get(self, request):
        query_from = request.query_params.get('from', None)
        query_till = request.query_params.get('till', None)

        # 时间段，默认过去24小时
        now = datetime.now()
        if query_from is None:
            time_from = now - timedelta(days=1)
        else:
            time_from = DatetimeUtils.get_date_time_from_string(query_from)

        if query_till is None:
            time_till = now
        else:
            time_till = DatetimeUtils.get_date_time_from_string(query_till)

        stats = ActColdStat.objects.filter(created_at__range=(time_from, time_till),
                                           project__id=request.user['project_id']).order_by('created_at')

        # 如果没有提供时间区间，并数据量少于24，则取最后24个
        if query_from is None and query_till is None and stats.count() < 24:
            stats = ActColdStat.objects.filter(project__id=request.user['project_id']).order_by('-id')[:24:-1]

        serializer = ActColdStatSerializer(stats, many=True)

        return Response(ResponseUtils.custom_response_format(serializer.data, 'stats', True))
