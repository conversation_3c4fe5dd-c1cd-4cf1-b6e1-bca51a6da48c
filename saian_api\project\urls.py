from django.urls import path
from rest_framework.routers import SimpleRouter

from .views import (
    ProjectDetail,
    WebChartsView,
    WebPanelsApiView,
    ProjectPanelsListView,
    ProjectPanelDetailView,
    EnviromentViewSet,
    WebProjectViewSet,
    WebMenuViewSet,
    WebManualView,
    WebManualDetailView,
    ProjectChartListView,
    ProjectChartDetailView, ProjectSettingsViewSet,
    ProjectSettingItemView, AcRunMode, DashboardProjectInfoView,
    IsWorkdayView
)

router = SimpleRouter()

# router.register('saianapi/v1/project_charts', ProjectChartsViewSet, basename='project_charts')
# router.register('saianapi/v1/project_panels', ProjectPanelsViewSet, basename='project_panels')

menu_list = WebMenuViewSet.as_view({
    'get': 'list',
    'post': 'create'
})

menu_detail = WebMenuViewSet.as_view({
    'get': 'retrieve',
    'put': 'partial_update',
    'delete': 'destroy'
})


enviroment = EnviromentViewSet.as_view({
    'get': 'list'
})

web_project = WebProjectViewSet.as_view({
    'get': 'retrieve',
    'put': 'partial_update'
})

project_setting = ProjectSettingsViewSet.as_view({
    'get': 'retrieve',
    'put': 'update'
})

project_setting_item = ProjectSettingItemView.as_view({
    'get': 'retrieve',
})

ac_run_modes = AcRunMode.as_view({
    'get': 'list',
    'post': 'create'
})

web_charts = WebChartsView.as_view({
    'get': 'list',
    'post': 'create'
})

web_chart = WebChartsView.as_view({
    'get': 'retrieve',
    'put': 'partial_update',
    'delete': 'destroy'
})

dashboard_project_view = DashboardProjectInfoView.as_view({
    'get': 'list'
})

is_workday = IsWorkdayView.as_view({
    'get': 'retrieve'
})

urlpatterns = [
    path('saianapi/v1/projects/<int:pk>', ProjectDetail.as_view()),
    # path('saianapi/v1/web_projects/<int:pk>', ProjectDetail.as_view()),
    path('saianapi/v1/project_charts', ProjectChartListView.as_view()),
    path('saianapi/v1/project_charts/<int:pk>', ProjectChartDetailView.as_view()),
    path('saianapi/v1/web_charts', web_charts, name='web_charts'),
    path('saianapi/v1/web_charts/<int:pk>', web_chart, name='web_chart'),
    path('saianapi/v1/web_panels', WebPanelsApiView.as_view()),
    path('saianapi/v1/project_panels', ProjectPanelsListView.as_view()),
    path('saianapi/v1/project_panels/<int:pk>', ProjectPanelDetailView.as_view()),
    path('saianapi/v1/web_menus', menu_list, name='menu-list'),
    path('saianapi/v1/web_menus/<int:pk>', menu_detail, name='menu-detail'),
    path('saianapi/v1/enviroments', enviroment, name='enviroment'),
    path('saianapi/v1/projects/<int:prj>/enviroments', enviroment, name='prj_enviroment'),
    path('saianapi/v1/web_projects/<int:pk>', web_project, name='web-project'),
    path('saianapi/v1/web_manuals', WebManualView.as_view()),
    path('saianapi/v1/web_manuals/<int:pk>', WebManualDetailView.as_view()),
    path('saianapi/v5/project_settings', project_setting, name="project-settings"),
    path('saianapi/v5/project_settings/<str:name>', project_setting_item, name='project-setting-item'),
    path('saianapi/v5/ac_run_modes', ac_run_modes, name='ac-run-mode'),
    path('saianapi/v5/project_info', dashboard_project_view, name='project-info'),
    path('saianapi/v5/is_workday', is_workday, name='is-workday'),
]
