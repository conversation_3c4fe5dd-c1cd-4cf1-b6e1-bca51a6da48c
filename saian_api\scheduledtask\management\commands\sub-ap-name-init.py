"""
初始化：子设备属性名前缀，随子设备NickName变化
"""
import traceback

from django.core.management import BaseCommand, CommandError
from saian_api.device.models import DeviceLimit, DeviceTimer

from saian_api.group.models import AcStrategies, ActionTimer
from saian_api.linkage.models import LinkageRule, LinkageTrigger
from saian_api.project.models import Project
from saian_api.devdefine.models import DevicePrototype,AttributePrototype
from saian_api.scheduledtask.utils import get_projects, set_global_db


class Command(BaseCommand):
    help = "添加运行策略任务"

    # def add_arguments(self, parser):
    #     parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目列表
        # projects = get_projects(self, options)

        # for project_id in projects:
        # projects = [46, 48, 58, 66, 67, 68, 69, 70, 71]
        projects = [44]
        dp_id = 140
        for project_id in projects:
            try:
                set_global_db(project_id)
                # ac_strategies = AcStrategies.objects.filter(st_type__in=[20, 30])
                # delete_count = ac_strategies.count()
                # if delete_count:
                #     ac_strategies.delete()
                #     self.stdout.write(f'\t清除运行策略{delete_count}条。', ending='\n')
                project = Project.objects.get(pk=project_id)
                self.stdout.write(f"更新子设备属性名: {project.name}", ending='\n')
                
                for ap in AttributePrototype.objects.filter(identifier__endswith='_NickName'):
                  old_prefix = da.value if '_NickName' in identifier and da.value else ap.name.replace('昵称', "")


                # action_timers = ActionTimer.objects.all()
                # for timer in action_timers:
                #     if not AcStrategies.objects.filter(st_type=10, st_id=timer.id).exists():
                #         AcStrategies.objects.create(
                #             st_type=10,
                #             st_id=timer.id,
                #             status=20 if timer.is_finished else 10,
                #             created_at=timer.created_at,
                #             updated_at=timer.updated_at
                #         )
                self.stdout.write(f'\t从定时分组操作新增运行策略{action_timers.count()}条。', ending='\n')


            except CommandError:
                self.stderr.write(f"运行'添加运行策略'任务失败，项目ID：{project_id}，命令参数不合法！", ending='\n')
                # continue
            except Exception as e:
                self.stderr.write(f"运行'添加运行策略'任务失败，项目ID：{project_id}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
                # continue
