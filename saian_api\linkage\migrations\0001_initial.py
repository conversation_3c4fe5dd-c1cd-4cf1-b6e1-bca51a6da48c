# Generated by Django 3.2.8 on 2021-12-30 15:16

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='CrossAttribute',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ('identifier', models.Char<PERSON>ield(max_length=255, unique=True)),
                ('data_type', models.IntegerField()),
                ('default_value', models.Char<PERSON>ield(max_length=255, null=True)),
                ('remark', models.Char<PERSON>ield(max_length=255, null=True)),
                ('min', models.Char<PERSON>ield(max_length=255, null=True)),
                ('max', models.Char<PERSON>ield(max_length=255, null=True)),
            ],
            options={
                'db_table': 'cross_attributes',
            },
        ),
        migrations.CreateModel(
            name='CrossAttributePrototype',
            fields=[
                ('id', models.<PERSON><PERSON><PERSON><PERSON>ield(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
            options={
                'db_table': 'cross_attribute_prototypes',
            },
        ),
        migrations.CreateModel(
            name='LinkageAttribute',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('attribute_id', models.PositiveBigIntegerField()),
            ],
            options={
                'db_table': 'linkage_attributes',
            },
        ),
        migrations.CreateModel(
            name='LinkageRule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('trigger_type', models.IntegerField()),
                ('code', models.TextField()),
                ('enabled', models.BooleanField(default=False)),
                ('remark', models.CharField(max_length=255, null=True)),
                ('show_in_so', models.BooleanField(default=False)),
                ('so_name', models.CharField(max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'linkage_rules',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='LinkageSnpVar',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_trigger', models.BooleanField(default=False)),
            ],
            options={
                'db_table': 'linkage_snp_vars',
            },
        ),
        migrations.CreateModel(
            name='LinkageTarget',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('target_id', models.PositiveBigIntegerField()),
            ],
            options={
                'db_table': 'linkage_targets',
            },
        ),
        migrations.CreateModel(
            name='LinkageTrigger',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('repeat', models.BooleanField(default=False)),
                ('run_date', models.DateField(null=True)),
                ('run_time', models.TimeField(null=True)),
                ('wdays', models.CharField(max_length=255, null=True)),
                ('is_finished', models.BooleanField(default=False)),
                ('interval', models.IntegerField(null=True)),
                ('last_run_at', models.DateTimeField(null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'linkage_triggers',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='LinkageVar',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('mac', models.CharField(max_length=255)),
                ('identifier', models.CharField(max_length=255)),
                ('is_trigger', models.BooleanField(default=False)),
            ],
            options={
                'db_table': 'linkage_vars',
            },
        ),
        migrations.CreateModel(
            name='SnpVar',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('mac', models.CharField(max_length=255)),
                ('identifier', models.CharField(max_length=255)),
                ('var_type', models.IntegerField()),
            ],
            options={
                'db_table': 'snp_vars',
            },
        ),
    ]
