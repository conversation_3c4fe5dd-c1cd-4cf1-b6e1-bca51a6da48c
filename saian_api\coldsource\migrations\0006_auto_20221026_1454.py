# Generated by Django 3.2.8 on 2022-10-26 14:54

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0006_webuser_msg_types'),
        ('device', '0009_auto_20221010_1142'),
        ('terminal', '0005_auto_20220906_1030'),
        ('coldsource', '0005_alter_cseeranalyse_created_at'),
    ]

    operations = [
        migrations.AddField(
            model_name='ecmeter',
            name='formula',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='ecmeter',
            name='is_virtual',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='ecmeter',
            name='terminal',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='terminal.terminal'),
        ),
        migrations.AlterField(
            model_name='ecmeter',
            name='device',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='device.device'),
        ),
        migrations.AlterField(
            model_name='ecmeter',
            name='idx',
            field=models.IntegerField(null=True),
        ),
        migrations.CreateModel(
            name='ManualMeterReading',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('identifier', models.CharField(max_length=255)),
                ('value', models.CharField(max_length=255)),
                ('reading_time', models.DateTimeField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('ec_meter', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='coldsource.ecmeter')),
                ('web_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='user.webuser')),
            ],
            options={
                'db_table': 'manual_meter_readings',
            },
        ),
    ]
