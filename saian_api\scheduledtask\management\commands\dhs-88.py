import datetime
import logging
import math
import re

from django.core.management import BaseCommand
from django.db.models import Avg

from saian_api.dimension.models import DimensionHourlyStat, DimensionMonthlyStat, DimensionDailyStat, DimensionAttribute
from saian_api.project.models import Project
from saian_api.scheduledtask.utils import get_projects, set_global_db
from saian_api.utils.tools import is_number

class Command(BaseCommand):
    help = '每小时统计维度的数据'

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    @staticmethod
    def cus_create(dimension, dimension_attribute, value, min_value='--', max_value='--', now=None):
        if now is None:
            datetime.datetime.now()
        created_at = (now - datetime.timedelta(hours=1)).strftime('%Y-%m-%d %H:59:59')
        record = DimensionHourlyStat.objects.filter(dimension_id=dimension.id, dimension_attribute_id=dimension_attribute.id,
                                                    created_at=created_at).last()

        if record is None:
            DimensionHourlyStat.objects.create(
                dimension_id=dimension.id,
                dimension_attribute_id=dimension_attribute.id,
                avg=value,
                min=min_value,
                max=max_value,
                created_at=created_at
            )
        else:
            if record.avg != str(value):
                logging.info(f'{dimension_attribute.name}, {created_at}, old-{record.avg}, new-{value}')
                record.avg = value
                # record.save()

    def handle(self, *args, **options):
        # 从命令行解释的项目ID列表
        projects = get_projects(self, options)
        # projects = [66]
        # 统计任务开始时的时间。
        now = datetime.datetime.now()
        # now = datetime.datetime.strptime('20240501005500', '%Y%m%d%H%M%S')

        for project_id in projects:
            try:
                # 设置全局数据库
                set_global_db(project_id)
                project = Project.objects.get(pk=project_id)

                self.stdout.write(f"开始生成维度小时数据: {project.name}", ending='\n')
                if now.minute <= 5:
                    continue

                for da in DimensionAttribute.objects.filter(formula__isnull=True):
                    target_da_id = da.id
                    target_dimension_id = da.dimension_id

                    ec_month = DimensionMonthlyStat.objects.filter(dimension_attribute_id=target_da_id, dimension_id=target_dimension_id,
                                                                   created_at__month=now.month).aggregate(avg=Avg('avg'))['avg']

                    ref_ec_month = float(DimensionMonthlyStat.objects.get(dimension_attribute_id=target_da_id, dimension_id=target_dimension_id,
                                                                          created_at__year=now.year - 1, created_at__month=now.month).avg)

                    ref_day = float(DimensionDailyStat.objects.get(dimension_attribute_id=target_da_id, dimension_id=target_dimension_id,
                                                                   created_at__year=now.year - 1,
                                                                   created_at__month=now.month, created_at__day=now.day).avg)

                    ref_hour = float(DimensionHourlyStat.objects.get(dimension_attribute_id=target_da_id, dimension_id=target_dimension_id,
                                                                     created_at__year=now.year - 1,
                                                                     created_at__month=now.month, created_at__day=now.day,
                                                                     created_at__hour=now.hour).avg)

                    day_cons = ec_month * (ref_day / ref_ec_month)
                    hour_cons = day_cons * (ref_hour / ref_day)
                    now_cons = hour_cons * ((math.ceil((now.minute + 1) / 5)) / (60 / 5))

                    created_at = now.strftime('%Y-%m-%d %H:59:59')
                    # print(created_at, da.name, day_cons, hour_cons, now_cons)

                    dhs = DimensionHourlyStat.objects.filter(dimension_attribute_id=target_da_id, dimension_id=target_dimension_id,
                                                             created_at=created_at).last()
                    if dhs is not None:
                        if str(dhs.avg) != str(now_cons):
                            print(f'\t {created_at}- old: {dhs.avg}, new: {now_cons}')
                            dhs.avg = now_cons
                            dhs.save()
                    else:
                        DimensionHourlyStat.objects.create(
                            dimension_id=target_dimension_id,
                            dimension_attribute_id=target_da_id,
                            avg=now_cons,
                            min=0,
                            max=0,
                            created_at=created_at
                        )
                        pass

                # 最后计算有公式的维度属性值
                created_at = now.strftime('%Y-%m-%d %H:59:59')
                for da in DimensionAttribute.objects.filter(formula__isnull=False):
                    da_ids = re.findall(r'{{(.*?)}}', da.formula)
                    da_values = []
                    for da_id in da_ids:
                        dhs = DimensionHourlyStat.objects.filter(created_at=created_at, dimension_attribute_id=da_id).last()
                        # da = DimensionAttribute.objects.get(pk=da_id)
                        da_values.append(float(dhs.avg) if dhs is not None and is_number(dhs.avg) else 0)
                    # 构建计算表达式
                    eval_str = da.formula
                    for v in da_values:
                        eval_str = re.sub(r'{{(.*?)}}', f'{v}', eval_str, 1)

                    try:
                        value = eval(eval_str)
                        # print(f'formula str: {eval_str}, value: {value}')

                        dhs = DimensionHourlyStat.objects.filter(dimension_attribute_id=da.id, dimension_id=da.dimension_id,
                                                                 created_at=created_at).last()
                        if dhs is not None:
                            if str(dhs.avg) != str(value):
                                # print(f'\t {created_at}-{da.name} old: {dhs.avg}, new: {value}')
                                dhs.avg = value
                                dhs.save()
                        else:
                            DimensionHourlyStat.objects.create(
                                dimension_id=da.dimension_id,
                                dimension_attribute_id=da.id,
                                avg=value,
                                min=0,
                                max=0,
                                created_at=created_at
                            )

                    except Exception as e:
                        self.stdout.write(f'计算维度属性 {da.id}-{da.name} 公式时出错，表达式为: {eval_str}; ERROR: {e.__str__()}')

            except Exception as e:
                logging.error(f'error: {e.__str__()}')
