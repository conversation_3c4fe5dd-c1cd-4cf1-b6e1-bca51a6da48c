from django.test import TestCase, SimpleTestCase, TransactionTestCase
from .models import SnpVar
from saian_api.project.models import Project


# Create your tests here.

class TestSnpVarManager(SimpleTestCase):
    # databases = 'default'

    def test_var_value(self):
        identifier = "S_OutDoorTemp"
        project = Project.objects.get(pk=11)

        print(SnpVar.objects.var_value(identifier, project))
