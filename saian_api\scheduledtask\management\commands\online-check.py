"""
  维护设备在离线状态
"""
import datetime
import traceback
from datetime import datetime, timedelta

from django.core.management.base import BaseCommand, CommandError

from saian_api.devdefine.models import DevicePrototype
from saian_api.device.models import Device, DeviceEvent
from saian_api.message.models import Message
from saian_api.notifi.models import Notification
from saian_api.project.models import Project
from saian_api.scheduledtask.utils import get_projects, set_global_db
from saian_api.terminal.models import Terminal, TerminalAttribute
from saian_api.utils.inthttpapi.device import AdminDeviceApi
from saian_api.utils.tools import is_number

class Command(BaseCommand):
    help = '定时维护设备在离线状态'

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目ID列表
        projects = get_projects(self, options)

        for project_id in projects:
            try:
                # 设置全局数据库
                set_global_db(project_id)
                project = Project.objects.get(pk=project_id)
                now = datetime.now()

                self.stdout.write(f"\n设备在线检查: {project.name}", ending='')

                project_dps = set(Device.objects.values_list('device_prototype_id', flat=True))
                for dp_id in project_dps:
                    dp = DevicePrototype.objects.get(pk=dp_id)
                    timeout = dp.timeout_set
                    if not is_number(timeout):
                        timeout = 7200

                    offline_threshold = now - timedelta(seconds=timeout)
                    offline_candidate = Device.objects.filter(updated_at__lte=offline_threshold, device_prototype_id=dp_id,
                                                              online=True, project_id__isnull=False)

                    offline_terminals = []
                    offline_devices = []
                    if offline_candidate.exists():
                        for device in offline_candidate:
                            # 验收模式不更新状态
                            if device.in_acc:
                                continue
                            # DTU网关 在线状态处理
                            last_event = DeviceEvent.objects.filter(mac__startswith=device.mac).exclude(mac=device.mac).order_by('created_at').last()
                            if last_event is not None and (now - last_event.created_at).total_seconds() < 7200:
                                continue

                            # 标记设备离线
                            device.online = False
                            device.status = 30
                            offline_devices.append(device)

                            # 离线对应的终端离线
                            for terminal in Terminal.objects.filter(device_id=device.id, online=True):
                                terminal.online = False
                                offline_terminals.append(terminal)

                            Message.objects.device_offline(device)
                            data = {
                                'content': f'设备离线: 设备【{device.id}-{device.nick_name}】离线',
                                'device_id': device.id,
                                'device_nickname': device.nick_name
                            }
                            Notification.objects.notify(4, device, data)
                        Device.objects.bulk_update(offline_devices, fields=['online', 'status'])
                        Terminal.objects.bulk_update(offline_terminals, fields=['online'])
                        # Device.objects.using('syadmindb').bulk_update(offline_devices, fields=['online', 'status'])
                        # for device in offline_devices:
                        #     AdminDeviceApi.update_device(device.id, {'online': device.online, 'status': device.status})

                        self.stdout.write(f"\n\t设备类型-{dp.name} 离线设备数：{len(offline_devices)}", ending='\n')

                # 终端在线检查
                dps = DevicePrototype.objects.filter(id__in=Terminal.objects.values_list('device_prototype', flat=True)).values('id', 'timeout_set')
                update_terminals = []

                for dp in dps:
                    dp_id = dp.get('id')
                    timeout = dp.get('timeout_set', None)
                    if timeout is None:
                        timeout = 7200

                    terminals = Terminal.objects.filter(device_prototype_id=dp_id)

                    for terminal in terminals:
                        last_updated = terminal.updated_at
                        last_updated_ta = TerminalAttribute.objects.filter(terminal=terminal).order_by('updated_at').last()
                        if last_updated_ta is not None and last_updated_ta.updated_at > last_updated:
                            last_updated = last_updated_ta.updated_at
                        is_offline = (now - last_updated).total_seconds() > timeout
                        if is_offline and terminal.terminal_type == 40 and terminal.prefix is None and terminal.idx is None:
                            device = Device.objects.get(pk=terminal.device_id)
                            last_updated = device.updated_at
                            is_offline = (now - last_updated).total_seconds() > timeout

                        if is_offline and terminal.online is True:
                            terminal.online = False
                            data = {
                                'content': f'终端离线: 终端【{terminal.id}-{terminal.nick_name}】离线',
                                'terminal_id': terminal.id,
                                'terminal_nickname': terminal.nick_name
                            }
                            Notification.objects.notify(4, terminal.device, data, terminal=terminal)
                            update_terminals.append(terminal)
                        if not is_offline and terminal.online is False and terminal.device.online:
                            terminal.online = True
                            update_terminals.append(terminal)
                        if not is_offline and terminal.online is True and terminal.device.online is False:
                            terminal.online = False
                            update_terminals.append(terminal)

                Terminal.objects.bulk_update(update_terminals, fields=['online'])

            except CommandError:
                self.stderr.write(f"运行'设备在线检查'任务失败，项目ID：{project_id}，命令参数不合法！", ending='\n')
                continue
            except Exception as e:
                self.stderr.write(f"运行'设备在线检查'任务失败，项目ID：{project_id}, error: {e.__str__()}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
                continue
