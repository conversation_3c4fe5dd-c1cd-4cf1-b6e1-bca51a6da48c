"""
计算电表峰平谷尖累计值
"""
import datetime
import json
import traceback

from django.core.management import BaseCommand, CommandError
from saian_api.device.models import DaSnapshot, DaSnapshotHistory, Device, DeviceAttribute, DeviceEvent, DeviceEventHistory

from saian_api.project.models import Project
from saian_api.report.models import DeviceHourlyStat
from saian_api.scheduledtask.utils import get_projects, set_global_db


class Command(BaseCommand):
    help = "计算电表峰平谷尖累计值"

    # def add_arguments(self, parser):
    #     parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目列表
        # projects = get_projects(self, options)

        projects = [89]
        for project_id in projects:
            try:
                set_global_db(project_id)
                # ac_strategies = AcStrategies.objects.filter(st_type__in=[20, 30])
                # delete_count = ac_strategies.count()
                # if delete_count:
                #     ac_strategies.delete()
                #     self.stdout.write(f'\t清除运行策略{delete_count}条。', ending='\n')
                project = Project.objects.get(pk=project_id)
                self.stdout.write(f"计算电表峰平谷尖累计值: {project.name}", ending='\n')

                device_id = 17898
                ap_id = 36903
                idf = 'Meter_kVarh_1'

                # 高峰时段9:00-12:00，16:00-21:00;
                # 低谷时段23:00-7:00:
                # 平时段7:00-9:00，12:00-16:00,21:00-23:00;
                # 其中，1、12月高峰时段中11:00-12:00，18:00-19:00为尖峰时段;7、8月高峰时段中11:00-12:00，16:00-17:00为尖峰时段。
                ppv_config_10 = [9, 10, 11, 16, 17, 18, 19, 20]
                ppv_config_20 = [7, 8, 12, 13, 14, 15, 21, 22]
                ppv_config_30 = [0, 1, 2, 3, 4, 5, 6, 23]
                # ppv_config_40_112 = [11, 18]
                # ppv_config_40_78 = [11, 16]
                # dhs = DeviceHourlyStat.objects.filter(device_id=17898, identifier='Meter_Power_Cons_3')
                # dhs_20 = list(filter(lambda x: x.created_at.hour in ppv_config_20, dhs))
                # dhs_30 = list(filter(lambda x: x.created_at.hour in ppv_config_30, dhs))
                # dhs_40 = list(filter(lambda x: (x.created_at.month in [1, 12] and x.created_at.hour in ppv_config_40_112) or (
                #     x.created_at.month in [7, 8] and x.created_at.hour in ppv_config_40_78), dhs))
                # dhs_40_ids = [dhs.id for dhs in dhs_40]
                # dhs_10 = list(filter(lambda x: x.created_at.hour in ppv_config_10 and x.id not in dhs_40_ids, dhs))

                # Meter_Power_Cons_P_3 = round(sum([float(x.avg) for x in dhs_10]), 2)
                # Meter_Power_Cons_F_3 = round(sum([float(x.avg) for x in dhs_20]), 2)
                # Meter_Power_Cons_V_3 = round(sum([float(x.avg) for x in dhs_30]), 2)
                # Meter_Power_Cons_S_3 = round(sum([float(x.avg) for x in dhs_40]), 2)
                device = Device.objects.get(id=device_id)

                # da_P = DeviceAttribute.objects.query_object_by_idf(device, 'Meter_Power_Cons_P_3')
                # da_P.value = Meter_Power_Cons_P_3
                # DeviceAttribute.objects.save_to_redis(device, da_P)

                # da_F = DeviceAttribute.objects.query_object_by_idf(device, 'Meter_Power_Cons_F_3')
                # da_F.value = Meter_Power_Cons_F_3
                # DeviceAttribute.objects.save_to_redis(device, da_F)

                # da_V = DeviceAttribute.objects.query_object_by_idf(device, 'Meter_Power_Cons_V_3')
                # da_V.value = Meter_Power_Cons_V_3
                # DeviceAttribute.objects.save_to_redis(device, da_V)

                # da_S = DeviceAttribute.objects.query_object_by_idf(device, 'Meter_Power_Cons_S_3')
                # da_S.value = Meter_Power_Cons_S_3
                # DeviceAttribute.objects.save_to_redis(device, da_S)

                # tmp_P = 0
                # tmp_F = 0
                # tmp_V = 0
                # tmp_S = 0
                # sum = 0
                
                da_P = DeviceAttribute.objects.query_object_by_idf(device, idf[:-2] + '_P' + idf[-2:])
                da_F = DeviceAttribute.objects.query_object_by_idf(device, idf[:-2] + '_F' + idf[-2:])
                da_V = DeviceAttribute.objects.query_object_by_idf(device, idf[:-2] + '_V' + idf[-2:])
                da_S = DeviceAttribute.objects.query_object_by_idf(device, idf[:-2] + '_S' + idf[-2:])
                da_sum = DeviceAttribute.objects.query_object_by_idf(device, idf)

                tmp_P = float(da_P.value) if da_P is not None else 0
                tmp_F = float(da_F.value) if da_F is not None else 0
                tmp_V = float(da_V.value) if da_V is not None else 0
                tmp_S = float(da_S.value) if da_S is not None else 0
                sum = da_sum
                
                his_records = DaSnapshotHistory.objects.filter(device_id=device_id, attribute_prototype_id=ap_id).order_by('snapped_at')
                his_begin_at = his_records.first().snapped_at.replace(minute=0, second=0, microsecond=0)
                his_end_at = his_records.last().snapped_at.replace(minute=0, second=0, microsecond=0)

                his_interval = (his_end_at-his_begin_at).total_seconds() / 3600 + 2

                his_dt_1 = his_begin_at - datetime.timedelta(hours=1)
                for i in range(int(his_interval)):
                    value = 0
                    his_dt_1 = his_dt_1 + datetime.timedelta(hours=1)
                    his_dt_2 = his_dt_1.replace(minute=59, second=59, microsecond=999999)
                    his_dt_3 = his_dt_1 + datetime.timedelta(hours=1)
                    his_dt_4 = his_dt_3.replace(minute=59, second=59, microsecond=999999)

                    his_filter_1 = his_records.filter(snapped_at__range=[his_dt_1, his_dt_2]).order_by('snapped_at')
                    his_filter_2 = his_records.filter(snapped_at__range=[his_dt_3, his_dt_4]).order_by('snapped_at')

                    if his_filter_1.count() > 1 and his_filter_2.count() > 1:
                        min = float(his_filter_1.last().value)
                        max = float(his_filter_2.last().value)
                        value = round(max - min, 2)
                        flag = ''
                        if his_dt_3.hour in ppv_config_10 and value > 0:
                            if (his_dt_3.month != 1 and his_dt_3.month != 12) and (his_dt_3.hour == 11 or his_dt_3.hour == 18):
                                tmp_S = tmp_S - value
                                tmp_P = tmp_P + value
                                flag = f'P:{tmp_P},S:{tmp_S}'
                            elif (his_dt_3.month != 7 and his_dt_3.month != 8) and (his_dt_3.hour == 11 or his_dt_3.hour == 16):
                                tmp_S = tmp_S - value
                                tmp_P = tmp_P + value
                                flag = f'P:{tmp_P},S:{tmp_S}'

                        # if his_dt_3.hour in ppv_config_10:
                        #     if (his_dt_3.month == 1 or his_dt_3.month == 12) and (his_dt_3.hour == 11 or his_dt_3.hour == 18):
                        #         tmp_S = tmp_S + value
                        #         flag = flag.join('S')
                        #     elif (his_dt_3.month == 7 or his_dt_3.month == 8) and (his_dt_3.hour == 11 or his_dt_3.hour == 16):
                        #         tmp_S = tmp_S + value
                        #         flag = flag.join('S')
                        #     else:
                        #         tmp_P = tmp_P + value
                        #         flag = flag.join('P')
                        # elif his_dt_3.hour in ppv_config_20:
                        #     tmp_F = tmp_F + value
                        #     flag = flag.join('F')
                        # elif his_dt_3.hour in ppv_config_30:
                        #     tmp_V = tmp_V + value
                        #     flag = flag.join('V')

                        # sum = sum + value
                        # print(his_dt_3, flag, round(max, 2), value, round(sum, 2))
                        # print(round(min, 2))
                        if flag:
                            self.stdout.write(f'{his_dt_3}\t{round(min, 2)}\t{round(max, 2)}\t{value}\t{flag}', ending='\n')

                # record = DaSnapshot.objects.filter(device_id=device_id, attribute_prototype_id=ap_id).order_by('snapped_at')
                # record_begin_at = record.first().snapped_at.replace(minute=0, second=0, microsecond=0)
                # record_end_at = record.last().snapped_at.replace(minute=0, second=0, microsecond=0)

                # record_interval = (record_end_at-record_begin_at).total_seconds() / 3600 + 2
                
                # record_dt_1 = record_begin_at - datetime.timedelta(hours=1)
                # for i in range(int(record_interval)):
                #     value = 0
                #     record_dt_1 = record_dt_1 + datetime.timedelta(hours=1)
                #     record_dt_2 = record_dt_1.replace(minute=59, second=59, microsecond=999999)
                #     record_dt_3 = record_dt_1 + datetime.timedelta(hours=1)
                #     record_dt_4 = record_dt_3.replace(minute=59, second=59, microsecond=999999)

                #     record_filter_1 = record.filter(snapped_at__range=[record_dt_1, record_dt_2]).order_by('snapped_at')
                #     record_filter_2 = record.filter(snapped_at__range=[record_dt_3, record_dt_4]).order_by('snapped_at')

                #     if record_filter_1.count() > 1 and record_filter_2.count() > 1:
                #         min = float(record_filter_1.last().value)
                #         max = float(record_filter_2.last().value)
                #         value = round(max - min, 2)
                #         flag = ''
                #         if record_dt_3.hour in ppv_config_10:
                #             if (record_dt_3.month == 1 or record_dt_3.month == 12) and (record_dt_3.hour == 11 or record_dt_3.hour == 18):
                #                 tmp_S = tmp_S + value
                #                 flag = flag.join('S')
                #             elif (record_dt_3.month == 7 or record_dt_3.month == 8) and (record_dt_3.hour == 11 or record_dt_3.hour == 16):
                #                 tmp_S = tmp_S + value
                #                 flag = flag.join('S')
                #             else:
                #                 tmp_P = tmp_P + value
                #                 flag = flag.join('P')
                #         elif record_dt_3.hour in ppv_config_20:
                #             tmp_F = tmp_F + value
                #             flag = flag.join('F')
                #         elif record_dt_3.hour in ppv_config_30:
                #             tmp_V = tmp_V + value
                #             flag = flag.join('V')
                #         sum = sum + value

                #         self.stdout.write(f'{record_dt_1}\t{record_dt_3}\t{flag}\t{round(min, 2)}\t{round(max, 2)}\t{value}\t{round(sum, 2)}', ending='\n')
                        # print(record_dt_3, flag, round(min, 2), round(max, 2), value, round(sum, 2))
                        # print(round(min, 2))

                


                da_P.value = round(tmp_P, 2)
                DeviceAttribute.objects.save_to_redis(device, da_P)

                # da_F.value = round(tmp_F, 2)
                # DeviceAttribute.objects.save_to_redis(device, da_F)

                # da_V.value = round(tmp_V, 2)
                # DeviceAttribute.objects.save_to_redis(device, da_V)

                da_S.value = round(tmp_S, 2)
                DeviceAttribute.objects.save_to_redis(device, da_S)

                self.stdout.write(
                    f'\t计算星光天地冷源-冷冻泵电表-峰平谷尖累计值。\ntmp_P:{da_P.value}\ntmp_S:{da_S.value}\n', ending='\n')
                # self.stdout.write(
                #     f'\t计算星光天地冷源-冷冻泵电表-峰平谷尖累计值。\ntmp_P:{da_P.value}\ntmp_F:{da_F.value}\ntmp_V:{da_V.value}\ntmp_S:{da_S.value}\nsum:{sum}', ending='\n')
            except CommandError:
                self.stderr.write(f"计算星光天地冷源-冷冻泵电表-峰平谷尖累计值失败", ending='\n')
                # continue
            except Exception as e:
                self.stderr.write(f"计算星光天地冷源-冷冻泵电表-峰平谷尖累计值失败", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
                # continue
