# Generated by Django 3.2.8 on 2023-01-12 16:09

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('project', '0006_project_run_mode'),
        ('terminal', '0006_terminalattribute_use_for'),
    ]

    operations = [
        migrations.CreateModel(
            name='Dimension',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=64)),
                ('type_name', models.Char<PERSON>ield(max_length=64)),
                ('thumbs', models.CharField(blank=True, max_length=20, null=True)),
                ('images', models.CharField(blank=True, max_length=20, null=True)),
                ('coords', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'dimensions',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='DimensionAttribute',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=24)),
                ('ap_ids', models.CharField(blank=True, max_length=255, null=True)),
                ('identifier', models.CharField(max_length=255)),
                ('is_cum', models.BooleanField(default=False)),
                ('read_only', models.BooleanField(default=True)),
                ('value', models.CharField(blank=True, max_length=64, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'dimension_attributes',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DimensionDailyStat',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('dimension_id', models.BigIntegerField(db_index=True)),
                ('dimension_attribute_id', models.BigIntegerField(db_index=True)),
                ('avg', models.CharField(max_length=64)),
                ('min', models.CharField(max_length=64)),
                ('max', models.CharField(max_length=64)),
                ('created_at', models.DateTimeField()),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'dimension_daily_stats',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DimensionHourlyStat',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('dimension_id', models.BigIntegerField(db_index=True)),
                ('dimension_attribute_id', models.BigIntegerField(db_index=True)),
                ('avg', models.CharField(max_length=64)),
                ('min', models.CharField(max_length=64)),
                ('max', models.CharField(max_length=64)),
                ('created_at', models.DateTimeField()),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'dimension_hourly_stats',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DimensionMonthlyStat',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('dimension_id', models.BigIntegerField(db_index=True)),
                ('dimension_attribute_id', models.BigIntegerField(db_index=True)),
                ('avg', models.CharField(max_length=64)),
                ('min', models.CharField(max_length=64)),
                ('max', models.CharField(max_length=64)),
                ('created_at', models.DateTimeField()),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'dimension_monthly_stats',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DimensionTerminal',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
            options={
                'db_table': 'dimension_terminals',
            },
        ),
        migrations.CreateModel(
            name='DimensionWeeklyStat',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('dimension_id', models.BigIntegerField(db_index=True)),
                ('dimension_attribute_id', models.BigIntegerField(db_index=True)),
                ('avg', models.CharField(max_length=64)),
                ('min', models.CharField(max_length=64)),
                ('max', models.CharField(max_length=64)),
                ('created_at', models.DateTimeField()),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'dimension_weekly_stats',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DimensionYearlyStat',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('dimension_id', models.BigIntegerField(db_index=True)),
                ('dimension_attribute_id', models.BigIntegerField(db_index=True)),
                ('avg', models.CharField(max_length=64)),
                ('min', models.CharField(max_length=64)),
                ('max', models.CharField(max_length=64)),
                ('created_at', models.DateTimeField()),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'dimension_yearly_stats',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='dimensionyearlystat',
            index=models.Index(fields=['dimension_id', 'dimension_attribute_id'], name='dimension_y_dimensi_a02ffe_idx'),
        ),
        migrations.AddIndex(
            model_name='dimensionyearlystat',
            index=models.Index(fields=['dimension_id', 'created_at'], name='dimension_y_dimensi_7e0684_idx'),
        ),
        migrations.AddIndex(
            model_name='dimensionyearlystat',
            index=models.Index(fields=['dimension_id', 'dimension_attribute_id', 'created_at'], name='dimension_y_dimensi_49725b_idx'),
        ),
        migrations.AddIndex(
            model_name='dimensionweeklystat',
            index=models.Index(fields=['dimension_id', 'dimension_attribute_id'], name='dimension_w_dimensi_5670d3_idx'),
        ),
        migrations.AddIndex(
            model_name='dimensionweeklystat',
            index=models.Index(fields=['dimension_id', 'created_at'], name='dimension_w_dimensi_1085bc_idx'),
        ),
        migrations.AddIndex(
            model_name='dimensionweeklystat',
            index=models.Index(fields=['dimension_id', 'dimension_attribute_id', 'created_at'], name='dimension_w_dimensi_45a600_idx'),
        ),
        migrations.AddField(
            model_name='dimensionterminal',
            name='dimension',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dimension.dimension'),
        ),
        migrations.AddField(
            model_name='dimensionterminal',
            name='terminal',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='terminal.terminal'),
        ),
        migrations.AddIndex(
            model_name='dimensionmonthlystat',
            index=models.Index(fields=['dimension_id', 'dimension_attribute_id'], name='dimension_m_dimensi_323909_idx'),
        ),
        migrations.AddIndex(
            model_name='dimensionmonthlystat',
            index=models.Index(fields=['dimension_id', 'created_at'], name='dimension_m_dimensi_26f513_idx'),
        ),
        migrations.AddIndex(
            model_name='dimensionmonthlystat',
            index=models.Index(fields=['dimension_id', 'dimension_attribute_id', 'created_at'], name='dimension_m_dimensi_336046_idx'),
        ),
        migrations.AddIndex(
            model_name='dimensionhourlystat',
            index=models.Index(fields=['dimension_id', 'dimension_attribute_id'], name='dimension_h_dimensi_0926a5_idx'),
        ),
        migrations.AddIndex(
            model_name='dimensionhourlystat',
            index=models.Index(fields=['dimension_id', 'created_at'], name='dimension_h_dimensi_7f1ee5_idx'),
        ),
        migrations.AddIndex(
            model_name='dimensionhourlystat',
            index=models.Index(fields=['dimension_id', 'dimension_attribute_id', 'created_at'], name='dimension_h_dimensi_0eee28_idx'),
        ),
        migrations.AddIndex(
            model_name='dimensiondailystat',
            index=models.Index(fields=['dimension_id', 'dimension_attribute_id'], name='dimension_d_dimensi_2af64d_idx'),
        ),
        migrations.AddIndex(
            model_name='dimensiondailystat',
            index=models.Index(fields=['dimension_id', 'created_at'], name='dimension_d_dimensi_b501c1_idx'),
        ),
        migrations.AddIndex(
            model_name='dimensiondailystat',
            index=models.Index(fields=['dimension_id', 'dimension_attribute_id', 'created_at'], name='dimension_d_dimensi_553b3f_idx'),
        ),
        migrations.AddField(
            model_name='dimensionattribute',
            name='dimension',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dimension.dimension'),
        ),
        migrations.AddField(
            model_name='dimension',
            name='parent',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='dimension.dimension'),
        ),
        migrations.AddField(
            model_name='dimension',
            name='project',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='project.project'),
        ),
        migrations.AddField(
            model_name='dimension',
            name='terminals',
            field=models.ManyToManyField(through='dimension.DimensionTerminal', to='terminal.Terminal'),
        ),
    ]
