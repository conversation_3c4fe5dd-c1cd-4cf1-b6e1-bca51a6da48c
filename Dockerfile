FROM swr.cn-south-1.myhuaweicloud.com/cloud-gz/saian-api-base:v0.8
# 设置环境变量
ENV PYTHONUNBUFFERED=1

#RUN mkdir /saian

# 设置工作目录
WORKDIR /saian

COPY . ./

# 安装包
#RUN pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/

#CMD ["python","manage.py","runserver", "0.0.0.0:8000"]
#ENTRYPOINT ["gunicorn", "-c", "gunicorn_conf.py", "saian_api.wsgi","--reload"]
#CMD ["gunicorn", "-c", "gunicorn_conf.py", "saian_api.wsgi", "--reload"]
RUN chmod +x start.sh
CMD ["./start.sh"]
