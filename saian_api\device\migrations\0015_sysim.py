# Generated by Django 3.2.19 on 2023-07-07 12:04

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('device', '0014_deviceruntime_prefix'),
    ]

    operations = [
        migrations.CreateModel(
            name='SySim',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('iccid', models.CharField(db_index=True, max_length=255)),
                ('imei', models.CharField(db_index=True, max_length=255, null=True)),
                ('msisdn', models.Char<PERSON>ield(max_length=255)),
                ('platform', models.Char<PERSON>ield(max_length=24)),
                ('status', models.Char<PERSON>ield(max_length=24, null=True)),
                ('stop_reason', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('data_consumed', models.IntegerField(default=0)),
                ('remark', models.<PERSON>r<PERSON><PERSON>(blank=True, max_length=255, null=True)),
                ('activated_at', models.DateTimeField(null=True)),
                ('opened_at', models.DateTimeField(null=True)),
                ('sync_at', models.DateTimeField(null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'sy_sims',
                'ordering': ['-created_at'],
            },
        ),
    ]
