# Generated by Django 3.2.8 on 2021-12-30 15:16

import builtins
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Project',
            fields=[
                ('id', models.BigIntegerField(primary_key=builtins.id, serialize=False)),
                ('name', models.<PERSON>r<PERSON><PERSON>(max_length=255, null=True)),
                ('project_type', models.<PERSON>r<PERSON>ield(max_length=255, null=True)),
                ('intro', models.Char<PERSON>ield(max_length=255, null=True)),
                ('address', models.Char<PERSON>ield(max_length=255, null=True)),
                ('images', models.Char<PERSON>ield(max_length=255, null=True)),
                ('regionid', models.Char<PERSON>ield(max_length=255, null=True)),
                ('cooling_area', models.IntegerField(null=True)),
                ('electricity', models.Integer<PERSON>ield(null=True)),
                ('completed_at', models.DateTime<PERSON>ield(null=True)),
                ('warranty', models.Integer<PERSON>ield(null=True)),
                ('contact_name', models.CharField(max_length=255, null=True)),
                ('contact_no', models.Char<PERSON><PERSON>(max_length=255, null=True)),
                ('contact_email', models.CharField(max_length=255, null=True)),
                ('director_name', models.CharField(max_length=255, null=True)),
                ('director_no', models.CharField(max_length=255, null=True)),
                ('director_email', models.CharField(max_length=255, null=True)),
                ('maintainer_name', models.CharField(max_length=255, null=True)),
                ('maintainer_no', models.CharField(max_length=255, null=True)),
                ('maintainer_email', models.CharField(max_length=255, null=True)),
                ('engineer_a_name', models.CharField(max_length=255, null=True)),
                ('engineer_a_no', models.CharField(max_length=255, null=True)),
                ('engineer_b_name', models.CharField(max_length=255, null=True)),
                ('engineer_b_no', models.CharField(max_length=255, null=True)),
                ('sales_agent', models.CharField(max_length=255, null=True)),
                ('building_owner', models.CharField(max_length=255, null=True)),
                ('building_user', models.CharField(max_length=255, null=True)),
                ('builder', models.CharField(max_length=255, null=True)),
                ('builder_contact_name', models.CharField(max_length=255, null=True)),
                ('builder_contact_no', models.CharField(max_length=255, null=True)),
                ('pmc', models.CharField(max_length=255, null=True)),
                ('pmc_contact_name', models.CharField(max_length=255, null=True)),
                ('pmc_contact_no', models.CharField(max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('admin_region_id', models.BigIntegerField()),
                ('hs_weight', models.IntegerField(null=True)),
                ('ms_weight', models.IntegerField(null=True)),
                ('ls_weight', models.IntegerField(null=True)),
                ('fan_time', models.IntegerField(null=True)),
                ('logo', models.CharField(max_length=255, null=True)),
                ('slogan', models.CharField(max_length=255, null=True)),
                ('en_temhum_sms', models.BooleanField(default=False)),
                ('en_gas_sms', models.BooleanField(default=False)),
                ('enable_web', models.BooleanField(default=False)),
                ('agent_id', models.BigIntegerField()),
                ('domain', models.CharField(max_length=255, null=True)),
                ('in_acc', models.BooleanField(default=False)),
                ('enable_ec', models.BooleanField(default=False)),
                ('en_ot_aircon', models.BooleanField(default=False)),
                ('pc_formula', models.CharField(max_length=500, null=True)),
                ('cc_formula', models.CharField(max_length=500, null=True)),
                ('wc_formula', models.CharField(max_length=500, null=True)),
            ],
            options={
                'db_table': 'projects',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='WebChart',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('uni_name', models.CharField(max_length=255, unique=True)),
                ('enabled', models.BooleanField(default=False)),
                ('icon', models.CharField(max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'web_charts',
            },
        ),
        migrations.CreateModel(
            name='WebPanel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('uni_name', models.CharField(max_length=255, unique=True)),
                ('enabled', models.BooleanField(default=False)),
                ('icon', models.CharField(max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'web_panels',
            },
        ),
        migrations.CreateModel(
            name='WebMenu',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('seq', models.IntegerField()),
                ('enabled', models.BooleanField(default=False)),
                ('uni_name', models.CharField(max_length=255, unique=True)),
                ('is_super', models.BooleanField(default=False)),
                ('agent_id', models.BigIntegerField(null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='project.webmenu')),
            ],
            options={
                'db_table': 'web_menus',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProjectWebMenu',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='project.project')),
                ('web_menu', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='project.webmenu')),
            ],
            options={
                'db_table': 'project_web_menus',
            },
        ),
        migrations.CreateModel(
            name='ProjectPanel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('num', models.IntegerField()),
                ('template', models.CharField(max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='project.project')),
                ('web_panel', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='project.webpanel')),
            ],
            options={
                'db_table': 'project_panels',
            },
        ),
        migrations.CreateModel(
            name='ProjectChart',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('num', models.IntegerField()),
                ('template', models.CharField(max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='project.project')),
                ('web_chart', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='project.webchart')),
            ],
            options={
                'db_table': 'project_charts',
            },
        ),
        migrations.AddField(
            model_name='project',
            name='web_charts',
            field=models.ManyToManyField(through='project.ProjectChart', to='project.WebChart'),
        ),
        migrations.AddField(
            model_name='project',
            name='web_menus',
            field=models.ManyToManyField(through='project.ProjectWebMenu', to='project.WebMenu'),
        ),
        migrations.AddField(
            model_name='project',
            name='web_panels',
            field=models.ManyToManyField(through='project.ProjectPanel', to='project.WebPanel'),
        ),
    ]
