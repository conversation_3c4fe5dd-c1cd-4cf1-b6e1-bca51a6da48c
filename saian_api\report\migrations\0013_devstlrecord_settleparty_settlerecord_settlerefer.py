# Generated by Django 3.2.19 on 2024-08-20 14:09

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('report', '0012_statreport'),
    ]

    operations = [
        migrations.CreateModel(
            name='DevstlRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('terminal_id', models.IntegerField()),
                ('power_cons', models.CharField(max_length=255, null=True)),
                ('es_value', models.CharField(max_length=255, null=True)),
                ('con_rt', models.CharField(max_length=255, null=True)),
                ('period', models.IntegerField()),
                ('settle_dt', models.DateTimeField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'devstl_records',
            },
        ),
        migrations.CreateModel(
            name='SettleParty',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('ec_meter_id', models.IntegerField(null=True)),
                ('settle_ways', models.CharField(max_length=255, null=True)),
                ('device_filter', models.CharField(max_length=255, null=True)),
                ('rt_stat', models.CharField(max_length=255)),
                ('ec_src', models.CharField(max_length=255)),
                ('periods', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'settle_parties',
            },
        ),
        migrations.CreateModel(
            name='SettleRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('settle_party_id', models.IntegerField()),
                ('period', models.IntegerField()),
                ('conver_ec', models.CharField(max_length=255)),
                ('actual_ec', models.CharField(max_length=255)),
                ('es_rate', models.CharField(max_length=255)),
                ('run_time', models.IntegerField()),
                ('settle_dt', models.DateTimeField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'settle_records',
            },
        ),
        migrations.CreateModel(
            name='SettleRefer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('settle_party_id', models.IntegerField()),
                ('param_name', models.CharField(max_length=255, null=True)),
                ('param_idf', models.CharField(max_length=255, null=True)),
                ('param_value', models.CharField(max_length=255, null=True)),
                ('param_unit', models.CharField(max_length=255, null=True)),
                ('c', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'settle_refers',
            },
        ),
    ]
