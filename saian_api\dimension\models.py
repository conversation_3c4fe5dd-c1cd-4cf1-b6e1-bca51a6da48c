import datetime
import json
import logging
import re

from django.db import models
from django.db.models import Q, Sum

from saian_api.building.models import ActiveRoom
from saian_api.project.models import Project
from saian_api.terminal.models import Terminal, TerminalAttribute
from saian_api.utils.db.Redis import RedisHelper

# Create your models here.


class Dimension(models.Model):
    """维度表"""
    # 项目
    project = models.ForeignKey(Project, on_delete=models.CASCADE, null=True)
    # 维度的名字
    name = models.CharField(max_length=64)
    # 维度类型的名字
    type_name = models.CharField(max_length=64)
    # 父维度
    parent = models.ForeignKey('self', related_name='children', on_delete=models.CASCADE, null=True)
    # 缩略图地址id
    thumbs = models.CharField(max_length=20, null=True, blank=True)
    # 维度图片的地址id
    images = models.CharField(max_length=20, null=True, blank=True)
    # 维度平面图上元素的位置信息
    coords = models.TextField(null=True, blank=True)
    # 单位面积
    unit_area = models.FloatField(null=True, default=None)
    # 用能人数
    ec_persons = models.IntegerField(null=True, default=None)
    # 与维度关联的终端
    terminals = models.ManyToManyField('terminal.Terminal', through='DimensionTerminal', through_fields=('dimension', 'terminal'))

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'dimensions'
        ordering = ['name']


class DimensionRoom(models.Model):
    """维度与房间关系表"""
    # 对应的维度
    dimension = models.ForeignKey(Dimension, on_delete=models.CASCADE)
    # 对应的房间
    room = models.ForeignKey(ActiveRoom, on_delete=models.CASCADE)

    class Meta:
        db_table = 'dimension_rooms'


class DimensionTerminal(models.Model):
    """维度与终端关系表"""
    # 对应的维度
    dimension = models.ForeignKey(Dimension, on_delete=models.CASCADE)
    # 对应的终端
    terminal = models.ForeignKey(Terminal, on_delete=models.CASCADE)

    class Meta:
        db_table = 'dimension_terminals'


class DimensionUser(models.Model):
    """维度与用户关系表"""
    # 对应的维度
    dimension = models.ForeignKey(Dimension, on_delete=models.CASCADE)
    # 对应的用户
    web_user = models.ForeignKey('user.WebUser', on_delete=models.CASCADE)

    class Meta:
        db_table = 'dimension_users'


class DimensionAttributeManager(models.Manager):
    @classmethod
    def calc_hourly_stats(cls, da, from_at, till_at):
        """ 计算维度属性da，在时间dt的小时统计 """
        from saian_api.device.models import DeviceAttribute

        values = []
        if da.ta_ids is not None:
            ta_ids = da.ta_ids.split(',')
            tas = list(TerminalAttribute.objects.filter(id__in=ta_ids))
            terminals = list(Terminal.objects.filter(id__in=[ta.terminal_id for ta in tas]))

            for ta in tas:
                terminal = next(filter(lambda x: x.id == ta.terminal_id, terminals), None)
                if terminal is None:
                    continue
                if da.is_cum:
                    ta_value = DeviceAttribute.objects.calc_hourly_cum_stat(terminal.device_id, ta.identifier, from_at, till_at)
                else:
                    ta_value = DeviceAttribute.objects.calc_hourly_val_stat(terminal.device_id, ta.identifier, from_at, till_at)
                values.append(ta_value)

            if da.is_cum:
                return sum(values)
            else:
                return sum(values) / len(values)
        else:
            da_ids = re.findall(r'{{(.*?)}}', da.formula)
            da_values = []
            for da_id in da_ids:
                sub_da = DimensionAttribute.objects.get(pk=da_id)
                da_values.append(cls.calc_hourly_stats(sub_da, from_at, till_at))
            # 构建计算表达式
            eval_str = da.formula
            for v in da_values:
                eval_str = re.sub(r'{{(.*?)}}', f'{v}', eval_str, 1)

            try:
                value = eval(eval_str)
                return value
            except Exception as e:
                logging.error(f'计算维度属性 {da.id}-{da.name} 公式时出错，表达式为: {eval_str}; ERROR: {e.__str__()}')
                return None

    @classmethod
    def calc_value(cls, device, data: dict):
        """
            设备上报数据时，更新设备（终端）绑定的维度的属性值（如果有的话）
        """
        terminals = Terminal.objects.filter(device=device)
        if terminals.exists():
            # 一个设备可能有多个终端绑定维度，set得到唯一维度，避免重复计算
            dimension_ids = set(
                DimensionTerminal.objects.filter(terminal_id__in=terminals.values_list('id', flat=True)).values_list('dimension_id', flat=True))
            # 当设备终端绑定了维度
            if len(dimension_ids):
                from saian_api.utils.tools import is_number
                now = datetime.datetime.now()
                updated_das = set()
                # 上报的数据中是否有绑定的维度属性，
                for dimension_id in dimension_ids:
                    for da in DimensionAttribute.objects.filter(dimension_id=dimension_id):
                        da_updated_up = da.updated_at
                        # 维度属性无公式时
                        if da.ta_ids:
                            # 维度属性绑定的ta的标识
                            tas = TerminalAttribute.objects.filter(id__in=da.ta_ids.split(','))
                            identifiers = set(tas.values_list('identifier', flat=True))
                            for identifier in identifiers:
                                # 如果设备上报中有维度属性的值，则计算更新
                                if identifier in data:
                                    # 维度属性绑定的终端属性值列表
                                    values = []
                                    for t in list(filter(lambda ta: is_number(ta.value), tas)):
                                        ta = TerminalAttribute.objects.query_object_by_ta(t, device.project_id)
                                        values.append(float(ta.value))
                                    # values = [float(t.value) for t in list(filter(lambda ta: is_number(ta.value), tas))]
                                    # 无值则不更新
                                    if len(values):
                                        if not da.is_cum:
                                            # 非累计值时，取平均值
                                            da.value = round(sum(values) / len(values), 3)
                                        else:
                                            # 累计值时，取和
                                            da.value = round(sum(values), 3)
                                        da.save()
                                        updated_das.add(str(da.id))

                                        da_data = {
                                            'value': da.value,
                                            'dt': now.strftime('%Y-%m-%d %H:%M:%S')
                                        }
                                        if (now - da_updated_up).seconds >= 60:
                                            RedisHelper.push_list(device.project_id, f'dimension_attribute:{da.id}',
                                                                  json.dumps(da_data), True, limit=10)
                                        else:
                                            RedisHelper.set_list_tail_item(device.project_id, f'dimension_attribute:{da.id}', 1,
                                                                           json.dumps(da_data), True)

                if len(updated_das):
                    for formula_da in DimensionAttribute.objects.filter(formula__isnull=False):
                        da_updated_up = formula_da.updated_at
                        da_ids = re.findall(r'{{(.*?)}}', formula_da.formula)
                        should_update = len(set(da_ids) & updated_das)
                        if should_update:
                            value = formula_da.calc_formula_value()
                            if value:
                                formula_da.value = value
                                formula_da.save()

                                da_data = {
                                    'value': formula_da.value,
                                    'dt': now.strftime('%Y-%m-%d %H:%M:%S')
                                }
                                if (now - da_updated_up).seconds >= 60:
                                    RedisHelper.push_list(device.project_id, f'dimension_attribute:{formula_da.id}',
                                                          json.dumps(da_data), True, limit=10)
                                else:
                                    RedisHelper.set_list_tail_item(device.project_id, f'dimension_attribute:{formula_da.id}', 1,
                                                                   json.dumps(da_data), True)


class DimensionAttribute(models.Model):
    """用于存储维度当前的属性信息，属性信息在于设备绑定时由用户配置"""
    # 所属维度
    dimension = models.ForeignKey(Dimension, on_delete=models.CASCADE)
    # 属性名字
    name = models.CharField(max_length=24)
    # 所属设备的属性id，维度可以绑定多个不同类型但都有反应相同物理量的设备，所以可能会有多个，多个时以逗号分隔
    ta_ids = models.TextField(blank=True, null=True)
    # 参数标识
    identifier = models.CharField(max_length=255, null=True)
    # 是否为累计值，来自设备属性
    is_cum = models.BooleanField(default=False)
    # 是否只读
    read_only = models.BooleanField(default=True)
    # 当前属性值，多个属性时，取平均值或和
    value = models.CharField(max_length=64, null=True, blank=True)
    # 维度"虚拟"属性计算公式
    formula = models.CharField(max_length=500, null=True)
    # 是否能耗关联的属性，以及其能耗类型. 10-电，20-水, 30-冷, 40-燃气, 50-燃油, 60-热
    ec_type = models.IntegerField(null=True, default=None)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'dimension_attributes'
        ordering = ['-created_at']

    objects = DimensionAttributeManager()

    def calc_formula_value(self, created_at=None):
        """计算维度属性(公式)当前(或 created_at 时)的值"""
        from saian_api.device.models import ParamRecord

        value = 0
        da_ids = re.findall(r'{{(.*?)}}', self.formula)
        da_values = []

        das = list(DimensionAttribute.objects.filter(id__in=da_ids))

        if created_at is None:
            for da_id in da_ids:
                da = next((da for da in das if da.id == int(da_id)), None)
                if da is None:
                    da_values.append(0)
                    logging.error(f'维度属性: {self.id}-{self.name}, 公式计算: {self.formula} 中，维度属性id-{da_id} 不存在。')
                    continue
                da_values.append(da.value)
        else:
            ta_ids = []
            for da in das:
                if da.ta_ids:
                    ta_ids += da.ta_ids.split(',')
            tas = TerminalAttribute.objects.filter(id__in=ta_ids).select_related('terminal')
            query = [
                Q(device_id=ta.terminal.device_id, identifier=ta.identifier)
                for ta in tas
            ]
            records = ParamRecord.objects.filter(Q(*query, _connector=Q.OR), created_at=created_at).values('device_id', 'identifier', 'value')
            record_dict = {(r['device_id'], r['identifier']): r['value'] for r in records}

            for da_id in da_ids:
                da = next((da for da in das if da.id == int(da_id)), None)
                if da is None:
                    da_values.append(0)
                    logging.error(f'维度属性: {self.id}-{self.name}, 公式计算: {self.formula} 中，维度属性id-{da_id} 不存在。')
                    continue
                current_hour_begin_cons = 0
                da_ta_ids = [int(ta_id) for ta_id in da.ta_ids.split(',') if ta_id]
                da_tas = [ta for ta in tas if ta.id in da_ta_ids]
                for ta in da_tas:
                    record = record_dict.get((ta.terminal.device_id, ta.identifier), 0)
                    if record:
                        current_hour_begin_cons += float(record)

                da_values.append(current_hour_begin_cons)

        # for da_id in da_ids:
        #     try:
        #         da = DimensionAttribute.objects.get(pk=da_id)
        #         if created_at is None:
        #             da_values.append(da.value)
        #         else:
        #             tas = TerminalAttribute.objects.filter(id__in=da.ta_ids.split(','))
        #             current_hour_begin_records = ParamRecord.objects.filter(created_at=created_at)
        #             current_hour_begin_cons = 0
        #             for ta in tas:
        #                 record = current_hour_begin_records.filter(identifier=ta.identifier, device_id=ta.terminal.device_id).last()
        #                 if record is not None:
        #                     current_hour_begin_cons += float(record.value)
        #             da_values.append(current_hour_begin_cons)
        #     except DimensionAttribute.DoesNotExist:
        #         da_values.append(0)
        #         logging.error(f'维度属性: {self.id}-{self.name}, 公式计算: {self.formula} 中，维度属性id-{da_id} 不存在。')
        # 构建计算表达式
        eval_str = self.formula
        for v in da_values:
            eval_str = re.sub(r'{{(.*?)}}', f'{v}', eval_str, 1)

        try:
            value = eval(eval_str)
            value = round(value, 3)

        except Exception as e:
            logging.error(f'计算维度属性 {self.id}-{self.name} 公式时出错，表达式为: {eval_str}; ERROR: {e.__str__()}')

        return value

    def calc_current_hour_cons(self, project_id=None):
        """计算维度属性当前小时的用量"""
        now = datetime.datetime.now()
        current_hour_cons = 0
        # - 当前时的用量, 上一小时统计未完成时不计算当前小时用量
        current_hour = now.strftime('%Y-%m-%d %H:05:00')
        # - clac_cum 定时任务05分开始
        if now.minute >= 5:
            if self.formula:
                # 当前时各个终端属性值的和
                now_cons = self.calc_formula_value()
                # 从 ParamRecord 查询当前小时开始时的记录
                current_hour_begin_cons = self.calc_formula_value(current_hour)
                current_hour_cons += now_cons - current_hour_begin_cons
            elif self.ta_ids:
                from saian_api.device.models import ParamRecord

                # 维度属性关联的终端属性
                tas = TerminalAttribute.objects.filter(id__in=self.ta_ids.split(',')).select_related('terminal')
                # 当前时各个终端属性值的和
                ta_values = [ta.value for ta in tas]
                now_cons = sum([float(v) for v in ta_values if v])
                # 从 ParamRecord 查询当前小时开始时的记录
                query = [
                    Q(device_id=ta.terminal.device_id, identifier=ta.identifier)
                    for ta in tas
                ]
                record_sum = ParamRecord.objects.filter(Q(*query, _connector=Q.OR), created_at=current_hour).aggregate(sum=Sum('value'))['sum']

                if record_sum:
                    current_hour_cons += now_cons - record_sum
                else:
                    current_hour_cons = 0

        return round(current_hour_cons, 2)

    @property
    def cur_day_cons(self):
        """查询维度属性当月的统计值之和"""
        result = 0
        now = datetime.datetime.now()
        today_begin = datetime.datetime(now.year, now.month, now.day, 0, 0)

        hourly_value = DimensionHourlyStat.objects.filter(dimension_id=self.dimension_id, dimension_attribute_id=self.id,
                                                          created_at__gte=today_begin).aggregate(sum=Sum('avg'))['sum']
        if hourly_value:
            result += hourly_value

        result += self.calc_current_hour_cons()

        return result

    @property
    def cur_month_cons(self):
        """查询维度属性当月的统计值之和"""
        result = 0
        now = datetime.datetime.now()
        month_begin = datetime.datetime(now.year, now.month, 1, 0, 0)
        daily_value = DimensionDailyStat.objects.filter(dimension_id=self.dimension_id, dimension_attribute_id=self.id,
                                                        created_at__gte=month_begin).aggregate(sum=Sum('avg'))['sum']
        if daily_value:
            result += daily_value

        result += self.cur_day_cons

        return result

    @property
    def cur_year_cons(self):
        """查询维度属性当年的统计值之和"""
        result = 0
        now = datetime.datetime.now()
        year_begin = datetime.datetime(now.year, 1, 1, 0, 0)
        monthly_value = DimensionMonthlyStat.objects.filter(dimension_id=self.dimension_id, dimension_attribute_id=self.id,
                                                            created_at__gte=year_begin).aggregate(sum=Sum('avg'))['sum']
        if monthly_value:
            result += monthly_value

        result += self.cur_month_cons

        return result


class DimensionHourlyStatManager(models.Manager):
    def get_report_stat(self, dimension_attr_id, from_at, till_at):
        stats = self.filter(dimension_attribute_id=dimension_attr_id, created_at__range=[from_at, till_at]).order_by('created_at')
        result = []

        for item in stats:
            result.append({
                'time': datetime.datetime.strftime(item.created_at, '%Y-%m-%d %H:%M:%S'),
                'value': round(float(item.avg), 2) if item.avg != '--' else 0,
                'min': round(float(item.min), 2) if item.min != '--' else 0,
                'max': round(float(item.max), 2) if item.max != '--' else 0,
            })

        return result


class DimensionHourlyStat(models.Model):
    """维度小时统计表"""
    # 所属维度id
    dimension_id = models.BigIntegerField(db_index=True)
    # 维度属性id
    dimension_attribute_id = models.BigIntegerField(db_index=True)
    # 统计值，如果是累计值，则为周期内的增量，否则为周期内的平均值
    avg = models.CharField(max_length=64)
    # 最小值
    min = models.CharField(max_length=64)
    # 最小值
    max = models.CharField(max_length=64)

    # 数据创建时间
    created_at = models.DateTimeField()
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'dimension_hourly_stats'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['dimension_id', 'dimension_attribute_id']),
            models.Index(fields=['dimension_id', 'created_at']),
            models.Index(fields=['dimension_id', 'dimension_attribute_id', 'created_at']),
        ]

    objects = DimensionHourlyStatManager()


class DimensionDailyStatManager(models.Manager):
    def get_report_stat(self, dimension_attr_id, from_at, till_at):
        stats = self.filter(dimension_attribute_id=dimension_attr_id, created_at__range=[from_at, till_at]).order_by('created_at')
        result = []

        for item in stats:
            result.append({
                'time': datetime.datetime.strftime(item.created_at, '%Y-%m-%d %H:%M:%S'),
                'value': round(float(item.avg), 2) if item.avg != '--' else '--',
                'min': round(float(item.min), 2) if item.min != '--' else '--',
                'max': round(float(item.max), 2) if item.max != '--' else '--',
            })

        return result


class DimensionDailyStat(models.Model):
    """维度日统计表"""
    # 所属维度id
    dimension_id = models.BigIntegerField(db_index=True)
    # 维度属性id
    dimension_attribute_id = models.BigIntegerField(db_index=True)
    # 统计值，如果是累计值，则为周期内的增量，否则为周期内的平均值
    avg = models.CharField(max_length=64)
    # 最小值
    min = models.CharField(max_length=64)
    # 最小值
    max = models.CharField(max_length=64)

    # 数据创建时间
    created_at = models.DateTimeField()
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'dimension_daily_stats'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['dimension_id', 'dimension_attribute_id']),
            models.Index(fields=['dimension_id', 'created_at']),
            models.Index(fields=['dimension_id', 'dimension_attribute_id', 'created_at']),
        ]

    objects = DimensionDailyStatManager()


class DimensionWeeklyStat(models.Model):
    """维度周统计表"""
    # 所属维度id
    dimension_id = models.BigIntegerField(db_index=True)
    # 维度属性id
    dimension_attribute_id = models.BigIntegerField(db_index=True)
    # 统计值，如果是累计值，则为周期内的增量，否则为周期内的平均值
    avg = models.CharField(max_length=64)
    # 最小值
    min = models.CharField(max_length=64)
    # 最小值
    max = models.CharField(max_length=64)

    # 数据创建时间
    created_at = models.DateTimeField()
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'dimension_weekly_stats'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['dimension_id', 'dimension_attribute_id']),
            models.Index(fields=['dimension_id', 'created_at']),
            models.Index(fields=['dimension_id', 'dimension_attribute_id', 'created_at']),
        ]


class DimensionMonthlyStatManager(models.Manager):
    def get_report_stat(self, dimension_attr_id, from_at, till_at):
        stats = self.filter(dimension_attribute_id=dimension_attr_id, created_at__range=[from_at, till_at]).order_by('created_at')
        result = []

        for item in stats:
            result.append({
                'time': datetime.datetime.strftime(item.created_at, '%Y-%m-%d %H:%M:%S'),
                'value': round(float(item.avg), 2) if item.avg != '--' else 0,
                'min': round(float(item.min), 2) if item.min != '--' else 0,
                'max': round(float(item.max), 2) if item.max != '--' else 0,
            })

        return result


class DimensionMonthlyStat(models.Model):
    """维度月统计表"""
    # 所属维度id
    dimension_id = models.BigIntegerField(db_index=True)
    # 维度属性id
    dimension_attribute_id = models.BigIntegerField(db_index=True)
    # 统计值，如果是累计值，则为周期内的增量，否则为周期内的平均值
    avg = models.CharField(max_length=64)
    # 最小值
    min = models.CharField(max_length=64)
    # 最小值
    max = models.CharField(max_length=64)

    # 数据创建时间
    created_at = models.DateTimeField()
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'dimension_monthly_stats'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['dimension_id', 'dimension_attribute_id']),
            models.Index(fields=['dimension_id', 'created_at']),
            models.Index(fields=['dimension_id', 'dimension_attribute_id', 'created_at']),
        ]

    objects = DimensionMonthlyStatManager()


class DimensionYearlyStat(models.Model):
    """维度年统计表"""
    # 所属维度id
    dimension_id = models.BigIntegerField(db_index=True)
    # 维度属性id
    dimension_attribute_id = models.BigIntegerField(db_index=True)
    # 统计值，如果是累计值，则为周期内的增量，否则为周期内的平均值
    avg = models.CharField(max_length=64)
    # 最小值
    min = models.CharField(max_length=64)
    # 最小值
    max = models.CharField(max_length=64)

    # 数据创建时间
    created_at = models.DateTimeField()
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'dimension_yearly_stats'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['dimension_id', 'dimension_attribute_id']),
            models.Index(fields=['dimension_id', 'created_at']),
            models.Index(fields=['dimension_id', 'dimension_attribute_id', 'created_at']),
        ]


class PpvConfigManager(models.Manager):
    def calc_ppv(self, device, event, idf_list):
        from saian_api.device.models import DeviceAttribute
        # 获取上报的数据
        reported_data = event.get('data')
        ppv_suffix = {10: "_P", 20: "_F", 30: "_V", 40: "_S"}

        for idf in idf_list:
            # 新上报的数据值
            reported_value = float(reported_data[idf])
            # 现存储的数据值
            store_da = DeviceAttribute.objects.query_object_by_idf(device, idf)
            store_value = float(store_da.value) if store_da is not None else 0

            # 判断当前时间属于峰平谷尖哪一个时期
            now = datetime.datetime.now()
            ppv_config_untreated_list = PpvConfig.objects.filter(project_id=device.project_id, ec_type=10).order_by('begin_at')

            if ppv_config_untreated_list.exists():
                # 是否需要完善峰平谷尖配置
                if ppv_config_untreated_list[0].begin_at.hour > 0:
                    new_config = PpvConfig(project=device.project_id,
                                           begin_at=datetime.datetime.strptime('00:00:00', '%H:%M:%S').time(),
                                           price=ppv_config_untreated_list[len(ppv_config_untreated_list) - 1].price,
                                           unit=ppv_config_untreated_list[len(ppv_config_untreated_list) - 1].unit,
                                           ec_category=ppv_config_untreated_list[len(ppv_config_untreated_list) - 1].ec_category,
                                           ec_type=ppv_config_untreated_list[len(ppv_config_untreated_list) - 1].ec_type,
                                           ppv_type=ppv_config_untreated_list[len(ppv_config_untreated_list) - 1].ppv_type)
                    ppv_config_untreated_list.append(new_config)

                # 标识当前数据所属时期
                ppv_type = ppv_config_untreated_list.filter(begin_at__hour__range=[0, now.hour]).order_by('-begin_at')[0].ppv_type

                target_idf = idf + ppv_suffix[ppv_type]

                # # 峰
                # if ppv_type == 10:
                #     target_idf = idf + '_P'

                # # 平
                # elif ppv_type == 20:
                #     target_idf = idf + '_F'

                # # 谷
                # elif ppv_type == 30:
                #     target_idf = idf + '_V'

                # # 尖
                # elif ppv_type == 40:
                #     target_idf = idf + '_S'

                target_da = DeviceAttribute.objects.query_object_by_idf(device, target_idf)
                new_value = (float(target_da.value) if target_da is not None else 0) + (reported_value - store_value)
                event['data'][target_idf] = new_value

            else:
                # 临时处理：天津电表-峰平谷尖统计
                # 现场配置错误，要等施工人员到现场重新配置电表
                # 高峰时段9:00-12:00，16:00-21:00;
                # 低谷时段23:00-7:00:
                # 平时段7:00-9:00，12:00-16:00,21:00-23:00;
                # 其中，1、12月高峰时段中11:00-12:00，18:00-19:00为尖峰时段;7、8月高峰时段中11:00-12:00，16:00-17:00为尖峰时段。
                if device.id == 17898 or device.id == 18167 or device.id == 18173:
                    ppv_config_10 = [9, 10, 11, 16, 17, 18, 19, 20]
                    ppv_config_20 = [7, 8, 12, 13, 14, 15, 21, 22]
                    ppv_config_30 = [0, 1, 2, 3, 4, 5, 6, 23]
                    if now.hour in ppv_config_10:
                        ppv_type = 10
                    elif now.hour in ppv_config_20:
                        ppv_type = 20
                    elif now.hour in ppv_config_30:
                        ppv_type = 30
                    if ppv_type == 10:
                        if (now.month == 1 or now.month == 12) and (now.hour == 11 or now.hour == 18):
                            ppv_type = 40
                        elif (now.month == 7 or now.month == 8) and (now.hour == 11 or now.hour == 16):
                            ppv_type = 40
                    idx = re.findall(r'\d+', idf)
                    target_idf = idf[:-2] + ppv_suffix[ppv_type] + '_' + idx[0]

                    target_da = DeviceAttribute.objects.query_object_by_idf(device, target_idf)
                    new_value = (float(target_da.value) if target_da is not None else 0) + (reported_value - store_value)
                    event['data'][target_idf] = round(new_value, 2)


class PpvConfig(models.Model):
    """峰平谷尖配置表"""
    # 对应的项目id
    project = models.ForeignKey(Project, on_delete=models.CASCADE, null=True)
    # 时间段开始时间，如：08:00:00
    begin_at = models.TimeField()
    # 时间段结束时间，如：08:00:00
    # end_at = models.TimeField()
    # 价格，单位：分
    price = models.CharField(max_length=20)
    # 单位，如：kWh
    unit = models.CharField(max_length=20)
    # 备用，能耗分类，比如：1-10kv
    ec_category = models.CharField(max_length=255, null=True, default=None)
    # 能耗类型，10-电，20-水, 30-冷, 40-燃气, 50-燃油
    ec_type = models.IntegerField()
    # 配置对应的峰平谷尖分类，0-默认，10-峰，20-平，30-谷，40-尖
    ppv_type = models.IntegerField()

    class Meta:
        db_table = 'ppv_configs'

    objects = PpvConfigManager()


class EsrStat(models.Model):
    """节能率统计表"""
    # 项目
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    # 时间维度类型
    type = models.CharField(max_length=5)
    # 节能率，如："34.4"，单位：%
    esr_value = models.CharField(max_length=20)
    # 当期用电量，单位：kWh
    cur_value = models.CharField(max_length=20)
    # 基准同期用电量，单位：kWh
    refer_value = models.CharField(max_length=20)

    # 数据创建时间
    created_at = models.DateTimeField()
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'esr_stats'
