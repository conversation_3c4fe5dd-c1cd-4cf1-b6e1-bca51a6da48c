import json
from asyncio import constants
import datetime
from xml.dom.minidom import Identified

from django.db import transaction
from django.db.models import Count, Q
from django.shortcuts import get_object_or_404
from rest_framework import exceptions, viewsets
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from saian_api.coldsource.serializers import (ColdSourceListSerializer,
                                              ColdSourceDetailSerializer,
                                              EcMeterSerializer,
                                              MeterReadingSerializer,
                                              CsCopSerializer)
from saian_api.coldsource.serializers import EcSourceModelSerializer, EcSourceSerializer, EcSourceDetailSerializer
from saian_api.terminal.models import Terminal, TerminalAttribute
from saian_api.devdefine.models import DevicePrototype, AttributePrototype
from saian_api.utils.sy_jsonrenderer import SyJSONRender, ResponseUtils
from .models import ColdSource, EcMeter, ManualMeterReading, CsCop
from .models import CsEerAnalyse, EcSource
from ..dashboard.views import MiniStatsInfo
from ..device.models import Device
from ..project.models import Project

# Create your views here.


class ColdSourceViewSet(viewsets.ModelViewSet):
    renderer_classes = (SyJSONRender,)
    serializer_class = ColdSourceListSerializer

    def get_queryset(self):
        project_id = self.request.query_params.get('prjid', None)
        name = self.request.query_params.get('name', None)
        parent_id = self.request.query_params.get('parent_id', None)

        if project_id is None:
            project_id = self.request.user.get('project_id', None)

        if project_id is None:
            return exceptions.NotFound

        queryset = ColdSource.objects.filter(project_id=project_id)

        if name is not None:
            queryset = queryset.filter(uni_name=name)
        
        if parent_id is not None:
            queryset = queryset.filter(parent_id=parent_id)

        return queryset.order_by('id')

    # 创建前添加项目id
    def perform_create(self, serializer):
        serializer.save(project_id=self.request.user['project_id'])

    def list(self, request, *args, **kwargs):
        cold_sources = super(ColdSourceViewSet, self).list(request, *args, **kwargs).data

        res_data = {
            'cold_sources': cold_sources['results'],
            'count': cold_sources['count']
        }

        return Response(res_data)

    def create(self, request, *args, **kwargs):
        cold_source = super(ColdSourceViewSet, self).create(request, *args, **kwargs).data

        res_data = {
            'cold_source': cold_source
        }

        return Response(res_data)


class ColdSourceDetailViewSet(viewsets.ModelViewSet):
    renderer_classes = (SyJSONRender,)
    serializer_class = ColdSourceDetailSerializer
    queryset = ColdSource.objects.all()

    def retrieve(self, request, *args, **kwargs):
        cold_source = super(ColdSourceDetailViewSet, self).retrieve(request, *args, **kwargs).data
        res_data = {
            'cold_source': cold_source
        }

        return Response(res_data)

    def partial_update(self, request, *args, **kwargs):
        cold_source = super().partial_update(request, *args, **kwargs).data

        res_data = {
            'cold_source': cold_source
        }

        return Response(res_data)

    def destroy(self, request, *args, **kwargs):
        cold_source = super(ColdSourceDetailViewSet, self).destroy(request, *args, **kwargs).data
        res_data = {
            'cold_source': cold_source
        }

        return Response(res_data)


class ColdSourceStatsViewSet(viewsets.ModelViewSet):
    renderer_classes = (SyJSONRender,)

    def list(self, request, *args, **kwargs):
        # Web冷源概况配置，如果配置了冷源，则只返回该配置的冷源信息，否则返所有冷源
        project_settings = json.loads(Project.objects.get(pk=request.user.get('project_id')).settings)
        cs_mac = MiniStatsInfo.get_cs_mac(project_settings)
        display_stats_info = MiniStatsInfo.is_display(project_settings, cs_mac)

        # 冷源网关
        terminals = Terminal.objects.filter(terminal_type=40, idx=None)
        if display_stats_info:
            terminals = terminals.filter(device__mac=cs_mac)

        # 冷源子设备
        preifx_terminals = Terminal.objects.filter(terminal_type=40).exclude(idx=None)
        idfs = [['CWOPTemp', 'CWRPTemp'], ['COWOPTemp', 'COWRPTemp'], ['CWOPPress', 'CWRPPress']]
        labels = ['冷冻供回水温度', '冷却供回水温度', '冷冻供回水压力']
        cold_sources = []
        for terminal in terminals:
            device = terminal.device
            name = terminal.nick_name
            stats = []
            for idx, label in enumerate(labels):
                # tas = TerminalAttribute.objects.filter(terminal_id=terminal.id, identifier__in=idfs[idx]).values('identifier', 'value')
                ap_ids = AttributePrototype.objects.filter(device_prototype_id=device.device_prototype_id,
                                                           identifier__in=idfs[idx]).values_list('id', flat=True)
                tas = TerminalAttribute.objects.query_object_list(terminal, ap_ids, request.user['project_id'])
                attres = {}
                for ta in tas:
                    attres[ta.identifier] = ta.value
                if len(tas) != 0:
                    stats_tmp = {
                        'label': label,
                        'attres': attres
                    }
                    stats.append(stats_tmp)
            # 子设备类型(除了阀)
            prefixs_tmp = Terminal.objects.filter(device_id=terminal.device_id).exclude(prefix__contains='Valve')
            # prefix去重去空
            prefixs = list(filter(None, prefixs_tmp.order_by('device_prototype_id').values_list('prefix', flat=True).distinct()))
            ec_types = []
            for prefix in prefixs:
                extensions = []
                # 获取在线子设备
                online_terms = prefixs_tmp.filter(prefix=prefix, online=1)
                for idx in range(online_terms.count()):
                    idx = str(idx + 1)
                    if prefix == 'Host':
                        # 末端类型为主机时获取运行状态和实时负荷
                        prefix_idfs = [''.join([prefix, '_', idx, '_RunStatus']), ''.join([prefix, '_', idx, '_RTLoad'])]
                    else:
                        # 获取实时频率和实时功率
                        prefix_idfs = [''.join([prefix, '_', idx, '_Freq']), ''.join([prefix, '_', idx, '_Power']), ''.join([prefix, '_', idx, '_RTFreq']), ''.join([prefix, '_', idx, '_RTPower'])]
                    # 获取子设备的末端
                    sub_terminal = preifx_terminals.filter(prefix=prefix, device_id=terminal.device_id, idx=idx).last()

                    aps = AttributePrototype.objects.filter(device_prototype_id=device.device_prototype_id, identifier__in=prefix_idfs)
                    ap_ids = [ap.id for ap in aps]
                    attres_tmp = TerminalAttribute.objects.query_object_list(sub_terminal, ap_ids, request.user['project_id'])

                    attres = []
                    for attr in attres_tmp:
                        ap = [ap for ap in aps if ap.id == attr.attribute_prototype_id][0]
                        attres.append({
                            'identifier': attr.identifier,
                            'value': attr.value,
                            'unit': ap.unit if ap.unit else ''
                        })

                    extensions.append({
                        'idx': idx,
                        'attres': attres
                    })

                uni_name = DevicePrototype.objects.filter(prefix=prefix).last().uni_name
                ec_types.append({
                    'uni_name': uni_name,
                    'total': online_terms.count(),
                    'online': prefixs_tmp.filter(prefix=prefix, online=1).count(),
                    'extensions': extensions
                })
            cold_sources.append({
                'id': terminal.device_id,
                'name': name,
                'mac': terminal.device.mac,
                'stats': stats,
                'ec_types': ec_types}
            )
        res_data = cold_sources
        return Response(res_data)


class EcMeterViewSet(viewsets.ModelViewSet):
    serializer_class = EcMeterSerializer
    renderer_classes = (SyJSONRender,)

    def get_queryset(self):
        queryset = EcMeter.objects.all().order_by('-created_at')

        ec_type = self.request.query_params.get('ec_type', None)
        if ec_type is not None:
            queryset = queryset.filter(ec_type=ec_type)

        is_virtual = self.request.query_params.get('is_virtual', None)
        if is_virtual is not None:
            queryset = queryset.filter(is_virtual=is_virtual)

        return queryset

    def list(self, request, *args, **kwargs):
        meters = super(EcMeterViewSet, self).list(request, *args, **kwargs).data
        return Response({
            'status': status.HTTP_200_OK,
            'count': meters['count'],
            'ec_meters': meters['results']
        })

    def create(self, request, *args, **kwargs):
        ec_meter = super(EcMeterViewSet, self).create(request, *args, **kwargs).data
        return Response({
            'status': status.HTTP_200_OK,
            'results': ec_meter
        })


class EcMeterListView(APIView):
    def get(self, request):
        ec_type = request.query_params.get('ec_type', None)

        if ec_type is None:
            ec_meters = EcMeter.objects.all()
        else:
            ec_meters = EcMeter.objects.filter(ec_type=ec_type)

        result = []
        annotates = ec_meters.values('device__nick_name', 'device__id').annotate(Count('device_id'))
        for anno in annotates:
            meters = []
            device_id = anno['device__id']
            device_nickname = anno['device__nick_name']
            for item in ec_meters.filter(device_id=anno['device__id']):
                meters.append({
                    'name': item.name,
                    'identifier': item.identifier,
                    'device_id': device_id
                })

            result.append({
                'id': device_id,
                'nick_name': device_nickname,
                'meters': meters
            })

        return Response(ResponseUtils.custom_response_format(result, 'devices', True))


class CsEerAnalyseView(viewsets.ModelViewSet):
    def list(self, request, *args, **kwargs):
        queryset = CsEerAnalyse.objects.all()

        cop_type = request.query_params.get('type', 'di')
        queryset = queryset.filter(type=cop_type)
        date_format = '%m-%d' if cop_type == 'di' else '%m-%d %H:%M'

        page = self.request.query_params.get('page', None)
        per_page = self.request.query_params.get('per_page', None)
        if page is not None and per_page is not None:
            queryset = self.paginate_queryset(queryset)

        from_at = self.request.query_params.get('from', None)
        if from_at is not None:
            from_at = datetime.datetime.strptime(from_at, '%Y%m%d%H%M%S')
            queryset = queryset.filter(created_at__gte=from_at)

        till_at = self.request.query_params.get('till', None)
        if till_at is not None:
            till_at = datetime.datetime.strptime(till_at, '%Y%m%d%H%M%S')
            queryset = queryset.filter(created_at__lte=till_at)

        result = []
        cop_ids = request.query_params.get('cs_cop_ids', None)
        if cop_ids is not None and cop_ids.strip() != '':
            for cop_id in cop_ids.split(','):
                cop = CsCop.objects.get(pk=cop_id)
                cop_result = []
                cop_queryset = queryset.filter(cs_cop_id=cop_id).order_by('created_at')
                for cop_analyse in cop_queryset:
                    cop_result.append({
                        'power_cons': cop_analyse.power_cons,
                        'cold_cons': cop_analyse.cold_cons,
                        'eer': cop_analyse.value,
                        'created_at': cop_analyse.created_at.strftime(date_format)
                    })

                result.append({
                    'name': cop.name,
                    'identifier': cop.identifier,
                    'stats': cop_result,
                    'total': len(cop_result)
                })

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'chart_data': result
            }
        })


class ColdSourceEerAnalyses(APIView):
    # 冷源能耗分析数据

    def get(self, request):
        csid = request.query_params.get('csid')
        # mac = ColdSource.objects.get(cold_source_id=csid).mac
        cs_eer_analyse = CsEerAnalyse.objects.filter(cold_source_id=csid)
        now = datetime.datetime.now()
        yesterday = now - datetime.timedelta(days=1)
        month_ago = now - datetime.timedelta(days=30)
        month_stats = cs_eer_analyse.filter(created_at__range=[month_ago.strftime('%Y-%m-%d 00:00:00'), yesterday.strftime('%Y-%m-%d 23:59:59')])
        chart_data = []
        while month_ago <= now:
            begin = month_ago.strftime('%Y-%m-%d 00:00:00')
            end = (month_ago + datetime.timedelta(days=1)).strftime('%Y-%m-%d 00:00:00')
            day_stats = month_stats.filter(created_at__range=[begin, end]).order_by('updated_at')
            if day_stats.exists():
                chart_data.append({
                    "power_cons": day_stats.last().power_cons,
                    "cold_cons": day_stats.last().cold_cons,
                    "eer": day_stats.last().eer,
                    "created_at": month_ago.strftime('%m-%d')
                })
            month_ago = month_ago + datetime.timedelta(days=1)

        # for eer in analyses:
        #     # chart_data.insert(0, {
        #     #     "power_cons": eer.power_cons,
        #     #     "cold_cons": eer.cold_cons,
        #     #     "eer": eer.eer})
        #     chart_data.append({
        #         "power_cons": eer.power_cons,
        #         "cold_cons": eer.cold_cons,
        #         "eer": eer.eer,
        #         "created_at": eer.created_at.strftime('%H:%M')
        #     })
        data = {
            "status": status.HTTP_200_OK,
            "total": len(chart_data),
            "data": {
                "chart_data": chart_data
            }
        }
        return Response(data=data, status=status.HTTP_200_OK)

    def post(self, request):
        # 重新执行某个时间一小时前的冷源能耗分析任务，
        # 请求体： date："20220708091011" project: 27
        cal_date = request.data.get('date', None)
        if cal_date is None:
            return Response({
                'status': status.HTTP_400_BAD_REQUEST,
                'error': 'date field is required'
            })
        project_id = request.data.get('project_id', None)
        if project_id is None:
            return Response({
                'status': status.HTTP_400_BAD_REQUEST,
                'error': 'project_id field is required'
            })
        date = datetime.datetime.strptime(cal_date, '%Y%m%d%H%M%S')
        CsEerAnalyse.objects.do_stat(date, project_id)
        return Response({
            "status": status.HTTP_200_OK,
            "data": None
        })


class EcSourceViewSet(viewsets.ModelViewSet):
    # renderer_classes = (SyJSONRender,)
    serializer_class = EcSourceModelSerializer

    def get_queryset(self):
        queryset = EcSource.objects.all()

        csid = self.request.query_params.get('csid', None)
        if csid is not None:
            queryset = queryset.filter(cold_source_id=csid)

        ec_type = self.request.query_params.get('ec_type', None)
        if ec_type is not None:
            queryset = queryset.filter(ec_type=ec_type)

        return queryset.order_by('id')

    def list(self, request, *args, **kwargs):
        data = super().list(request, *args, **kwargs).data

        cold_source = None
        csid = self.request.query_params.get('csid', None)
        if csid is not None:
            cs = ColdSource.objects.get(id=csid)
            cold_source = {
                'id': cs.id,
                'name': cs.name,
                'mac': cs.mac
            }
        res_data = {
            'status': status.HTTP_200_OK,
            'total': len(data['results']),
            'data': {
                'cold_source': cold_source,
                'ec_sources': data['results']
            }
        }

        return Response(res_data)

    def create(self, request, *args, **kwargs):
        data = super().create(request, *args, **kwargs).data

        return Response({
            'status': status.HTTP_200_OK,
            'data': data
        })

    def destroy(self, request, *args, **kwargs):
        super().destroy(request, *args, **kwargs)
        return Response(status=status.HTTP_200_OK)

class CopViewSet(viewsets.ModelViewSet):
    serializer_class = CsCopSerializer
    queryset = CsCop.objects.all().order_by('id')

    def list(self, request, *args, **kwargs):
        data = super().list(request, *args, **kwargs).data

        return Response({
            'status': status.HTTP_200_OK,
            'count': len(data['results']),
            'data': {
                'cs_cops': data['results']
            }
        })

    def create(self, request, *args, **kwargs):
        data = super().create(request, *args, **kwargs).data

        return Response({
            'status': status.HTTP_200_OK,
            'data': data
        })



class MeterReading(viewsets.ModelViewSet):
    serializer_class = MeterReadingSerializer
    renderer_classes = (SyJSONRender,)

    def get_queryset(self):
        queryset = ManualMeterReading.objects.all()

        meter_id = self.request.query_params.get('meter_id', None)
        if meter_id is not None:
            queryset = queryset.filter(ec_meter_id=meter_id)

        year = self.request.query_params.get('year', None)
        if year is not None:
            queryset = queryset.filter(reading_time__year=year)

        return queryset.order_by('reading_time')

    def create(self, request, *args, **kwargs):
        date_string = request.data.get('reading_time')
        if len(date_string) != 6:
            return Response(status=status.HTTP_400_BAD_REQUEST, data={'error': "reading_time 格式错误"})

        reading_time = datetime.datetime.strptime(date_string + '01000000', '%Y%m%d%H%M%S')
        meter_id = request.data.get('ec_meter')
        ec_meter = get_object_or_404(EcMeter, pk=meter_id)
        if not ec_meter.is_virtual:
            return Response({
                'status': status.HTTP_400_BAD_REQUEST,
                'error': "目标仪表不是虚拟仪表"
            })

        if ManualMeterReading.objects.filter(reading_time=reading_time).exists():
            return Response({
                'status': status.HTTP_400_BAD_REQUEST,
                'error': "记录已存在"
            })

        data = request.data
        data['reading_time'] = reading_time
        data['web_user'] = request.user.get('id')

        serializer = MeterReadingSerializer(data=data)

        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)

        return Response(serializer.data, status=status.HTTP_201_CREATED)

    def destroy(self, request, *args, **kwargs):
        super(MeterReading, self).destroy(request, *args, **kwargs)

        return Response({
            'status': status.HTTP_204_NO_CONTENT,
            'data': None
        })

    def list(self, request, *args, **kwargs):
        readings = super(MeterReading, self).list(request, *args, **kwargs).data
        return Response({
            'status': status.HTTP_200_OK,
            'count': readings['count'],
            'meter_reading': readings['results']
        })

    def partial_update(self, request, *args, **kwargs):
        super(MeterReading, self).partial_update(request, *args, **kwargs)

        # 更新用户
        instance = self.get_object()
        instance.web_user_id = request.user.get('id')
        instance.save()

        serializer = MeterReadingSerializer(instance)

        return Response({
            'status': status.HTTP_200_OK,
            'results': serializer.data
        })


class ColdSourceOverview(viewsets.ModelViewSet):

    def retrieve(self, request, *args, **kwargs):
        mac = request.query_params.get('mac', None)
        if mac is None:
            raise exceptions.ValidationError(detail={'detail': 'mac is required!'})

        cs_device = get_object_or_404(Device, mac=mac)

        terminals = Terminal.objects.filter(device_id=cs_device.id).order_by('id')

        cs_terminal = terminals.get(idx__isnull=True)
        # cs_terminal_attrs = TerminalAttribute.objects.filter(terminal=cs_terminal)
        # temp_set_attr = cs_terminal_attrs.filter(identifier='CWOTempSetVal').last()
        temp_set_attr = TerminalAttribute.objects.query_object_by_idf(cs_terminal, 'CWOTempSetVal', cs_device)
        # run_status = cs_terminal_attrs.filter(Q(identifier='CSRunStatus') | Q(identifier='RunStatus')).last()
        run_status = TerminalAttribute.objects.query_object_by_idf(cs_terminal, 'CSRunStatus', cs_device)
        if run_status is None:
            run_status = TerminalAttribute.objects.query_object_by_idf(cs_terminal, 'RunStatus', cs_device)
        # cwop_temp = cs_terminal_attrs.filter(identifier='CWOPTemp').last()
        cwop_temp = TerminalAttribute.objects.query_object_by_idf(cs_terminal, 'CWOPTemp', cs_device)
        # cwrp_temp = cs_terminal_attrs.filter(identifier='CWRPTemp').last()
        cwrp_temp = TerminalAttribute.objects.query_object_by_idf(cs_terminal, 'CWRPTemp', cs_device)
        # cwp_tempdiff = cs_terminal_attrs.filter(identifier='CWPTempDiff').last()
        cwp_tempdiff = TerminalAttribute.objects.query_object_by_idf(cs_terminal, 'CWPTempDiff', cs_device)
        # cwp_pressdiff = cs_terminal_attrs.filter(identifier='CWPPressDiff').last()
        cwp_pressdiff = TerminalAttribute.objects.query_object_by_idf(cs_terminal, 'CWPPressDiff', cs_device)
        # cwop_press = cs_terminal_attrs.filter(identifier='CWOPPress').last()
        cwop_press = TerminalAttribute.objects.query_object_by_idf(cs_terminal, 'CWOPPress', cs_device)
        # cwrp_press = cs_terminal_attrs.filter(identifier='CWRPPress').last()
        cwrp_press = TerminalAttribute.objects.query_object_by_idf(cs_terminal, 'CWRPPress', cs_device)
        # cowop_temp = cs_terminal_attrs.filter(identifier='COWOPTemp').last()
        cowop_temp = TerminalAttribute.objects.query_object_by_idf(cs_terminal, 'COWOPTemp', cs_device)
        # cowrp_temp = cs_terminal_attrs.filter(identifier='COWRPTemp').last()
        cowrp_temp = TerminalAttribute.objects.query_object_by_idf(cs_terminal, 'COWRPTemp', cs_device)
        # cowp_tempdiff = cs_terminal_attrs.filter(identifier='COWPTempDiff').last()
        cowp_tempdiff = TerminalAttribute.objects.query_object_by_idf(cs_terminal, 'COWPTempDiff', cs_device)
        # cowp_pressdiff = cs_terminal_attrs.filter(identifier='COWPPressDiff').last()
        cowp_pressdiff = TerminalAttribute.objects.query_object_by_idf(cs_terminal, 'COWPPressDiff', cs_device)
        # cowop_press = cs_terminal_attrs.filter(identifier='COWOPPress').last()
        cowop_press = TerminalAttribute.objects.query_object_by_idf(cs_terminal, 'COWOPPress', cs_device)
        # cowrp_press = cs_terminal_attrs.filter(identifier='COWRPPress').last()
        cowrp_press = TerminalAttribute.objects.query_object_by_idf(cs_terminal, 'COWRPPress', cs_device)

        ap_ids = list(AttributePrototype.objects.filter(Q(identifier__endswith='RTLoad') | Q(identifier__endswith='RunStatus') |
                                                        Q(identifier__endswith='Freq') | Q(identifier__endswith='Power'),
                                                        device_prototype_id=cs_device.device_prototype_id).values_list('id', flat=True))
        tas = TerminalAttribute.objects.query_object_list(cs_device, ap_ids, request.user['project_id'])

        host_terminals = terminals.filter(prefix='Host')
        hosts = []
        for terminal in host_terminals:
            # host_tas = TerminalAttribute.objects.filter(terminal=terminal)
            # rt_load = host_tas.filter(identifier__endswith='RTLoad').last()
            # host_run_status = host_tas.filter(identifier__endswith='RunStatus').last()

            rt_load = next((ta for ta in tas if ta.identifier.endswith('RTLoad') and
                            terminal.prefix in ta.identifier and f'_{terminal.idx}_' in ta.identifier), None)
            host_run_status = next((ta for ta in tas if ta.identifier.endswith('RunStatus') and
                                    terminal.prefix in ta.identifier and f'_{terminal.idx}_' in ta.identifier), None)

            hosts.append({
                'nick_name': terminal.nick_name,
                'rt_load': rt_load.value if rt_load is not None else None,
                'run_status': host_run_status.value if host_run_status is not None else None
            })

        fpump_terminals = terminals.filter(prefix='FPump')
        fpumps = []
        for terminal in fpump_terminals:
            # fpump_tas = TerminalAttribute.objects.filter(terminal=terminal)
            # fpump_run_status = fpump_tas.filter(identifier__endswith='RunStatus').last()
            # freq = fpump_tas.filter(identifier__endswith='Freq').last()
            # power = fpump_tas.filter(identifier__endswith='Power').last()

            fpump_run_status = next((ta for ta in tas if ta.identifier.endswith('RunStatus')
                                     and terminal.prefix in ta.identifier and f'_{terminal.idx}_' in ta.identifier), None)
            freq = next((ta for ta in tas if ta.identifier.endswith('Freq')
                         and terminal.prefix in ta.identifier and f'_{terminal.idx}_' in ta.identifier), None)
            power = next((ta for ta in tas if ta.identifier.endswith('Power')
                          and terminal.prefix in ta.identifier and f'_{terminal.idx}_' in ta.identifier), None)
            fpumps.append({
                'nick_name': terminal.nick_name,
                'run_status': fpump_run_status.value if fpump_run_status is not None else None,
                'freq': freq.value if freq is not None else None,
                'power': power.value if power is not None else None
            })

        cpump_terminals = terminals.filter(prefix='CPump')
        cpumps = []
        for terminal in cpump_terminals:
            # cpump_tas = TerminalAttribute.objects.filter(terminal=terminal)
            # cpump_run_status = cpump_tas.filter(identifier__endswith='RunStatus').last()
            # freq = cpump_tas.filter(identifier__endswith='Freq').last()
            # power = cpump_tas.filter(identifier__endswith='Power').last()

            cpump_run_status = next((ta for ta in tas if ta.identifier.endswith('RunStatus')
                                     and terminal.prefix in ta.identifier and f'_{terminal.idx}_' in ta.identifier), None)
            freq = next((ta for ta in tas if ta.identifier.endswith('Freq')
                         and terminal.prefix in ta.identifier and f'_{terminal.idx}_' in ta.identifier), None)
            power = next((ta for ta in tas if ta.identifier.endswith('Power')
                          and terminal.prefix in ta.identifier and f'_{terminal.idx}_' in ta.identifier), None)

            cpumps.append({
                'nick_name': terminal.nick_name,
                'run_status': cpump_run_status.value if cpump_run_status is not None else None,
                'freq': freq.value if freq is not None else None,
                'power': power.value if power is not None else None,
            })

        ctower_terminals = terminals.filter(prefix='CTower')
        ctowers = []
        for terminal in ctower_terminals:
            # ctower_tas = TerminalAttribute.objects.filter(terminal=terminal)
            # ctower_run_status = ctower_tas.filter(identifier__endswith='RunStatus').last()
            # freq = ctower_tas.filter(identifier__endswith='Freq').last()
            # power = ctower_tas.filter(identifier__endswith='Power').last()

            ctower_run_status = next((ta for ta in tas if ta.identifier.endswith('RunStatus')
                                      and terminal.prefix in ta.identifier and f'_{terminal.idx}_' in ta.identifier), None)
            freq = next((ta for ta in tas if ta.identifier.endswith('Freq')
                         and terminal.prefix in ta.identifier and f'_{terminal.idx}_' in ta.identifier), None)
            power = next((ta for ta in tas if ta.identifier.endswith('Power')
                          and terminal.prefix in ta.identifier and f'_{terminal.idx}_' in ta.identifier), None)

            ctowers.append({
                'nick_name': terminal.nick_name,
                'run_status': ctower_run_status.value if ctower_run_status is not None else None,
                'freq': freq.value if freq is not None else None,
                'power': power.value if power is not None else None,
            })

        overview = {
            'id': cs_device.id,
            'nick_name': cs_device.nick_name,
            'temp_set': temp_set_attr.value if temp_set_attr is not None else None,
            'run_status': run_status.value if run_status is not None else None,
            'cwop_temp': cwop_temp.value if cwop_temp is not None else None,
            'cwrp_temp': cwrp_temp.value if cwrp_temp is not None else None,
            'cwp_tempdiff': cwp_tempdiff.value if cwp_tempdiff is not None else None,
            'cwp_pressdiff': cwp_pressdiff.value if cwp_pressdiff is not None else None,
            'cwop_press': cwop_press.value if cwop_press is not None else None,
            'cwrp_press': cwrp_press.value if cwrp_press is not None else None,
            'cowop_temp': cowop_temp.value if cowop_temp is not None else None,
            'cowrp_temp': cowrp_temp.value if cowrp_temp is not None else None,
            'cowp_tempdiff': cowp_tempdiff.value if cowp_tempdiff is not None else None,
            'cowp_pressdiff': cowp_pressdiff.value if cowp_pressdiff is not None else None,
            'cowop_press': cowop_press.value if cowop_press is not None else None,
            'cowrp_press': cowrp_press.value if cowrp_press is not None else None,
            'hosts': hosts,
            'fpumps': fpumps,
            'cpumps': cpumps,
            'ctowers': ctowers
        }

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'overview': overview
            }
        })
