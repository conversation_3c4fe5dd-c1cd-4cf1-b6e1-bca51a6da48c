import base64
import hmac
import logging
import os
import re
import time
from urllib.parse import quote

from rest_framework import authentication
from rest_framework import exceptions

# 服务器api调用鉴权
class IntapiAuth(authentication.BaseAuthentication):
    def authenticate(self, request):
        if 'Authorization' not in request.headers:
            raise exceptions.AuthenticationFailed('鉴权失败！')

        auth = request.headers.get('Authorization', None)
        project_id = request.headers.get('project', None)
        if not auth:
            raise exceptions.AuthenticationFailed('鉴权失败！')
        if not project_id:
            raise exceptions.AuthenticationFailed('请求头缺少项目id')
        if project_id == 'null':
            project_id = None

        # 匹配header中的Authentication的参数
        # 如：appid=saee54d4ab&et=1537255523&res=products/123123&sign=ZjA1NzZlMmMxYzIOTg3MjBzNjYTI2MjA4Yw=
        searchObj = re.search(r'appid=(.*)&et=(.*)&res=(.*)&sign=(.*)', auth, re.M | re.I)

        # 签名参数是否完备
        if searchObj:
            appid = searchObj.group(1)
            # 过期时间
            et = searchObj.group(2)
            # 请求的url路径，去掉第一个字符(斜杠)
            res = request.path[1:]
            target_sign = searchObj.group(4)

            # 检查appid是否在环境变量中已配置
            if appid not in os.environ:
                logging.error(f'找不到用户！')
                raise exceptions.NotFound('找不到用户！')

            # 当前时间
            ct = int(time.time())

            # 检查签名时间是否过期
            if ct > int(et):
                logging.error(f'签名已过期！ct: {ct}, et: {et}')
                raise exceptions.NotFound('签名已过期！')

            # 从环境变量中查找appid对应的access_key
            access_key = os.environ[appid]

            # 采用sha1算法
            method = 'sha1'
            key = base64.b64decode(access_key)

            org = appid + '&' + et + '&' + res
            sign_b = hmac.new(key=key, msg=org.encode(), digestmod=method)
            # 生成签名
            sign = base64.b64encode(sign_b.digest()).decode()
            sign = quote(sign, safe='')

            # 检查签名是否匹配
            if target_sign != sign:
                logging.error(f'签名错误！')
                raise exceptions.AuthenticationFailed('签名错误！')

            # 把调用方信息放到request上下文，方便后续调取
            current_user = {
                'appid': appid,
                'project_id': project_id
            }
        else:
            logging.error(f'签名所需参数缺失！')
            raise exceptions.AuthenticationFailed('签名所需参数缺失！')

        return (current_user, None)

    # 测试时跳过校验
    # def authenticate(self, request):
    #     current_user = {
    #         'appid': 'saee54d4ab',
    #         'project_id': 1
    #     }
    #     return (current_user, None)
