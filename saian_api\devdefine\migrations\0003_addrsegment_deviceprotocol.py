# Generated by Django 3.2.8 on 2022-03-24 15:23

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('devdefine', '0002_alter_attributetype_options'),
    ]

    operations = [
        migrations.CreateModel(
            name='DeviceProtocol',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('p_type', models.IntegerField(null=True)),
                ('identifier', models.CharField(max_length=255, null=True)),
                ('data_type', models.IntegerField(null=True)),
                ('data_len', models.IntegerField(null=True)),
                ('in_use', models.BooleanField(default=True)),
                ('devider', models.IntegerField(default=1)),
                ('ro', models.BooleanField()),
                ('slaveid', models.IntegerField(null=True)),
                ('func_code', models.CharField(max_length=100, null=True)),
                ('objid', models.CharField(max_length=100, null=True)),
                ('insid', models.CharField(max_length=100, null=True)),
                ('resid', models.CharField(max_length=100, null=True)),
                ('position', models.CharField(max_length=100, null=True)),
                ('prefix', models.CharField(max_length=100, null=True)),
                ('suffix', models.CharField(max_length=100, null=True)),
                ('read_time', models.IntegerField(null=True)),
                ('topic', models.CharField(max_length=255, null=True)),
                ('topic_set', models.CharField(max_length=255, null=True)),
                ('sp_defs', models.TextField(null=True)),
                ('model_no', models.CharField(max_length=255, null=True)),
                ('w_code', models.CharField(max_length=100, null=True)),
                ('w_addr', models.CharField(max_length=100, null=True)),
                ('device_prototype', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='devdefine.deviceprototype')),
            ],
            options={
                'db_table': 'device_protocols',
            },
        ),
        migrations.CreateModel(
            name='AddrSegment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_addr', models.CharField(max_length=255, null=True)),
                ('end_addr', models.CharField(max_length=255, null=True)),
                ('interval', models.IntegerField()),
                ('device_prototype', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='devdefine.deviceprototype')),
            ],
            options={
                'db_table': 'addr_segments',
            },
        ),
    ]
