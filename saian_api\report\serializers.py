from django.contrib.contenttypes.models import ContentType
from rest_framework import serializers, exceptions

from .models import (ReportConfigurer, User<PERSON>hart, UserAnalyse, UserAnalysisCharts, ManualRecord, DlTask, StatReport,
                     SettleParty, SettleRefer, SettleRecord, DevstlRecord)
from ..building.models import ActiveRoom
from ..devdefine.models import AttributePrototype
from ..device.models import Device
from saian_api.utils.httpapi.image import ImageAPI
from rest_framework.validators import UniqueValidator
import json

from ..user.models import WebUser


class ReportSerializer(serializers.ModelSerializer):
    class Meta:
        model = ReportConfigurer
        fields = ['name', 'unit', 'identifier', 'chart_type', 'target_type', 'target_id', 'device_idx','do_minstat']


class SimpleDeviceSerializer(serializers.ModelSerializer):
    class Meta:
        model = Device
        fields = ['id', 'nick_name', 'mac', 'device_prototype_id']

class SimpleRoomSerializer(serializers.ModelSerializer):
    class Meta:
        model = ActiveRoom
        fields = ['id', 'name', 'building']

class UserChartSerializer(serializers.ModelSerializer):
    uni_name = serializers.CharField(max_length=255, validators=[UniqueValidator(queryset=UserChart.objects.all())])
    chart_type = serializers.IntegerField()
    # data_src = serializers.IntegerField(required=False)
    data_params = serializers.JSONField(required=False)
    style_params = serializers.JSONField(required=False)

    class Meta:
        model = UserChart
        fields = '__all__'
        read_only_fields = ('id','created_at', 'updated_at')

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        ret['thumb'] = ImageAPI.get_url(request=self.context['request'], image_id=ret['thumb'])

        ret.pop('web_user')

        return ret

    def validate(self, attrs):
        data_src = None
        if 'data_src' in attrs:
            data_src = int(attrs['data_src'])

            if data_src == 60 and 'data_name' not in attrs:
                raise exceptions.ValidationError('data_name是必须的!')

            if data_src != 60 and 'data_params' not in attrs:
                raise exceptions.ValidationError('data_params是必须的!')

        return attrs

class UserAnalyseSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserAnalyse
        fields = '__all__'

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        ret['thumb'] = ImageAPI.get_url(request=self.context['request'], image_id=ret['thumb'])

        ret.pop('web_user')

        view = self.context['view']

        acs = []

        if view.action == 'retrieve':
            analyse_charts = UserAnalysisCharts.objects.filter(user_analyse_id=instance.id).order_by('seq')

            for ac in analyse_charts:
                chart = ac.content
                if not chart:
                    continue
                acs.append({
                    'id': chart.id,
                    # 'content_type': ac.content_type.model,
                    'name': chart.name,
                    'uni_name': chart.uni_name,
                    'chart_type': chart.chart_type,
                    'data_src': chart.data_src,
                    'query_time': chart.query_time,
                    'gran': chart.gran,
                    'data_params': chart.data_params,
                    'style_params': chart.style_params,
                    'seq': ac.seq,
                    'created_at': chart.created_at,
                    'updated_at': chart.updated_at
                })

        ret['charts'] = acs

        return ret

class ManualRecordSerializer(serializers.ModelSerializer):
    name = serializers.CharField(validators=[UniqueValidator(queryset=ManualRecord.objects.all())])

    class Meta:
        model = ManualRecord
        fields = '__all__'

    def validate(self, attrs):
        records = attrs.get('data', None)
        data_type = attrs.get('data_type', None)

        if records is not None:
            try:
                json_data = json.loads(records)
            except Exception:
                raise exceptions.ValidationError('invalid data!')
        
            if data_type is not None:
                data_type = int(attrs['data_type'])
                if data_type == 40:
                    if isinstance(json_data, list):
                        raise exceptions.ValidationError('invalid data!')
                else:
                    if not isinstance(json_data, list):
                        raise exceptions.ValidationError('invalid data!')

        return attrs


class DlTaskSerializer(serializers.ModelSerializer):
    class Meta:
        model = DlTask
        fields = '__all__'

class StatReportSerializer(serializers.ModelSerializer):
    class Meta:
        model = StatReport
        fields = '__all__'

class SettlePartySerializer(serializers.ModelSerializer):
    class Meta:
        model = SettleParty
        fields = '__all__'

class SettleReferSerializer(serializers.ModelSerializer):
    run_params = serializers.JSONField()
    class Meta:
        model = SettleRefer
        fields = ['id', 'settle_party_id', 'param_name', 'param_idf', 'param_value', 'param_unit', 'run_params', 'created_at','updated_at']

class SettleRecordSerializer(serializers.ModelSerializer):
    class Meta:
        model = SettleRecord
        fields = '__all__'

class DevstlRecordSerializer(serializers.ModelSerializer):
    class Meta:
        model = DevstlRecord
        fields = '__all__'
