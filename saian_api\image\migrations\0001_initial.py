# Generated by Django 3.2.8 on 2022-01-21 10:41

from django.db import migrations, models
import saian_api.image.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Image',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to=saian_api.image.models.upload_to)),
                ('created_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'images',
            },
        ),
    ]
