# Generated by Django 3.2.19 on 2024-05-31 17:30

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('report', '0011_auto_20240520_1042'),
    ]

    operations = [
        migrations.CreateModel(
            name='StatReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('content_name', models.CharField(max_length=255)),
                ('periods', models.IntegerField()),
                ('report_dt', models.DateTimeField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'stat_reports',
                'ordering': ['report_dt'],
            },
        ),
    ]
