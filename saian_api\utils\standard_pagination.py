from collections import OrderedDict

from rest_framework.exceptions import NotFound
from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response

class StandardResultsSetPagination(PageNumberPagination):
    page_size = 100
    page_size_query_param = 'per_page'
    max_page_size = 2000

    # 当请求的页号超出范围，默认会返回404，这里改为返回空列表
    def paginate_queryset(self, queryset, request, view=None):
        try:
            return super(StandardResultsSetPagination, self).paginate_queryset(queryset, request, view=view)
        except NotFound:  # intercept NotFound exception
            return list()

    def get_paginated_response(self, data):
        if hasattr(self, 'page') and self.page is not None:
            return super(StandardResultsSetPagination, self).get_paginated_response(data)
        else:
            return Response(OrderedDict([
                ('count', None),
                ('next', None),
                ('previous', None),
                ('results', data)
            ]))
