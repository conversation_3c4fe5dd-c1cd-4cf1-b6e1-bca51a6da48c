import datetime
import json
import logging
import traceback
import asyncio
from asgiref.sync import sync_to_async

from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.db import models
from django.utils.module_loading import import_string

from saian_api.devdefine.models import DevicePrototype
from saian_api.device.models import Device, DeviceAttribute, DeviceTimer
from saian_api.project.models import Project
from saian_api.terminal.models import Terminal, TerminalAttribute
from saian_api.user.models import WebUser

logger = logging.getLogger('django')

loop = asyncio.new_event_loop()
asyncio.set_event_loop(loop)

# Create your models here.
class Group(models.Model):
    """
      设备分组
    """
    # 所属项目
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    # 创建分组的用户
    web_user = models.ForeignKey(WebUser, null=True, on_delete=models.CASCADE)
    # 分组名字
    name = models.CharField(max_length=255)
    # 是否共享分组
    shared = models.BooleanField(default=False)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'groups'
        ordering = ['-created_at']

    # 返回只包含单个设备类型的分组的设备类型
    def device_prototype(self):
        result = None
        group_devices = self.groupdevice_set.all()
        if len(group_devices) != 0:
            first_model = group_devices[0].content_type
            if first_model.name == 'deviceprototype':
                result = DevicePrototype.objects.filter(id=group_devices[0].object_id).first()

            if first_model.name == 'device':
                device = Device.objects.filter(id=group_devices[0].object_id).first()
                result = DevicePrototype.objects.filter(id=device.device_prototype_id).first()

            if first_model.name == 'terminal':
                terminal = Terminal.objects.filter(id=group_devices[0].object_id).first()
                result = DevicePrototype.objects.filter(id=terminal.device_prototype_id).first()
                result = result.parent if result.parent else result
        return result

    # 分组是否是跨设备类型分组
    def is_cross(self):
        result = False
        group_devices = self.groupdevice_set.all()

        if len(group_devices) != 0:
            first_model = group_devices[0].content_type

            # 单个设备类型的分组
            # if len(group_devices) == 1 and first_model.name == 'deviceprototype':
            if len(group_devices) == 1:
                result = False

            else:

                # 多个设备类型的分组
                if len(group_devices) > 1 and first_model.name == 'deviceprototype':
                    result = True

                # # 1个设备
                # if len(group_devices) == 1 and first_model.name == 'device':
                #     result = False

                # 如果分组目标是设备，而且设备属于不同的类型，则为跨类型分组
                if len(group_devices) > 1 and first_model.name == 'device':
                    device_ids = group_devices.values_list('object_id', flat=True)
                    dp_ids = Device.objects.filter(id__in=device_ids).values_list('device_prototype_id', flat=True)
                    result = len(set(dp_ids)) > 1

                # 如果分组目标是终端
                if first_model.name == 'terminal':
                    # terminal_ids = [gd.object_id for gd in group_devices]
                    terminal_ids = group_devices.values_list('object_id', flat=True)
                    dp_ids = Terminal.objects.filter(id__in=terminal_ids).values_list('device_prototype_id', flat=True)
                    result = len(set(dp_ids)) > 1
        return result

    # 分组包含的设备数量
    def device_num(self):
        group_items = GroupDevice.objects.filter(group_id=self.id)
        if group_items.exists():
            item_model = ContentType.objects.get(pk=group_items.first().content_type_id)
            count = 0

            if item_model.model_class().__name__ == 'DevicePrototype':
                for group_item in group_items:
                    count += Device.objects.filter(device_prototype_id=group_item.object_id).count()
            else:
                count = group_items.count()

            return count
        else:
            return 0

    # 分组的快捷操作
    def actions(self):
        actions = self.groupaction_set.all()
        return [{"id": action.id, "name": action.name} for action in actions]

    # 分组的设备
    def devices(self):
        if self.groupdevice_set.all().count() > 0:
            group_member_object = self.groupdevice_set.all()[0].content_object
            if group_member_object is None:
                return []
            if isinstance(group_member_object, Device):
                devices = Device.objects.filter(id__in=self.groupdevice_set.all().values_list('object_id', flat=True))
                return devices

            elif isinstance(group_member_object, Terminal):
                terminals = Terminal.objects.filter(id__in=self.groupdevice_set.all().values_list('object_id', flat=True))
                # device_ids = list(set(terminals.values_list('device_id', flat=True)))
                # devices = Device.objects.filter(id__in=device_ids)
                # return devices
                return terminals

            elif isinstance(group_member_object, DevicePrototype):
                return []
        else:
            return []

class GroupDevice(models.Model):
    """
      分组设备关系表
    """
    # 所属分组
    group = models.ForeignKey(Group, on_delete=models.CASCADE)
    # 设备或设备类型
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveBigIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'group_devices'
        ordering = ['-created_at']


class GroupAction(models.Model):
    """
      快捷（分组）操作
    """
    # 所属分组
    group = models.ForeignKey(Group, on_delete=models.CASCADE)
    # 名称
    name = models.CharField(max_length=255)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'group_actions'
        ordering = ['-created_at']

    def attr_model(self):
        if self.group.is_cross():
            return import_string('saian_api.linkage.models.CrossAttribute')
        else:
            return import_string('saian_api.devdefine.models.AttributePrototype')

    @staticmethod
    def handle_not_cross_execute(targets, changes, executor, action_log_id):
        if len(targets) and isinstance(targets[0], Terminal):
            device_changes = {}
            for target in targets:
                terminal_changes = device_changes.get(target.device_id, {})
                for k, v in changes.items():
                    if TerminalAttribute.objects.filter(terminal=target, identifier=k).exists():
                        if k not in terminal_changes:
                            terminal_changes[k] = v
                device_changes[target.device_id] = terminal_changes

            device_ids = list(set(targets.values_list('device_id', flat=True)))
            now = datetime.datetime.now().replace(second=0, microsecond=0)
            off_identifiers = json.dumps([{"identifier": "SW", "value": "0"}])
            device_map = {d.id: d for d in Device.objects.filter(pk__in=device_ids)}

            for device_id in device_ids:
                try:
                    if device_changes.get(device_id, None):
                        device = device_map[device_id]
                        changes = device_changes[device_id]
                        keys = list(changes.keys())
                        # 检查设备是否存在延迟执行的设备云定时
                        # 只检查单参数，设备云定时包含相同的参数
                        if len(keys) == 1:
                            device_timer = DeviceTimer.objects.filter(device_id=device.id, name__startswith='延时',
                                                                      enabled=True, repeat=False, is_finished=False,
                                                                      run_date__isnull=False, run_time__isnull=False,
                                                                      identifiers=off_identifiers).first()
                            if device_timer:
                                run_at = datetime.datetime.combine(device_timer.run_date, device_timer.run_time)
                                if run_at >= now:
                                    continue

                        # 康泽的终端分组任务，把每一个的指令都拆分出来，一起发送可能无法生效
                        if device.project_id == 44:
                            for k, v in changes.items():
                                device.send_ctrls(executor, {k: v}, False, action_log_id, device.project_id)
                        else:
                            device.send_ctrls(executor, device_changes[device_id], False, action_log_id, device.project_id)
                except Exception:
                    logger.exception(traceback.format_exc())
                    continue
        else:
            now = datetime.datetime.now().replace(second=0, microsecond=0)
            off_identifiers = json.dumps([{"identifier": "SW", "value": "0"}])

            for device in targets:
                try:
                    keys = list(changes.keys())
                    # 检查设备是否存在延迟执行的设备云定时
                    # 只检查单参数，设备云定时包含相同的参数
                    if len(keys) == 1:
                        device_timer = DeviceTimer.objects.filter(device_id=device.id, name__startswith='延时',
                                                                  enabled=True, repeat=False, is_finished=False,
                                                                  run_date__isnull=False, run_time__isnull=False,
                                                                  identifiers=off_identifiers).first()
                        if device_timer:
                            run_at = datetime.datetime.combine(device_timer.run_date, device_timer.run_time)
                            if run_at >= now:
                                continue

                    device.send_ctrls(executor, changes, False, action_log_id, device.project_id)
                except Exception:
                    logger.exception(traceback.format_exc())
                    continue

    @staticmethod
    def handle_cross_execute(targets, action_attribute, ap_value, executor, action_log_id):
        changes = {}
        # device_changes 归纳不同设备类型的参数，再遍历设备,在 device_changes 找到对应的参数下发
        device_changes = {}
        # for ap in action_attribute.content_object.attribute_prototypes.all():
        ap = action_attribute.content_object
        dp_changes = device_changes.get(ap.device_prototype_id, {})
        if ap.identifier not in dp_changes:
            dp_changes = {**dp_changes, ap.identifier: ap_value}
        device_changes[ap.device_prototype_id] = dp_changes

        if len(targets) and isinstance(targets[0], Terminal):
            devices = set([terminal.device for terminal in targets])
        else:
            devices = targets

        for device in devices:
            try:
                if device_changes.get(device.device_prototype_id, None):
                    loop.run_until_complete(sync_to_async(device.send_ctrls)(executor, device_changes[device.device_prototype_id], False,
                                                                             action_log_id, device.project_id))
                    # 更新 changes，用于在 action_log 记录下发参数
                    changes = {**changes, **device_changes[device.device_prototype_id]}
            except Exception:
                logger.exception(traceback.format_exc())
                continue
        return changes

    def execute_by_manual(self, executor, attributes):
        """手动触发分组操作"""
        # targets 可能是设备，也有可能是终端
        targets = self.group.devices()

        # 记录运行日志
        executor_type = ContentType.objects.get_for_model(executor)
        remark = ''
        changes = {}
        if executor_type == 'WebUser':
            remark = f"name={executor.name}"

        action_log = ActionLog.objects.create(
            executor_id=executor.id,
            executor_type=executor_type,
            actor_name=executor_type.model_class().__name__,
            action_name=self.name,
            values={},
            remark=remark,
            op_type=10,
            op_id=self.id
        )
        # 单个设备类型分组处理
        if not self.group.is_cross():
            # 旧版分组操作执行
            if isinstance(attributes, list):
                for attr in attributes:
                    action_attribute = ActionAttribute.objects.get(pk=attr['attr_id'])
                    if attr.get('value', None) is not None:
                        changes[action_attribute.content_object.identifier] = attr['value']
            # 快捷操作执行
            else:
                changes = attributes

            self.handle_not_cross_execute(targets, changes, executor, action_log.id)

        # 跨设备类型分组处理
        else:
            # TODO  快捷操作执行支持跨设备类型
            for attr in attributes:
                action_attribute = ActionAttribute.objects.get(pk=attr['attr_id'])
                attr_changes = self.handle_cross_execute(targets, action_attribute, attr['value'], executor, action_log.id)

                changes = {**changes, **attr_changes}

        action_log.values = json.dumps(changes, ensure_ascii=False)
        action_log.save()

    def execute_by_timer(self, timer):
        """定时触发执行分组操作"""
        targets = self.group.devices()
        changes = {}

        # 记录运行日志
        executor_type = ContentType.objects.get_for_model(timer)
        remark = f"""{{"repeat": "{'是' if timer.repeat else '否'}", "run_date": "{timer.run_date if timer.run_date is not None else '无'}", 
                "run_time": "{timer.run_time if timer.run_time is not None else '无'}", "week": "{timer.week if timer.week is not None else '无'}", 
                "is_finished": "{'是' if timer.is_finished else '否'}", "enabled": "'是' if timer.enabled else '否'"}}"""

        action_log = ActionLog.objects.create(
            executor_id=timer.id,
            executor_type=executor_type,
            actor_name=executor_type.model_class().__name__,
            action_name=self.name,
            values={},
            remark=remark,
            op_type=10,
            op_id=timer.group_action_id)

        if not self.group.is_cross():
            action_timer_attributes = timer.actiontimerattribute_set.all()
            for ata in action_timer_attributes:
                changes[ata.action_attribute.content_object.identifier] = ata.value

            self.handle_not_cross_execute(targets, changes, timer, action_log.id)
        else:
            for ata in timer.actiontimerattribute_set.all():
                ata_changes = self.handle_cross_execute(targets, ata.action_attribute, ata.value, timer, action_log.id)

                changes = {**changes, **ata_changes}

        action_log.values = json.dumps(changes, ensure_ascii=False)
        action_log.save()

class ActionAttribute(models.Model):
    """
      快捷操作参数定义
    """
    # 所属的快捷操作
    group_action = models.ForeignKey(GroupAction, on_delete=models.CASCADE)
    # 发起方，可能是用户、定时器或联动操作
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveBigIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'action_attributes'
        ordering = ['-created_at']

class ActionTimer(models.Model):
    """
      快捷操作定时器
    """
    # 所属的快捷操作
    group_action = models.ForeignKey(GroupAction, on_delete=models.CASCADE)
    # 是否重复
    repeat = models.BooleanField(default=False)
    # 执行日期
    run_date = models.DateField(null=True)
    # 执行时间
    run_time = models.TimeField(null=True)
    # 周x，多个时用逗号隔开
    week = models.CharField(max_length=255, null=True)
    # 是否已完成
    is_finished = models.BooleanField(default=False)
    # 是否启用
    enabled = models.BooleanField(default=False)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    action_attributes = models.ManyToManyField(ActionAttribute, through=u'ActionTimerAttribute', related_name=u'action_timer_attributes')

    # 有效时间范围
    time_ranges = models.TextField(null=True, blank=True)

    class Meta:
        db_table = 'action_timers'
        ordering = ['-created_at']

class ActionTimerAttribute(models.Model):
    """
      快捷操作参数值定义
    """
    # 所属的定时器
    action_timer = models.ForeignKey(ActionTimer, on_delete=models.CASCADE)
    # 所属的快捷操作参数
    action_attribute = models.ForeignKey(ActionAttribute, on_delete=models.CASCADE)
    # 预设值
    value = models.CharField(max_length=255, null=True, blank=True)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'action_timer_attributes'
        ordering = ['-created_at']

class ActionLog(models.Model):
    """
      操作日志记录
    """
    # 发起方，可能是用户、定时器或联动操作
    executor_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    executor_id = models.PositiveBigIntegerField()
    executor = GenericForeignKey('executor_type', 'executor_id')

    # 发起方名字
    actor_name = models.CharField(max_length=255, null=True)
    # 操作的名字
    action_name = models.CharField(max_length=255, null=True)

    # 下发的值，json格式
    values = models.TextField()
    # 备注
    remark = models.CharField(max_length=255, null=True)

    # 操作类型，10-分组操作，20-联动操作，30-设备软定时
    op_type = models.IntegerField(null=True)
    # 对应操作的id，分组操作为group_action的id，联动操作为linkage_rule的id
    op_id = models.PositiveBigIntegerField(null=True)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'action_logs'
        ordering = ['-created_at']

class Shortcut(models.Model):
    """
      快捷操作
    """
    # 快捷操作名字
    name = models.CharField(max_length=255, null=True)
    # 快捷标识名字
    uni_name = models.CharField(max_length=255, null=True)

    # 操作类型，10-分组操作，20-联动操作，30-定制操作
    op_type = models.IntegerField()
    # 对应操作的id，分组操作为group_action的id，联动操作为linkage_rule的id
    op_id = models.PositiveBigIntegerField(null=True)
    # 备注
    remark = models.CharField(max_length=255, null=True)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'shortcuts'
        ordering = ['-created_at']


class ShortcutAttribute(models.Model):
    """快捷操作参数表"""
    # 目标快捷操作
    shortcut = models.ForeignKey(Shortcut, on_delete=models.CASCADE)
    # 参数id
    object_id = models.PositiveBigIntegerField()
    # 参数类型，可以是属性类型(AttributePrototype)和跨设备类型(CrossAttribute)
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    content_object = GenericForeignKey('content_type', 'object_id')
    # 参数值
    value = models.CharField(max_length=255, blank=True, null=True)
    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'shortcut_attributes'
        ordering = ['-created_at']

class AcStrategies(models.Model):
    """
      运行策略
    """
    # 策略类型，10-定时分组操作，20-定时联动操作，30-被动联动操作，40-设备软定时，50-末端温度限制
    st_type = models.IntegerField(null=True)
    # 对应操作的id，分组操作为group_actions的action_timer的id，联动操作为linkage_rules的id，设备软定时为device_timers的id，固定操作id为device_limits的id
    st_id = models.PositiveBigIntegerField(null=True)
    # 策略状态，10-正常，20-完成，30-隐藏
    status = models.IntegerField()

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'ac_strategies'
        ordering = ['-created_at']
