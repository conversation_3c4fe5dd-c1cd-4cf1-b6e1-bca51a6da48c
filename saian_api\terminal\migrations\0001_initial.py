# Generated by Django 3.2.8 on 2022-06-13 15:21

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('device', '0004_auto_20220218_1044'),
        ('devdefine', '0005_deviceprotocol_addr'),
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'category',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Terminal',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('idx', models.IntegerField(blank=True, null=True)),
                ('prefix', models.CharField(blank=True, max_length=255, null=True)),
                ('in_fault', models.BooleanField(default=False)),
                ('in_alarm', models.BooleanField(default=False)),
                ('online', models.BooleanField(default=False)),
                ('sw_on', models.BooleanField(default=False)),
                ('nick_name', models.CharField(blank=True, db_index=True, max_length=255, null=True)),
                ('terminal_type', models.IntegerField(blank=True, null=True)),
                ('terminal_label', models.CharField(blank=True, max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('device', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='device.device')),
                ('device_prototype', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='devdefine.deviceprototype')),
            ],
            options={
                'db_table': 'terminals',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='TerminalAttribute',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('attribute_prototype_id', models.BigIntegerField(db_index=True)),
                ('identifier', models.CharField(max_length=255)),
                ('value', models.CharField(max_length=255, null=True)),
                ('is_set_temp', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('terminal', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='terminal.terminal')),
            ],
            options={
                'db_table': 'terminal_attribute',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CategoryTerminal',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('Category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='terminal.category')),
                ('terminal', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='terminal.terminal')),
            ],
            options={
                'db_table': 'category_terminals',
            },
        ),
        migrations.AddField(
            model_name='category',
            name='terminals',
            field=models.ManyToManyField(through='terminal.CategoryTerminal', to='terminal.Terminal'),
        ),
    ]
