import ast
import time

import requests
from rest_framework.response import Response
from rest_framework.views import APIView

from saian_api.regions.models import Region

class OriginRegion(APIView):
    def get(self, request):
        # pid = request.query_params.get('pid')

        # print('base region: ', eval(result.content).get('data').get('regions')[0])
        firLevel = OriginRegion.getOriginRegions('000000')
        print('first level: ', type(firLevel), len(firLevel))
        for fir in firLevel:
            firRegion = OriginRegion.getNewRegion(fir)
            firRegion.save()
            secLevel = OriginRegion.getOriginRegions(fir.get('id'))
            print(fir.get('name'))

            for sec in secLevel:
                secRegion = OriginRegion.getNewRegion(sec)
                # secRegion.save()
                time.sleep(1)
                thrLevel = OriginRegion.getOriginRegions(sec.get('id'))
                print('      ', sec.get('name'))

                thrRegions = [secRegion]

                for thr in thrLevel:
                    thrRegion = OriginRegion.getNewRegion(thr)
                    thrRegions.append(thrRegion)
                    # thrRegion.save()
                    # time.sleep(1)
                    # fourLevel = OriginRegion.getOriginRegions(thr.get('id'))
                    print('          ', thr.get('name'))

                Region.objects.bulk_create(thrRegions)

                    # for four in fourLevel:
                    #     fourRegion = OriginRegion.getNewRegion(four)
                    #     fourRegion.save()
                    #     print('              ', four.get('name'))

        return Response(Region.objects.all())

    @staticmethod
    def getOriginRegions(pid):
        Authorization = 'Token token=J5ahxvAzBgyNrZ4Hl3SxrzG1RLdygZgZiQBaAtiD9fxgGd/AjNNYFMgYcGI/JEA+5GKvHpyjfyPAvhOB5J8W2A==, identity=66, project=27'
        result = requests.get('https://bg.syense.cn/saianapi/v1/web_regions?pid=' + str(pid),
                              headers={'Authorization': Authorization})
        return ast.literal_eval(result.text).get('data').get('regions')

    @staticmethod
    def getNewRegion(origin_region):
        region = Region(id=origin_region.get('id'), name=origin_region.get('name'),
                        short_name=origin_region.get('short_name'),
                        level=origin_region.get('level'), weather_code=origin_region.get('weather_code'))
        return region
# def getRegions(request):
#     Authorization = 'Token token=J5ahxvAzBgyNrZ4Hl3SxrzG1RLdygZgZiQBaAtiD9fxgGd/AjNNYFMgYcGI/JEA+5GKvHpyjfyPAvhOB5J8W2A==, identity=66, project=27'
#     result = requests.get('https://bg.syense.cn/saianapi/v1/web_regions?pid=' + pid,
#                           headers={'Authorization': Authorization})
#     # print('base region: ', eval(result.content).get('data').get('regions')[0])
#     print('region: ', ast.literal_eval(result.text).get('data'))
#     Region.objects.all()
#     # return eval()
