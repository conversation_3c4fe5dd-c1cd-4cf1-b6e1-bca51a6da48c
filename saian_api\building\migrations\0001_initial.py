# Generated by Django 3.2.8 on 2021-12-30 15:16

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ActiveRoom',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.CharField(blank=True, max_length=255, null=True)),
                ('name', models.CharField(blank=True, max_length=255, null=True)),
                ('min_temp', models.IntegerField(null=True)),
                ('max_temp', models.IntegerField(null=True)),
                ('min_humidity', models.IntegerField(null=True)),
                ('max_humidity', models.IntegerField(null=True)),
                ('min_tvoc', models.IntegerField(null=True)),
                ('max_tvoc', models.IntegerField(null=True)),
                ('min_co2', models.IntegerField(null=True)),
                ('max_co2', models.IntegerField(null=True)),
                ('min_pm25', models.IntegerField(null=True)),
                ('max_pm25', models.IntegerField(null=True)),
                ('show_in_datav', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'active_rooms',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Building',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.CharField(blank=True, max_length=255, null=True)),
                ('name', models.CharField(max_length=255)),
                ('address', models.CharField(blank=True, max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'buildings',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='RoomParam',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('room_no', models.CharField(db_index=True, max_length=255)),
                ('identifier', models.CharField(max_length=255)),
                ('value', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('active_room', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='building.activeroom')),
            ],
            options={
                'db_table': 'room_params',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Floor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('floor_no', models.IntegerField(null=True)),
                ('image', models.CharField(blank=True, max_length=255, null=True)),
                ('name', models.CharField(blank=True, max_length=255, null=True)),
                ('thumb', models.CharField(blank=True, max_length=255, null=True)),
                ('coords_arr', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('building', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='building.building')),
            ],
            options={
                'db_table': 'floors',
                'ordering': ['-created_at'],
            },
        ),
    ]
