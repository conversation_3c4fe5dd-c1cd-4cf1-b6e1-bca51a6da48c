from django.urls import path

from .views import DeviceMaintenanceViewSet, MaintenanceStatViewSet

device_m = DeviceMaintenanceViewSet.as_view({
    'get': 'list',
    'post': 'create'
})

m_stat = MaintenanceStatViewSet.as_view({
    'get': 'list'
})

urlpatterns = [
    path('saianapi/v1/device_maintenances', device_m, name='device_m'),
    path('saianapi/v1/projects/<int:prj>/device_maintenances', device_m, name='prj_device_m'),
    path('saianapi/v1/maintenance_stats', m_stat, name='m_stat'),
    path('saianapi/v1/projects/<int:prj>/maintenance_stats', m_stat, name='prj_m_stat')
]
