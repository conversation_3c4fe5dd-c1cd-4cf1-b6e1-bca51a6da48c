"""saian_api URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/3.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.urls import include, path

from saian_api.act.urls import urlpatterns as act_urls
from saian_api.building.urls import urlpatterns as building_urls
from saian_api.coldsource.urls import urlpatterns as cs_urls
from saian_api.dashboard.urls import urlpatterns as dashboard_urls
from saian_api.devdefine.urls import urlpatterns as devdefine_urls
from saian_api.device.urls import urlpatterns as device_urls
from saian_api.group.urls import urlpatterns as group_urls
from saian_api.image.urls import urlpatterns as image_urls
from saian_api.intapi.urls import urlpatterns as intapi_urls
from saian_api.issue.urls import urlpatterns as issue_urls
from saian_api.linkage.urls import urlpatterns as rule_urls
from saian_api.maint.urls import urlpatterns as maint_urls
from saian_api.message.urls import urlpatterns as message_urls
from saian_api.notifi.urls import urlpatterns as notifi_urls
from saian_api.project.urls import urlpatterns as project_urls
from saian_api.regions.urls import urlpatterns as region_urls
from saian_api.report.urls import urlpatterns as report_urls
from saian_api.terminal.urls import urlpatterns as terminal_urls
from saian_api.user.urls import urlpatterns as user_urls
from saian_api.openapi.urls import urlpatterns as openapi_urls
from saian_api.dimension.urls import urlpatterns as dimension_urls

# router = routers.DefaultRouter()
# router.register('saianapi/v1/web_users', user_views.WebUser)

urlpatterns = [
    # path('admin/', admin.site.urls),
    # path('', include(router.urls)),
    path('', include(building_urls)),
    path('', include(intapi_urls)),
    path('', include(device_urls)),
    path('', include(dashboard_urls)),
    path('', include(project_urls)),
    path('', include(image_urls)),
    path('', include(notifi_urls)),
    path('', include(user_urls)),
    path('', include(region_urls)),
    path('', include(act_urls)),
    path('', include(report_urls)),
    path('', include(cs_urls)),
    path('', include(rule_urls)),
    path('', include(group_urls)),
    path('', include(devdefine_urls)),
    path('', include(issue_urls)),
    path('', include(maint_urls)),
    path('', include(user_urls)),
    path('', include(notifi_urls)),
    path('', include(message_urls)),
    path('', include(terminal_urls)),
    path('', include(openapi_urls)),
    path('', include(dimension_urls)),

    # path('saianapi/v1/web_users',user_views.WebUserList.as_view()),
    # path('saianapi/v1/web_users/<int:pk>/', user_views.WebUserDetail.as_view()),
    # path('saianapi/v1/projects/<int:pk>/', project_views.ProjectDetail.as_view()),
    # path('saianapi/v1/web_regions/', regions_view.RegionList.as_view()),
    # path('saianapi/v1/bluk_regions/', bulk_create.OriginRegion.as_view())
    #     path('api-auth/', include('rest_framework.urls', namespace='rest_framework')),
    #     # path('test_models/', views.test_model_list),
]
