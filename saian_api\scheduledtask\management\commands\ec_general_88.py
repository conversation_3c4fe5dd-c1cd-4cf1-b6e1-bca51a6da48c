import calendar
import datetime
import logging

from django.core.management import BaseCommand
from django.db.models import Sum

from saian_api.dimension.models import DimensionHourlyStat, DimensionDailyStat, DimensionMonthlyStat
from saian_api.scheduledtask.utils import get_projects, set_global_db

class Command(BaseCommand):
    help = "冷源能效日统计"

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目ID列表
        projects = get_projects(self, options)
        now = datetime.datetime.now()

        for project_id in projects:
            set_global_db(project_id)

            # year = 2023
            # ref_year = 2023
            # month = 12
            #
            # month_day = calendar.monthrange(year, month)[1]
            # ref_month_day = calendar.monthrange(ref_year, month)[1]
            #
            # if month_day != ref_month_day:
            #     print(f'not equal {month}')
            #     # continue
            #
            # for target_id in [1,3,4,5,6]:
            #
            #     ref_day_stats = list(DimensionDailyStat.objects.using('prj68db').filter(
            #         dimension_attribute_id=6, created_at__year=ref_year, created_at__month=month).order_by('created_at').values_list('avg', flat=True))
            #     if month_day != ref_month_day:
            #         ref_day_stats = ref_day_stats[:-1]
            #     ref_sum = sum([float(stat) for stat in ref_day_stats])
            #     ec_sum = float(DimensionMonthlyStat.objects.get(dimension_attribute_id=target_id, created_at__year=year, created_at__month=month).avg)
            #
            #     day = 1
            #     while day <= month_day:
            #         created_at = datetime.datetime(year=year, month=month, day=day, hour=23, minute=59, second=59, microsecond=0)
            #         dds = DimensionDailyStat.objects.filter(dimension_attribute_id=target_id, created_at=created_at).last()
            #         if dds is None:
            #             rate = float(ref_day_stats[day - 1]) / ref_sum
            #             value = ec_sum * rate
            #             print(f'day: {day}, rate: {rate}, value: {value}')
            #
            #             DimensionDailyStat.objects.create(
            #                 dimension_id=1,
            #                 dimension_attribute_id=target_id,
            #                 avg=value,
            #                 min=0,
            #                 max=0,
            #                 created_at=created_at
            #             )
            #         else:
            #             rate = float(ref_day_stats[day - 1]) / ref_sum
            #             value = ec_sum * rate
            #             print(f'day: {day}, old: {dds.avg}, new: {value}')
            #             dds.avg = value
            #             dds.save()
            #
            #         day += 1
            #     pass

            year = 2023
            ref_year = 2024
            ref_month = 1
            for month in [1]:

                month_day = calendar.monthrange(year, month)[1]
                ref_month_day = calendar.monthrange(ref_year, month)[1]

                if month_day != ref_month_day:
                    print(f'not equal {month}')
                    # continue

                for target_id in [1, 3, 4, 5, 6]:
                    # ref_day_stats = list(DimensionDailyStat.objects.using('prj73db').filter(
                    #     dimension_attribute_id=2, created_at__year=ref_year, created_at__month=month).values_list('avg', flat=True))
                    # ref_sum = sum([float(stat) for stat in ref_day_stats])
                    # ec_sum = float(DimensionMonthlyStat.objects.get(dimension_attribute_id=target_id, created_at__year=year, created_at__month=month).avg)

                    day = 1
                    while day <= month_day:
                        ref_hour_stats = list(DimensionHourlyStat.objects.using('prj73db').filter(dimension_attribute_id=2,
                                                                                                  created_at__year=ref_year,
                                                                                                  created_at__month=ref_month,
                                                                                                  created_at__day=day).order_by('created_at').values_list('avg', flat=True))

                        ref_sum = sum([float(stat) for stat in ref_hour_stats])
                        ec_sum = float(DimensionDailyStat.objects.get(dimension_attribute_id=target_id, created_at__year=year,
                                                                      created_at__month=month, created_at__day=day).avg)

                        hour = 0
                        day_sum = 0
                        while hour <= 23:

                            created_at = datetime.datetime(year=year, month=month, day=day, hour=hour, minute=59, second=59, microsecond=0)
                            dhs = DimensionHourlyStat.objects.filter(dimension_attribute_id=target_id, created_at=created_at).last()
                            if dhs is None:
                                rate = float(ref_hour_stats[hour]) / ref_sum
                                value = ec_sum * rate
                                day_sum += value
                                print(f'day: {day}, da: {target_id}, hour: {hour}, rate: {rate}, value: {value}')

                                DimensionHourlyStat.objects.create(
                                    dimension_id=1,
                                    dimension_attribute_id=target_id,
                                    avg=value,
                                    min=0,
                                    max=0,
                                    created_at=created_at
                                )
                            else:
                                rate = float(ref_hour_stats[hour]) / ref_sum
                                value = ec_sum * rate
                                day_sum += value
                                print(f'day: {day}, da: {target_id}, hour: {hour}, rate: {rate}, old: {dhs.avg}, new: {value}')
                                dhs.avg = value
                                dhs.save()
                            hour += 1
                        print(f'day: {day_sum}')
                        day += 1
                    pass
            # ====================================================================================water
            # year = 2024
            # ref_year = 2024
            # month = 4
            # ref_month = 4
            #
            # month_day = calendar.monthrange(year, month)[1]
            # ref_month_day = calendar.monthrange(ref_year, ref_month)[1]
            #
            # if month_day != ref_month_day:
            #     print(f'not equal {month}')
            #     # continue
            #
            # for target_id in [2]:
            #     ref_day_stats = []
            #
            #     for day in range(ref_month_day):
            #         day = day + 1
            #         stat = DimensionDailyStat.objects.using('prj47db').filter(
            #         dimension_attribute_id__in=[10,12,13,14,15,18,11], created_at__year=ref_year, created_at__month=ref_month,created_at__day=day).aggregate(value=Sum('avg'))['value']
            #         ref_day_stats.append(stat)
            #
            #     print(ref_day_stats)
            #
            #     if month_day != ref_month_day:
            #         ref_day_stats = ref_day_stats[:-1]
            #
            #     ref_sum = sum([float(stat) for stat in ref_day_stats])
            #     ec_sum = float(DimensionMonthlyStat.objects.get(dimension_attribute_id=target_id, created_at__year=year, created_at__month=month).avg)
            #
            #     day = 1
            #     while day <= month_day:
            #         created_at = datetime.datetime(year=year, month=month, day=day, hour=23, minute=59, second=59, microsecond=0)
            #         dds = DimensionDailyStat.objects.filter(dimension_attribute_id=target_id, created_at=created_at).last()
            #         if dds is None:
            #             rate = float(ref_day_stats[day - 1]) / ref_sum
            #             value = ec_sum * rate
            #             print(f'day: {day}, rate: {rate}, value: {value}')
            #
            #             DimensionDailyStat.objects.create(
            #                 dimension_id=1,
            #                 dimension_attribute_id=target_id,
            #                 avg=value,
            #                 min=0,
            #                 max=0,
            #                 created_at=created_at
            #             )
            #         else:
            #             rate = float(ref_day_stats[day - 1]) / ref_sum
            #             value = ec_sum * rate
            #             print(f'day: {day}, old: {dds.avg}, new: {value}')
            #             dds.avg = value
            #             dds.save()
            #
            #         day += 1
            #     pass

            year = 2024
            ref_year = 2024
            ref_month = 4
            for month in [4]:

                month_day = calendar.monthrange(year, month)[1]
                ref_month_day = calendar.monthrange(ref_year, ref_month)[1]

                if month_day != ref_month_day:
                    print(f'not equal {month}')
                    # continue

                for target_id in [2]:
                    # ref_day_stats = list(DimensionDailyStat.objects.using('prj73db').filter(
                    #     dimension_attribute_id=2, created_at__year=ref_year, created_at__month=month).values_list('avg', flat=True))
                    # ref_sum = sum([float(stat) for stat in ref_day_stats])
                    # ec_sum = float(DimensionMonthlyStat.objects.get(dimension_attribute_id=target_id, created_at__year=year, created_at__month=month).avg)

                    day = 1
                    while day <= month_day:
                        ref_hour_stats = []

                        for hour in range(24):
                            stat = DimensionHourlyStat.objects.using('prj47db').filter(
                                    dimension_attribute_id__in=[10,12,13,14,15,18,11], created_at__year=ref_year, created_at__month=ref_month,
                                created_at__day=day,created_at__hour=hour).aggregate(value=Sum('avg'))['value']

                            ref_hour_stats.append(stat)

                        ref_sum = sum([float(stat) for stat in ref_hour_stats])
                        ec_sum = float(DimensionDailyStat.objects.get(dimension_attribute_id=target_id, created_at__year=year,
                                                                      created_at__month=month, created_at__day=day).avg)

                        hour = 0
                        day_sum = 0
                        while hour <= 23:

                            created_at = datetime.datetime(year=year, month=month, day=day, hour=hour, minute=59, second=59, microsecond=0)
                            dhs = DimensionHourlyStat.objects.filter(dimension_attribute_id=target_id, created_at=created_at).last()
                            if dhs is None:
                                rate = float(ref_hour_stats[hour]) / ref_sum
                                value = ec_sum * rate
                                day_sum += value
                                print(f'day: {day}, da: {target_id}, hour: {hour}, rate: {rate}, value: {value}')

                                DimensionHourlyStat.objects.create(
                                    dimension_id=1,
                                    dimension_attribute_id=target_id,
                                    avg=value,
                                    min=0,
                                    max=0,
                                    created_at=created_at
                                )
                            else:
                                rate = float(ref_hour_stats[hour]) / ref_sum
                                value = ec_sum * rate
                                day_sum += value
                                print(f'day: {day}, da: {target_id}, hour: {hour}, rate: {rate}, old: {dhs.avg}, new: {value}')
                                dhs.avg = value
                                dhs.save()
                            hour += 1
                        print(f'day: {day_sum}')
                        day += 1
                    pass
