# Generated by Django 3.2.8 on 2023-03-27 17:06

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('linkage', '0007_linkagerule_group'),
    ]

    operations = [
        migrations.AddField(
            model_name='linkagevar',
            name='data_type',
            field=models.IntegerField(default=None, null=True),
        ),
        migrations.AddField(
            model_name='linkagevar',
            name='in_crement',
            field=models.CharField(max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='linkagevar',
            name='max_value',
            field=models.CharField(max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='linkagevar',
            name='min_value',
            field=models.CharField(max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='linkagevar',
            name='options',
            field=models.CharField(max_length=255, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name='linkagevar',
            name='pre_cision',
            field=models.Char<PERSON>ield(default=None, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='linkagevar',
            name='set_by_user',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='linkagevar',
            name='unit',
            field=models.CharField(max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='linkagevar',
            name='value',
            field=models.CharField(default=None, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='linkagevar',
            name='var_name',
            field=models.CharField(default='', max_length=255),
        ),
        migrations.AddField(
            model_name='linkagevar',
            name='var_type',
            field=models.IntegerField(default=10),
        ),
        migrations.AlterField(
            model_name='linkagevar',
            name='mac',
            field=models.CharField(max_length=255, null=True),
        ),
    ]
