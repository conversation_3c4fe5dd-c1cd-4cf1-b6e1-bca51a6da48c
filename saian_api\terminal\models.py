import datetime
import json
import logging
import operator
import pickle
import re
from copy import deepcopy
from functools import reduce

from django.core import exceptions
from django.db import models
# Create your models here.
from django.db.models import Q, Case, When, Value, BooleanField
from django.db.models.base import ModelState

from saian_api.devdefine.models import DevicePrototype, AttributePrototype
from saian_api.utils.db.Redis import RedisHelper

class TerminalManager(models.Manager):

    def bulk_update_by_device(self, device, key, value, db=None):
        if db is None:
            terminals = self.filter(device=device).exclude(prefix='Meter')
            if isinstance(value, bool):
                terminals = terminals.filter(**{key: not value})
            for terminal in terminals:
                setattr(terminal, key, value)
            self.bulk_update(terminals, [key])
        else:
            # 由定时任务调用时需要指定数据库
            terminals = self.using(db).filter(device=device).exclude(prefix='Meter')
            if isinstance(value, bool):
                terminals = terminals.using(db).filter(**{key: not value})
            for terminal in terminals:
                setattr(terminal, key, value)
            self.using(db).bulk_update(terminals, [key])

    def create_by_device(self, device):
        from saian_api.device.models import DeviceAttribute

        dp = DevicePrototype.objects.filter(pk=device.device_prototype_id).last()
        if dp is not None:
            aps = AttributePrototype.objects.filter(device_prototype=dp)
            terminal_type = self.get_terminal_type(dp)
            terminal_label = self.get_terminal_label(dp)

            # 如果有子设备并且不是冷源，并且不是末端，则创建子设备终端
            ap_qty = aps.filter(identifier__endswith='Qty')

            if ap_qty.exists() and terminal_type != 40 and terminal_type != 10:
                for child_ap_qty in ap_qty:
                    # 子设备数目
                    # dp_qty = DeviceAttribute.objects.filter(device=device, attribute_prototype_id=child_ap_qty.id)
                    dp_qty = DeviceAttribute.objects.query_object_by_ap(device, child_ap_qty)
                    prefix = dp.prefix
                    child_dp = None

                    if dp_qty:
                        s_count = int(dp_qty.value) if dp_qty.value else 0
                        qty_idf = child_ap_qty.identifier
                        prefix = qty_idf[:qty_idf.index('Qty')]
                        if prefix.endswith('_'):
                            prefix = prefix[:prefix.index('_')]
                        child_dps = DevicePrototype.objects.filter(prefix=prefix, parent_id=dp.id)
                        if child_dps.exists():
                            child_dp = child_dps.first()
                    else:
                        s_count = 0
                    for i in range(1, s_count + 1):
                        if not self.filter(device=device, device_prototype=child_dp, prefix=prefix, idx=i).exists():
                            # 获得子设备昵称
                            nick_name = self.get_sub_device_nickname(device, i)

                            self.create(
                                device=device,
                                device_prototype=child_dp if child_dp is not None else dp,
                                idx=i,
                                prefix=prefix,
                                terminal_type=terminal_type,
                                terminal_label=terminal_label,
                                nick_name=nick_name,
                                online=device.online,
                                sw_on=device.sw_on
                            )
                # 三集一体风柜风阀控制器 本身也创建终端
                if dp.uni_name == '三集一体风柜风阀控制器':
                    self_terminal = self.filter(device=device, idx__isnull=True, prefix__isnull=True).last()
                    if self_terminal is None:
                        # 创建设备终端
                        self.create(
                            device=device,
                            device_prototype=dp,
                            terminal_type=terminal_type,
                            terminal_label=terminal_label,
                            nick_name=device.nick_name,
                            online=device.online,
                            sw_on=device.sw_on,
                            in_fault=device.in_fault,
                            in_alarm=device.in_alarm
                        )
                    else:
                        # 更新设备对应终端的昵称
                        if self_terminal.nick_name != device.nick_name:
                            self_terminal.nick_name = device.nick_name
                            self_terminal.save()
            else:
                # 冷源子设备终端。除了冷源电表，冷源子设备终端默认隐藏
                if terminal_type == 40:
                    # 板换系统
                    if dp.uni_name == '板换系统':
                        prefix_list = ['CPump']
                    # 处理冷源子设备
                    else:
                        s_dps = DevicePrototype.objects.filter(parent=dp, prefix__isnull=False)
                        prefix_list = [dp.prefix for dp in s_dps]

                    for prefix in prefix_list:
                        s_qty = AttributePrototype.objects.filter(identifier=f'{prefix}Qty', device_prototype=dp)
                        if s_qty.exists():
                            s_qty = s_qty.last()
                            # da = DeviceAttribute.objects.filter(attribute_prototype_id=s_qty.id, device=device)
                            da = DeviceAttribute.objects.query_object_by_ap(device, s_qty)
                            if da:
                                s_count = int(da.value) if da.value else 0
                            else:
                                s_count = 0

                            child_dp = DevicePrototype.objects.filter(parent=dp, prefix=prefix).last()

                            for i in range(1, s_count + 1):
                                if not Terminal.objects.filter(device=device, device_prototype=dp if child_dp is None else child_dp, idx=i,
                                                               prefix=prefix).exists():
                                    # 获取昵称
                                    nick_name = self.get_sub_device_nickname(device, i, prefix)
                                    # 冷源终端类型字符串
                                    sub_terminal_label = dp.uni_name if child_dp is None else child_dp.uni_name

                                    self.create(
                                        device=device,
                                        device_prototype=dp if child_dp is None else child_dp,
                                        idx=i,
                                        prefix=prefix,
                                        terminal_type=20 if 'Meter' in prefix else terminal_type,
                                        terminal_label=sub_terminal_label,
                                        nick_name=nick_name,
                                        sw_on=True if 'Meter' in prefix else device.sw_on,
                                        online=device.online,
                                        show_en=('Meter' in prefix)
                                    )

                # 创建设备终端
                self_terminal = self.filter(device=device, idx__isnull=True, prefix__isnull=True).last()
                if self_terminal is None:
                    # 创建设备终端
                    self.create(
                        device=device,
                        device_prototype=dp,
                        terminal_type=terminal_type,
                        terminal_label=terminal_label,
                        nick_name=device.nick_name,
                        online=device.online,
                        sw_on=device.sw_on,
                        in_fault=device.in_fault,
                        in_alarm=device.in_alarm
                    )
                else:
                    # 更新设备对应终端的昵称
                    if self_terminal.nick_name != device.nick_name:
                        self_terminal.nick_name = device.nick_name
                        self_terminal.save()

    @classmethod
    def get_terminal_type(cls, dp: DevicePrototype):
        # 判断冷源
        device_type = dp.device_type
        if device_type.parent_id == 1:
            return 40

        # 判断末端
        aps = list(AttributePrototype.objects.filter(device_prototype=dp))
        is_terminal = len(list(filter(lambda x: TerminalAttribute.objects.get_use_for(x) == 10, aps))) > 0

        if is_terminal:
            return 10

        # 判断仪表
        is_meter = len(list(filter(lambda x: 'Meter' in x.identifier, aps))) > 0
        if is_meter:
            return 20

        # 判断水阀
        if '阀' in dp.uni_name and dp.parent is None:
            return 30

        return 50

    @classmethod
    def get_terminal_label(cls, dp):
        if '温控器' in dp.uni_name:
            return '温控器'
        if '分体空调' in dp.uni_name:
            return '分体空调'
        if '智能插座' in dp.uni_name:
            return '智能插座'
        if '烟感' in dp.uni_name:
            return '烟感'
        if '风柜' in dp.uni_name:
            return '风柜'
        if '精密空调' in dp.uni_name:
            return '精密空调'
        if '新风机' in dp.uni_name:
            return '新风机'
        if 'VRV' in dp.uni_name:
            return 'VRV'
        if '恒温恒湿空调' in dp.uni_name:
            return '恒温恒湿空调'
        if '电表' in dp.uni_name:
            return '电表'
        if '水表' in dp.uni_name:
            return '水表'
        if '冷量表' in dp.uni_name:
            return '冷量表'
        if '流量计' in dp.uni_name:
            return '流量计'
        # 一些 dp.uni_name 只有型号的，通过 aps 判断
        meter_queryset = AttributePrototype.objects.filter(identifier__contains='Meter', device_prototype_id=dp.id)
        if meter_queryset.filter(identifier__contains='Power').exists():
            return '电表'
        if meter_queryset.filter(identifier__contains='Water').exists():
            return '水表'
        if meter_queryset.filter(identifier__contains='Cool').exists():
            return '冷量表'
        return dp.uni_name

    # 子设备默认昵称
    def default_sub_device_nickname(self, device, idx, prefix):
        from saian_api.device.domain_models.cs import CommCs

        if prefix:
            prefix_name = CommCs.sub_device_prefix(device).get(prefix, '')
            nick_name = f'{idx}号{prefix_name}' if prefix_name else f'{idx}_{prefix}#{device.nick_name}'
        else:
            nick_name = f'{idx}#{device.nick_name}'

        return nick_name

    # 获取子设备昵称
    def get_sub_device_nickname(self, device, idx, prefix=None):
        from saian_api.device.models import DeviceAttribute

        nick_name = self.default_sub_device_nickname(device, idx, prefix)

        dp = device.device_prototype
        aps = AttributePrototype.objects.filter(device_prototype=dp, identifier__contains='NickName')
        nickname_ap = aps.filter(Q(identifier__endswith=f'_{idx}') | Q(identifier__contains=f'_{idx}_'))

        if dp.prefix and dp.prefix != '0':
            nickname_ap = nickname_ap.filter(identifier__startswith=f'{dp.prefix}_')
        elif prefix is not None:
            nickname_ap = nickname_ap.filter(identifier__startswith=f'{prefix}_')
        if nickname_ap.exists():
            # da = DeviceAttribute.objects.filter(device=device, attribute_prototype_id=nickname_ap.first().id)
            da = DeviceAttribute.objects.query_object_by_ap(device, nickname_ap.first())
            if da:
                nick_name = da.value
                if not nick_name or nick_name == '0':
                    nick_name = self.default_sub_device_nickname(device, idx, prefix)

        return nick_name

    # 当报警故障发生时，更新终端的状态
    def update_by_issue_add(self, issue_type, device, glossary):
        from saian_api.device.domain_models.cs import CommCs
        # cs_sub_device_prefix = {
        #     'Host': '主机',
        #     'FPump': '冷冻泵',
        #     'CPump': '冷却泵',
        #     'FValve': '冷冻阀',
        #     'CValve': '冷却阀',
        #     'CTower': '冷却塔',
        #     'CYPump': '循环泵',
        #     'CYValve': '循环阀',
        #     'HPump': '供暖泵',
        #     'CHValve': '冷暖阀',
        #     'HRWPump': '热回水泵',
        #     'HRCYPump': '热回收泵',
        #     'SecRWValve': '回水阀',
        #     'BPValve': '旁通阀',
        #     'CtrlValve': '调节阀',
        #     'EMeter': '电表'
        # }
        cs_sub_device_prefix = CommCs.sub_device_prefix(device)
        issue_key = 'in_alarm' if issue_type == 20 else 'in_fault'
        # 设备对应的终端
        terminals = Terminal.objects.filter(device=device)
        if terminals.exists():
            if terminals.count() == 1:
                terminal = terminals.first()
                # 终端数量只有一个时
                # 如果这个唯一的终端并非子设备终端，则可以直接更新
                # 否则要根据 glossary 判断发生故障报警的是否这个终端对应的子设备
                if glossary.startswith(terminal.nick_name) or terminal.idx is None:
                    setattr(terminal, issue_key, True)
                    terminal.save()
                return
            else:
                # 有多个终端，要区分子设备终端的issue
                if glossary:
                    idx = re.findall(r'\d+', glossary)
                    if (len(idx) == 1 and glossary[0].isdigit()) or (
                            (glossary.startswith('仪表') or glossary.startswith('内机')) and glossary[2].isdigit()) or (
                            glossary.startswith('室内机') and glossary[3].isdigit()):
                        # 以数字开头，（风柜，冷源）, 或者"仪表"后接idx(三项电表)
                        idx = idx[0]
                        terminal_idx = terminals.filter(idx=idx)
                        if terminal_idx.exists():
                            if terminal_idx.first().terminal_type == 40:
                                for terminal in terminal_idx:
                                    if cs_sub_device_prefix[terminal.prefix] in glossary:
                                        setattr(terminal, issue_key, True)
                                        terminal.save()
                            else:
                                terminal = terminal_idx.first()
                                setattr(terminal, issue_key, True)
                                terminal.save()
                                return
                    else:
                        # 如果不是数字开头，则检查是否是子设备昵称开头（冷源，仪表）
                        for terminal in terminals:
                            if terminal.nick_name and glossary.startswith(terminal.nick_name):
                                setattr(terminal, issue_key, True)
                                terminal.save()
                                return

                        # 如果是冷源，则更新冷源本身的终端(只有冷源设备才有多个终端，并且冷源本身对应的终端idx为None)
                        if terminals.filter(idx=None).exists():
                            terminal = terminals.filter(idx=None).first()
                            setattr(terminal, issue_key, True)
                            terminal.save()
                            return

                        # 如果上面条件都不满足，则全部更新
                        logging.warning(f"终端更新issue状态时，找不到对应的终端：glossary-{glossary}, device_id-{device.id}")
                        for terminal in terminals:
                            setattr(terminal, issue_key, True)
                        Terminal.objects.bulk_update(terminals, [issue_key])

    # 当报警故障恢复时，更新终端的状态
    def update_by_issue_recovery(self, issue_type, device, glossary):
        issue_key = 'in_alarm' if issue_type == 20 else 'in_fault'
        # 设备对应的终端
        terminals = Terminal.objects.filter(device=device)
        # 只需要处理子设备终端
        if terminals.count() >= 1:
            from saian_api.issue.models import DeviceIssue

            if glossary:
                idx = re.findall(r'\d+', glossary)
                if (len(idx) == 1 and glossary[0].isdigit()) or (
                        (glossary.startswith('仪表') or glossary.startswith('内机')) and glossary[2].isdigit()) or (
                        glossary.startswith('室内机') and glossary[3].isdigit()):
                    # 以数字开头，（风柜，冷源）
                    idx = idx[0]
                    terminal = terminals.filter(idx=idx)
                    if terminal.exists():
                        terminal = terminal.first()
                        # 查看是否还存在这个idx的未解决issue
                        other_issues = DeviceIssue.objects.filter(device=device, issue_type=issue_type, is_solved=False)
                        other_issues = other_issues.filter(Q(display_name__startswith=idx) | Q(display_name__startswith='内机') |
                                                           Q(display_name__startswith='室内机') | Q(display_name__startswith='仪表'))
                        if other_issues.exists():
                            exist = False
                            # 还要区分idx和idx开始的数字（例如：1 和 10，11）
                            for issue in other_issues:
                                issue_idx = re.findall(r'\d+', issue.display_name)[0]
                                if issue_idx == idx:
                                    exist = True
                            if not exist:
                                setattr(terminal, issue_key, False)
                                terminal.save()
                        else:
                            setattr(terminal, issue_key, False)
                            terminal.save()

                else:
                    for terminal in terminals:
                        if glossary.startswith(terminal.nick_name):
                            other_issues = DeviceIssue.objects.filter(device=device, issue_type=issue_type, is_solved=False,
                                                                      display_name__startswith=terminal.nick_name)
                            if not other_issues.exists():
                                setattr(terminal, issue_key, False)
                                terminal.save()
                                return

                    other_issues = DeviceIssue.objects.filter(device=device, issue_type=issue_type, is_solved=False)

                    if not other_issues.exists():
                        # 如果是冷源，则只更新冷源本身终端的状态
                        if terminals.filter(idx=None).exists():
                            terminal = terminals.filter(idx=None).first()
                            setattr(terminal, issue_key, False)
                            terminal.save()
                        else:
                            # 否则更新该设备下的所有终端
                            for terminal in terminals:
                                setattr(terminal, issue_key, False)
                            Terminal.objects.bulk_update(terminals, [issue_key])

    def get_terminal_of_device(self, device):
        """获取设备的本身的终端"""
        device_terminals = self.filter(device=device, idx=None)
        if device_terminals.exists():
            return device_terminals.first()
        return None

    def get_sub_terminal_of_device(self, device, idx, prefix=None):
        """获取设备的某个子设备终端"""
        sub_terminals = self.filter(device=device, idx=idx)
        if prefix:
            sub_terminals = sub_terminals.filter(prefix=prefix)

        if sub_terminals.exists():
            return sub_terminals.first()
        return None

    def handle_qty_change(self, device, ap, prev_count, cur_count):
        """Qty 更改时，终端的处理"""
        # prefix
        prefix = ap.identifier[:ap.identifier.index('Qty')]
        if prefix.endswith('_'):
            prefix = prefix[:prefix.index('_')]
        # device_prototype
        dp = device.device_prototype
        s_dp = None
        s_dps = DevicePrototype.objects.filter(parent=ap.device_prototype, prefix=prefix)
        if s_dps.exists():
            s_dp = s_dps.first()

        if s_dp is not None:
            terminal_type = self.get_terminal_type(s_dp)
            terminal_label = self.get_terminal_label(s_dp) if terminal_type == 10 else None
        else:
            terminal_type = self.get_terminal_type(dp)
            terminal_label = self.get_terminal_label(dp) if terminal_type == 10 else None

        show_en = True
        if self.get_terminal_type(dp) == 40 and prefix is not None and 'Meter' not in prefix:
            show_en = False

        # 创建终端
        for i in range(1, cur_count + 1):
            if not Terminal.objects.filter(device=device, idx=i, prefix=prefix, device_prototype=s_dp if s_dp else dp).exists():
                nick_name = Terminal.objects.get_sub_device_nickname(device, i, prefix=prefix)
                Terminal.objects.create(
                    device=device,
                    device_prototype=s_dp if s_dp is not None else dp,
                    idx=i,
                    prefix=prefix,
                    terminal_type=terminal_type,
                    terminal_label=terminal_label,
                    nick_name=nick_name,
                    online=device.online,
                    sw_on=True if 'Meter' in prefix else device.sw_on,
                    show_en=show_en
                )
        # 创建ta
        TerminalAttribute.objects.create_by_device(device)
        # 将对应的终端隐藏或显示
        if prev_count > cur_count:
            terminals = Terminal.objects.filter(device=device, idx__gt=cur_count, show_en=True)
            for terminal in terminals:
                terminal.show_en = False
            Terminal.objects.bulk_update(terminals, ['show_en'])
        if prev_count < cur_count:
            terminals = Terminal.objects.filter(device=device, idx__range=[prev_count + 1, cur_count], show_en=False)
            for terminal in terminals:
                terminal.show_en = True
            Terminal.objects.bulk_update(terminals, ['show_en'])

    @classmethod
    def append_dashboard_attres(cls, terminals, serializer, device_prototypes: list[DevicePrototype] = None):
        from saian_api.device.models import Device

        """
        接受一个终端列表，返回终端的字典列表，同时计算每个终端的 dashboard_attres
        """

        from saian_api.device.models import DeviceAttribute

        if device_prototypes is None:
            dp_ids = [t.device_prototype_id for t in terminals]
            device_prototypes = list(DevicePrototype.objects.filter(id__in=dp_ids))

        dp_dashboard_attrs = {}
        ap_q = Q()

        for dp in device_prototypes:
            if not dp.dashboard_attres:
                continue
            config = json.loads(dp.dashboard_attres)
            dp_dashboard_attrs[dp.id] = config
            idfs = []
            for key, value in config.items():
                if isinstance(value, list):
                    idfs += [item['idf'] for item in value]

            ap_q |= Q(device_prototype_id=dp.id, identifier__in=idfs)
            if dp.parent_id:
                ap_q |= Q(device_prototype_id=dp.parent_id, identifier__in=idfs)

        target_aps = list(AttributePrototype.objects.filter(ap_q))
        ap_ids = [ap.id for ap in target_aps]
        # target_das = list(DeviceAttribute.objects.filter(attribute_prototype_id__in=ap_ids, device_id__in=[t.device_id for t in terminals]))
        target_das = []
        for device_id in set([t.device_id for t in terminals]):
            device = Device.objects.get(pk=device_id)
            target_das += DeviceAttribute.objects.query_object_list(device, ap_ids)

        results = []
        for terminal in terminals:
            config = dp_dashboard_attrs.get(terminal.device_prototype_id, None)
            if config is None:
                continue
            if terminal.idx:
                attr_key = f'{terminal.prefix}_{terminal.idx}'
            else:
                attr_key = 'default'

            attres = config.get(attr_key, None)
            dashboard_attres = deepcopy(attres)
            if attres is not None:
                for idx, attr, in enumerate(attres):
                    da = None
                    ap = None
                    name = attr['name'] if 'name' in attres else None
                    aps = list(filter(lambda x: x.identifier == attr['idf'], target_aps))

                    if len(aps):
                        ap = aps[0]
                        das = list(filter(lambda x: x.attribute_prototype_id == ap.id and x.device_id == terminal.device_id, target_das))
                        if len(das):
                            da = das[0]
                    if da is None:
                        # da = DeviceAttribute.objects.get_by_idf(terminal.device, attr['idf'])
                        da = DeviceAttribute.objects.query_object_by_idf(terminal.device, attr['idf'])
                    if da is not None:
                        DevicePrototype.objects.ap_values(dashboard_attres[idx], da, ap=ap)
                        if name is not None:
                            dashboard_attres[idx]['name'] = name

            data = serializer(terminal).data
            data['dashboard_attres'] = dashboard_attres
            results.append(data)

        return results


class Terminal(models.Model):
    # 设备
    device = models.ForeignKey('device.Device', on_delete=models.CASCADE)
    # 设备类型
    device_prototype = models.ForeignKey(DevicePrototype, on_delete=models.CASCADE)
    # 子设备序号
    idx = models.IntegerField(null=True, blank=True)
    # 子设备前缀
    prefix = models.CharField(null=True, blank=True, max_length=255)
    # 是否处于故障
    in_fault = models.BooleanField(default=False)
    # 是否处于报警
    in_alarm = models.BooleanField(default=False)
    # 是否在线
    online = models.BooleanField(default=False)
    # 开关状态
    sw_on = models.BooleanField(default=False)
    # 昵称
    nick_name = models.CharField(max_length=255, blank=True, null=True, db_index=True)
    # 终端类型: 10-末端, 20-仪表, 30-水阀, 40-冷源, 50-其他
    terminal_type = models.IntegerField(null=True, blank=True)
    # 类型字符串。某一终端类型下的更细致分类，例如末端有: 温控器 分体空调 风柜 精密空调 新风机 VRV 恒温恒湿空调, 仪表有: 电表 水表 冷量表
    terminal_label = models.CharField(null=True, blank=True, max_length=255)
    # 是否显示该终端
    show_en = models.BooleanField(default=True)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "terminals"
        ordering = ['-created_at']

    objects = TerminalManager()

    @property
    def terminal_temp(self):
        if self.terminal_type != 10:
            return 0
        tas = TerminalAttribute.objects.filter(terminal=self, use_for=10)
        if tas.exists():
            temp = tas.last().value
            return temp if temp else 0
        else:
            return 0

    @property
    def status(self):
        # 状态 10 - 初始化，20 - 上线，30-下线，40-故障
        if not self.online:
            return 30
        if self.in_fault:
            return 40
        return 20

    def parse_attres(self, attres):
        from saian_api.device.models import DeviceAttribute
        idf_list = [attr['idf'] for attr in attres]
        target_aps = list(AttributePrototype.objects.filter(device_prototype_id=self.device_prototype_id, identifier__in=idf_list))
        ap_ids = [ap.id for ap in target_aps]
        # target_das = list(DeviceAttribute.objects.filter(attribute_prototype_id__in=ap_ids, device_id=self.device_id))
        target_das = DeviceAttribute.objects.query_object_list(self.device, ap_ids)

        for idx, attr in enumerate(attres):
            da = None
            ap = None
            name = attr['name'] if 'name' in attr else None
            aps = list(filter(lambda x: x.identifier == attr['idf'], target_aps))
            if len(aps):
                ap = aps[0]
                das = list(filter(lambda x: x.attribute_prototype_id == ap.id, target_das))
                if len(das):
                    da = das[0]
            if da is None:
                # da = DeviceAttribute.objects.get_by_idf(self.device, attr['idf'])
                da = DeviceAttribute.objects.query_object_by_idf(self.device, attr['idf'])
            if da is not None:
                DevicePrototype.objects.ap_values(attres[idx], da, ap=ap)
                if name is not None:
                    attres[idx]['name'] = name

        return attres

    def dashboard_attres(self):
        dp = self.device_prototype
        if dp.dashboard_attres:
            dashboard_attres = json.loads(dp.dashboard_attres)
            try:
                if self.idx:
                    # idx 存在说明是子设备类型，子设备类型是没有 default 的，可以直接 get attr_key
                    attr_key = f'{self.prefix}_{self.idx}'
                    if dashboard_attres.get(attr_key, None) is not None:
                        return self.parse_attres(dashboard_attres[attr_key])
                    return None
                else:
                    dashboard_attres = json.loads(dp.dashboard_attres)
                    if dashboard_attres.get('default', None) is not None:
                        return self.parse_attres(dashboard_attres['default'])
                    else:
                        return None
            except exceptions.FieldError:
                logging.error('Dashboard attributes invalid!')
        else:
            return None

    def key_attributes(self, project_id=None):
        """关键属性列表"""
        # key_tas = list(self.terminalattribute_set.all())
        ap_ids = TerminalAttribute.objects.filter(terminal=self).values_list('attribute_prototype_id', flat=True)
        key_tas = TerminalAttribute.objects.query_object_list(self, ap_ids, project_id)
        key_aps = list(AttributePrototype.objects.filter(id__in=[ta.attribute_prototype_id for ta in key_tas]))
        ka_fields = []

        for ap in key_aps:
            tas = list(filter(lambda x: x.attribute_prototype_id == ap.id, key_tas))
            if len(tas):
                ta = tas[0]
            else:
                ta = TerminalAttribute.objects.get_by_idf(self, ap.identifier)

            if ta is not None:
                ka_fields.append({
                    'id': ap.id,
                    'name': ap.label if ap.label is not None else ap.name,
                    'unit': ap.unit,
                    'identifier': ap.identifier,
                    'value': ta.value if ap.identifier != 'RSSI' else int(ta.value, 16) if ta.value is not None and ta.value.isdigit() else 0,
                })

        return ka_fields

class CategoryManager(models.Manager):
    def get_children_id(self, pid):
        children = list(self.filter(parent_id=pid).values_list('id', flat=True))
        if len(children) == 0:
            return []

        ids = children
        for child in children:
            ids += self.get_children_id(child)

        return ids


class Category(models.Model):
    # 类别名字
    name = models.CharField(max_length=255)
    # 排序字段
    seq = models.IntegerField(default=0, null=True, blank=True)
    # 类别下的终端
    terminals = models.ManyToManyField(Terminal, through='CategoryTerminal')
    # 访问端。10-小程序(默认), 20-web
    acc_end = models.IntegerField(default=10)
    # 父级分类
    parent = models.ForeignKey('self', blank=True, null=True, related_name='children', db_column='pid', on_delete=models.CASCADE)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "category"
        ordering = ['seq', '-created_at']

    objects = CategoryManager()

class CategoryTerminal(models.Model):
    # 终端
    terminal = models.ForeignKey(Terminal, on_delete=models.CASCADE)
    # 类别
    category = models.ForeignKey(Category, on_delete=models.CASCADE)

    class Meta:
        db_table = "category_terminals"


class TerminalAttributeManager(models.Manager):
    @classmethod
    def save_to_redis(cls, project_id, ta):
        """更新 TerminalAttribute 对象到缓存"""
        ta.save(update_fields=['value', 'updated_at'])
        RedisHelper.set_value(project_id, f'terminal_attribute:{ta.terminal_id}_{ta.attribute_prototype_id}', ta, False)

    @classmethod
    def query_mult_object_list(cls, terminals, ap_ids, project_id):
        """查询多个终端的多个参数。如果终端不止一个，返回的结果可能包含额外的终端属性，需要对结果进行过滤。"""
        names = []

        for terminal in terminals:
            names += [f'terminal_attribute:{terminal.id}_{ap_id}' for ap_id in ap_ids]

        results = RedisHelper.get_multi_value(project_id, names, False)

        tas = []
        not_cache_tas = []

        for idx, result in enumerate(results):
            if result:
                tas.append(result)

        for terminal in terminals:
            terminal_attrs = [ta for ta in tas if ta.terminal_id == terminal.id]
            terminal_ids = [ta.attribute_prototype_id for ta in terminal_attrs]
            not_cache_ap_ids = set(ap_ids) - set(terminal_ids)
            not_cache_tas += list(TerminalAttribute.objects.filter(terminal_id=terminal.id, attribute_prototype_id__in=not_cache_ap_ids))

        if len(not_cache_tas):
            values = {}
            for ta in not_cache_tas:
                name = f'terminal_attribute:{ta.terminal_id}_{ta.attribute_prototype_id}'
                values[name] = ta
            RedisHelper.set_multi_value(project_id, values, False)

        return tas + not_cache_tas

    @classmethod
    def query_object_list(cls, device, ap_ids, project_id=None):
        names = []

        if isinstance(device, Terminal):
            terminal = device
            if project_id is None:
                project_id = terminal.device.project_id
            names = [f'terminal_attribute:{terminal.id}_{ap_id}' for ap_id in ap_ids]
        else:
            project_id = device.project_id
            for terminal in Terminal.objects.filter(device_id=device.id):
                names += [f'terminal_attribute:{terminal.id}_{ap_id}' for ap_id in ap_ids]

        results = RedisHelper.get_multi_value(project_id, names, False)

        not_cache_ap_ids = []
        tas = []

        for idx, result in enumerate(results):
            if result:
                tas.append(result)

        not_cache_ap_ids = set(ap_ids) - set([t.attribute_prototype_id for t in tas])

        if isinstance(device, Terminal):
            not_cache_tas = list(TerminalAttribute.objects.filter(terminal_id=device.id, attribute_prototype_id__in=not_cache_ap_ids))
        else:
            not_cache_tas = list(TerminalAttribute.objects.filter(terminal__device_id=device.id, attribute_prototype_id__in=not_cache_ap_ids))

        if len(not_cache_tas):
            values = {}
            for ta in not_cache_tas:
                name = f'terminal_attribute:{ta.terminal_id}_{ta.attribute_prototype_id}'
                values[name] = ta
            RedisHelper.set_multi_value(project_id, values, False)

        return tas + not_cache_tas

    @classmethod
    def query_object_by_idf(cls, terminal, idf, device=None):
        result = None

        if device is None:
            device = terminal.device

        ap = AttributePrototype.objects.get_by_idf(device, idf)
        if ap is not None:
            result = cls.query_object_by_ap(terminal, ap, device.project_id)

        return result

    @classmethod
    def query_object_by_ap(cls, terminal, ap, project_id=None):
        name = f'terminal_attribute:{terminal.id}_{ap.id}'

        if project_id is None:
            project_id = terminal.device.project_id

        result = RedisHelper.get_value(project_id, name, False)
        if result is None:
            # ap = AttributePrototype.objects.get(pk=ap.id)
            result = cls.get_by_ap(terminal, ap)

            if result is not None:
                RedisHelper.set_value(project_id, name, result, False)

        return result

    @classmethod
    def query_object_by_ta(cls, ta, project_id=None):
        name = f'terminal_attribute:{ta.terminal_id}_{ta.attribute_prototype_id}'

        if project_id is None:
            project_id = ta.terminal.device.project_id

        result = RedisHelper.get_value(project_id, name, False)

        if result is None:
            return ta

        return result


    @classmethod
    def get_by_ap(cls, terminal, ap: AttributePrototype):
        try:
            ta = TerminalAttribute.objects.get(terminal=terminal, attribute_prototype_id=ap.id)
        except TerminalAttribute.DoesNotExist:
            logging.info(f'TerminalAttribute 不存在，重新创建：terminal={terminal.id}-{terminal.nick_name}, ap={ap.id}-{ap.name}')
            ta = TerminalAttribute.objects.create(terminal=terminal,
                                                  attribute_prototype_id=ap.id,
                                                  identifier=ap.identifier,
                                                  value=ap.default_value,
                                                  is_set_temp=cls.is_set_temp_ap(ap),
                                                  use_for=cls.get_use_for(ap))
        except TerminalAttribute.MultipleObjectsReturned:
            tas = TerminalAttribute.objects.filter(terminal=terminal, attribute_prototype_id=ap.id)
            logging.error(f'TerminalAttribute 存在{tas.count()}个, '
                          f'terminal={terminal.id}-{terminal.nick_name},'
                          f' ap={ap.id}')
            ta = tas.last()
            # TODO 不删除dimension_attribute 中引用的终端
            if '_Cons' not in ap.identifier:
                tas.exclude(id=ta.id).delete()
                logging.info(f"删除重复的 TerminalAttribute 中旧的记录")

        return ta

    def create_by_device(self, device, ap_id=None):
        from saian_api.device.models import DeviceAttribute

        terminals = Terminal.objects.filter(device=device)
        dp = device.device_prototype

        for terminal in terminals:
            if ap_id is None:
                aps = AttributePrototype.objects.filter(device_prototype=dp)
            else:
                aps = AttributePrototype.objects.filter(pk=ap_id)
            if terminal.idx and terminal.idx > 0:
                # 如果是子设备终端，则把子设备的 ap 找出来
                aps = aps.filter(Q(identifier__contains=f'_{terminal.idx}_') | Q(identifier__endswith=f'_{terminal.idx}'))
                if terminal.prefix and terminal.prefix != '0':
                    # aps = aps.filter(identifier__contains=terminal.prefix)
                    aps = aps.filter(identifier__startswith=f'{terminal.prefix}_')
            else:
                # 否则，把属于子设备的 ap 过滤掉
                s_terminals = terminals.filter(idx__isnull=False)
                if s_terminals.count() > 0:
                    prefix_list = set([t.prefix for t in s_terminals])
                    prefix_list = list(filter(lambda x: x != '0', prefix_list))
                    aps = aps.exclude(reduce(operator.or_, (Q(identifier__contains=f'{prefix}_') for prefix in prefix_list)))

            for ap in aps:
                if not self.filter(terminal=terminal, attribute_prototype_id=ap.id).exists():
                    # da = DeviceAttribute.objects.filter(device=device, attribute_prototype_id=ap.id)
                    da = DeviceAttribute.objects.query_object_by_ap(device, ap)
                    value = da.value if da else ap.default_value
                    self.create(
                        terminal=terminal,
                        attribute_prototype_id=ap.id,
                        identifier=ap.identifier,
                        value=value,
                        is_set_temp=self.is_set_temp_ap(ap),
                        use_for=self.get_use_for(ap)
                    )

    @staticmethod
    def is_set_temp_ap(ap):
        return ap.identifier.endswith('SetTemp') or ap.identifier.endswith('TargetTemp') or ap.name == '回风温度设定' or ap.name == '送风温度设定'

    @staticmethod
    def get_use_for(ap: AttributePrototype):
        """属性的用途，10-设定温度，20-设定模式，30-设定风速，40-开关，50-现场温度, 60-仪表用电(水,冷)量, 仪表流量"""
        if not ap.read_only:
            if (ap.identifier.endswith('SetTemp') or
                    ap.identifier.endswith('TargetTemp') or
                    ('TempDiff' in ap.identifier and 'Target' in ap.identifier) or
                    ap.name == '目标温度' or
                    ap.name == '回风温度设定' or
                    ap.name == '送风温度设定'):
                return 10
            elif (ap.identifier == 'RunMode' or ap.identifier.endswith('SysRunMode') or ap.identifier.endswith('RunningMode') or
                  ap.identifier.endswith('SetRunMode') or ap.identifier == 'CoolOrWarmSelect' or ap.identifier.endswith('_RunMode')):
                return 20
            elif ap.identifier.endswith('FanSpeed'):
                return 30
            elif ap.identifier.endswith('SW'):
                return 40
        else:
            if ap.identifier.endswith('LocalTemp') or ap.identifier == 'ReturnAirTemp':
                return 50
            # 电表用电量(XXX_XXX_Cons)
            elif 'Meter_' in ap.identifier and (ap.identifier.endswith('Cons') or '_Cons_' in ap.identifier) or ap.identifier == 'CumColdCons':
                return 60
            # 部分电表用电量(粤南穗电表-kWh, SPM91-Meter_Power)
            elif ap.identifier == 'kWh' or ap.identifier == 'Meter_Power':
                return 60
            # 流量计，累计冷量
            elif 'CumHeat' in ap.identifier:
                return 60
            else:
                return None

    @classmethod
    def get_by_da(cls, da, ap: AttributePrototype):
        from saian_api.utils.tools import get_idx, get_prefix

        ta = None
        identifier = ap.identifier
        idx = get_idx(identifier)
        prefix = get_prefix(identifier)
        if idx is None:
            terminal = Terminal.objects.filter(device_id=da.device_id, idx__isnull=True, prefix__isnull=True).order_by('created_at').last()
        else:
            terminal = Terminal.objects.filter(device_id=da.device_id, idx=idx, prefix=prefix).order_by('created_at').last()

        if terminal is not None:
            tas = TerminalAttribute.objects.filter(terminal_id=terminal.id, attribute_prototype_id=ap.id,
                                                   identifier=identifier).order_by('created_at')
            if tas.count() == 0:
                logging.info(f'TerminalAttribute 不存在，重新创建：terminal={terminal.id}-{terminal.nick_name}, ap={ap.id}-{ap.name}')
                ta = TerminalAttribute.objects.create(terminal=terminal,
                                                      attribute_prototype_id=ap.id,
                                                      identifier=ap.identifier,
                                                      value=da.value,
                                                      is_set_temp=False,
                                                      use_for=TerminalAttribute.objects.get_use_for(ap))
            elif tas.count() != 1:
                logging.error(f'TerminalAttribute 存在{tas.count()}个, '
                              f'terminal={terminal.id}-{terminal.nick_name},'
                              f' ap={da.attribute_prototype_id}')
                ta = tas.order_by('created_at').last()
                if '_Cons' not in ta.identifier:
                    tas.exclude(id=ta.id).delete()
                    logging.info(f"删除重复的 TerminalAttribute 中旧的记录")
            else:
                ta = tas.last()
        return ta

    def update_with_da(self, da, ta=None, ap=None, project_id=None):
        """根据 da 更新对应的 ta，使其同步"""
        if ap is None:
            ap = da.attribute_prototype

        if ta is None:
            # tas = self.filter(terminal__device_id=da.device_id, attribute_prototype_id=da.attribute_prototype_id)
            ta = self.get_by_da(da, ap)

        if ta is not None:
            orig_value = ta.value
            ta.value = da.value

            if project_id is not None:
                ta.updated_at = da.updated_at
                name = f'terminal_attribute:{ta.terminal_id}_{ta.attribute_prototype_id}'
                RedisHelper.set_value(project_id, name, ta, False)

            if orig_value != ta.value:
                TerminalAttribute.objects.filter(id=ta.id).update(value=ta.value, updated_at=datetime.datetime.now())

            terminal = ta.terminal
            # 更新终端昵称或者子设备终端
            if 'NickName' in ap.identifier:
                terminal.nick_name = da.value
                terminal.save()
            # 终端是否显示
            elif ap.identifier.endswith('ShowEN'):
                if not ta.value or ta.value == '0':
                    terminal.show_en = False
                else:
                    terminal.show_en = True
                terminal.save()
            else:
                Terminal.objects.filter(id=terminal.id).update(
                    updated_at=datetime.datetime.now(),
                    online=Case(
                        When(online=False, then=Value(True)),
                        default=Value(True),
                        output_field=BooleanField()
                    )
                )

    def get_by_idf(self, terminal, idf):
        ta = None
        try:
            ta = self.get(terminal=terminal, identifier=idf)

        except TerminalAttribute.DoesNotExist:
            logging.error(f'终端属性不存在：terminal-{terminal.id}-{terminal.nick_name}, identifier: {idf}')
            pass
        except TerminalAttribute.MultipleObjectsReturned:
            ta = self.filter(terminal=terminal, identifier=idf).last()

        return ta


class TerminalAttribute(models.Model):
    # 所属终端
    terminal = models.ForeignKey(Terminal, on_delete=models.CASCADE)
    # 属性类型
    attribute_prototype_id = models.BigIntegerField(db_index=True)
    # 属性标识
    identifier = models.CharField(max_length=255)
    # 属性值
    value = models.CharField(max_length=255, null=True)
    # 是否设置温度。废弃不用
    is_set_temp = models.BooleanField(default=False)
    # 属性的用途，10-设定温度，20-设定模式，30-设定风速，40-开关，50-现场温度, 60-仪表用电(水,冷)量
    use_for = models.IntegerField(null=True, blank=True, default=None)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "terminal_attributes"
        ordering = ['-created_at']

    objects = TerminalAttributeManager()

    def __getstate__(self):
        """自定义对象序列化时的状态"""
        state = self.__dict__.copy()  # 获取对象的所有属性
        result = {}
        for k, v in state.items():
            if k == '_state':
                state_adding = v.adding
                state_db = v.db
                v = {
                    "adding": state_adding,
                    "db": state_db
                }

            if k.endswith('at') and isinstance(v, datetime.datetime):
                v = v.strftime('%Y-%m-%d %H:%M:%S')
            result[k] = v
        return result

    def __setstate__(self, state):
        """自定义反序列化时的状态"""
        for k, v in state.items():
            if k.endswith('at') and isinstance(v, str):
                try:
                    state[k] = datetime.datetime.strptime(v, '%Y-%m-%d %H:%M:%S')
                except ValueError:
                    pass  # 跳过无法转换的字段
            if k == '_state' and isinstance(v, dict):
                state_adding = v.get('adding', False)
                state_db = v.get('db', None)
                model_state = ModelState()
                model_state.adding = state_adding
                model_state.db = state_db
                model_state.fields_cache = {}
                state[k] = model_state
        self.__dict__.update(state)


class TerminalCoolingStats(models.Model):
    # 供冷强度系数，所有末端的实际温度和设定温度之差，求和
    cic = models.CharField(max_length=255)
    # 供冷不均系数，所有末端的实际温度和设定温度之差的绝对值，求和
    cuc = models.CharField(max_length=255, null=True)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "terminal_cooling_stats"
        ordering = ['-created_at']

class TerminalNameplate(models.Model):
    # 终端id
    terminal_id = models.IntegerField(null=True)
    # 铭牌id
    np_id = models.CharField(max_length=255)
    # 铭牌名称
    np_name = models.CharField(max_length=255)
    # 铭牌内容，json格式，如：[{name:信息名称,value:信息值,remark:备注}]
    np_content = models.TextField()
    # 铭牌照片
    np_img = models.CharField(max_length=255, null=True)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'terminal_nameplates'
