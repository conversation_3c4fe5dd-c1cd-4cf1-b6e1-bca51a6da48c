from django.urls import path

from .views import (BuildingViewSet, FloorViewSet, RoomStatViewSet, RoomViewSet, RoomDeviceViewSet, FloorV2ViewSet, RoomTemphumsViewSet, FloorV5View,
                    FloorTerminalView, RoomHourlyTemp, RoomStatsView, FloorDeviceStatView, SimpleRoomView)

building_list = BuildingViewSet.as_view({
    'get': 'list',
    'post': 'create'
})

building_detail = BuildingViewSet.as_view({
    'get': 'retrieve',
    'put': 'partial_update',
    # 'patch': 'partial_update',
    'delete': 'destroy'
})

floor_list = FloorViewSet.as_view({
    'get': 'list',
    'post': 'create'
})

floor_detail = FloorViewSet.as_view({
    'get': 'retrieve',
    'put': 'partial_update',
    # 'patch': 'partial_update',
    'delete': 'destroy'
})

simple_rooms = SimpleRoomView.as_view({
    'get': 'list'
})

room_list = RoomViewSet.as_view({
    'get': 'list',
    'post': 'create'
})

room_detail = RoomViewSet.as_view({
    'get': 'retrieve',
    'put': 'partial_update',
    # 'patch': 'partial_update',
    'delete': 'destroy'
})

room_device_list = RoomDeviceViewSet.as_view({
    'post': 'create'
})

room_device_detail = RoomDeviceViewSet.as_view({
    'delete': 'destroy'
})

room_temphums_list = RoomTemphumsViewSet.as_view({
    'get': 'list'
})

floorv2_list = FloorV2ViewSet.as_view({
    'get': 'list'
})

floorv2_detail = FloorV2ViewSet.as_view({
    'get': 'retrieve'
})

room_stats = RoomStatViewSet.as_view({
    'get': 'list'
})

floor_v5_list = FloorV5View.as_view({
    'get': 'list'
})

floor_terminals = FloorTerminalView.as_view({
    'get': 'list'
})

room_hourly_temp = RoomHourlyTemp.as_view({
    'get': 'list'
})

dl_room_stats = RoomStatsView.as_view({
    'post': 'create'
})

floor_device_stats = FloorDeviceStatView.as_view({
    'get': 'retrieve'
})
floor_device_list = FloorDeviceStatView.as_view({
    'get': 'list'
})

urlpatterns = [
    path('saianapi/v1/buildings', building_list, name='building-list'),
    path('saianapi/v1/buildings/<int:pk>', building_detail, name='building-detail'),
    path('saianapi/v1/floors', floor_list, name='floor-list'),
    path('saianapi/v1/buildings/<int:building_id>/floors', floor_list, name='building-floor'),
    path('saianapi/v1/floors/<int:pk>', floor_detail, name='floor-detal'),
    path('saianapi/v1/buildings/<int:building_id>/floors/<int:pk>', floor_detail, name='floor-detal'),
    path('saianapi/v1/buildings/<int:building>/floors/<int:floor>/active_rooms', room_list, name='building-floor-room'),
    path('saianapi/v1/buildings/<int:building>/floors/<int:floor>/active_rooms/<int:pk>', room_detail, name='building-floor-room'),
    path('saianapi/v5/active_rooms', simple_rooms, name='simple-rooms'),
    path('saianapi/v1/active_rooms', room_list, name='room-list'),
    path('saianapi/v1/active_rooms/<int:pk>', room_detail, name='room-detail'),
    path('saianapi/v1/room_devices', room_device_list, name='room-device-list'),
    path('saianapi/v1/room_devices/<int:pk>', room_device_detail, name='room-device-detail'),
    path('saianapi/v1/temphums', room_temphums_list, name='room-temphums-list'),
    path('saianapi/v2/floors', floorv2_list, name='floorv2-list'),
    path('saianapi/v2/floors/<int:pk>', floorv2_detail, name='floorv2-detal'),
    path('saianapi/v2/room_stats', room_stats, name='room-stat'),

    path('saianapi/v5/floors', floor_v5_list, name='floor_v5'),
    path('saianapi/v5/floor_terminals', floor_terminals, name='floor_terminals'),
    path('saianapi/v1/room_hourly_temphums', room_hourly_temp, name='room_hourly_temp'),
    path('saianapi/v1/dl_room_stats', dl_room_stats, name='dl_room_stats'),
    path('saianapi/v5/floor_device_stats', floor_device_stats, name='floor_device_stats'),
    path('saianapi/v5/floor_device_list', floor_device_list, name='floor_device_list'),
]
