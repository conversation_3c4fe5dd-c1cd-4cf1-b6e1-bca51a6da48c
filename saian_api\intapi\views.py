import base64
import datetime
import heapq
import json
import logging
import os
import re
import traceback
from collections import defaultdict

import pymysql
from django.contrib.auth.hashers import make_password
from django.contrib.contenttypes.models import ContentType
from django.db import transaction, IntegrityError
from django.db.models import Q, OuterRef, Case, When, IntegerField, Subquery
from django.shortcuts import get_object_or_404
from openpyxl import Workbook
from openpyxl.styles import Border, Side, Font
from openpyxl.utils import get_column_letter
from rest_framework import viewsets, status, exceptions
from rest_framework.response import Response

from saian_api.device.models import DeviceEvent, Device, DeviceAttribute, SySim, DaSnapshot, DaSnapshotHistory
from saian_api.linkage.models import SnpVar
from saian_api.project.models import Project, ProjectWebMenu, ProjectChart, WebChart, WebMenu
from saian_api.utils.intapi_auth import IntapiAuth
from .serializers import (
    AttributeTypeSerializer,
    DeviceTypeSerializer,
    DevicePrototypeSerializer,
    AttributePrototypeSerializer,
    DeviceSerializer,
    ProjectWebMenuSerializer,
    ProjectChartSerializer,
    WebChartSerializer,
    BuildingSerializer,
    FloorSerializer,
    ActiveRoomSerializer,
    WebRegionSerializer,
    WebMenuSerializer,
    ReportConfigurerSerializer,
    WebUserRoleSerializer,
    DeviceAttributeSerializer,
    WebRoleMenuSerializer,
    DeviceProtocolSerializer,
    AddrSegmentSerializer,
    EcUserSerializer,
    SnpVarSerializer,
    DeviceListSerializer,
    DevicePrototypeRatioSerializer,
    TerminalSerializer, SySimSerializer, ReportConfigurationSerializer,
)
from .serializers import DeviceEventSerializer, ProjectSerializer
from .serializers import EcMeterSerializer
from .serializers import MessageSerializer
from .serializers import WebUserSerializer, WebRoleSerializer, UserProjectSerializer, UserDeviceSerializer, UserStatSerializer
from .. import settings
# Create your views here.
from ..building.models import Building, Floor, ActiveRoom
from ..coldsource.models import EcMeter
from ..devdefine.models import (
    AttributeType,
    DevicePrototype,
    DeviceType,
    AttributePrototype,
    DeviceProtocol,
    DevicePrototypeRatio
)
from ..devdefine.serializers import SimpleDevicePrototypeSerializer
from ..device.serializers import DaSnapshotSerializer
from ..device.tasks import task_unbind_device
from ..device.views import DeviceAttributeViewV5
from ..issue.models import DeviceIssue
from ..issue.serializers import DeviceIssueSerializer
from ..message.models import Message
from ..notifi.models import Notification
from ..regions.models import Region
from ..report.models import ReportConfigurer
from ..report.serializers import SimpleDeviceSerializer
from ..scheduledtask.utils import set_global_db
from ..terminal.models import Terminal, TerminalAttribute
from ..user.models import WebUser, WebRole, UserProject, UserDevice, UserStat
from ..user.serializers import WebUserInfoSerializer
from ..utils.db.Convert import Convert
from ..utils.db.Redis import RedisHelper
from ..utils.inthttpapi.device import AdminDeviceApi
from ..utils.tools import object_to_dict, sanitize_filename
from ..utils.utils import AuthUtils, ExcelUtils
from saian_api.utils.utils import DatetimeUtils

logger = logging.getLogger('django')


class ProjectViewSet(viewsets.ModelViewSet):
    authentication_classes = (IntapiAuth,)

    def create(self, request):
        project_id = request.data.get('id', None)
        project = None
        if project_id is not None:
            # 如果已经存在则更新
            # projects = Project.objects.filter(name=name)
            projects = Project.objects.filter(pk=project_id)

            if len(projects) != 0:
                project = projects.first()
                self.partial_update(request, project.id)

        if project is None:
            serializer = ProjectSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            project = Project()
            self.__save(project, serializer.validated_data)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def partial_update(self, request, pk=None):
        project = Project.objects.get(pk=pk)
        serializer = ProjectSerializer(project, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)

        self.__save(project, serializer.validated_data)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def __save(self, project, validated_data):
        for key, value in validated_data.items():
            try:
                if (
                        (key == 'in_acc' or
                         key == 'enable_ec' or
                         key == 'en_ot_aircon' or
                         key == 'en_temhum_sms' or
                         key == 'en_gas_sms' or
                         key == 'enable_web')
                        and value is None
                ):
                    value = False

                if key == 'project_type' and value is None:
                    value = '销售或EMC项目'

                # 避免项目信息被覆盖, 以下字段以新框架数据为准，不能用旧框架的数据覆盖
                if key in ['name', 'address', 'images', 'logo', 'slogan', 'user_panel_settings', 'settings', 'run_mode']:
                    if getattr(project, key):
                        continue

                setattr(project, key, value)
            except AttributeError:
                pass

        if project.created_at is None:
            setattr(project, 'created_at', datetime.datetime.now())

        project.save()


class ProjectWebMenuViewSet(viewsets.ModelViewSet):
    authentication_classes = (IntapiAuth,)
    # authentication_classes = ()

    def list(self, request, *args, **kwargs):
        project_id = request.user['project_id']
        web_menus_ids = ProjectWebMenu.objects.filter(project_id=project_id).values_list('web_menu_id', flat=True)
        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'menu_ids': web_menus_ids
            }
        })

    def create(self, request, *args, **kwargs):
        project_id = request.user['project_id']
        # project_id = 66
        from saian_api.scheduledtask.utils import set_global_db
        set_global_db(project_id)

        current_menu_ids = list(ProjectWebMenu.objects.filter(project_id=project_id).values_list('web_menu_id', flat=True))

        menu_ids = request.data.get('menu_ids', None)
        if menu_ids is None:
            raise exceptions.ValidationError(detail={'detail': 'menu_ids is required!'})

        new_menu_ids = [int(i) for i in menu_ids.split(',')]
        delete_ids = set(current_menu_ids) - set(new_menu_ids)
        add_ids = set(new_menu_ids) - set(current_menu_ids)

        with transaction.atomic(using=f"prj{project_id}db"):
            project_menus = ProjectWebMenu.objects.filter(project_id=project_id)
            project_menus.filter(web_menu_id__in=delete_ids).delete()

            for menu_in in add_ids:
                if not project_menus.filter(web_menu_id=menu_in).exists():
                    ProjectWebMenu.objects.create(
                        project_id=project_id,
                        web_menu_id=menu_in
                    )

        r = AdminDeviceApi.update_project_web_menus({'project_id': project_id, 'menu_ids': menu_ids})
        if r.status_code != 200:
            logging.error(f'向管理后台更新项目菜单失败，project-{project_id}, menus-{menu_ids}')

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def destroy(self, request, pk=None):
        web_menu_id = request.data.get('web_menu_id', None)
        project_id = request.data.get('project_id', None)
        pwm = ProjectWebMenu.objects.filter(web_menu_id=web_menu_id, project_id=project_id)
        if pwm.count() != 0:
            pwm.delete()

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })


class ProjectChartViewSet(viewsets.GenericViewSet):
    authentication_classes = (IntapiAuth,)

    def create(self, request):
        web_chart_id = request.data.get('web_chart_id', None)
        project_chart = None

        if web_chart_id is not None:
            project_charts = ProjectChart.objects.filter(project_id=request.user['project_id'], web_chart_id=web_chart_id)
            if len(project_charts) != 0:
                project_chart = project_charts.first()

        # 如果有了就不创，避免重复数据
        if project_chart is None:
            serializer = ProjectChartSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            project_chart = ProjectChart()
            self.__save(project_chart, serializer.validated_data)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def partial_update(self, request, pk=None):
        project_chart = ProjectChart.objects.get(pk=pk)
        serializer = ProjectChartSerializer(project_chart, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)

        self.__save(project_chart, serializer.validated_data)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def __save(self, object, validated_data):
        for key, value in validated_data.items():
            try:
                setattr(object, key, value)
            except AttributeError:
                pass

        if object.created_at is None:
            setattr(object, 'created_at', datetime.datetime.now())

        object.save()


class WebChartViewSet(viewsets.ModelViewSet):
    authentication_classes = (IntapiAuth,)
    queryset = WebChart.objects.all()
    serializer_class = WebChartSerializer

    def list(self, request, *args, **kwargs):
        results = super().list(request, *args, **kwargs).data.get('results')

        return Response({
            'status': status.HTTP_200_OK,
            'rows': results,
            'count': self.queryset.count()
        })

    def create(self, request, *args, **kwargs):
        uni_name = request.data.get('uni_name', None)
        web_chart = None
        if uni_name is not None:
            web_charts = WebChart.objects.filter(uni_name=uni_name)

            if len(web_charts) != 0:
                web_chart = web_charts.first()
                self.partial_update(request, web_chart.id)

        if web_chart is None:
            serializer = WebChartSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            web_chart = WebChart()
            self.__save(web_chart, serializer.validated_data)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def partial_update(self, request, pk=None):
        try:
            web_chart = WebChart.objects.get(pk=pk)
        except WebChart.DoesNotExist:
            return Response({
                'status': status.HTTP_200_OK,
                'data': None
            })
        serializer = WebChartSerializer(web_chart, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)

        self.__save(web_chart, serializer.validated_data)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def destroy(self, request, *args, **kwargs):
        super().destroy(request, *args, **kwargs)
        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def __save(self, object, validated_data):
        for key, value in validated_data.items():
            try:
                if key == 'enable' and value is None:
                    value = False
                setattr(object, key, value)
            except AttributeError:
                pass

        if object.created_at is None:
            setattr(object, 'created_at', datetime.datetime.now())

        object.save()


class DeviceEventViewSet(viewsets.ModelViewSet):
    authentication_classes = (IntapiAuth,)
    serializer_class = DeviceEventSerializer
    queryset = DeviceEvent.objects.all()

    def create(self, request, *args, **kwargs):
        if request.user.get('project_id', None) is not None:
            DeviceEvent.objects.cus_create(request.data)
        return Response()

class DeviceTypeViewSet(viewsets.GenericViewSet):
    authentication_classes = (IntapiAuth,)

    def list(self, request):
        queryset = DeviceType.objects.all()
        total = queryset.count()
        queryset = self.paginate_queryset(queryset)
        serializer = DeviceTypeSerializer(queryset, many=True)
        return Response({
            'status': status.HTTP_200_OK,
            'rows': serializer.data,
            'total': total
        })

    def retrieve(self, request, pk=None):
        dp = DeviceType.objects.get(pk=pk)
        serializer = DeviceTypeSerializer(dp)
        return Response({
            'status': status.HTTP_200_OK,
            'row': serializer.data
        })

    def create(self, request):
        uni_name = request.data.get('uni_name', None)
        device_type = None

        if uni_name is not None:
            device_types = DeviceType.objects.filter(uni_name=uni_name)
            if len(device_types) != 0:
                device_type = device_types.first()
                self.partial_update(request, device_type.id)

        if device_type is None:
            serializer = DeviceTypeSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            device_type = DeviceType()
            self.__save(device_type, serializer.validated_data)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def partial_update(self, request, pk=None):
        device_type = DeviceType.objects.filter(pk=pk).last()
        if device_type is None:
            self.create(request)
        else:
            serializer = DeviceTypeSerializer(device_type, data=request.data, partial=True)
            serializer.is_valid(raise_exception=True)

            self.__save(device_type, serializer.validated_data)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def destroy(self, request, pk=None):
        device_type = DeviceType.objects.get(pk=pk)
        device_type.delete()

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def __save(self, device_type, validated_data):
        for key, value in validated_data.items():
            if key == 'is_default' and value is None:
                value = False

            if key == 'pid':
                if value is None or int(value) == 0:
                    setattr(device_type, 'parent_id', None)
                else:
                    setattr(device_type, 'parent_id', value)
            else:
                try:
                    setattr(device_type, key, value)
                except AttributeError as e:
                    logging.error(f'failed to save device type, error: {e.__str__()}')

        if device_type.created_at is None:
            setattr(device_type, 'created_at', datetime.datetime.now())

        device_type.save()


class DevicePrototypeViewSet(viewsets.GenericViewSet):
    authentication_classes = (IntapiAuth,)

    def list(self, request):
        dp_ids = Device.objects.filter(
            project_id=self.request.user['project_id']
        ).values_list('device_prototype_id', flat=True)
        queryset = DevicePrototype.objects.filter(id__in=dp_ids).order_by('device_type_id')
        total = queryset.count()
        queryset = self.paginate_queryset(queryset)
        serializer = SimpleDevicePrototypeSerializer(queryset, many=True)
        return Response({
            'status': status.HTTP_200_OK,
            'rows': serializer.data,
            'total': total
        })

    def retrieve(self, request, pk=None):
        dp = DevicePrototype.objects.get(pk=pk)
        serializer = DevicePrototypeSerializer(dp)
        return Response({
            'status': status.HTTP_200_OK,
            'row': serializer.data
        })

    def create(self, request):
        uni_name = request.data.get('uni_name', None)
        device_prototype = None

        if uni_name is not None:
            dps = DevicePrototype.objects.filter(uni_name=uni_name)
            if len(dps) != 0:
                device_prototype = dps.first()
                self.partial_update(request, device_prototype.id)

        if device_prototype is None:
            try:
                serializer = DevicePrototypeSerializer(data=request.data)
                serializer.is_valid(raise_exception=True)
            except Exception as e:
                print(f"创建设备类型 Validation failed: {e}, data: {request.data}")
                raise

            device_prototype = DevicePrototype()
            self.__save(device_prototype, serializer.validated_data)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def partial_update(self, request, pk=None):
        device_prototype = DevicePrototype.objects.filter(id=pk).last()
        if device_prototype is None:
            self.create(request)
        else:
            serializer = DevicePrototypeSerializer(device_prototype, data=request.data, partial=True)
            serializer.is_valid(raise_exception=True)

            self.__save(device_prototype, serializer.validated_data)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def destroy(self, request, pk=None):
        device_prototype = DevicePrototype.objects.get(pk=pk)
        device_prototype.delete()

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def __save(self, device_prototype, validated_data):
        for key, value in validated_data.items():
            if key == 'skip_config' and value is None:
                value = False

            # m_name 如果已经有值，则不更新
            if key == 'm_name' and device_prototype.m_name:
                value = device_prototype.m_name

            if key == 'pid':
                setattr(device_prototype, 'parent_id', value)
            else:
                try:
                    setattr(device_prototype, key, value)
                except AttributeError:
                    pass

        if device_prototype.created_at is None:
            setattr(device_prototype, 'created_at', datetime.datetime.now())

        device_prototype.save()


class AttributeTypeViewSet(viewsets.ModelViewSet):
    authentication_classes = (IntapiAuth,)

    def list(self, request, *args, **kwargs):
        queryset = AttributeType.objects.all()

        search = request.query_params.get('search', None)
        if search is not None:
            queryset = queryset.filter(name__icontains=search)

        dp_id = request.query_params.get('dp_id', None)
        if dp_id is not None:
            queryset = queryset.filter(device_prototype_id=dp_id)

        total = queryset.count()
        queryset = self.paginate_queryset(queryset)
        serializer = AttributeTypeSerializer(queryset, many=True)
        return Response({
            'status': status.HTTP_200_OK,
            'rows': serializer.data,
            'total': total
        })

    def retrieve(self, request, *args, **kwargs):
        pk = kwargs.get('pk')
        ap = AttributeType.objects.get(pk=pk)
        serializer = AttributeTypeSerializer(ap)
        return Response({
            'status': status.HTTP_200_OK,
            'row': serializer.data
        })

    def create(self, request):
        uni_name = request.data.get('uni_name', None)
        device_prototype_id = request.data.get('device_prototype_id', None)
        attribute_type = None

        if uni_name is not None and device_prototype_id is not None:
            attribute_types = AttributeType.objects.filter(uni_name=uni_name, device_prototype_id=device_prototype_id)
            if len(attribute_types) != 0:
                attribute_type = attribute_types.first()
                self.partial_update(request, attribute_type.id)

        if attribute_type is None:
            serializer = AttributeTypeSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            attribute_type = AttributeType()
            self.__save(attribute_type, serializer.validated_data)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def partial_update(self, request, pk=None):
        attribute_type = AttributeType.objects.filter(id=pk).last()
        if attribute_type is None:
            self.create(request)
        else:
            serializer = AttributeTypeSerializer(attribute_type, data=request.data, partial=True)
            serializer.is_valid(raise_exception=True)

            self.__save(attribute_type, serializer.validated_data)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def destroy(self, request, pk=None):
        attribute_type = AttributeType.objects.get(pk=pk)
        attribute_type.delete()

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def __save(self, object, validated_data):
        for key, value in validated_data.items():
            if (key == 'hidden' or key == 'in_add') and value is None:
                value = False

            if key == 'seq' and value is None:
                value = 0

            if key == 'uni_name' and value is None:
                value = validated_data['name']

            try:
                setattr(object, key, value)
            except AttributeError:
                pass

        if object.created_at is None:
            setattr(object, 'created_at', datetime.datetime.now())

        object.save()


class AttributePrototypeViewSet(viewsets.ModelViewSet):
    authentication_classes = (IntapiAuth,)

    def list(self, request, *args, **kwargs):
        from saian_api.devdefine.serializers import AttributePrototypeSerializer
        queryset = AttributePrototype.objects.all()

        device_prototype = request.query_params.get('device_prototype', None)
        if device_prototype is not None:
            queryset = queryset.filter(device_prototype_id=device_prototype)
        read_only = request.query_params.get('read_only', None)
        if read_only is not None:
            queryset = queryset.filter(read_only=read_only)

        total = queryset.count()

        keywords = ['温度', '有功', '电量', '功率', '电流', '频率', '负荷', '压力', '冷热量', '冷量', '热量', ]

        order_case = Case(
            *[
                When(name__contains=kw, then=pos)
                for pos, kw in enumerate(keywords)
            ],
            default=99,
            output_field=IntegerField()
        )

        queryset = queryset.annotate(
            priority=order_case
        ).order_by('priority', 'name', 'seq')

        queryset = self.paginate_queryset(queryset)
        serializer = AttributePrototypeSerializer(queryset, many=True)

        return Response({
            'status': status.HTTP_200_OK,
            'rows': serializer.data,
            'total': total
        })

    def retrieve(self, request, *args, **kwargs):
        pk = kwargs.get('pk')
        ap = AttributePrototype.objects.get(pk=pk)
        serializer = AttributePrototypeSerializer(ap)
        return Response({
            'status': status.HTTP_200_OK,
            'row': serializer.data
        })

    def create(self, request):
        idf = request.data.get('identifier', None)
        device_prototype_id = request.data.get('device_prototype_id', None)
        attribute_prototype = None

        if idf is not None and device_prototype_id is not None:
            aps = AttributePrototype.objects.filter(identifier=idf, device_prototype_id=device_prototype_id)
            if aps.exists():
                attribute_prototype = aps.first()
                self.partial_update(request, attribute_prototype.id)
            else:
                read_only = request.data.get('read_only', None)

                if read_only is None:
                    request.data['read_only'] = False

                serializer = AttributePrototypeSerializer(data=request.data)
                serializer.is_valid(raise_exception=True)

                attribute_prototype = AttributePrototype()
                self.__save(attribute_prototype, serializer.validated_data)

        # 如果已经有设备，则同步增加设备属性
        if attribute_prototype is not None:
            devices = Device.objects.filter(device_prototype_id=attribute_prototype.device_prototype_id)
            if len(devices) != 0:
                for device in devices:
                    try:
                        if not DeviceAttribute.objects.filter(device=device, attribute_prototype_id=attribute_prototype.id).exists():
                            DeviceAttribute.objects.create(
                                attribute_prototype_id=attribute_prototype.id,
                                device_id=device.id,
                                value=attribute_prototype.default_value,
                                show_in_list=False if attribute_prototype.show_in_list is None else attribute_prototype.show_in_list
                            )

                        TerminalAttribute.objects.create_by_device(device)

                    except Exception:
                        logger.error(traceback.format_exc())
                        pass

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def partial_update(self, request, pk=None):
        dp_id = request.data.get('device_prototype_id', None)
        identifier = request.data.get('identifier', None)
        if dp_id is not None and identifier is not None:
            for attribute_prototype in AttributePrototype.objects.filter(device_prototype_id=dp_id, identifier=identifier):
                serializer = AttributePrototypeSerializer(attribute_prototype, data=request.data, partial=True)
                serializer.is_valid(raise_exception=True)
                self.__save(attribute_prototype, serializer.validated_data)

                # 删除缓存的ap
                name = f'attribute_prototype:{dp_id}_{identifier}'
                RedisHelper.delete(request.user['project_id'], name)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def destroy(self, request, pk=None):
        dp_id = request.query_params.get('device_prototype_id', None)
        identifier = request.query_params.get('identifier', None)
        if dp_id is not None and identifier is not None:
            names = []
            for attribute_prototype in AttributePrototype.objects.filter(device_prototype_id=dp_id, identifier=identifier):
                # 如果已经有设备，则同步删除设备属性
                das = DeviceAttribute.objects.filter(attribute_prototype_id=attribute_prototype.id)
                names += [f'device_attribute:{da.device_id}_{attribute_prototype.id}' for da in das]

                tas = TerminalAttribute.objects.filter(attribute_prototype_id=attribute_prototype.id)
                names += [f'terminal_attribute:{ta.terminal_id}_{attribute_prototype.id}' for ta in tas]

                das.delete()
                tas.delete()
                attribute_prototype.delete()

            # 删除缓存da, ta
            RedisHelper.delete_multi_obj(request.user['project_id'], names)
            # 删除缓存的ap
            RedisHelper.delete(request.user['project_id'], f'attribute_prototype:{dp_id}_{identifier}')

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def __save(self, object, validated_data):
        for key, value in validated_data.items():
            if (
                    (key == 'read_only' or
                     key == 'is_key' or
                     key == 'can_debug' or
                     key == 'hidden' or
                     key == 'in_add' or
                     key == 'show_in_list' or
                     key == 'is_cum' or
                     key == 'do_export')
                    and value is None
            ):
                value = False

            if key == 'label' and value is None:
                value = validated_data['name']

            if (key == 'seq' or key == 'length') and value is None:
                value = 0

            if key == 'data_type' and value is None:
                value = 10

            if key == 'id':
                continue

            try:
                setattr(object, key, value)
            except AttributeError:
                pass

        if object.created_at is None:
            setattr(object, 'created_at', datetime.datetime.now())

        try:
            object.save()
        except IntegrityError:
            if not AttributeType.objects.filter(pk=object.attribute_type_id).exists():
                logger.error(f'创建属性类型时出错，不满足约束条件: attribute_type: {object.attribute_type_id} 不存在。')


class WebRoleViewSet(viewsets.ModelViewSet):
    authentication_classes = (IntapiAuth,)
    queryset = WebRole.objects.all()
    serializer_class = WebRoleSerializer

    def list(self, request, *args, **kwargs):
        web_roles = super(WebRoleViewSet, self).list(request, *args, **kwargs).data.get('results')
        return Response({
            'status': status.HTTP_200_OK,
            'rows': web_roles,
            'total': self.queryset.count()
        })

    def create(self, request):
        name = request.data.get('name', None)
        web_role = None

        if name is not None:
            web_roles = WebRole.objects.filter(name=name, project_id=request.user['project_id'])
            if len(web_roles) != 0:
                web_role = web_roles.first()
                self.partial_update(request, web_role.id)

        if web_role is None:
            serializer = WebRoleSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            web_role = WebRole()
            self.__save(web_role, serializer.validated_data)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def partial_update(self, request, pk=None):
        web_role = WebRole.objects.get(pk=pk)
        serializer = WebRoleSerializer(web_role, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)

        self.__save(web_role, serializer.validated_data)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def __save(self, object, validated_data):
        for key, value in validated_data.items():
            try:
                setattr(object, key, value)
            except AttributeError:
                pass

        if object.created_at is None:
            setattr(object, 'created_at', datetime.datetime.now())

        object.save()


class WebUserViewSet(viewsets.ModelViewSet):
    authentication_classes = (IntapiAuth,)
    serializer_class = WebUserInfoSerializer

    def get_queryset(self):
        queryset = WebUser.objects.all()
        search = self.request.query_params.get('search', None)
        if search is not None:
            queryset = queryset.filter(Q(username__icontains=search) | Q(name__icontains=search))
        return queryset.order_by('-id')

    def list(self, request, *args, **kwargs):
        web_users = super(WebUserViewSet, self).list(request, *args, **kwargs).data.get('results', None)
        return Response({
            'status': status.HTTP_200_OK,
            'rows': web_users,
            'total': self.get_queryset().count()
        })

    def retrieve(self, request, *args, **kwargs):
        return Response({
            'status': status.HTTP_200_OK,
            'row': super(WebUserViewSet, self).retrieve(request, *args, **kwargs).data
        })

    def create(self, request, *args, **kwargs):
        unionid = request.data.get('unionid', None)
        web_user = None

        if unionid is not None:
            web_users = WebUser.objects.filter(unionid=unionid)
            if len(web_users) != 0:
                web_user = web_users.first()
                self.partial_update(request, web_user.id)
        if web_user is None:
            serializer = WebUserSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            web_user = WebUser()
            web_user.last_login = datetime.datetime.now()
            self.__save(web_user, serializer.validated_data)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def partial_update(self, request, pk=None, **kwargs):
        web_user = WebUser.objects.get(pk=pk)
        serializer = WebUserSerializer(web_user, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)

        self.__save(web_user, serializer.validated_data)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def __save(self, object, validated_data):

        for key, value in validated_data.items():
            try:
                if key == 'is_super' and value is None:
                    value = False

                if key == 'status' and value is None:
                    value = 10

                if key == 'last_login' and value is None:
                    value = datetime.datetime.now()

                if key == 'web_roles':
                    object.web_roles.set(WebRole.objects.filter(id__in=value))
                else:
                    setattr(object, key, value)

            except AttributeError:
                pass

        if object.created_at is None:
            setattr(object, 'created_at', datetime.datetime.now())

        object.save()


class UserProjectViewSet(viewsets.ModelViewSet):
    authentication_classes = (IntapiAuth,)

    def create(self, request):
        user_id = request.data.get('web_user_id', None)
        project_id = request.data.get('project_id', None)
        unionid = request.data.get('unionid', None)

        if not user_id and unionid:
            web_users = WebUser.objects.filter(unionid=unionid)

            if web_users.exists():
                user_id = web_users.first().id
            else:
                # 如果没用户则创建
                openid = request.data.get('openid', None)
                name = request.data.get('name', None)
                mobile = request.data.get('mobile', None)
                avatar = request.data.get('avatar', None)
                email = request.data.get('email', None)
                is_super = request.data.get('is_super', None)

                role = WebRole.objects.get(en_name='thirdparty')
                if bool(is_super):
                    role = WebRole.objects.filter(en_name='admin').first()

                # 生成token
                raw_token = AuthUtils.generate_random_char(64)
                base64_token = base64.b64encode(raw_token.encode('ascii'))
                authentication_token = str(base64_token, 'ascii')

                # 生成随机密码
                password = make_password(AuthUtils.generate_random_char(8))

                web_user = WebUser.objects.create(
                    username=mobile,
                    openid=openid,
                    unionid=unionid,
                    name=name,
                    mobile=mobile,
                    avatar=avatar,
                    email=email,
                    is_super=is_super,
                    permissions=role.permissions,
                    status=10,
                    last_login=datetime.datetime.now(),
                    authentication_token=authentication_token,
                    password=password
                )

                # 创建用户角色关系
                web_user.web_roles.add(role)

                user_id = web_user.id

            up_exists = UserProject.objects.filter(web_user_id=user_id, project_id=project_id).exists()
            if not up_exists:
                UserProject.objects.create(web_user_id=user_id, project_id=project_id)
        else:

            if user_id and project_id:
                up_exists = UserProject.objects.filter(web_user_id=user_id, project_id=project_id).exists()
                if not up_exists:
                    serializer = UserProjectSerializer(data=request.data)
                    serializer.is_valid(raise_exception=True)
                    user_project = UserProject()
                    self.__save(user_project, serializer.validated_data)

            web_user = WebUser.objects.filter(pk=user_id).last()
            if web_user:
                # 如果用户没有角色，就默认第三方
                if web_user.web_roles.count() == 0:
                    web_user.web_roles.add(WebRole.objects.order_by('id').last())

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def partial_update(self, request, pk=None):
        user_project = UserProject.objects.get(pk=pk)
        serializer = UserProjectSerializer(user_project, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)

        self.__save(user_project, serializer.validated_data)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def destroy(self, request, pk=None):
        web_user_id = request.data.get('web_user_id', None)
        project_id = request.data.get('project_id', None)
        unionid = request.data.get('unionid', None)

        web_user = None

        if web_user_id:
            web_user = WebUser.objects.filter(pk=web_user_id).last()

        if not web_user and unionid:
            web_user = WebUser.objects.filter(unionid=unionid).last()

        if web_user and project_id:
            user_project = UserProject.objects.filter(web_user_id=web_user.id, project_id=project_id)
            user_project.delete()
            web_user.web_roles.clear()
            UserDevice.objects.filter(web_user_id=web_user.id).delete()


        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def __save(self, object, validated_data):
        for key, value in validated_data.items():
            try:
                setattr(object, key, value)
            except AttributeError:
                pass

        object.save()


class UserDeviceViewSet(viewsets.ModelViewSet):
    authentication_classes = (IntapiAuth,)

    def create(self, request):
        user_id = request.data.get('user_id', None)
        device_id = request.data.get('device_id', None)
        unit_idx = request.data.get('unit_idx', None)
        unit_prefix = request.data.get('unit_prefix', None)

        if user_id is not None and device_id is not None:
            queryset = UserDevice.objects.filter(web_user_id=user_id, device_id=device_id)
            if unit_idx is not None and not unit_idx:
                queryset = queryset.filter(unit_idx=unit_idx)
            if unit_prefix is not None and not unit_prefix:
                queryset = queryset.filter(unit_prefix=unit_prefix)

            if not queryset.exists():
                serializer = UserDeviceSerializer(data=request.data)
                serializer.is_valid(raise_exception=True)

                user_device = UserDevice()
                self.__save(user_device, serializer.validated_data)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def destroy(self, request, pk=None):
        user_device = UserDevice.objects.get(pk=pk)
        web_user_id = request.data.get('user_id', None)
        device_id = request.data.get('device_id', None)
        unit_idx = request.data.get('unit_idx', None)
        unit_prefix = request.data.get('unit_prefix', None)

        if web_user_id is not None and device_id is not None:
            if unit_idx is not None and unit_prefix is not None:
                user_device = UserDevice.objects.get(
                    web_user_id=web_user_id,
                    device_id=device_id,
                    unit_idx=unit_idx,
                    unit_prefix=unit_prefix
                )
            else:
                user_device = UserDevice.objects.get(
                    web_user_id=web_user_id,
                    device_id=device_id
                )

            user_device.delete()

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def __save(self, object, validated_data):
        for key, value in validated_data.items():
            if key == 'user_id':
                setattr(object, 'web_user_id', value)
            else:
                try:
                    setattr(object, key, value)
                except AttributeError:
                    pass

        object.save()


class EcMeterViewSet(viewsets.ModelViewSet):
    authentication_classes = (IntapiAuth,)

    def create(self, request):
        identifier = request.data.get('identifier', None)
        device_id = request.data.get('device_id', None)
        ec_meter = None
        if identifier is not None and device_id is not None:
            ec_meters = EcMeter.objects.filter(identifier=identifier, device_id=device_id)
            if len(ec_meters) != 0:
                ec_meter = ec_meters.first()
                self.partial_update(request, ec_meter.id)
        if ec_meter is None:
            serializer = EcMeterSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            ec_meter = EcMeter()
            self.__save(ec_meter, serializer.validated_data)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def partial_update(self, request, pk=None):
        ec_meter = EcMeter.objects.get(pk=pk)
        serializer = EcMeterSerializer(ec_meter, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)

        self.__save(ec_meter, serializer.validated_data)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def destroy(self, request, pk=None):
        ec_meter = EcMeter.objects.get(pk=pk)
        ec_meter.delete()

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def __save(self, object, validated_data):
        for key, value in validated_data.items():
            if key == 'enabled' and value is None:
                value = False

            try:
                setattr(object, key, value)
            except AttributeError:
                pass

        if object.created_at is None:
            setattr(object, 'created_at', datetime.datetime.now())

        object.save()


class DeviceViewSet(viewsets.ModelViewSet):
    authentication_classes = (IntapiAuth,)
    serializer_class = DeviceListSerializer

    def get_queryset(self):
        queryset = Device.objects.filter(project_id=self.request.user.get('project_id'))

        search = self.request.query_params.get('search', None)
        if search is not None:
            queryset = queryset.filter(nick_name__icontains=search)

        mac = self.request.query_params.get('mac', None)
        if mac is not None:
            queryset = queryset.filter(mac__contains=mac)

        device_status = self.request.query_params.get('status', None)
        if device_status is not None:
            queryset = queryset.filter(status=device_status)

        type_id = self.request.query_params.get('type_id', None)
        if type_id is not None:
            queryset = queryset.filter(device_prototype_id=type_id)

        return queryset.order_by(Convert('nick_name', 'gbk'))

    def list(self, request, *args, **kwargs):
        devices = super(DeviceViewSet, self).list(request, *args, **kwargs).data.get('results', None)
        return Response({
            'status': status.HTTP_200_OK,
            'rows': devices,
            'total': self.get_queryset().count()
        })

    def create(self, request, *args, **kwargs):
        device_id = request.data.get('id', None)
        mac = request.data.get('mac', None)
        device = None
        project_id = request.user['project_id']

        if device_id is not None:
            device = Device.objects.filter(pk=device_id).last()
        elif mac is not None:
            device = Device.objects.filter(mac=mac).last()

        if device is not None:
            # 重新绑定设备，但定时任务还没把设备删除
            if not device.project_id and request.data.get('project_id', None):
                # 重新生成终端
                Terminal.objects.create_by_device(device)
                TerminalAttribute.objects.create_by_device(device)
                # 更新终端的可见状态
                for terminal in Terminal.objects.filter(device=device):
                    # 隐藏冷源设备的非电表子终端
                    if terminal.terminal_type == 40 and terminal.prefix and terminal.prefix != 'Meter':
                        terminal.show_en = False
                    else:
                        terminal.show_en = True
                    terminal.save()
            self.partial_update(request, device.id)
            device.project_id = request.data.get('project_id', None)
            Device.objects.filter(id=device.id).update(project_id=device.project_id)

        if device is None:
            serializer = DeviceSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            with transaction.atomic(using=f"prj{project_id}db"):
                device = Device()
                self.__save(device, serializer.validated_data, request.data.get('_state', None))
                DeviceAttribute.objects.create_by_device(device)
                Terminal.objects.create_by_device(device)
                TerminalAttribute.objects.create_by_device(device)
                data = {
                    'content': f'新设备接入: 设备【{device.id}-{device.nick_name}-{device.mac}】',
                    'device_id': device.id,
                    'device_nickname': device.nick_name
                }
                Notification.objects.notify(7, device, data)

        device_type = DeviceType.objects.get(pk=device.device_type_id)
        # 设备分类是计量或环境，默认开关状态为True
        if device_type.parent_id in [3, 4]:
            device.sw_on = True
            Device.objects.filter(id=device.id).update(sw_on=device.sw_on)
            Terminal.objects.bulk_update_by_device(device, 'sw_on', device.sw_on)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def retrieve(self, request, *args, **kwargs):
        device = super(DeviceViewSet, self).retrieve(request, *args, **kwargs).data
        return Response({
            'status': status.HTTP_200_OK,
            'row': device
        })

    def partial_update(self, request, pk=None, **kwargs):
        device = Device.objects.get(pk=pk)
        serializer = DeviceSerializer(device, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)

        self.__save(device, serializer.validated_data, request.data.get('_state', None))

        # 更新终端nick_name
        if 'nick_name' in serializer.validated_data:
            nick_name = serializer.validated_data.get('nick_name', None)
            if nick_name is not None:
                try:
                    terminal = Terminal.objects.get(device=device, idx__isnull=True)
                    terminal.nick_name = nick_name
                    terminal.save()
                except Terminal.DoesNotExist:
                    pass
                except Terminal.MultipleObjectsReturned:
                    logging.error(f'设备{device.id}有多个非子终端')

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def destroy(self, request, *args, **kwargs):
        device = Device.objects.get(pk=kwargs['pk'])
        project_id = device.project_id

        if device.project_id is not None:
            r = AdminDeviceApi.update_device(device.id, {'project': None})
            if r.status_code == 200:
                device.status = 40
                device.project_id = None
                device.save()

                # 解绑设备时清除设备的关注数据
                UserDevice.objects.filter(device_id=device.id).delete()

                terminals = Terminal.objects.filter(device=device)
                terminals.update(show_en=False)

                task_unbind_device.delay(project_id, device.id)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def __save(self, object, validated_data, _state=None):
        for key, value in validated_data.items():
            if (
                    (key == 'in_alarm' or
                     key == 'in_fault' or
                     key == 'online' or
                     key == 'sw_on' or
                     key == 'in_acc' or
                     key == 'needs_m' or
                     key == 'live_update')
                    and value is None
            ):
                value = False

            try:
                # admin2 同步数据时不同步网关地址
                # _state 不为 None，则同步请求来自于 admin3
                if key == 'gw_end_point' and _state is None:
                    pass
                else:
                    setattr(object, key, value)

            except AttributeError:
                pass

        if object.created_at is None:
            setattr(object, 'created_at', datetime.datetime.now())

        object.save()


class BuildingViewSet(viewsets.ModelViewSet):
    authentication_classes = (IntapiAuth,)

    def create(self, request):
        serializer = BuildingSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        building = Building()
        self.__save(building, serializer.validated_data)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def partial_update(self, request, pk=None):
        building = Building.objects.get(pk=pk)
        serializer = BuildingSerializer(building, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)

        self.__save(building, serializer.validated_data)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def __save(self, object, validated_data):
        for key, value in validated_data.items():
            try:
                setattr(object, key, value)
            except AttributeError:
                pass

        if object.created_at is None:
            setattr(object, 'created_at', datetime.datetime.now())

        object.save()


class FloorViewSet(viewsets.ModelViewSet):
    authentication_classes = (IntapiAuth,)

    def create(self, request):
        serializer = FloorSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        floor = Floor()
        self.__save(floor, serializer.validated_data)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def partial_update(self, request, pk=None):
        floor = Floor.objects.get(pk=pk)
        serializer = FloorSerializer(floor, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)

        self.__save(floor, serializer.validated_data)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def __save(self, object, validated_data):
        for key, value in validated_data.items():
            try:
                setattr(object, key, value)
            except AttributeError:
                pass

        if object.created_at is None:
            setattr(object, 'created_at', datetime.datetime.now())

        object.save()


class ActiveRoomViewSet(viewsets.ModelViewSet):
    authentication_classes = (IntapiAuth,)

    def create(self, request):
        serializer = ActiveRoomSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        active_room = ActiveRoom()
        self.__save(active_room, serializer.validated_data)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def partial_update(self, request, pk=None):
        active_room = ActiveRoom.objects.get(pk=pk)
        serializer = ActiveRoomSerializer(active_room, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)

        self.__save(active_room, serializer.validated_data)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def __save(self, object, validated_data):
        for key, value in validated_data.items():
            if key == 'show_in_datav' and value is None:
                value = False

            try:
                setattr(object, key, value)
            except AttributeError:
                pass

        if object.created_at is None:
            setattr(object, 'created_at', datetime.datetime.now())

        object.save()


class WebRegionViewSet(viewsets.ModelViewSet):
    authentication_classes = (IntapiAuth,)

    def create(self, request):
        serializer = WebRegionSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        region = Region()
        self.__save(region, serializer.validated_data)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def partial_update(self, request, pk=None):
        region = Region.objects.get(pk=pk)
        serializer = WebRegionSerializer(region, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)

        self.__save(region, serializer.validated_data)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def __save(self, object, validated_data):
        for key, value in validated_data.items():
            try:
                setattr(object, key, value)
            except AttributeError:
                pass

        object.save()


class WebMenuViewSet(viewsets.ModelViewSet):
    authentication_classes = (IntapiAuth,)

    def list(self, request, *args, **kwargs):
        menu_ids = ProjectWebMenu.objects.filter(project_id=request.user['project_id']).values_list('web_menu_id', flat=True)
        queryset = WebMenu.objects.filter(id__in=menu_ids, parent=None).order_by('id')
        menus = []
        for menu in queryset:
            menu_fields = WebMenuSerializer(menu).data
            children = WebMenu.objects.filter(parent_id=menu.id).order_by('id')
            menu_fields['children'] = WebMenuSerializer(children, many=True).data
            menus.append(menu_fields)

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'menus': menus
            }
        })

    def create(self, request):
        uni_name = request.data.get('uni_name', None)
        web_menu = None
        if uni_name is not None:
            web_menus = WebMenu.objects.filter(uni_name=uni_name)
            if len(web_menus) != 0:
                web_menu = web_menus.first()
                self.partial_update(request, web_menu.id)

        if web_menu is None:
            serializer = WebMenuSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            web_menu = WebMenu()
            self.__save(web_menu, serializer.validated_data)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def partial_update(self, request, pk=None):
        web_menu = WebMenu.objects.filter(id=pk).last()
        if web_menu is None:
            self.create(request)
        else:
            serializer = WebMenuSerializer(web_menu, data=request.data, partial=True)
            serializer.is_valid(raise_exception=True)

            self.__save(web_menu, serializer.validated_data)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def destroy(self, request, pk=None):
        project_id = request.user.get('project_id')

        with transaction.atomic(using=f'prj{project_id}db'):
            web_menu = WebMenu.objects.get(pk=pk)

            project_web_menus = ProjectWebMenu.objects.filter(web_menu_id=web_menu.id)
            project_web_menus.delete()

            web_menu.delete()

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def __save(self, object, validated_data):
        for key, value in validated_data.items():
            try:
                if (
                        (key == 'seq' or
                         key == 'enabled' or
                         key == 'is_super')
                        and value is None
                ):
                    value = 0
                setattr(object, key, value)
            except AttributeError:
                pass

        if object.created_at is None:
            setattr(object, 'created_at', datetime.datetime.now())

        object.save()


class ReportConfigurerViewSet(viewsets.ModelViewSet):
    authentication_classes = (IntapiAuth,)
    serializer_class = ReportConfigurationSerializer

    def get_queryset(self):
        queryset = ReportConfigurer.objects.all()

        proto_id = self.request.query_params.get('proto_id', None)
        if proto_id is not None:
            dp_content = ContentType.objects.get_for_model(DevicePrototype)
            queryset = queryset.filter(target_id=proto_id, target_type=dp_content)

        identifier = self.request.query_params.get('identifier', None)
        if identifier is not None:
            queryset = queryset.filter(identifier__icontains=identifier)

        name = self.request.query_params.get('name', None)
        if name is not None:
            queryset = queryset.filter(name__icontains=name)

        return queryset.order_by('-id')

    def list(self, request, *args, **kwargs):
        data = super().list(self, request, *args, **kwargs).data
        rcs = data['results']
        count = data['count']

        target_cache = {}

        for rc in rcs:
            target_type = ContentType.objects.get(pk=rc['target_type'])
            rc['target_type'] = target_type.model_class().__name__
            cache_key = f'{rc["target_type"]}_{rc["target_id"]}'
            target = target_cache.get(cache_key, None)
            if target is None:
                try:
                    target = target_type.get_object_for_this_type(pk=rc['target_id'])
                    rc['target_name'] = target.name
                    target_cache[cache_key] = target
                except target_type.model_class().DoesNotExist:
                    rc['target_name'] = None
                    target_cache[cache_key] = None
            else:
                rc['target_name'] = target.name
        return Response({
            'status': status.HTTP_200_OK,
            'rows': rcs,
            'total': count
        })

    def create(self, request):
        target_id = request.data.get('target_id', None)
        identifier = request.data.get('identifier', None)
        report = None

        # 如果项目内没有设备类型是 target_id 的设备，就跳过
        if target_id not in set(Device.objects.values_list('device_prototype_id', flat=True)):
            return Response({
                'status': status.HTTP_200_OK,
                'data': None
            })

        if target_id is not None and identifier is not None:
            reports = ReportConfigurer.objects.filter(target_id=target_id, identifier=identifier)
            if len(reports) != 0:
                report = reports.first()
                self.partial_update(request, report.id)

        if report is None:
            serializer = ReportConfigurerSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            report = ReportConfigurer()
            self.__save(report, serializer.validated_data)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def partial_update(self, request, pk=None):
        target_id = request.data.get('target_id', None)
        identifier = request.data.get('identifier', None)
        if target_id is not None and identifier is not None:
            dp_model = ContentType.objects.get_for_model(DevicePrototype)
            for report in ReportConfigurer.objects.filter(target_type=dp_model, target_id=target_id, identifier=identifier):
                serializer = ReportConfigurerSerializer(report, data=request.data, partial=True)
                serializer.is_valid(raise_exception=True)

                self.__save(report, serializer.validated_data)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def destroy(self, request, pk=None):
        target_id = request.query_params.get('target_id', None)
        identifier = request.query_params.get('identifier', None)
        if target_id is not None and identifier is not None:
            dp_model = ContentType.objects.get_for_model(DevicePrototype)
            reports = ReportConfigurer.objects.filter(target_type=dp_model, identifier=identifier, target_id=target_id)
            reports.delete()

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def __save(self, object, validated_data):
        for key, value in validated_data.items():
            if (
                    (key == 'chart_type' or
                     key == 'device_idx' or
                     key == 'seq' or
                     key == 'refer_point')
                    and value is None
            ):
                value = 0

            if key == 'id':
                continue

            if key == 'configurable_id':
                setattr(object, 'target_id', value)
            elif key == 'configurable_type':
                target_type = ContentType.objects.get(model=str(value).lower())
                setattr(object, 'target_type', target_type)
            else:
                try:
                    setattr(object, key, value)
                except AttributeError:
                    pass

        object.save()


class WebUserRoleViewSet(viewsets.ModelViewSet):
    authentication_classes = (IntapiAuth,)

    def create(self, request):
        serializer = WebUserRoleSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        web_role = WebRole.objects.get(pk=serializer.validated_data['web_role_id'])
        web_user = WebUser.objects.get(pk=serializer.validated_data['web_user_id'])

        if web_user.web_roles.filter(pk=web_role.id).exists():
            return Response({
                'status': status.HTTP_200_OK,
                'data': None
            })
        web_user.web_roles.clear()
        web_user.web_roles.add(web_role)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })


class WebRoleMenuViewSet(viewsets.ModelViewSet):
    authentication_classes = (IntapiAuth,)

    def create(self, request):
        serializer = WebRoleMenuSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        web_role = WebRole.objects.get(pk=serializer.validated_data['web_role_id'])
        web_menu = WebMenu.objects.get(pk=serializer.validated_data['web_menu_id'])
        web_role.web_menus.add(web_menu)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })


class DeviceAttributeViewSet(viewsets.ModelViewSet):
    authentication_classes = (IntapiAuth,)
    # authentication_classes = ()

    def list(self, request, *args, **kwargs):
        device_id = request.query_params.get('device_id', None)
        mac = request.query_params.get('mac', None)
        device = None

        if mac is not None:
            device = Device.objects.filter(mac=mac).first()
            if device is not None:
                device_id = device.id

        if device_id is None:
            return Response(status=status.HTTP_400_BAD_REQUEST, data=None)

        if device is None:
            device = Device.objects.get(pk=device_id)

        ap_ids = DeviceAttribute.objects.filter(device_id=device_id).values_list('attribute_prototype_id', flat=True)
        aps = AttributePrototype.objects.filter(id__in=ap_ids)

        identifier = request.query_params.get('identifier', None)
        if identifier is not None:
            if ' ' in identifier:
                identifiers = list(filter(None, identifier.split(' ')))
                idf_query = Q()
                for idf in identifiers:
                    idf_query |= Q(identifier__icontains=idf)
                aps = aps.filter(idf_query)
            else:
                aps = aps.filter(identifier__icontains=identifier)

        search = request.query_params.get('search', None)
        if search is not None:
            if ' ' in search:
                names = list(filter(None, search.split(' ')))
                name_query = Q()
                for name in names:
                    name_query |= Q(name__icontains=name)
                aps = aps.filter(name_query)
            else:
                aps = aps.filter(name__icontains=search)

        readonly = request.query_params.get('readonly', None)
        if readonly is not None:
            aps = aps.filter(read_only=readonly)

        at_id = request.query_params.get('at_id', None)
        if at_id is not None:
            aps = aps.filter(attribute_type_id=at_id)

        ap_ids = [ap.id for ap in aps]

        keywords = ['温度', '有功', '电量', '功率', '电流', '频率', '负荷', '压力', '冷热量', '冷量', '热量', ]

        prototype_name_subquery = AttributePrototype.objects.filter(
            id=OuterRef('attribute_prototype_id')
        ).values('name')[:1]

        # 构造排序权重 Case
        order_case = Case(
            *[
                When(prototype_name__contains=kw, then=pos)
                for pos, kw in enumerate(keywords)
            ],
            default=99,
            output_field=IntegerField()
        )

        queryset = DeviceAttribute.objects.filter(device_id=device_id, attribute_prototype_id__in=ap_ids).annotate(
            prototype_name=Subquery(prototype_name_subquery),
            priority=order_case
        ).order_by('priority', 'prototype_name')

        das = self.paginate_queryset(queryset)

        ap_ids = [da.attribute_prototype_id for da in das]
        das = DeviceAttribute.objects.query_object_list(device, ap_ids)
        serializer = DeviceAttributeSerializer(das, many=True)

        return Response({
            'status': status.HTTP_200_OK,
            'total': queryset.count(),
            'rows': serializer.data
        })

    def create(self, request):
        attribute_prototype_id = request.data.get('attribute_prototype_id', None)
        device_id = request.data.get('device_id', None)
        device_attribute = None

        if attribute_prototype_id is not None and device_id is not None:
            device_attributes = DeviceAttribute.objects.filter(device_id=device_id, attribute_prototype_id=attribute_prototype_id)
            if len(device_attributes) != 0:
                device_attribute = device_attributes.first()
                self.partial_update(request, device_attribute.id)

        if device_attribute is None:
            serializer = DeviceAttributeSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            device_attribute = DeviceAttribute()
            self.__save(device_attribute, serializer.validated_data)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def partial_update(self, request, pk=None, **kwargs):
        da = DeviceAttribute.objects.get(pk=pk)
        serializer = DeviceAttributeSerializer(da, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)

        send_to_device = request.data.get('send_to_device', None)
        value = request.data.get('value', None)

        prev_value = da.value

        try:
            ap = AttributePrototype.objects.get(pk=da.attribute_prototype_id)
            device = da.device

            # 是否发送到设备
            if send_to_device:
                device.send_ctrls(None, {ap.identifier: value})

            self.__save(da, serializer.validated_data)

            # 如果更新的是 Qty，子设备数量发生变化
            if ap.identifier.endswith('Qty'):
                prev_count = int(prev_value) if prev_value else 0
                cur_count = int(da.value) if da.value else 0
                Terminal.objects.handle_qty_change(device, ap, prev_count, cur_count)

            DeviceAttribute.objects.save_to_redis(device, da)
            TerminalAttribute.objects.update_with_da(da, project_id=device.project_id)
            # if 'NickName' in ap.identifier or ap.identifier.endswith('ShowEN'):
            #     idx = re.findall(r'\d+', ap.identifier)
            #     if len(idx):
            #         idx = int(idx[0])
            #         hide_terminals = Terminal.objects.filter(device=device, idx=idx)
            #         if hide_terminals.exists():
            #             if hide_terminals.count() > 1:
            #                 prefix = ap.identifier.split('_')[0]
            #                 hide_terminals = hide_terminals.filter(prefix=prefix)
            #             terminal = hide_terminals.first()
            #
            #             if 'NickName' in ap.identifier:
            #                 terminal.nick_name = da.value
            #
            #             if ap.identifier.endswith('ShowEN'):
            #                 if not da.value or da.value == '0':
            #                     terminal.show_en = False
            #                 else:
            #                     terminal.show_en = True
            #
            #             terminal.save()

        except AttributePrototype.DoesNotExist:
            self.__save(da, serializer.validated_data)
            pass

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def destroy(self, request, pk=None, **kwargs):
        device_id = request.data.get('device_id', None)
        attribute_prototype_id = request.data.get('attribute_prototype_id', None)

        if device_id is not None and attribute_prototype_id is not None:
            device_attribute = DeviceAttribute.objects.filter(device_id=device_id,
                                                              attribute_prototype_id=attribute_prototype_id)
            device_attribute.delete()

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def retrieve(self, request, *args, **kwargs):
        da = DeviceAttribute.objects.get(pk=kwargs.get('pk'))
        device = da.device

        from_at = request.query_params.get('from', None)
        till_at = request.query_params.get('till', None)
        page = request.query_params.get('page', None)
        per_page = request.query_params.get('per_page', None)
        interval = request.query_params.get('interval', 0)
        from_snap = request.query_params.get('snap', None)
        fill_gap = request.query_params.get('fill_gap', None)
        if from_snap:
            series, count = DeviceAttributeViewV5.query_snaps(self, da, from_at, till_at, page, per_page, interval, fill_gap)
        else:
            series, count = DeviceAttributeViewV5.query_stats(self, da, from_at, till_at, page, per_page, interval)

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                "device_id": device.id,
                'nick_name': device.nick_name,
                'series': series
            },
            'total': count
        })

    def __save(self, object, validated_data):
        for key, value in validated_data.items():
            if key == 'show_in_list' and value is None:
                value = 0

            if key == 'id':
                continue

            try:
                setattr(object, key, value)
            except AttributeError:
                pass

        if object.created_at is None:
            setattr(object, 'created_at', datetime.datetime.now())

        object.save()


class DeviceProtocolViewSet(viewsets.GenericViewSet):
    authentication_classes = (IntapiAuth,)

    def list(self, request):
        mac = request.query_params.get('mac', None)
        reat_time = request.query_params.get('rt', None)
        topic = request.query_params.get('topic', None)

        if mac is None:
            raise exceptions.ValidationError(detail={'detail': 'mac是必须的！'})

        device = Device.objects.filter(mac=mac).first()

        devices = None

        # 如果找不到就用模糊查询虚拟mac
        if device is None:
            devices = Device.objects.filter(mac__contains=mac)
            device = devices.first()

            if len(devices) == 0:
                raise exceptions.ValidationError(detail={'detail': '无效的mac！'})

        confs = []
        # Modbus DTU仪表处理，根据表头选择（型号）筛选协议配置，替换slaveid为对应仪表的站号，添加idx参数作为仪表号
        if (device.device_prototype.uni_name == 'DTU三相电表网关'
                or device.device_prototype.uni_name == '流量计网关'
                or device.device_prototype.uni_name == 'PM800电表网关'):

            meter_qty = 0

            qty_da = DeviceAttribute.objects.query_object_by_idf(device, "MeterQty")
            if qty_da is not None:
                meter_qty = int(qty_da.value)
            # meter_qty = int(DeviceAttribute.objects.get_by_idf(device, "MeterQty").value)

            if meter_qty != 0:
                for idx in range(1, meter_qty + 1):
                    meter_type = device.get_value_by_idf("MeterType_" + str(idx))

                    if meter_type and meter_type != "无":
                        tmp_confs = DeviceProtocol.objects.filter(model_no=meter_type, in_use=True)
                        for conf in tmp_confs:
                            new_conf = {
                                'id': conf.id,
                                'p_type': conf.p_type,
                                'name': conf.name,
                                'identifier': conf.identifier,
                                'ro': conf.ro,
                                'slaveid': device.get_value_by_idf("MeterAddr_" + str(idx)),
                                'idx': str(idx),
                                'addr': conf.addr,
                                'data_type': conf.data_type,
                                'data_len': conf.data_len,
                                'devider': conf.devider,
                                'func_code': conf.func_code,
                                'prefix': conf.prefix,
                                'suffix': conf.suffix,
                                'read_time': conf.read_time,
                                'objid': conf.objid,
                                'insid': conf.insid,
                                'resid': conf.resid,
                                'position': conf.position,
                                'topic': conf.topic,
                                'topic_set': conf.topic_set,
                                'sp_defs': conf.sp_defs,
                                'w_code': conf.w_code,
                                'w_addr': conf.w_addr
                            }

                            confs.append(new_conf)
        elif device.device_prototype.uni_name == 'HY温压仪网关' or device.device_prototype.uni_name == 'NY温压仪网关':
            tp_qty = int(device.get_value_by_idf("TPQty"))

            for idx in range(1, tp_qty + 1):
                tmp_confs = DeviceProtocol.objects.filter(
                    device_prototype_id=device.device_prototype_id,
                    in_use=True,
                    addr__isnull=False
                )

                for conf in tmp_confs:
                    new_conf = {
                        'id': conf.id,
                        'p_type': conf.p_type,
                        'name': conf.name,
                        'identifier': conf.identifier,
                        'ro': conf.ro,
                        'slaveid': device.get_value_by_idf('TPAddr_' + str(idx)),
                        'idx': str(idx),
                        'addr': conf.addr,
                        'data_type': conf.data_type,
                        'data_len': conf.data_len,
                        'devider': conf.devider,
                        'func_code': conf.func_code,
                        'prefix': conf.prefix,
                        'suffix': conf.suffix,
                        'read_time': conf.read_time,
                        'objid': conf.objid,
                        'insid': conf.insid,
                        'resid': conf.resid,
                        'position': conf.position,
                        'topic': conf.topic,
                        'topic_set': conf.topic_set,
                        'sp_defs': conf.sp_defs,
                        'w_code': conf.w_code,
                        'w_addr': conf.w_addr
                    }

                    confs.append(new_conf)

        elif device.device_prototype.uni_name == 'CSP2000网关':
            csp_qty = int(device.get_value_by_idf("CSPQty"))

            for idx in range(1, csp_qty + 1):
                tmp_confs = DeviceProtocol.objects.filter(
                    device_prototype_id=device.device_prototype_id,
                    in_use=True,
                    addr__isnull=False
                )

                for conf in tmp_confs:
                    new_conf = {
                        'id': conf.id,
                        'p_type': conf.p_type,
                        'name': conf.name,
                        'identifier': conf.identifier,
                        'ro': conf.ro,
                        'slaveid': device.get_value_by_idf('CSPAddr_' + str(idx)),
                        'idx': str(idx),
                        'addr': conf.addr,
                        'data_type': conf.data_type,
                        'data_len': conf.data_len,
                        'devider': conf.devider,
                        'func_code': conf.func_code,
                        'prefix': conf.prefix,
                        'suffix': conf.suffix,
                        'read_time': conf.read_time,
                        'objid': conf.objid,
                        'insid': conf.insid,
                        'resid': conf.resid,
                        'position': conf.position,
                        'topic': conf.topic,
                        'topic_set': conf.topic_set,
                        'sp_defs': conf.sp_defs,
                        'w_code': conf.w_code,
                        'w_addr': conf.w_addr
                    }

                    confs.append(new_conf)

        elif device.device_prototype.uni_name == 'VRV空调网关(DTU)':
            # DTU VRV空调采用虚拟Mac
            if devices is not None:
                for device in devices:
                    indoorunit_qty = int(device.get_value_by_idf("IndoorUnitQty"))

                    for idx in range(1, indoorunit_qty + 1):
                        tmp_confs = DeviceProtocol.objects.filter(
                            device_prototype_id=device.device_prototype_id,
                            identifier__startswith=''.join(['IndoorUnit_', str(idx), '_']),
                            in_use=True,
                            addr__isnull=False
                        )

                        for conf in tmp_confs:
                            new_conf = {
                                'id': conf.id,
                                'p_type': conf.p_type,
                                'name': conf.name,
                                'identifier': conf.identifier,
                                'ro': conf.ro,
                                'slaveid': device.get_value_by_idf('SlaveId'),
                                'idx': str(idx),
                                'addr': conf.addr,
                                'data_type': conf.data_type,
                                'data_len': conf.data_len,
                                'devider': conf.devider,
                                'func_code': conf.func_code,
                                'prefix': conf.prefix,
                                'suffix': conf.suffix,
                                'read_time': conf.read_time,
                                'objid': conf.objid,
                                'insid': conf.insid,
                                'resid': conf.resid,
                                'position': conf.position,
                                'topic': conf.topic,
                                'topic_set': conf.topic_set,
                                'sp_defs': conf.sp_defs,
                                'w_code': conf.w_code,
                                'w_addr': conf.w_addr
                            }

                            confs.append(new_conf)

        else:
            cols = DeviceProtocol.objects.filter(device_prototype_id=device.device_prototype_id, in_use=True)
            confs = DeviceProtocolSerializer(cols, many=True).data

            if reat_time is not None:
                confs.filter(read_time=reat_time)

            if topic is not None:
                confs.filter(topic=topic)

        segments = []
        device_prototype = None

        if device is not None:
            dp = device.device_prototype
            segments = AddrSegmentSerializer(dp.addrsegment_set.all(), many=True).data

            device_prototype = {
                'id': dp.id,
                'name': dp.name,
                'uni_name': dp.uni_name
            }

        res_data = {
            'status': status.HTTP_200_OK,
            'total': len(confs),
            'data': {
                'device_protocols': confs,
                'segments': segments,
                'device_prototype': device_prototype,
                'master_ip': device.get_value_by_idf("MasterIp"),
                'master_port': device.get_value_by_idf("MasterPort")
            }
        }

        return Response(res_data)


class EcUserViewSet(viewsets.ModelViewSet):
    authentication_classes = (IntapiAuth,)

    def create(self, request, *args, **kwargs):
        serializer = EcUserSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        web_user = WebUser()
        web_user.last_login = datetime.datetime.now()

        for key, value in serializer.validated_data.items():
            if key == 'is_super' and value is None:
                value = False

            if key == 'status' and value is None:
                value = 10

            if key == 'password':
                value = make_password(value)

            if key == 'project_id':
                continue

            setattr(web_user, key, value)

        # 生成 ec_token
        raw_token = AuthUtils.generate_random_char(32)
        base64_token = str(base64.b64encode(raw_token.encode('ascii')), 'ascii')
        web_user.ec_token = base64_token[:-2]

        # 用户状态
        if web_user.status is None:
            web_user.status = 10

        web_user.save()
        # UserProject(web_user_id=web_user.id, project_id=request.data['project_id']).save()

        return Response(status=status.HTTP_200_OK,
                        data={
                            'status': status.HTTP_200_OK,
                            'web_user': WebUserSerializer(web_user).data
                        })


class MessageViewSet(viewsets.ModelViewSet):
    authentication_classes = (IntapiAuth,)
    serializer_class = MessageSerializer

    def get_queryset(self):
        msg_types = self.request.query_params.get('msg_types', None)
        search = self.request.query_params.get('search', None)
        from_at = self.request.query_params.get('from', None)
        till_at = self.request.query_params.get('till', None)

        query_set = Message.objects.all()

        if msg_types is not None:
            types = list(map(lambda x: int(x), msg_types.split(',')))
            query_set = query_set.filter(msg_type__in=types)
        if search is not None:
            query_set = query_set.filter(content__contains=search)
        if from_at is not None and till_at is not None:
            begin = datetime.datetime.strptime(from_at, '%Y%m%d%H%M%S')
            end = datetime.datetime.strptime(till_at, '%Y%m%d%H%M%S')
            query_set = query_set.filter(created_at__range=[begin, end])

        return query_set

    def list(self, request, *args, **kwargs):
        result = super(MessageViewSet, self).list(request, *args, **kwargs).data
        return Response({
            'status': status.HTTP_200_OK,
            'rows': result['results'],
            'total': self.get_queryset().count()
        })

    def create(self, request, *args, **kwargs):
        project_id = request.user['project_id']
        uid = request.data.get('uid', None)
        msg_type = request.data.get('msg_type', 1)
        content = request.data.get('content', None)
        if uid is None:
            return Response(status=status.HTTP_400_BAD_REQUEST,
                            data={'status': status.HTTP_400_BAD_REQUEST, 'error': 'uid 不能为空'})
        if Message.objects.filter(uid=uid).exists():
            return Response(status=status.HTTP_400_BAD_REQUEST,
                            data={'status': status.HTTP_400_BAD_REQUEST, 'error': 'uid 已存在'})
        if content is None:
            return Response(status=status.HTTP_400_BAD_REQUEST,
                            data={'status': status.HTTP_400_BAD_REQUEST, 'error': 'content 不能为空'})
        Message.objects.system_message(msg_type, project_id, content, uid)
        data = {
            'content': content
        }
        Notification.objects.notify(msg_type, device=None, data=data, uid=uid)
        return Response({'status': status.HTTP_200_OK, 'data': None})

    def destroy(self, request, *args, **kwargs):
        uid = kwargs.get('uid')
        message = Message.objects.filter(uid=uid)
        if not message.exists():
            return Response(status=status.HTTP_400_BAD_REQUEST, data={'error': 'uid 不存在'})
        message.delete()
        return Response({'status': status.HTTP_200_OK, 'data': None})


class SnpVarViewSet(viewsets.GenericViewSet):
    authentication_classes = (IntapiAuth,)

    def create(self, request):
        identifier = request.data.get('identifier', None)
        var_type = request.data.get('var_type', None)

        snp_var = None

        if identifier is not None and var_type is not None:
            snp_var = SnpVar.objects.filter(identifier=identifier, var_type=var_type).first()

        if snp_var is None:
            serializer = SnpVarSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            snp_var = SnpVar()
            self.__save(snp_var, serializer.validated_data)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def destroy(self, request, pk=None):
        identifier = request.data.get('identifier', None)
        var_type = request.data.get('var_type', None)

        if identifier is not None and var_type is not None:
            snp_var = SnpVar.objects.get(identifier=identifier, var_type=var_type)
            snp_var.delete()

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def __save(self, object, validated_data):
        for key, value in validated_data.items():
            try:
                setattr(object, key, value)
            except AttributeError:
                pass

        object.save()


class ParserTrialView(viewsets.ModelViewSet):
    authentication_classes = (IntapiAuth,)

    def create(self, request, *args, **kwargs):
        dp_id = request.data.get('dp_id', None)
        parse_type = request.data.get('type', 'down')
        to_device = request.data.get('to_device', False)
        data = None
        if dp_id is not None:
            dp = DevicePrototype.objects.get(id=dp_id)

            # data = dp.parse_down_data(db='syadmindb') if parse_type == 'down' else dp.parse_up_data(db='syadmindb')
            data = dp.parse_down_data() if parse_type == 'down' else dp.parse_up_data()
            if not isinstance(data, dict):
                return Response({
                    'status': status.HTTP_400_BAD_REQUEST,
                    'data': data,
                    'error': '返回结果不是json对象'
                })

        if to_device:
            if parse_type == 'up':
                DeviceEvent.objects.cus_create(data)
            else:
                pass

        return Response({
            'status': status.HTTP_200_OK,
            'data': data
        })


class DeviceIssueView(viewsets.ModelViewSet):
    authentication_classes = (IntapiAuth,)
    serializer_class = DeviceIssueSerializer

    def get_queryset(self):
        queryset = DeviceIssue.objects.all()
        from_at = self.request.query_params.get('from', None)
        till_at = self.request.query_params.get('till', None)

        if from_at is not None and till_at is not None:
            from_at = datetime.datetime.strptime(from_at, '%Y%m%d%H%M%S')
            till_at = datetime.datetime.strptime(till_at, '%Y%m%d%H%M%S')
            queryset = queryset.filter(created_at__range=(from_at, till_at))

        is_solved = self.request.query_params.get('is_solved', None)
        if is_solved is not None:
            queryset = queryset.filter(is_solved=is_solved)

        search = self.request.query_params.get('search', None)
        if search is not None:
            queryset = queryset.filter(display_name__contains=search)

        issue_type = self.request.query_params.get('issue_type', None)
        if issue_type is not None:
            queryset = queryset.filter(issue_type=issue_type)

        mac = self.request.query_params.get('mac', None)
        if mac is not None:
            queryset = queryset.filter(device__mac__icontains=mac)

        return queryset

    def list(self, request, *args, **kwargs):
        issues = super(DeviceIssueView, self).list(request, *args, **kwargs).data.get('results', None)
        return Response({
            'status': status.HTTP_200_OK,
            'rows': issues,
            'total': self.get_queryset().count()
        })

    def retrieve(self, request, *args, **kwargs):
        issue = super(DeviceIssueView, self).retrieve(request, *args, **kwargs).data
        return Response({
            'status': status.HTTP_200_OK,
            'row': issue
        })

class DevicePrototypeRatioViewSet(viewsets.ModelViewSet):
    authentication_classes = (IntapiAuth,)
    serializer_class = DevicePrototypeRatioSerializer

    def get_queryset(self):
        queryset = DevicePrototypeRatio.objects.all()

        dp_id = self.request.query_params.get('dp_id', None)
        if dp_id is not None:
            queryset = queryset.filter(device_prototype_id=dp_id)

        return queryset

    def list(self, request, *args, **kwargs):
        dp_ratios = super(DevicePrototypeRatioViewSet, self).list(request, *args, **kwargs).data.get('results', None)
        return Response({
            'status': status.HTTP_200_OK,
            'rows': dp_ratios,
            'total': self.get_queryset().count()
        })

    def create(self, request, *args, **kwargs):
        dp_ratio = super(DevicePrototypeRatioViewSet, self).create(request, *args, **kwargs).data
        return Response({
            'status': status.HTTP_200_OK,
            'row': dp_ratio
        })

    def partial_update(self, request, *args, **kwargs):
        dp_id = kwargs.get('pk')
        dp_ratio = DevicePrototypeRatio.objects.filter(device_prototype_id=dp_id)
        if not dp_ratio.exists():
            dp_ratio = DevicePrototypeRatio.objects.create(
                device_prototype_id=dp_id,
                params=request.data.get('params')
            )
        else:
            dp_ratio = dp_ratio.last()
            dp_ratio.params = request.data.get('params')
            dp_ratio.save()

        serializer = DevicePrototypeRatioSerializer(dp_ratio)

        return Response({
            'status': status.HTTP_200_OK,
            'row': serializer.data
        })

    def retrieve(self, request, *args, **kwargs):
        ratio = DevicePrototypeRatio.objects.filter(device_prototype_id=kwargs.get('pk'))
        if ratio.exists():
            serializer = DevicePrototypeRatioSerializer(ratio.first())
            row = serializer.data
        else:
            row = None

        return Response({
            'status': status.HTTP_200_OK,
            'row': row
        })

    def destroy(self, request, *args, **kwargs):
        super(DevicePrototypeRatioViewSet, self).destroy(request, *args, **kwargs)
        return Response(status=status.HTTP_200_OK, data={
            'status': status.HTTP_200_OK,
            'data': None
        })


class TerminalView(viewsets.ModelViewSet):
    authentication_classes = (IntapiAuth,)
    serializer_class = TerminalSerializer

    def get_queryset(self):
        queryset = Terminal.objects.all()

        dt_id = self.request.query_params.get("dt_id", None)
        dp_id = self.request.query_params.get("dp_id", None)
        device_id = self.request.query_params.get('device_id', None)
        terminal_type = self.request.query_params.get('terminal_type', None)
        terminal_label = self.request.query_params.get("terminal_label", None)
        search = self.request.query_params.get('search', None)

        if device_id is not None:
            queryset = queryset.filter(device_id=device_id)
        if dt_id is not None:
            dt_ids = dt_id.split(',')
            queryset = queryset.filter(device__device_type_id__in=dt_ids)
        if dp_id is not None:
            dp_ids = dp_id.split(',')
            c_dp = DevicePrototype.objects.filter(parent_id__in=dp_ids)
            queryset = queryset.filter(device_prototype_id__in=dp_ids + [dp.id for dp in c_dp])
        if terminal_type is not None:
            queryset = queryset.filter(terminal_type__in=terminal_type.split(','))
        if terminal_label is not None:
            queryset = queryset.filter(terminal_label=terminal_label)
        if search is not None:
            queryset = queryset.filter(nick_name__contains=search)

        return queryset

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()

        terminals = TerminalSerializer(self.paginate_queryset(queryset), many=True).data
        total = queryset.count()

        dp_cache = {}
        device_cache = {}
        for terminal in terminals:
            dp_id = terminal.get('device_prototype')
            device_id = terminal.get('device')

            dp = dp_cache.get(dp_id, None)
            device_data = device_cache.get(device_id, None)

            if dp is None:
                dp = DevicePrototype.objects.get(pk=dp_id)
                dp_cache[dp_id] = dp

            if device_data is None:
                device = Device.objects.get(pk=device_id)
                device_data = SimpleDeviceSerializer(device).data
                device_cache[device_id] = device_data
            terminal['device'] = device_data

            terminal['custz_detail'] = bool(dp.web_content)
            terminal['device_prototype'] = dp.name

        return Response({
            'status': status.HTTP_200_OK,
            'terminals': terminals,
            'total': total,
        })

    def create(self, request, *args, **kwargs):
        data = request.data
        device = Device.objects.get(pk=data.get('device'))
        data['online'] = device.online
        data['sw_on'] = device.sw_on
        if data.get('device_prototype', None) is None:
            data['device_prototype'] = device.device_prototype_id
        if data.get('nick_name', None) is None:
            data['nick_name'] = device.nick_name
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)

        return Response({
            'status': status.HTTP_200_OK,
            'row': serializer.data
        })

    def partial_update(self, request, *args, **kwargs):
        terminal = get_object_or_404(Terminal, pk=kwargs.get('pk'))
        serializer = self.get_serializer(terminal, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)

        for k, v in request.data.items():
            setattr(terminal, k, v)

        # intapi更新终端字段时，只更新请求带的data，不自动更新updated_at
        terminal.save(update_fields=request.data.keys())

        return Response({
            'status': status.HTTP_200_OK,
            'row': self.serializer_class(terminal).data
        })

    def destroy(self, request, *args, **kwargs):
        super().destroy(request, *args, **kwargs)

        return Response({
            'status': status.HTTP_200_OK,
            'row': None
        })

class SySimView(viewsets.ModelViewSet):
    authentication_classes = (IntapiAuth,)
    serializer_class = SySimSerializer

    def get_object(self):
        obj_id = self.kwargs.get('id')
        return get_object_or_404(SySim, Q(iccid=obj_id) | Q(imei=obj_id) | Q(id=obj_id if obj_id.isdigit() else 0))

    def get_queryset(self):
        queryset = SySim.objects.all()

        # iccid, imei 或 备注 搜索
        search = self.request.query_params.get('search', None)
        if search is not None:
            queryset = queryset.filter(Q(iccid__icontains=search) | Q(imei__icontains=search) | Q(remark__icontains=search))

        # imei 精准搜索
        imei = self.request.query_params.get('imei', None)
        if imei is not None:
            queryset = queryset.filter(imei=imei)

        # iccid 精准搜索
        iccid = self.request.query_params.get('iccid', None)
        if iccid is not None:
            queryset = queryset.filter(iccid=iccid)

        # 状态搜索
        status = self.request.query_params.get('status', None)
        if status is not None:
            queryset = queryset.filter(status=status)
        # 开卡平台
        platform = self.request.query_params.get('platform', None)
        if platform is not None:
            queryset = queryset.filter(platform=platform)

        # 激活日期
        activated_from = self.request.query_params.get('activated_from', None)
        activated_till = self.request.query_params.get('activated_till', None)
        if activated_from is not None and activated_till is not None:
            from_dt = datetime.datetime.strptime(activated_from, '%Y%m%d%H%M%S')
            till_dt = datetime.datetime.strptime(activated_till, '%Y%m%d%H%M%S')
            queryset = queryset.filter(activated_at__range=[from_dt, till_dt])
        # 开卡日期
        opened_from = self.request.query_params.get('opened_from', None)
        opened_till = self.request.query_params.get('opened_till', None)
        if activated_from is not None and activated_till is not None:
            from_dt = datetime.datetime.strptime(opened_from, '%Y%m%d%H%M%S')
            till_dt = datetime.datetime.strptime(opened_till, '%Y%m%d%H%M%S')
            queryset = queryset.filter(opened_at__range=[from_dt, till_dt])

        project_id = self.request.query_params.get('project_id', None)
        if project_id is not None:
            mac = Device.objects.filter(project_id=project_id).values_list('mac', flat=True)
            queryset = queryset.filter(imei__in=mac)

        dp_id = self.request.query_params.get('dp_id', None)
        if dp_id is not None:
            mac = Device.objects.filter(device_prototype_id=dp_id).values_list('mac', flat=True)
            queryset = queryset.filter(imei__in=mac)

        return queryset

    def list(self, request, *args, **kwargs):
        data = super().list(request, *args, **kwargs).data

        return Response({
            'status': status.HTTP_200_OK,
            "rows": data['results'],
            'total': data['count']
        })

    def retrieve(self, request, *args, **kwargs):
        data = super().retrieve(request, *args, **kwargs).data

        return Response({
            'status': status.HTTP_200_OK,
            'row': data
        })

    def create(self, request, *args, **kwargs):
        data = request.data
        iccid = data.get('iccid', None)
        if iccid is None:
            raise exceptions.ValidationError(detail={'detail': 'iccid is required!'})

        sy_sim = SySim.objects.filter(iccid=iccid).last()
        if sy_sim is not None:
            serializer = SySimSerializer(sy_sim, data=request.data, partial=True)
            serializer.is_valid(raise_exception=True)
            self.__save(sy_sim, serializer.validated_data)

        else:
            serializer = SySimSerializer(data=data)
            if serializer.is_valid(raise_exception=True):
                sy_sim = SySim()
                self.__save(sy_sim, serializer.validated_data)

        return Response({
            'status': status.HTTP_200_OK,
            'sim': serializer.data
        })

    def partial_update(self, request, *args, **kwargs):
        sy_sim = self.get_object()

        serializer = SySimSerializer(sy_sim, request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        self.__save(sy_sim, serializer.validated_data)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def destroy(self, request, *args, **kwargs):
        super().destroy(request, *args, **kwargs)
        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def __save(self, obj, validated_data):
        for k, v in validated_data.items():
            try:
                setattr(obj, k, v)

            except AttributeError:
                pass

        if obj.created_at is None:
            setattr(obj, 'created_at', datetime.datetime.now())

        obj.save()

class ProjectSettingView(viewsets.ModelViewSet):
    authentication_classes = (IntapiAuth,)

    def update(self, request, *args, **kwargs):
        project_id = request.user['project_id']
        project = Project.objects.get(pk=project_id)
        project_settings = json.loads(project.settings) if project.settings else {}

        new_settings = request.data.get('settings', None)

        if type(new_settings).__name__ == 'dict':
            project_settings = {**project_settings, **new_settings}

            project.settings = json.dumps(project_settings)
            project.save()

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })


class RedisDataView(viewsets.ModelViewSet):
    authentication_classes = (IntapiAuth,)

    def retrieve(self, request, *args, **kwargs):
        project_id = request.user['project_id']
        label = request.query_params.get('label', None)
        name = kwargs.get('name')

        if label is not None:
            name = f'{label}:{name}'

        is_native = 'mini_user' in name
        result = RedisHelper.get_value(project_id, name, is_native)

        if result is not None and not isinstance(result, dict):
            result = object_to_dict(result)

        return Response({
            'status': status.HTTP_200_OK,
            'row': {
                label: result
            }
        })

    def destroy(self, request, *args, **kwargs):
        project_id = request.user['project_id']
        label = request.query_params.get('label', None)
        name = kwargs.get('name')

        if label is not None:
            name = f'{label}:{name}'

        RedisHelper.delete(project_id, name)

        return Response({
            'status': status.HTTP_200_OK,
            'row': None
        })


class DrfAssetsView(viewsets.ModelViewSet):
    authentication_classes = (IntapiAuth,)

    def create(self, request, *args, **kwargs):
        file = request.FILES.get('file', None)
        name = request.data.get('name', 'example.xlsx')
        if file is not None:
            path = os.path.join(settings.BASE_DIR, f'drf-assets/exports/{name}')
            with open(path, 'wb+') as des:
                for chunk in file.chunks():
                    des.write(chunk)
        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })
    
class UserStatView(viewsets.ModelViewSet):
    serializer_class = UserStatSerializer

    def get_queryset(self):

        queryset = UserStat.objects.all()

        period = self.request.query_params.get('period', None)
        if period is not None:
            queryset = queryset.filter(periods=period)

        from_at = self.request.query_params.get('from', None)
        if from_at is not None:
            from_at = datetime.datetime.strptime(from_at, '%Y%m%d%H%M%S')
            queryset = queryset.filter(target_dt__gte=from_at)

        till_at = self.request.query_params.get('till', None)
        if till_at is not None:
            till_at = datetime.datetime.strptime(till_at, '%Y%m%d%H%M%S')
            queryset = queryset.filter(target_dt__lte=till_at)

        uid = self.request.query_params.get('uid', None)
        if uid is not None:
            queryset = queryset.filter(web_user_id=uid)

        return queryset
        
    def list(self, request, *args, **kwargs):
        data = super().list(request, *args, **kwargs).data

        return Response({
            'status': status.HTTP_200_OK,
            "data": {
                'user_stats': data['results']
            },
            'total': data['count']
        })

class SnapshotView(viewsets.ModelViewSet):
    authentication_classes = (IntapiAuth,)

    def list(self, request, *args, **kwargs):
        project_id = request.user['project_id']
        device_id = request.query_params.get('device_id', None)
        if device_id is None:
            raise exceptions.ValidationError(detail={'detail': 'device_id is required!'})

        ap_ids = DeviceAttribute.objects.filter(device_id=device_id).values_list('attribute_prototype_id', flat=True)
        aps = AttributePrototype.objects.filter(id__in=ap_ids)

        identifier = request.query_params.get('identifier', None)
        if identifier is not None:
            if ' ' in identifier:
                identifiers = list(filter(None, identifier.split(' ')))
                idf_query = Q()
                for idf in identifiers:
                    idf_query |= Q(identifier__icontains=idf)
                aps = aps.filter(idf_query)
            else:
                aps = aps.filter(identifier__icontains=identifier)

        search = request.query_params.get('search', None)
        if search is not None:
            if ' ' in search:
                names = list(filter(None, search.split(' ')))
                name_query = Q()
                for name in names:
                    name_query |= Q(name__icontains=name)
                aps = aps.filter(name_query)
            else:
                aps = aps.filter(name__icontains=search)

        readonly = request.query_params.get('readonly', None)
        if readonly is not None:
            aps = aps.filter(read_only=readonly)

        at_id = request.query_params.get('at_id', None)
        if at_id is not None:
            aps = aps.filter(attribute_type_id=at_id)

        total = aps.count()
        aps = self.paginate_queryset(aps)
        ap_ids = [ap.id for ap in aps]

        dt = request.query_params.get('snapshot_dt', None)
        if dt is not None:
            dt = datetime.datetime.strptime(dt, '%Y%m%d%H%M%S')

        snapshots = DaSnapshot.objects.get_latest_snapshots(project_id, device_id, ap_ids, dt)

        results = []

        for ap in aps:
            snap = next(filter(lambda x: x.attribute_prototype_id == ap.id, snapshots), None)
            if snap is None:
                result = {
                    "id": None,
                    "attribute_prototype_id": ap.id,
                    "value": None,
                    "snapped_at": None,
                    "updated_at": None,
                    "device_id": device_id
                }
            else:
                result = DaSnapshotSerializer(snap).data
            result['attribute_prototype'] = AttributePrototypeSerializer(ap).data
            results.append(result)

        return Response({
            'status': status.HTTP_200_OK,
            'rows': results,
            'total': total
        })

class DaSnapshotHistoryView(viewsets.ModelViewSet):
    authentication_classes = (IntapiAuth,)

    def list(self, request, *args, **kwargs):
        now = datetime.datetime.now()
        device_id = request.query_params.get('device_id', None)
        if device_id is None:
            raise exceptions.ValidationError(detail={'detail': 'device_id is required!'})
        device = Device.objects.get(pk=device_id)

        ap_ids = request.query_params.get('ap_ids', None)
        if ap_ids is None:
            raise exceptions.ValidationError(detail={'detail': 'ap_ids is required!'})
        else:
            ap_ids = ap_ids.split(',')

        from_at = request.query_params.get('from', None)
        if from_at is None:
            from_at = (now - datetime.timedelta(days=2)).replace(hour=0, minute=0, second=0, microsecond=0)
        else:
            from_at = datetime.datetime.strptime(from_at, '%Y%m%d%H%M%S')
        till_at = request.query_params.get('till', None)
        if till_at is None:
            till_at = now
        else:
            till_at = datetime.datetime.strptime(till_at, '%Y%m%d%H%M%S')

        # 是否填充缺失的分钟数据
        fill_gap = request.query_params.get('fill_gap', None)
        fill_gap = fill_gap in ['1', 'y', 'yes']

        multi_series = []
        for ap_id in ap_ids:
            values = []
            snaps = list(DaSnapshot.objects.filter(device_id=device_id, attribute_prototype_id=ap_id,
                                                   snapped_at__range=[from_at, till_at]).order_by('snapped_at'))
            snap_histories = None
            if (now.date() - from_at.date()).days > 1:
                snap_histories = list(DaSnapshotHistory.objects.filter(device_id=device_id, attribute_prototype_id=ap_id,
                                                                       snapped_at__range=[from_at, till_at]).order_by('snapped_at'))
                before_snap = DaSnapshotHistory.objects.filter(device_id=device_id, attribute_prototype_id=ap_id,
                                                               snapped_at__lte=from_at).order_by('-snapped_at').first()
            else:
                before_snap = DaSnapshot.objects.filter(device_id=device_id, attribute_prototype_id=ap_id,
                                                        snapped_at__lte=from_at).order_by('-snapped_at').first()
            if before_snap is not None:
                values.append({'value': before_snap.value, 'time': from_at})
            if snap_histories:
                values += [{'value': snap.value, 'time': snap.snapped_at} for snap in snap_histories]
            values += [{'value': snap.value, 'time': snap.snapped_at} for snap in snaps]

            if len(values):
                # till_at 是当前时间才在最后添加一个当前时间的值
                if till_at.date() == now.date() and till_at.hour == now.hour and till_at.minute == now.minute:
                    values.append({'value': values[-1]['value'], 'time': now})

            fill_values = []
            if fill_gap and len(values):
                prev_value = None
                prev_time = None
                for snap in values:
                    time = snap['time']
                    value = snap['value']
                    if prev_value is None:
                        prev_value = value
                        prev_time = time
                        fill_values.append(snap)
                    else:
                        next_minute = prev_time + datetime.timedelta(minutes=1)
                        while next_minute < time:
                            fill_values.append({'value': prev_value, 'time': next_minute})
                            prev_time = next_minute
                            next_minute = prev_time + datetime.timedelta(minutes=1)
                        fill_values.append(snap)
                        prev_value = snap['value']
                        prev_time = snap['time']

            ap = AttributePrototype.objects.get(pk=ap_id)
            series = {
                'id': ap.id,
                'name': ap.name,
                'label': ap.label,
                'identifier': ap.identifier,
                'data_type': ap.data_type,
                'options': ap.options,
                'unit': ap.unit,
                'values': fill_values if (fill_gap and len(fill_values)) else values
            }
            multi_series.append(series)

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'device_id': device.id,
                'nick_name': device.nick_name,
                'multi_series': multi_series
            },
        })

class BulkUpdateAttribute(viewsets.ViewSet):
    authentication_classes = (IntapiAuth,)
    
    """ 批量更新设备参数 data格式：{'mac': 'xxxx', 'data': [{'idf': 'xxx', 'value': 'xxx'}]}"""
    def create(self, request):
        data = request.data.get('data', None)
        mac = request.data.get('mac', None)

        device = None

        if not mac:
            raise exceptions.ValidationError('参数不完整！')

        try:
            device = Device.objects.filter(mac=mac).get()

        except Exception as e:
            raise exceptions.NotFound('找不到设备!')

        if data:
            try:
                json.dumps(data)
            except Exception:
                raise exceptions.ValidationError('data参数不合法！')

        for param in data:

            aps = AttributePrototype.objects.filter(device_prototype_id=device.device_prototype_id, identifier=param['idf'])

            if aps:
                ap = aps.get()

                das = DeviceAttribute.objects.filter(device_id=device.id, attribute_prototype_id=ap.id)

                if das:
                    da = das.get()
                    da.value = param['value']
                    da.save()
                    DeviceAttribute.objects.save_to_redis(device, da)

        res_data = {
            'status': status.HTTP_200_OK,
            'data': None
        }

        return Response(res_data)


class DeviceHistoryDataExportView(viewsets.ModelViewSet):
    authentication_classes = (IntapiAuth,)

    def create(self, request, *args, **kwargs):
        now = datetime.datetime.now()

        device_ids = request.data.get('device_ids', [])
        devices = Device.objects.filter(id__in=device_ids)
        ap_ids = request.data.get('ap_ids', [])
        aps = AttributePrototype.objects.filter(id__in=ap_ids)
        snap = request.data.get('snap', True)
        from_dt = request.data.get('from', None)
        if from_dt is None:
            from_dt = now.replace(hour=0, minute=0, second=0, microsecond=0)
        else:
            from_dt = datetime.datetime.fromisoformat(from_dt)

        till_dt = request.data.get('till', None)
        if till_dt is None:
            till_dt = now.replace(hour=23, minute=59, second=59)
        else:
            till_dt = datetime.datetime.fromisoformat(till_dt)

        das = DeviceAttribute.objects.select_related('device').filter(device_id__in=device_ids, attribute_prototype_id__in=ap_ids)

        results = defaultdict(dict)

        times = []
        for da in das:
            if snap in [True, 1, '1']:
                series, count = DeviceAttributeViewV5.query_snaps(self, da, from_dt, till_dt, fill_gap='1')
            else:
                series, count = DeviceAttributeViewV5.query_stats(self, da, from_dt, till_dt)

            values = series['values']
            time_value_map = {item['time']: item['value'] for item in values}
            results[da.device_id][da.attribute_prototype_id] = time_value_map
            times.append([value['time'] for value in series['values']])

        # 处理时间
        dt_list = []
        merged = heapq.merge(*times)
        prev_time = None
        for t in merged:
            if t != prev_time:
                dt_list.append(t)
                prev_time = t

        wb = Workbook()
        ws = wb.active
        ws.merge_cells('A1:A2')
        ws['A1'] = '时间'
        ExcelUtils.make_cell_center(ws['A1'])

        cell_border = Border(left=Side(style='thin'))
        # 处理表头
        ap_length = len(aps)
        for device_idx, device in enumerate(devices):
            column_idx = device_idx * ap_length + 2
            device_begin_column = get_column_letter(column_idx)
            device_end_column = get_column_letter(column_idx + ap_length - 1)
            if not ap_length == 1:
                ws.merge_cells(f'{device_begin_column}1:{device_end_column}1')
            ws[f'{device_begin_column}1'] = device.nick_name
            ws[f'{device_begin_column}1'].font = Font(bold=True, sz=12)
            ExcelUtils.make_cell_center(ws[f'{device_begin_column}1'])
            ws[f'{device_begin_column}1'].border = cell_border

            for ap_idx, ap in enumerate(aps):
                ws[f'{get_column_letter(device_idx * ap_length + 2 + ap_idx)}2'] = ap.name
                ExcelUtils.make_cell_center(ws[f'{get_column_letter(device_idx * ap_length + 2 + ap_idx)}2'])
                if ap_idx == 0:
                    ws[f'{get_column_letter(device_idx * ap_length + 2 + ap_idx)}2'].border = cell_border

        ws.freeze_panes = "A3"  # 冻结前两行

        for time_idx, time in enumerate(dt_list):
            ws[f'A{time_idx + 3}'] = time
            for device_idx, device in enumerate(devices):
                for ap_idx, ap in enumerate(aps):
                    try:
                        value = results[device.id][ap.id].get(time, None)
                        if value is not None:
                            column_idx = device_idx * ap_length + ap_idx + 2
                            begin_column = get_column_letter(column_idx)
                            ws[f'{begin_column}{time_idx + 3}'] = value

                    except Exception as e:
                        logging.error(e)

        # 调整宽度
        prev_cell_length = 9
        for idx, col in enumerate(ws.columns):
            max_length = 9

            if idx:
                column_letter = get_column_letter(idx + 1)
                if (idx - 1) % ap_length == 0:
                    try:  # Necessary to avoid error on empty cells
                        label_cell_length = len(str(col[0].value).encode('gbk')) / ap_length
                        idf_cell_length = len(str(col[1].value).encode('gbk'))

                        cell_length = max(label_cell_length, idf_cell_length)
                        if cell_length > max_length:
                            max_length = cell_length

                    except:
                        pass
                    prev_cell_length = max_length
                else:
                    max_length = prev_cell_length
            else:
                column_letter = "A"
                max_length = 20

            adjusted_width = max_length
            ws.column_dimensions[column_letter].width = adjusted_width

        device_names = ",".join(device.nick_name for device in devices)
        ap_names = ",".join(ap.name for ap in aps)
        filename = f'{device_names[:20]}({ap_names[:30]}){AuthUtils.generate_random_comm_char(9)}'
        # 移除文件名中的特殊符号(#)
        filename = sanitize_filename(filename)
        path = f'drf-assets/files/{filename}.xlsx'
        wb.save(filename=path)

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                "filepath": path,
                "filename": filename
            }
        })
