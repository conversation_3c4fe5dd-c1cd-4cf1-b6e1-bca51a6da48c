import datetime

from django.core.management import BaseCommand

from saian_api.act.models import ActColdStat
from saian_api.building.models import Building, Floor, ActiveRoom
from saian_api.coldsource.models import ColdSource, CsEerAnalyse
from saian_api.dashboard.models import ProjectWeather
from saian_api.devdefine.models import AttributePrototype, DeviceType
from saian_api.device.models import Device, DeviceAttribute, ParamRecord, DeviceLimit
from saian_api.dimension.models import DimensionAttribute, DimensionHourlyStat, DimensionDailyStat, DimensionMonthlyStat, \
    DimensionYearlyStat, EsrStat
from saian_api.group.models import Group
from saian_api.issue.models import DeviceIssue
from saian_api.linkage.models import LinkageRule
from saian_api.scheduledtask.utils import set_global_db
from saian_api.terminal.models import Terminal, TerminalAttribute, Category
from saian_api.user.models import WebUser

class Command(BaseCommand):
    help = '删除解除项目绑定的设备'
    __PROJECT_ID = 81
    __INTERVAL = 30

    def __init__(self, stdout=None, stderr=None, no_color=False, force_color=False):
        super().__init__(stdout, stderr, no_color, force_color)
        self.now = datetime.datetime.now()
        self.time_threshold = self.now - datetime.timedelta(minutes=self.__INTERVAL)

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 设置全局数据库
        set_global_db(self.__PROJECT_ID)

        self.sync_cs()
        self.sync_dimension()
        self.sync_cs_eer()
        self.sync_act_cold_stats()
        self.sync_project_weathers()
        self.sync_devices()
        self.sync_buildings()
        self.sync_device_issues()
        self.sync_groups()
        self.sync_device_limits()
        self.sync_linkages()
        self.sync_categories()
        self.sync_esr_stats()

    def sync_categories(self):
        # 终端分类-保利-74
        db = 'prj74db'
        demo_categories = list(Category.objects.values_list('id', flat=True))
        orig_categories = Category.objects.using(db).filter(updated_at__gte=self.time_threshold).exclude(id__in=demo_categories)
        Category.objects.bulk_update(orig_categories, ['name', 'seq'])


    def sync_linkages(self):
        # 设备联动-德兴人医-80
        db = 'prj80db'

        orig_linkages = LinkageRule.objects.using(db).filter(updated_at__gte=self.time_threshold)
        if orig_linkages.exists():
            for linkage in orig_linkages:
                linkage.project_id = self.__PROJECT_ID
                linkage.save()



    def sync_device_limits(self):
        # 末端温度设定-地大-58
        db = 'prj58db'
        orig_limits = list(DeviceLimit.objects.using(db).filter(updated_at__gte=self.time_threshold))
        for limit in orig_limits:
            limit.project_id = self.__PROJECT_ID
        DeviceLimit.objects.bulk_update(orig_limits, ['device_prototype_id', 'limit_type', 'name', 'attribute_prototypes',
                                                      'remark', 'up_value', 'low_value', 'enabled', 'updated_at'])

    def sync_groups(self):
        # 设备分组-黄山百大-70
        db = 'prj70db'

        # 项目成员
        orig_users = list(WebUser.objects.using(db).filter(updated_at__gte=self.time_threshold))
        WebUser.objects.bulk_update(orig_users, ['username', 'name', 'status', 'avatar', 'permissions', 'email', 'mobile', 'last_login', 'updated_at'])

        # 分组
        orig_groups = list(Group.objects.using(db).filter(updated_at__gte=self.time_threshold))
        for group in orig_groups:
            group.project_id = self.__PROJECT_ID
        Group.objects.bulk_update(orig_groups, ['name', 'shared'])




    def sync_device_issues(self):
        # 故障报警，黄山百大-70
        db = 'prj70db'
        demo_issues = list(DeviceIssue.objects.filter(created_at__gte=self.time_threshold).values_list('id', flat=True))
        orig_issues = list(DeviceIssue.objects.using(db).filter(created_at__gte=self.time_threshold).exclude(id__in=demo_issues))

        for issue in orig_issues:
            issue.project_id = self.__PROJECT_ID

        DeviceIssue.objects.bulk_create(orig_issues)

    def sync_buildings(self):
        # 建筑楼层房间-黄山百大-70, 房间终端-德兴人医-80
        projects = [70, 80]
        for project_id in projects:
            db = f'prj{project_id}db'
            for building in Building.objects.using(db).filter(updated_at__gte=self.time_threshold):
                building.project_id = self.__PROJECT_ID
                building.save()

            for floor in Floor.objects.using(db).filter(updated_at__gte=self.time_threshold):
                floor.save()

            for room in ActiveRoom.objects.using(db).filter(updated_at__gte=self.time_threshold):
                room.save()


    def sync_devices(self):
        # 设备列表，黄山百大-70 德兴人医-80 保利-74
        projects = [70, 74, 80]
        for project in projects:
            db = f'prj{project}db'
            devices = Device.objects.using(db).filter(project_id__isnull=False, updated_at__gte=self.time_threshold)
            update_das = []
            update_tas = []
            for device in devices:
                self.sync_device(device, db, update_das, update_tas)
            DeviceAttribute.objects.bulk_update(update_das, ['value', 'updated_at'])
            TerminalAttribute.objects.bulk_update(update_tas, ['value', 'updated_at'])


    def sync_act_cold_stats(self):
        # 末端供冷需求冷量统计使用五院-28
        db = 'prj28db'

        demo_stats = list(ActColdStat.objects.filter(created_at__gte=self.time_threshold).values_list('id', flat=True))
        orig_stats = list(ActColdStat.objects.using(db).filter(created_at__gte=self.time_threshold).exclude(id__in=demo_stats))

        for stat in orig_stats:
            stat.project_id = self.__PROJECT_ID
        ActColdStat.objects.bulk_create(orig_stats)

    def sync_project_weathers(self):
        db = 'prj28db'

        demo_weathers = list(ProjectWeather.objects.filter(created_at__gte=self.time_threshold).values_list('id', flat=True))
        orig_weathers = list(ProjectWeather.objects.using(db).filter(created_at__gte=self.time_threshold).exclude(id__in=demo_weathers))

        for weather in orig_weathers:
            weather.project_id = self.__PROJECT_ID
        ProjectWeather.objects.bulk_create(orig_weathers)


    def sync_dimension(self):
        """ 维度相关全使用保利项目的数据 """
        project_id = 74
        db = f'prj{project_id}db'

        # dimension 以及 dimension_attribute 只来源自保利，不考虑冲突
        # dimensions = Dimension.objects.using(db).order_by('parent_id')
        # for dimension in dimensions:
        #     dimension.project_id = self.__PROJECT_ID
        #     dimension.save()
        das = DimensionAttribute.objects.using(db).filter(updated_at__gte=self.time_threshold)
        DimensionAttribute.objects.bulk_update(das, ['value', 'updated_at'])

        self.sync_dimension_stats(db, DimensionHourlyStat.objects)
        self.sync_dimension_stats(db, DimensionDailyStat.objects)
        self.sync_dimension_stats(db, DimensionMonthlyStat.objects)
        self.sync_dimension_stats(db, DimensionYearlyStat.objects)

        # 所有计量设备
        devices = Device.objects.using(db).filter(device_type_id__in=DeviceType.objects.filter(parent_id=3).values_list('id', flat=True))
        update_devices = devices.filter(updated_at__gte=self.time_threshold)

        update_das = []
        update_tas = []
        for device in update_devices:
            self.sync_device(device, db, update_das, update_tas)

        DeviceAttribute.objects.bulk_update(update_das, ['value', 'updated_at'])
        TerminalAttribute.objects.bulk_update(update_tas, ['value', 'updated_at'])

        # param_records 用于计算当前小时的用电量
        device_ids = list(devices.using(db).values_list('id', flat=True))
        self.sync_param_record(device_ids, db)

    def sync_cs_eer(self):
        project_id = 27
        db = f'prj{project_id}db'
        cs_mac = set()

        for cs in ColdSource.objects.using(db).all():
            cs_mac.add(cs.mac)
            cs.project_id = self.__PROJECT_ID
            cs.save()

        for device in Device.objects.using(db).filter(mac__in=cs_mac):
            device.project_id = self.__PROJECT_ID
            device.save()

            demo_cs_eer = list(CsEerAnalyse.objects.filter(created_at__gte=self.time_threshold).values_list('id', flat=True))
            orig_cs_eer = list(CsEerAnalyse.objects.using(db).filter(created_at__gte=self.time_threshold).exclude(id__in=demo_cs_eer))
            CsEerAnalyse.objects.bulk_create(orig_cs_eer)

    def sync_dimension_stats(self, db, data_from):
        demo_ids = list(data_from.values_list('id', flat=True))
        orig_stats = data_from.using(db).exclude(id__in=demo_ids)
        data_from.bulk_create(orig_stats)

    def sync_param_record(self, device_ids, db):
        one_hour_ago = self.now - datetime.timedelta(hours=1)
        param_records = ParamRecord.objects.using(db).filter(device_id__in=device_ids, created_at__minute=5,
                                                             created_at__second=0, created_at__gte=one_hour_ago)

        prs = list(ParamRecord.objects.filter(device_id__in=device_ids, created_at__gte=one_hour_ago).values('device_id', 'identifier', 'created_at'))

        for pr in param_records:
            if next(filter(lambda x: x['device_id'] == pr.device_id and x['identifier'] == pr.identifier and x['created_at'] == pr.created_at, prs), None) is None:
                created_at = pr.created_at
                pr.id = None
                pr.save()
                pr.created_at = created_at
                pr.save()

    def sync_device(self, device, db, update_das, update_tas):
        device.project_id = self.__PROJECT_ID
        device.save()

        das = list(DeviceAttribute.objects.using(db).filter(updated_at__gte=self.time_threshold, device_id=device.id))

        ap_ids = [da.attribute_prototype_id for da in das]
        orig_aps = list(AttributePrototype.objects.using(db).filter(id__in=ap_ids))
        demo_aps = list(AttributePrototype.objects.filter(device_prototype_id=device.device_prototype_id, identifier__in=[ap.identifier for ap in orig_aps]))

        demo_das = DeviceAttribute.objects.filter(device_id=device.id, attribute_prototype_id__in=[ap.id for ap in demo_aps])

        for da in das:
            orig_ap = next(filter(lambda x: x.id == da.attribute_prototype_id, orig_aps), None)
            if orig_ap is not None:
                demo_ap = next(filter(lambda x: x.identifier == orig_ap.identifier, demo_aps), None)
                if demo_ap is not None:
                    demo_da = next(filter(lambda x: x.attribute_prototype_id == demo_ap.id, demo_das), None)
                    if demo_da is not None:
                        demo_da.value = da.value
                        demo_da.updated_at = self.now
                        # demo_da.save()
                        update_das.append(demo_da)
                    else:
                        da.id = None
                        da.attribute_prototype_id = demo_ap.id
                        da.save()

        self.sync_terminals(device, db, orig_aps, demo_aps, update_tas)

    def sync_terminals(self, device, db, orig_aps, demo_aps, update_tas):
        terminals = Terminal.objects.using(db).filter(updated_at__gte=self.time_threshold, device_id=device.id)
        for terminal in terminals:
            orig_terminal_id = terminal.id
            demo_terminals = Terminal.objects.filter(device_id=device.id, idx=terminal.idx, prefix=terminal.prefix)
            if demo_terminals.exists():
                terminal.id = demo_terminals.last().id
            else:
                terminal.id = None
            terminal.save()

            tas = list(TerminalAttribute.objects.using(db).filter(updated_at__gte=self.time_threshold, terminal_id=orig_terminal_id))
            # ap_ids = TerminalAttribute.objects.filter(terminal_id=terminal.id).values('id', 'attribute_prototype_id')
            demo_tas = TerminalAttribute.objects.filter(terminal_id=terminal.id, attribute_prototype_id__in=[ap.id for ap in demo_aps])

            for ta in tas:
                orig_ap = next(filter(lambda x: x.id == ta.attribute_prototype_id, orig_aps), None)
                if orig_ap is not None:
                    demo_ap = next(filter(lambda x: x.identifier == orig_ap.identifier, demo_aps), None)
                    if demo_ap is not None:
                        demo_ta = next(filter(lambda x: x.attribute_prototype_id == demo_ap.id, demo_tas), None)
                        if demo_ta is not None:
                            demo_ta.value = ta.value
                            demo_ta.updated_at = self.now
                            update_tas.append(demo_ta)
                            # demo_ta.save()
                        else:
                            ta.id = None
                            ta.terminal_id = terminal.id
                            ta.attribute_prototype_id = demo_ap.id
                            ta.save()

    def sync_cs(self):
        """冷源数据-保利项目（id有1，2）, 版换系统-广州市档案馆-11（id是3）"""
        db = f'prj74db'
        cs_mac = set()

        update_das = []
        update_tas = []

        update_cs = []
        for cs in ColdSource.objects.using(db).filter(updated_at__gte=self.time_threshold):
            cs_mac.add(cs.mac)
            cs.project_id = self.__PROJECT_ID
            update_cs.append(cs)

        ColdSource.objects.bulk_update(update_cs, ['coords_arr', 'updated_at'])

        for device in Device.objects.filter(mac__in=cs_mac):
            self.sync_device(device, db, update_das, update_tas)

        db = 'prj11db'
        demo_cs = ColdSource.objects.filter(name__contains="板换").last()
        for cs in ColdSource.objects.using(db).filter(name__contains="板换", updated_at__gte=self.time_threshold):
            cs.project_id = self.__PROJECT_ID
            if demo_cs is not None:
                cs.id = demo_cs.id
                cs.save(update_fields=['coords_arr', 'updated_at'])

                device = Device.objects.using(db).get(mac=cs.mac)
                self.sync_device(device, db, update_das, update_tas)

        DeviceAttribute.objects.bulk_update(update_das, ['value', 'updated_at'])
        TerminalAttribute.objects.bulk_update(update_tas, ['value', 'updated_at'])


    def sync_esr_stats(self):
        db = 'prj74db'

        demo_stats = list(EsrStat.objects.filter(created_at__gte=self.time_threshold).values_list('id', flat=True))
        orig_stats = list(EsrStat.objects.using(db).filter(created_at__gte=self.time_threshold).exclude(id__in=demo_stats))

        for stat in orig_stats:
            stat.project_id = self.__PROJECT_ID

        EsrStat.objects.bulk_create(orig_stats)
