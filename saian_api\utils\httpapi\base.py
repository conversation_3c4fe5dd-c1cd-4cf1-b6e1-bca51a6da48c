"""
  httpapi的共用功能
"""
class BaseAPI():
    """
      构造鉴权header
    """
    def headers(request):
        current_user = request.user
        # auth_str = 'Token token=' + current_user['token'] + ', identity='+ current_user['id'] +', project=' + current_user['project_id']
        auth_str = f"Token token={current_user.get('token', '')}, identity={current_user.get('id', '')}, project={current_user.get('project_id', '')}"
        headers = {'Authorization': auth_str}

        return headers
