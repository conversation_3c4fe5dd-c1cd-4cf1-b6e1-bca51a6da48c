import json
import logging
import os

import math
from datetime import datetime
from json.decoder import JSONDecodeError

import numpy as np
import requests
from django.db.models.base import ObjectDoesNotExist

from saian_api.coldsource.models import ColdSource
from saian_api.devdefine.models import AttributePrototype
from saian_api.devdefine.models import DeviceProtocol
from saian_api.device.models import DeviceLimit, Device, DyAttribute, DeviceAttribute, ParamRecord
from saian_api.issue.models import DeviceIssue
from saian_api.notifi.models import Notification
from saian_api.openapi.tasks import tp_data_push
from saian_api.report.models import DeviceMinuteStat, ReportConfigurer
from saian_api.terminal.models import TerminalAttribute, Terminal
from saian_api.utils.converter import DeviceCmd
from saian_api.utils.db.Redis import RedisHelper
from saian_api.utils.inthttpapi import DOMAIN
from saian_api.utils.inthttpapi.base import BaseAPI
from saian_api.utils.tools import to_int, is_number

"""
  设备基类，提供共用的功能
"""


class BaseDevice:
    """
      根据设备上报数据更新对应设备的参数值
      枚举类型直接更新为枚举值
    """
    @classmethod
    def update_attres(cls, device, event):
        # 机智云的did和prd_key是在数据推送中上报
        if 'did' in event:
            device.did = event['did']

        if 'prd_key' in event:
            device.prd_key = event['prd_key']

        # device.save(update_fields=['updated_at'])

        data = event.get('data', None)

        # 特定设备的数据注入处理
        device.data_inject(data)

        # 一次查询所有上报数据中涉及的 AttributePrototype, DeviceProtocol, DeviceAttribute, TerminalAttribute
        event_keys = list(data.keys())
        # 普通 AttributePrototype
        # aps = AttributePrototype.objects.filter(device_prototype_id=device.device_prototype_id, identifier__in=event_keys)
        # aps_identifiers = aps.values_list('identifier', flat=True)
        aps = AttributePrototype.objects.query_by_idfs(device, event_keys)
        aps_identifiers = [ap.identifier for ap in aps]

        # attribute_prototypes 表中没有的定义的数据点在 DeviceProtocol 中找
        device_protocols = []
        protocol_aps = []
        protocol_keys = list(set(event_keys) - set(aps_identifiers))
        if len(protocol_keys):
            device_protocols = list(DeviceProtocol.objects.filter(device_prototype_id=device.device_prototype_id, identifier__in=protocol_keys))
            protocol_ap_keys = []
            for dpc in device_protocols:
                if dpc.sp_defs is not None:
                    dpc.sp_defs = json.loads(dpc.sp_defs)
                    protocol_ap_keys += map(lambda x: x['idf'], dpc.sp_defs)
            # protocol_aps = AttributePrototype.objects.filter(device_prototype_id=device.device_prototype_id, identifier__in=protocol_ap_keys)
            if len(protocol_ap_keys):
                protocol_aps = AttributePrototype.objects.query_by_idfs(device, protocol_ap_keys)

        # 设备参数和终端参数 device_attributes and terminal_attributes
        # ap_ids = list(aps.values_list('id', flat=True)) + list(protocol_aps.values_list('id', flat=True))
        ap_ids = [ap.id for ap in aps] + [ap.id for ap in protocol_aps]
        # das = list(DeviceAttribute.objects.filter(device_id=device.id, attribute_prototype_id__in=ap_ids))
        das = DeviceAttribute.objects.query_object_list(device, ap_ids)
        # tas = list(TerminalAttribute.objects.filter(terminal__device_id=device.id, attribute_prototype_id__in=ap_ids))
        tas = TerminalAttribute.objects.query_object_list(device, ap_ids)
        # aps = list(aps)
        # device_protocols = list(device_protocols)
        # protocol_aps = list(protocol_aps)

        if data is not None:
            for key, value in data.items():
                try:
                    attrs = list(filter(lambda x: x.identifier == key, aps))
                    if len(attrs):
                        attr = attrs[0]
                        da = cls.get_target_attr(das, attr, DeviceAttribute.objects.get_by_ap, device, attr, value)
                        ta = cls.get_target_attr(tas, attr, TerminalAttribute.objects.get_by_da, da, attr)

                        cls.update_da(device, event, attr, value, da, ta)
                    # # attr 有可能返回多个，取最后一个
                    # attrs = AttributePrototype.objects.filter(device_prototype_id=device.device_prototype_id, identifier=key)
                    # if len(attrs) != 0:
                    #     attr = attrs.last()
                    #     self.update_da(device, event, attr, value)
                    else:
                        # dpcs = DeviceProtocol.objects.filter(device_prototype_id=device.device_prototype_id, identifier=key)
                        dpcs = list(filter(lambda x: x.identifier == key, device_protocols))
                        if len(dpcs) != 0:
                            dpc = dpcs[0]
                            if dpc.sp_defs is not None:
                                # sp_defs = json.loads(dpc.sp_defs)
                                for sp_def in dpc.sp_defs:
                                    # 从value中按位取值
                                    try:
                                        bit_value = value[int(sp_def['idx']):int(sp_def['idx']) + 1]
                                        # attrs = AttributePrototype.objects.filter(device_prototype_id=device.device_prototype_id,
                                        #                                           identifier=sp_def['idf'])
                                        attrs = list(filter(lambda x: x.identifier == sp_def['idf'], protocol_aps))
                                        if len(attrs) != 0:
                                            attr = attrs[0]
                                            da = cls.get_target_attr(das, attr, DeviceAttribute.objects.get_by_ap, device, attr)
                                            ta = cls.get_target_attr(tas, attr, TerminalAttribute.objects.get_by_da, da, attr)
                                            cls.update_da(device, event, attr, bit_value, da, ta)
                                    # 不影响其他位的处理
                                    except ObjectDoesNotExist:
                                        continue
                                    except IndexError:
                                        continue
                                    except ValueError:
                                        continue


                # 不影响其他参数的处理
                except ObjectDoesNotExist:
                    continue
                except IndexError:
                    continue
                except ValueError:
                    continue

            # 冷源时间更新处理
            # ColdSource 可以有多个相同 Mac 的
            cold_sources = ColdSource.objects.filter(mac=device.mac)

            # 更新每一个冷源
            for cs in cold_sources:
                # 更新冷源最后更新时间
                # 避免因没任何数据变化没更新时间
                cs.updated_at = datetime.now()
                cs.save()

            # 检查是否配置了openapi环境变量
            if os.environ.get('openapiappid', None) is None or os.environ.get('openapisecret', None) is None:
                return

            # 检查第三方推送设置
            name = f'tp_devices:{device.device_prototype_id}'
            cache_tp_devices = RedisHelper.get_list(device.project_id, name, True)
            if len(cache_tp_devices) == 0:
                tp_devices = BaseDevice.get_tp_devices(device.project_id, device.device_prototype_id)
                entapi_devices = []
                if not tp_devices:
                    RedisHelper.push_list(device.project_id, name, ['0'], True)
                else:
                    for dp_id, device_ids in tp_devices.items():
                        RedisHelper.push_list(device.project_id, name, device_ids, True)
                    entapi_devices = [str(d) for d in tp_devices[str(device.device_prototype_id)]]
            else:
                entapi_devices = cache_tp_devices

            # 检查当前设备是否已配置了第三方推送
            if str(device.id) in entapi_devices:
                entapi_data = {}
                for entapi_ap in list(filter(lambda x: x.identifier in data and x.en_entapi, aps)):
                    entapi_data[entapi_ap.identifier] = data[entapi_ap.identifier]

                if len(entapi_data):
                    r = tp_data_push({"mac": device.mac, "data": entapi_data})
                    logging.info(f'tp_data_push: statu code: {r.status_code}. content: {r.text}')

    @classmethod
    def get_tp_devices(cls, project_id, dp_id):
        res = f'entapi/intapi/tp_devices'
        params = {
            'project_id': project_id,
            'dp_id': dp_id
        }
        headers = BaseAPI.admin_intapi_header(res)
        headers['Content-Type'] = 'application/json'
        url = f'{DOMAIN}/{res}'

        r = requests.get(url, params=params, headers=headers)
        if r.status_code != 200:
            logging.error(f'查询 tp_devices 失败！status_code: {r.status_code}')
            return {}
        else:
            return r.json().get('tp_devices', {})

    # 更新参数处理
    @classmethod
    def update_da(cls, device: Device, event, attr, value, da=None, ta=None):
        from saian_api.device.models import DeviceAttribute
        now = datetime.now()

        # da = DeviceAttribute.objects.get(device_id=device.id, attribute_prototype_id=attr.id)
        # da = DeviceAttribute.objects.get_by_idf(device, attr.identifier)
        if da is None:
            da = DeviceAttribute.objects.get_by_ap(device, attr)
        if da is not None:
            orig_value = da.value
            if ta is None:
                ta = TerminalAttribute.objects.get_by_da(da, attr)
            updated_at = None
            # 如果带有时间戳，判断时间先后，避免后上报的数据更新在后
            if 'unixTime' in event['data']:
                timestamp = event['data']['unixTime']
                updated_at = datetime.fromtimestamp(timestamp)
                if int(da.updated_at.timestamp()) > int(timestamp):
                    return

            if attr.data_type == 30:
                try:
                    options = attr.options.split(",")
                    # 特殊处理冷源和热源系统故障，每1位代表1个故障类型，可以同时有多个
                    if '系统故障' in attr.name or '故障详情' in attr.name:
                        CS_FaultType = ''
                        for i, v in enumerate(value):
                            try:
                                if int(v) == 1:
                                    CS_FaultType = options[i] if CS_FaultType == '' else CS_FaultType + ',' + options[i]
                            except IndexError:
                                pass
                        da.value = '无故障' if CS_FaultType == '' else CS_FaultType

                    else:
                        enum_idx = to_int(value)
                        if 0 <= enum_idx < len(options):
                            # 枚举类型更新为枚举值
                            da.value = options[enum_idx]
                            data = event['data']

                            # 蓝牙附加设备添加成功时, 创建对应的蓝牙设备属性 dy_attributes
                            if attr.identifier == 'NewAttachDeviceStatus' and da.value == '添加成功':
                                try:
                                    info_list = json.loads(data['AttachDevicesInfoList'])
                                    if info_list:
                                        DyAttribute.objects.create_by_info_list(device, info_list)

                                except Exception as e:
                                    logging.error(f'添加蓝牙设备成功，解析设备信息出错: {e.__str__()}')

                        else:
                            da.value = value
                except AttributeError:
                    logging.error(f'Update attr error in base.py. device: {device.id}-{device.nick_name}, value: {value},'
                                  f' identifier: {attr.identifier}, options: {attr.options}')
                    da.value = value
            elif attr.data_type == 10:
                # 布尔值统一处理
                if value == 'true' or value is True:
                    value = 1
                if value == 'false' or value is False:
                    value = 0

                da.value = int(float(value))
            elif attr.data_type == 90:
                # 字典类型
                da.value = value
                if value is not None and value != "":
                    # 字典类型的参数上报有三种情况:
                    # 1. 上报了列表的json字符串
                    # 2. 上报了多个key组成的字符串
                    # 3. 上报了单个key
                    try:
                        options = json.loads(attr.options)

                        if value.startswith('['):
                            keys = json.loads(value)
                        else:
                            keys = value.split(',')

                        values = []
                        for key in keys:
                            if key in options:
                                # 兼容嵌套结构的 options
                                if isinstance(options[key], dict):
                                    values.append(options[key].get('value', None))
                                else:
                                    values.append(options[key])
                            else:
                                values.append(key)

                        da.value = ','.join(values)

                    except (JSONDecodeError, TypeError):  # 非法 json 字符串或 None
                        da.value = value
            elif attr.data_type == 20:
                # VRV 值过滤，超出最大最小值时忽略
                if device.device_prototype_id == 194 or device.device_prototype_id == 309:
                    if float(value) < float(attr.min_value) or float(value) > float(attr.max_value):
                        return
                # 根据device_limits和系统总控的配置, 如果上报的设定值不在限定范围内，更改设备上下限
                device_limit = DeviceLimit.objects.filter(limit_type=10, device_prototype_id=device.device_prototype_id, enabled=True).last()
                project_run_mode = device.project.run_mode
                # 当设备运行模式与系统运行模式一致时，才会应用"温度限制策略"
                if project_run_mode and ta and (project_run_mode == '制暖' or project_run_mode == '制冷'):
                    terminal = ta.terminal
                    terminal_run_mode = terminal.terminalattribute_set.filter(use_for=20).last()
                    if device_limit is not None and terminal_run_mode is not None and ta and ta.use_for == 10:
                        executor = {"name": "温度限制策略"}
                        up_value = device_limit.up_value
                        low_value = device_limit.low_value
                        # 获取上限值
                        # high_da = DeviceAttribute.objects.get_by_idf(device, attr.identifier + 'High')
                        high_da = DeviceAttribute.objects.query_object_by_idf(device, attr.identifier + 'High')

                        # 获取下限值
                        # low_da = DeviceAttribute.objects.get_by_idf(device, attr.identifier + 'Low')
                        low_da = DeviceAttribute.objects.query_object_by_idf(device, attr.identifier + 'Low')

                        def handle_device_hl(target_value):
                            """触发温度限制策略时，设备上下限处理"""
                            if high_da and high_da.value and float(high_da.value) < float(target_value):
                                device.send_ctrls(executor, {attr.identifier + 'High': target_value})
                            if low_da and low_da.value and float(low_da.value) > float(target_value):
                                device.send_ctrls(executor, {attr.identifier + 'Low': target_value})

                        if project_run_mode == '制暖' and '暖' in terminal_run_mode.value and float(value) > float(up_value):
                            handle_device_hl(up_value)
                            device.send_ctrls(executor, {attr.identifier: up_value})
                        elif project_run_mode == '制冷' and '冷' in terminal_run_mode.value and float(value) < float(low_value):
                            handle_device_hl(low_value)
                            device.send_ctrls(executor, {attr.identifier: low_value})

                device_prototype = device.device_prototype
                if device_prototype.uni_name == "ADL400电表":
                    # ADL400电表 忽略上报的变比
                    if attr.identifier == 'Meter_CT':
                        return
                da.value = value
                if ('Meter_Power_Cons' in attr.identifier or 'Meter_Water_Cons' in attr.identifier) and len(attr.identifier.split('_')) <= 4:
                    if device_prototype.uni_name == 'ADL400电表' or device_prototype.uni_name == 'GL3012电表':
                        meter_ct = device.get_value_by_idf('Meter_CT')
                        if meter_ct is not None:
                            meter_ct = float(meter_ct)
                            value = round(float(value) * meter_ct, 2)
                            da.value = value
                        else:
                            msg = f'ADL400电表/GL3012电表 找不到变比！device: {device.id}-{device.nick_name}'
                            logging.error(msg)
                            Notification.objects.send_debug_mail(device, msg)
                    # 用电量，用水量屏蔽波动的数据。更换电表后需手动修改对应的参数值，否则新电表的上报的用电量值可能会被屏蔽。
                    current_da = DeviceAttribute.objects.query_object_by_ap(device, attr)
                    if current_da and current_da.value:
                        current_value = current_da.value

                        # 当前值与上报值的绝对值，用于判断上报的值是否有异常
                        abs_value = abs(float(value) - float(current_value))

                        if float(value) < float(current_value):
                            # 上报的值变小了
                            da.value = current_value

                            name = f'suspect_meter_{device.id}:{attr.identifier}'
                            if RedisHelper.exists(device.project_id, name):
                                his_values = RedisHelper.get_list(device.project_id, name, True)
                                his_values.append(value)

                                if abs_value < 1000:
                                    criteria = 10
                                else:
                                    thresholds = RedisHelper.get_value(device.project_id, f"meter_threshold:{device.id}_{attr.identifier}", True)
                                    thresholds = float(thresholds) if thresholds else 120
                                    criteria = math.ceil((abs_value / thresholds) / 5) + 1

                                if len(his_values) >= criteria:
                                    # 通过检验，更新为上报值
                                    da.value = value
                                    RedisHelper.delete(device.project_id, name)

                                    if now.minute <= 15:
                                        # 如果当前时间是每小时前十五分钟，param_params 的每小时统计可能是纠正前的值，这里把它改回来
                                        pr_created_at = now.strftime('%Y-%m-%d %H:00:00')
                                        last_pr = ParamRecord.objects.filter(created_at=pr_created_at, device_id=device.id,
                                                                             identifier=attr.identifier).last()
                                        if last_pr is not None and is_number(last_pr.value) and float(last_pr.value) > float(value):
                                            last_pr.value = value
                                            last_pr.save()
                                    Notification.objects.send_debug_mail(device, f'{device.id}-{device.nick_name}: {attr.identifier}, '
                                                                                 f'仪表用量变少，连续上报{criteria}次，通过检验，更新为上报值。'
                                                                                 f'当前值: {current_value}, 上报值: {value}, ')
                                else:
                                    RedisHelper.push_list(device.project_id, name, value, True, 3600 * 12)
                                    da.value = current_value
                                    Notification.objects.send_debug_mail(device, f'{device.id}-{device.nick_name}: {attr.identifier}, '
                                                                                 f'仪表用量变少，不更新。当前值: {current_value}, 上报值: {value}')
                            else:
                                RedisHelper.push_list(device.project_id, name, value, True, 3600 * 12)
                        else:
                            name = f'suspect_meter_{device.id}:{attr.identifier}'
                            # 删除因上报值变小而缓存的值
                            RedisHelper.delete(device.project_id, name)

                            if abs_value > 200:
                                # 上报的值过大，检查是否长时间离线后的第一次上报
                                pr_list = list(ParamRecord.objects.filter(device_id=device.id, identifier=attr.identifier
                                                                          ).exclude(created_at__minute=5, created_at__second=0
                                                                                    ).order_by('-created_at')[:10])
                                message = f'{device.id}-{device.nick_name}: {attr.identifier}, 仪表用量过大, 当前值: {current_value}, 上报值: {value}。'
                                if len(pr_list):
                                    pr_created_at, _, info = cls.get_abnormal_meter_pr(pr_list, now)
                                    message += f'\n [get_abnormal_meter_pr] now: {now}, pr_created_at: {pr_created_at}, info: {info} \n'
                                    span_minutes = abs((now - pr_created_at).total_seconds()) // 60

                                    # 上报的值大于每分钟阀值（默认120），则认为是异常，屏蔽该值，不更新
                                    thresholds = RedisHelper.get_value(device.project_id, f"meter_threshold:{device.id}_{attr.identifier}", True)
                                    thresholds = float(thresholds) if thresholds else 120
                                    if float(abs_value) > span_minutes * thresholds:
                                        message += f'数据异常，不更新。'
                                        message += f'span minutes: {span_minutes}, abs value: {abs_value}, minute threshold: {thresholds}'
                                        da.value = current_value
                                        Notification.objects.send_debug_mail(device, message)
                                    else:
                                        # 正常更新不必发送邮件通知
                                        message += f'正常更新。'
                                        message += f'span minutes: {span_minutes}, abs value: {abs_value}'
                                    # Notification.objects.send_debug_mail(device, message)
                                else:
                                    message += '。param_records 表无上一次上报记录，视作离线后第一次上报, 正常更新'
                                    Notification.objects.send_debug_mail(device, message)
                                # logging.warning(message)

            elif attr.data_type == 80:
                # 字符串
                data = event['data']
                # 更新蓝牙附加设备数据点信息
                if attr.identifier == 'AttachDevicesInfoList' and not data.get('NewAttachDeviceStatus', None):
                    try:
                        info_list = json.loads(value) if isinstance(value, str) else value
                        DyAttribute.objects.update_by_info_list(device, info_list)
                    except Exception as e:
                        logging.error(f'更新蓝牙设备信息出错: {e.__str__()}')

                # 更新蓝牙附加设备数据
                elif attr.identifier == 'AttachDevicesData':
                    try:
                        devices_data = json.loads(value) if isinstance(value, str) else value
                        DyAttribute.objects.update_by_devices_data(device, devices_data)
                    except Exception as e:
                        logging.error(f'更新蓝牙设备数据出错: {e.__str__()}')

                da.value = str(value)

            else:
                da.value = value

            if isinstance(da.value, str) and len(da.value) > 255:
                da.value = da.value[:255]

            # da.save()
            da.updated_at = datetime.now()
            # 如果上报数据中有 unixTime, da 的 updated_at 就是用 unixTime
            if updated_at is not None:
                # 因为 DeviceAttribute 类的 updated_at 声明了 auto_now=True，所以要用 queryset.update 方法来覆写 updated_at
                # DeviceAttribute.objects.filter(pk=da.id).update(updated_at=updated_at)
                da.updated_at = updated_at

            if orig_value != str(da.value):
                da.save(update_fields=['value', 'updated_at'])
            RedisHelper.set_value(device.project_id, f'device_attribute:{da.device_id}_{da.attribute_prototype_id}', da, False)
            # 保存设备参的属性类型id到缓存，快照任务需要使用
            RedisHelper.add_set(device.project_id, f'recently_updated_{now.minute:02}:{device.device_prototype_id}',
                                da.attribute_prototype_id, True, 120)
            if not attr.is_cum and attr.data_type == 20 and ReportConfigurer.objects.filter(target_id=device.device_prototype_id,
                                                                                            identifier=attr.identifier).exists():
                RedisHelper.push_list(device.project_id, f'device_event_{now.strftime("%Y%m%d%H")}:{device.id}_{attr.identifier}',
                                      da.value, True, 7200)
            TerminalAttribute.objects.update_with_da(da, ta, ap=attr, project_id=device.project_id)

            if ta is not None:
                terminal = ta.terminal
                # 更新非子设备终端开关, 一个设备可能有多种开关，当 ap.name 包含 dp.name，并且终端不是子设备终端，才能被视为终端的开关
                if attr.identifier.endswith('SW') and attr.data_type == 10 and device.device_prototype.uni_name in attr.name:
                    # VRV终端开关由 VRV 网关的 SW 控制
                    if not terminal.idx:
                        terminal.sw_on = device.sw_on
                        terminal.save()

                # 更新终端在线状态
                # 如果 identifier 是在线状态，则忽略。OnlineStatus 在线状态在设备类型的自定义上报解析中处理
                if not terminal.online and terminal.idx and f'FCU_{terminal.idx}_OnlineStatus' not in event['data']:
                    terminal.online = True
                    terminal.save()

            # 实时处理分钟统计
            if ReportConfigurer.objects.filter(target_type__model='DevicePrototype', target_id=device.device_prototype_id, identifier=attr.identifier, do_minstat=True).exists():
                DeviceMinuteStat.objects.do_stats(device, attr, da)

    @classmethod
    def get_abnormal_meter_pr(cls, pr_list, now, k=2.0):
        """当仪表用量过大时，找出用于判断数据是否异常的 pr"""

        # 取出时间戳并排序
        times = sorted([pr.created_at for pr in pr_list] + [now])
        if len(times) < 2:
            anchor = times[0] if times else None
            diff_minutes = (now - anchor).total_seconds() / 60 if anchor else None
            return anchor, diff_minutes, {"reason": "less than 2 records"}

        # 计算相邻间隔（秒）
        deltas = np.diff([t.timestamp() for t in times])
        if len(deltas) == 0:
            anchor = times[0]
            diff_minutes = (now - anchor).total_seconds() / 60
            return anchor, diff_minutes, {"reason": "single record"}

        # 均值 & 标准差
        mean_val = np.mean(deltas)
        std_val = np.std(deltas)
        if std_val == 0:
            logging.warning('[get_abnormal_meter_pr] 标准差为 0，跳过 gap 检测')
            anchor = times[-2]
            diff_minutes = (now - anchor).total_seconds() / 60
            return anchor, diff_minutes, {"reason": "std=0"}

        # 动态阈值
        threshold = mean_val + k * std_val

        # 找出 gap（delta > threshold）
        gap_idx = np.where(deltas > threshold)[0]

        if gap_idx.size > 0:
            # 取最近的 gap
            k_idx = gap_idx[-1]
            anchor = times[k_idx]  # gap 前的 created_at
            diff_minutes = (now - anchor).total_seconds() / 60
            info = {
                "mean_seconds": mean_val,
                "std_seconds": std_val,
                "threshold_seconds": threshold,
                "gap_seconds": float(deltas[k_idx]),
                "gap_start": times[k_idx],
                "gap_end": times[k_idx + 1],
            }
            return anchor, diff_minutes, info

        # 没有 gap：返回最后一条真实 created_at
        anchor = max([pr.created_at for pr in pr_list])
        diff_minutes = (now - anchor).total_seconds() / 60
        info = {
            "mean_seconds": mean_val,
            "std_seconds": std_val,
            "threshold_seconds": threshold,
            "reason": "no gap; return last created_at"
        }
        return anchor, diff_minutes, info



    @classmethod
    def fault(cls, device, event):
        # 故障处理
        # 字典类型的故障处理
        data = event.get('data', {})
        fault_keys = list(filter(lambda x: 'Fault' in x, data.keys()))
        for fault_key in fault_keys:
            fault_ap = AttributePrototype.objects.query_by_idf(device, fault_key)
            if fault_ap is not None and fault_ap.data_type == 90 and fault_ap.options:
                value = data[fault_key]
                if value is not None:
                    try:
                        options = json.loads(fault_ap.options)

                        if value != '' and value != '[]':
                            if value.startswith('['):
                                dict_keys = json.loads(value)
                            else:
                                dict_keys = value.split(',')

                            # 当前所有未恢复的故障
                            cur_faults = set(DeviceIssue.objects.filter(device_id=device.id, issue_type=10,
                                                                        is_solved=False).values_list('display_name', flat=True))
                            # 上报的故障
                            new_faults = set()
                            for key in dict_keys:
                                if key in options:
                                    new_faults.add(options[key])
                                else:
                                    new_faults.add(key)

                            # 已恢复的故障
                            for fault in cur_faults - new_faults:
                                DeviceIssue.objects.recover_fault(device, fault)
                            # 新增的故障
                            for fault in new_faults - cur_faults:
                                DeviceIssue.objects.add_fault(device, fault)

                        else:
                            if device.in_fault:
                                # 空，恢复所有故障
                                for fault in options.values():
                                    DeviceIssue.objects.recover_fault(device, fault)

                    except JSONDecodeError:
                        pass

    @classmethod
    def alarm(cls, device, event):
        # 报警处理
        # 字典类型的报警处理
        data = event.get('data', {})
        alarm_keys = list(filter(lambda x: 'Alarm' in x, data.keys()))
        for alarm_key in alarm_keys:
            alarm_ap = AttributePrototype.objects.query_by_idf(device, alarm_key)
            if alarm_ap is not None and alarm_ap.data_type == 90 and alarm_ap.options:
                value = data[alarm_key]
                if value is not None:
                    try:
                        options = json.loads(alarm_ap.options)

                        if value != '' and value != '[]':
                            if value.startswith('['):
                                dict_keys = json.loads(value)
                            else:
                                dict_keys = value.split(',')

                            # 当前所有未解除的报警
                            cur_alarms = set(DeviceIssue.objects.filter(device_id=device.id, issue_type=20,
                                                                        is_solved=False).values_list('display_name', flat=True))
                            # 上报的报警
                            new_alarms = set()
                            for key in dict_keys:
                                if key in options:
                                    new_alarms.add(options[key])
                                else:
                                    new_alarms.add(key)

                            # 已解除的报警
                            for alarm in cur_alarms - new_alarms:
                                DeviceIssue.objects.recover_alarm(device, alarm)
                            # 新增的报警
                            for alarm in new_alarms - cur_alarms:
                                DeviceIssue.objects.add_alarm(device, alarm)

                        else:
                            # 避免设备和终端的状态不一致，导致报警无法解除
                            if device.in_alarm or Terminal.objects.filter(device_id=device.id, in_alarm=True).exists():
                                # 空，解除所有报警
                                for alarm in options.values():
                                    DeviceIssue.objects.recover_alarm(device, alarm)

                    except JSONDecodeError:
                        pass
        pass

    @classmethod
    def exec_cmd(cls, device, data, executor):
        # 命令下发处理
        from saian_api.device.models import DeviceCtrlLog

        logging.info('base device exec cmd')
        #  设备不再验收模式
        if not device.check_acc:
            safe_data = None
            # 默认是机智云
            url = 'http://172.18.30.148:8080/saiangw/v1/device_controls'

            # 校验和完善参数
            safe_data = DeviceCmd.complete(device, data)

            # 派诺
            if device.platform == 20:
                url = 'http://172.18.30.148:8080/saiangw/v1/pnd_controls'

            # 移动onenet
            if device.platform == 30:
                url = 'http://172.18.30.148:8080/saiangw/v1/onenet_controls'

            # 自研
            if device.platform == 40:
                url = device.gw_end_point + "/saiants/v1/dtu_controls"

            # 阿里云
            if device.platform == 50:
                pass
            else:
                # 向网关发送指令
                # 记录日志
                DeviceCtrlLog.objects.create_cus(executor, device, data)
                pass

    @classmethod
    def get_target_attr(cls, target_attrs: list, attr, multi_object_handler, *args):
        """
            从 das 或 tas 中找出目标值
            multi_object_handler 函数，当从 target_attrs 查找的目标值不符合预期(多个或零个)时，调用该函数
        """

        target_attr = None

        result_attrs = list(filter(lambda x: x.attribute_prototype_id == attr.id, target_attrs))
        if len(result_attrs) == 1:
            target_attr = result_attrs[0]
        else:
            try:
                target_attr = multi_object_handler(*args)
            except Exception as e:
                logging.error(f'上报时间处理, 查找目标值时出错: {e.__str__()}')

        return target_attr
