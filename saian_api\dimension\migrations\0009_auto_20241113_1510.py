# Generated by Django 3.2.19 on 2024-11-13 15:10

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0014_alter_userstat_online_time'),
        ('dimension', '0008_alter_dimensionattribute_ta_ids'),
    ]

    operations = [
        migrations.AddField(
            model_name='dimension',
            name='unit_area',
            field=models.FloatField(default=None, null=True),
        ),
        migrations.CreateModel(
            name='DimensionUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('dimension', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dimension.dimension')),
                ('web_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='user.webuser')),
            ],
            options={
                'db_table': 'dimension_users',
            },
        ),
    ]
