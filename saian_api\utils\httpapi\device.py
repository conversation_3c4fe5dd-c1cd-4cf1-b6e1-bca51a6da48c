"""
  httpapi的共用功能
"""
import logging
import requests
from saian_api.device.models import Device


class DeviceAPI:
    @classmethod
    def live_update(cls, project):
        """
          向设备网关发送开启实时读取
        """
        devices = Device.objects.filter(live_update=1, gw_end_point__isnull=False)
        headers = {
            "Content-Type": "application/json",
                            "project": project
        }
        for device in devices:
            url = device.gw_end_point + "/saiants/v1/set_rtu"
            try:
                r = requests.post(url, headers=headers, data={"mac": device.mac, "enable_rtu": 1}, timeout=10)
                if r.status_code != 200:
                    logging.error(f'设备{device.nick_name}开启实时读取失败, status_code: {r.status_code}')
            except requests.exceptions.RequestException as err:
                logging.error(f'设备{device.nick_name}开启实时读取超时, error: {err.__str__()}')
