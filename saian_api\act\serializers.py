from rest_framework.serializers import ModelSerializer

from saian_api.act.models import AcTerminal, ActColdStat
from saian_api.devdefine.serializers import SimpleDevicePrototypeSerializer
from saian_api.project.serializers import SimpleProjectSerializer


class AcTerminalReadSerializer(ModelSerializer):
    project = SimpleProjectSerializer()
    device_prototype = SimpleDevicePrototypeSerializer()

    class Meta:
        model = AcTerminal
        exclude = ('load_rate',)


class AcTerminalWriteSerializer(ModelSerializer):
    class Meta:
        model = AcTerminal
        fields = '__all__'


class ActColdStatSerializer(ModelSerializer):
    class Meta:
        model = ActColdStat
        fields = ('base_load', 'current_load', 'load_rate', 'created_at')
