from django.urls import path

from .views import DeviceIssueViewSet, IssueStatViewSet, IssueWhiteListViewSet, CheckingRecordViewSet, WebIssueViewSet

deviceissue_list = DeviceIssueViewSet.as_view({
    'get': 'list'
})

deviceissue_detail = DeviceIssueViewSet.as_view({
    'get': 'retrieve'
})

whitelist_list = IssueWhiteListViewSet.as_view({
    'get': 'list',
    'post': 'create'
})

whitelist_detail = IssueWhiteListViewSet.as_view({
    'get': 'retrieve',
    'put': 'partial_update',
    'delete': 'destroy'
})

checking_record_list = CheckingRecordViewSet.as_view({
    'post': 'create'
})

issue_stats = IssueStatViewSet.as_view({
    'get': 'list'
})

web_issues = WebIssueViewSet.as_view({
    'get': 'list'
})

urlpatterns = [
    path('saianapi/v1/device_issues', deviceissue_list, name='deviceissue-list'),
    path('saianapi/v1/projects/<int:prj>/device_issues', deviceissue_list, name='prj_deviceissue_list'),
    path('saianapi/v1/device_issues/<int:pk>', deviceissue_detail, name='deviceissue-detail'),
    path('saianapi/v1/projects/<int:prj>/device_issues/<int:pk>', deviceissue_detail, name='prj_deviceissue_detail'),
    path('saianapi/v1/issue_whitelists', whitelist_list, name='whitelist-list'),
    path('saianapi/v1/issue_whitelists/<int:pk>', whitelist_detail, name='whitelist-detail'),
    path('saianapi/v1/checking_records', checking_record_list, name='checking_record-list'),
    path('saianapi/v1/web_issues', web_issues, name='web-issues-v1'),
    path('saianapi/v2/issue_stats', issue_stats, name='issue-stats'),
    path('saianapi/v2/web_issues', web_issues, name='web-issues'),
]
