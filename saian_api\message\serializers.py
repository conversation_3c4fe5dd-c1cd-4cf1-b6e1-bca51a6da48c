from rest_framework import serializers

from saian_api.message.models import Message, UserMessage

class MessageSerializer(serializers.ModelSerializer):
    class Meta:
        model = Message
        exclude = ('web_user',)
        read_only_field = ('created_at', 'uid')

    def to_representation(self, instance):
        ret = super(MessageSerializer, self).to_representation(instance)
        # - “有用”功能没有用到，暂时注释掉
        # useful, useless = UserMessage.objects.useful_count(ret.get('id'))
        # ret['num_of_useful'] = useful
        # ret['num_of_useless'] = useless
        # - 移到视图函数
        # request = self.context['request']
        # user_message = UserMessage.objects.filter(message_id=ret.get('id'), web_user_id=request.user['id']).first()
        # ret['useful'] = user_message.useful if user_message else 9
        # ret['read'] = user_message.read if user_message else False

        return ret
