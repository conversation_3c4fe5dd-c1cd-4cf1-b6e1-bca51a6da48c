from django.urls import path

from saian_api.dimension.views import (DimensionView, DimensionTerminalView, DimensionAttributeView, DimensionTypeView,
                                       DimensionAttributeEcItem, DimensionEcStats, DimensionStatView,
                                       DimensionQoqStatsView, DimensionEcRankingView, DimensionEtQoqStatsView, DimensionEcTypeView,
                                       DimensionEcReportView, DimensionEcAnalysisPrediction, PpvConfigView, EcTotalStatsView, EcComparisonView,
                                       EsrStatView, SysEcReportView, EcConsumeCalculationView, DimensionUserView, UnitRankingView, UnitConsView,
                                       UnitDimensionRoomRanking, DimensionEcSum, DimensionEcQuota, DimensionEcTrend, EcTotalView, UnitEcRankingView,
                                       DimensionAttrTreeView, EcReportView)

dimensions = DimensionView.as_view({
    'get': 'list',
    'post': 'create'
})
dimension = DimensionView.as_view({
    'get': 'retrieve',
    'put': 'partial_update',
    'delete': 'destroy'
})

dimension_attributes = DimensionAttributeView.as_view({
    'get': 'list',
    'post': 'create'
})
dimension_attribute = DimensionAttributeView.as_view({
    'get': 'retrieve',
    'put': 'partial_update',
    'delete': 'destroy'
})

dimension_terminals = DimensionTerminalView.as_view({
    'post': 'create',
    'get': 'list'
})
dimension_terminal = DimensionTerminalView.as_view({
    'delete': 'destroy'
})

dimension_types = DimensionTypeView.as_view({
    'get': 'list'
})

dimension_ec_items = DimensionAttributeEcItem.as_view({
    'get': 'list'
})

dimension_ec_types = DimensionEcTypeView.as_view({
    'get': 'list'
})

dimension_ec_stats = DimensionEcStats.as_view({
    'get': 'list'
})

dimension_stats = DimensionStatView.as_view({
    'post': 'create'
})

# 能耗排名
dimension_ec_rankings = DimensionEcRankingView.as_view({
    'get': 'list'
})
# 环比分析
dimension_qoq_stats = DimensionQoqStatsView.as_view({
    'get': 'list'
})
# 分项环比分析
dimension_et_qoq_stats = DimensionEtQoqStatsView.as_view({
    'get': 'list'
})
# 能耗报表
dimension_ec_reports = DimensionEcReportView.as_view({
    'get': 'list',
    'post': 'create'
})
ec_reports = EcReportView.as_view({
    'get': 'list',
    'post': 'create'
})
# 能耗分析与预测
ec_analysis_predict = DimensionEcAnalysisPrediction.as_view({
    'get': 'retrieve'
})
# 能耗汇总统计
ec_total_stats = EcTotalStatsView.as_view({
    'get': 'list'
})

# 能耗对比
ec_comparisons = EcComparisonView.as_view({
    'get': 'list'
})

# 峰平谷尖配置管理
ppv_configs = PpvConfigView.as_view({
    'get': 'list',
    'post': 'create'
})

# 峰平谷尖配置详情管理
ppv_config = PpvConfigView.as_view({
    'put': 'partial_update',
    'delete': 'destroy'
})

# 节能率统计
esr_stats = EsrStatView.as_view({
    'get': 'retrieve'
})

# 系统节能报告统计导出
sys_ec_report = SysEcReportView.as_view({
    'post': 'create'
})

# 地大未来城能耗用量计算
ec_consume_calculation = EcConsumeCalculationView.as_view({
    'get': 'list'
})

dimension_users = DimensionUserView.as_view({
    'get': 'list',
    'post': 'create'
})

# 单位排名
unit_rankings = UnitRankingView.as_view({
    'get': 'list'
})
# 单位用量
unit_cons = UnitConsView.as_view({
    'get': 'list'
})
# 单位房间排名
unit_room_rankings = UnitDimensionRoomRanking.as_view({
    'get': 'list'
})

# 能耗总量
ec_sum = DimensionEcSum.as_view({
    'get': 'list'
})

# 定额指标
ec_quotas = DimensionEcQuota.as_view({
    'get': 'list'
})

# 能耗趋势
ec_trends = DimensionEcTrend.as_view({
    'get': 'list'
})

# 能耗累计
ec_total = EcTotalView.as_view({
    'get': 'list'
})

# 单位设备用电排名（占比）
unit_ec_rankings = UnitEcRankingView.as_view({
    'get': 'list'
})

dimension_attr_tree = DimensionAttrTreeView.as_view({
    'get': 'list'
})

urlpatterns = [
    # 维度
    path('saianapi/v5/dimensions', dimensions, name="dimensions"),
    path('saianapi/v5/dimensions/<int:pk>', dimension, name="dimension"),
    # 维度属性
    path('saianapi/v5/dimension_attributes', dimension_attributes, name="dimension-attributes"),
    path('saianapi/v5/dimension_attributes/<int:pk>', dimension_attribute, name="dimension-attribute"),
    # 维度终端
    path('saianapi/v5/dimension_terminals', dimension_terminals, name="dimension-terminals"),
    path('saianapi/v5/dimension_terminals/<int:pk>', dimension_terminal, name="dimension-terminals"),
    # 维度类型
    path('saianapi/v5/dimension_types', dimension_types, name="dimension-types"),
    # 设置了能耗类型的维度属性
    path('saianapi/v5/dimension_ec_items', dimension_ec_items, name="dimension-ec-types"),
    # 能耗类型
    path('saianapi/v5/dimension_ec_types', dimension_ec_types, name="dimension_ec_types"),
    # 能耗总览
    path('saianapi/v5/ec_summaries', dimension_ec_stats, name='dimension-ec-stats'),
    # 维度属性统计导出
    path('saianapi/v5/dimension_stats', dimension_stats, name='dimension-stats'),
    # 能耗排名
    path('saianapi/v5/ec_rankings', dimension_ec_rankings, name='dimension-ec-rankings'),
    # 环比分析
    path('saianapi/v5/qoq_stats', dimension_qoq_stats, name='dimension-qoq-stats'),
    # 分项环比分析
    path('saianapi/v5/et_qoq_stats', dimension_et_qoq_stats, name='dimension-et-qoq-stats'),
    # 能耗报表
    path('saianapi/v5/ec_reports', ec_reports, name='dimension-ec-reports'),
    # 能耗分析与预测
    path('saianapi/v5/ec_analysis_predictions', ec_analysis_predict, name='ec-analysis-predict'),
    # 能耗汇总统计
    path('saianapi/v5/total_stats', ec_total_stats, name='ec-total-stats'),
    # 能耗对比
    path('saianapi/v5/ec_comparisons', ec_comparisons, name='ec-comparisons'),

    # 峰平谷尖配置管理
    path('saianapi/v1/ppv_configs', ppv_configs, name="ppv-configs"),
    path('saianapi/v1/ppv_configs/<int:pk>', ppv_config, name="ppv-config"),

    # 节能率统计
    path('saianapi/v5/esr_stats', esr_stats, name="esr-stats"),

    # 系统节能报告导出
    path('saianapi/v5/sys_ec_report', sys_ec_report, name="sys-ec-report"),

    # 地大未来城能耗用量计算
    path('saianapi/v5/ec_consume_calculation', ec_consume_calculation, name="ec-consume-calculation"),

    # 维度与用户
    path('saianapi/v5/dimension_users', dimension_users, name="dimension-users"),

    # 单位电耗排名
    path('saianapi/v5/unit_rankings', unit_rankings, name="unit-rankings"),
    # 单位用电量
    path('saianapi/v5/unit_cons', unit_cons, name="unit-cons"),
    # 单位房间用电量排名
    path('saianapi/v5/unit_room_rankings', unit_room_rankings, name="unit-room-rankings"),
    # 能耗总量
    path('saianapi/v5/ec_sums', ec_sum, name="ec-sum"),
    # 定额指标
    path('saianapi/v5/ec_quotas', ec_quotas, name="ec-quotas"),
    # 能耗趋势
    path('saianapi/v5/ec_trends', ec_trends, name="ec-trends"),
    # 能耗累计
    path('saianapi/v5/ec_totals', ec_total, name="ec-total"),
    # 单位设备用电排名（占比）
    path('saianapi/v5/unit_ec_rankings', unit_ec_rankings, name="unit_ec_rankings"),

    path('saianapi/v5/dimension_attr_tree', dimension_attr_tree, name="dimension_attr_tree"),
]
