# Generated by Django 3.2.8 on 2021-12-30 15:16

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('group', '0001_initial'),
        ('project', '0002_project_web_users'),
        ('contenttypes', '0002_remove_content_type_name'),
    ]

    operations = [
        migrations.AddField(
            model_name='group',
            name='project',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='project.project'),
        ),
        migrations.AddField(
            model_name='actiontimerattribute',
            name='action_attribute',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='group.actionattribute'),
        ),
        migrations.AddField(
            model_name='actiontimerattribute',
            name='action_timer',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='group.actiontimer'),
        ),
        migrations.AddField(
            model_name='actiontimer',
            name='group_action',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='group.groupaction'),
        ),
        migrations.AddField(
            model_name='actionlog',
            name='executor_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype'),
        ),
        migrations.AddField(
            model_name='actionattribute',
            name='content_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype'),
        ),
        migrations.AddField(
            model_name='actionattribute',
            name='group_action',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='group.groupaction'),
        ),
    ]
