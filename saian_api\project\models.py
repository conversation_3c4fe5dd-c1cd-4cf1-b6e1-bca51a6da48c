import os

from django.core import exceptions
from django.db import models

from saian_api.regions.models import Region
# Create your models here.
from saian_api.utils.utils import AuthUtils

"""
  web菜单
"""
class WebMenu(models.Model):
    # 名称
    name = models.CharField(max_length=255)
    # 父菜单
    parent = models.ForeignKey('self', blank=True, null=True, related_name='children', on_delete=models.CASCADE)
    # 排序字段
    seq = models.IntegerField()
    # 是否启用
    enabled = models.BooleanField(default=False)
    # 唯一名称
    uni_name = models.CharField(max_length=255, unique=True)
    # 是否需要超级管理员权限
    is_super = models.BooleanField(default=False)
    # 所属的agent
    agent_id = models.BigIntegerField(null=True)

    # 菜单对应页面的内容
    page_content = models.TextField(null=True)

    # 子菜单的展示方式
    display = models.CharField(max_length=20, default='List')

    # 是否是自定义菜单
    is_cus = models.BooleanField(default=False)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'web_menus'
        ordering = ['-created_at']


"""
  Web图表配置信息
"""
class WebChart(models.Model):
    # 图表名称
    name = models.CharField(max_length=255)
    # 唯一名称
    uni_name = models.CharField(max_length=255, unique=True)
    # 是否启用
    enabled = models.BooleanField(default=False)
    # 图标
    icon = models.CharField(max_length=255, null=True)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    # 图表类型，10-定制图表，20-折线或柱状图，30-饼图，40-表格，50-单项数据
    chart_type = models.IntegerField(default=10)

    # 图表的数据来源，10 - 定制数据，20-设备原始数据，30-报表配置数据，40-能耗录入数据，
    # 50-基准能耗数据，60-人工录入数据，70-设备实时数据，
    # 80-维度统计数据，90-维度实时数据，100-天气数据
    data_src = models.IntegerField(default=10)

    # 人工录入数据的名称
    data_name = models.CharField(max_length=255, null=True)

    # 数据粒度，10-实时，20-小时，30-日，40-周，50-月
    gran = models.IntegerField(null=True)

    # 查询数据的默认时间，如：近1小时，近6小时，近24小时，近7天，2023-01-01 ~ 2023-05-01等，自定义查询时间格式为：开始时间 ~ 结束时间
    query_time = models.CharField(max_length=255, default='近24小时')

    # 数据参数，为json格式，根据数据来源不同而不同，如：设备原始数据：[{"id": 1, "name": "现场温度"}]，人工录入数据：[{"name": "客房入住率"}]，天气数据：[{"name": "室外温度"}]
    data_params = models.TextField(null=True)

    # 样式配置数据，json格式，内容类似为：{"font": {"size": 12, "family": xxx}, "backgroud": {"color': red}}
    style_params = models.TextField(null=True)

    class Meta:
        db_table = 'web_charts'
        # ordering = ['-created_at']


"""
    Web用户看板配置信息
"""
class WebPanel(models.Model):
    # 图表名称
    name = models.CharField(max_length=255)
    # 唯一名称
    uni_name = models.CharField(max_length=255, unique=True)
    # 是否启用
    enabled = models.BooleanField(default=False)
    # 图标
    icon = models.CharField(max_length=255, null=True)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'web_panels'
        # ordering = ['-created_at']


"""
  项目管理器
"""
class ProjectManager(models.Manager):
    # Project.objects.get_by_request(request)
    def get_by_request(self, request):
        return Project.objects.get(pk=request.user['project_id'])


"""
  项目信息
"""
class Project(models.Model):
    # id
    id = models.BigIntegerField(primary_key=id)
    # 项目名
    name = models.CharField(max_length=255, null=True)
    # 项目类型
    project_type = models.CharField(max_length=255, null=True)
    # 简介
    intro = models.CharField(max_length=255, null=True)
    # 详细地址
    address = models.CharField(max_length=255, null=True)
    # 项目图片，多张
    images = models.CharField(max_length=255, null=True)
    # 行政区域id
    regionid = models.CharField(max_length=255, null=True)
    # 供冷面积，平方米
    cooling_area = models.IntegerField(null=True)
    # 用电量，单位kWh
    electricity = models.IntegerField(null=True)
    # 竣工时间
    completed_at = models.DateTimeField(null=True)
    # 维保期
    warranty = models.IntegerField(null=True)
    # 联系人姓名
    contact_name = models.CharField(max_length=255, null=True)
    # 联系电话
    contact_no = models.CharField(max_length=255, null=True)
    # 联系email
    contact_email = models.CharField(max_length=255, null=True)
    # 负责人姓名
    director_name = models.CharField(max_length=255, null=True)
    # 负责人联系电话
    director_no = models.CharField(max_length=255, null=True)
    # 负责人email
    director_email = models.CharField(max_length=255, null=True)
    # 维保联系人姓名
    maintainer_name = models.CharField(max_length=255, null=True)
    # 维保联系电话
    maintainer_no = models.CharField(max_length=255, null=True)
    # 维保联系email
    maintainer_email = models.CharField(max_length=255, null=True)
    # 工程师1姓名
    engineer_a_name = models.CharField(max_length=255, null=True)
    # 工程师1联系电话
    engineer_a_no = models.CharField(max_length=255, null=True)
    # 工程师2姓名
    engineer_b_name = models.CharField(max_length=255, null=True)
    # 工程师2联系电话
    engineer_b_no = models.CharField(max_length=255, null=True)
    # 销售代理方
    sales_agent = models.CharField(max_length=255, null=True)
    # 建筑业主方
    building_owner = models.CharField(max_length=255, null=True)
    # 建筑适使用方
    building_user = models.CharField(max_length=255, null=True)
    # 建筑适使用方
    building_user = models.CharField(max_length=255, null=True)
    # 施工单位
    builder = models.CharField(max_length=255, null=True)
    # 施工单位联系人
    builder_contact_name = models.CharField(max_length=255, null=True)
    # 施工单位联系电话
    builder_contact_no = models.CharField(max_length=255, null=True)
    # 物业管理公司
    pmc = models.CharField(max_length=255, null=True)
    # 物业管理公司负责人
    pmc_contact_name = models.CharField(max_length=255, null=True)
    # 物业管理公司联系电话
    pmc_contact_no = models.CharField(max_length=255, null=True)
    # 物业管理公司联系电话
    pmc_contact_no = models.CharField(max_length=255, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    # 关联的行政区域id
    admin_region_id = models.BigIntegerField()
    # 盘管使用高速系数
    hs_weight = models.IntegerField(null=True)
    # 盘管使用中速系数
    ms_weight = models.IntegerField(null=True)
    # 盘管使用低速系数
    ls_weight = models.IntegerField(null=True)
    # 盘管使用高速系数
    fan_time = models.IntegerField(null=True)
    # 项目logo
    logo = models.CharField(max_length=255, null=True)
    # 项目web的slogan
    slogan = models.CharField(max_length=255, null=True)
    # 是否开通房间温湿度超标短信
    en_temhum_sms = models.BooleanField(default=False)
    # 是否开通有害气体超标短信
    en_gas_sms = models.BooleanField(default=False)
    # 是否开通web
    enable_web = models.BooleanField(default=False)
    # 所属的agent
    agent_id = models.BigIntegerField()
    # 项目所在domain
    domain = models.CharField(max_length=255, null=True)
    # 是否处于验收模式
    in_acc = models.BooleanField(default=False)
    # 是否开通能耗平台
    enable_ec = models.BooleanField(default=False)
    # 是否开通加班空调
    en_ot_aircon = models.BooleanField(default=False)
    # 用户看板设置，json 格式
    user_panel_settings = models.TextField(blank=True, null=True)
    # 项目设置
    # 1、temp_t1，温度阈值1（末端温度统计）
    # 2、temp_t2，温度阈值2（末端温度统计）
    # 3、temp_t3，温度阈值3（末端温度统计）
    # 4、temp_t4，温度阈值4（末端温度统计）
    # 5、dis_termstats，是否展示末端概况
    # 6、dis_csinfo，是否展示冷源概况
    # 7、dis_mtn，是否展示保养信息
    # 8、dis_ecstats，是否展示能耗统计
    # 9、dis_ecqoq，是否展示环比分析
    # 10、dis_ecanalysis，是否展示能耗分析
    # 11、dis_ecsum，是否展示汇总统计（能耗）
    # 12、dis_accons，是否展示空调用电
    # 13、dis_ecitem，是否展示分项对比分析"
    settings = models.TextField(blank=True, null=True)
    # 目前软件系统的运行模式
    run_mode = models.CharField(max_length=255, null=True, blank=True, default=None)

    # 拥有的菜单
    web_menus = models.ManyToManyField(
        WebMenu,
        through='ProjectWebMenu',
        through_fields=('project', 'web_menu'),
    )

    # 拥有的图表
    web_charts = models.ManyToManyField(
        WebChart,
        through='ProjectChart',
        through_fields=('project', 'web_chart'),
    )

    # 用户看板的图表
    web_panels = models.ManyToManyField(
        WebPanel,
        through='ProjectPanel',
        through_fields=('project', 'web_panel')
    )

    # 有多个用户
    web_users = models.ManyToManyField(
        'user.WebUser',
        through='user.UserProject'
    )

    # 项目电耗计算公式，如：{{ec_meter_id1}} + {{ec_meter_id1}}
    pc_formula = models.CharField(max_length=500, null=True)

    # 项目冷耗计算公式，如：{{ec_meter_id1}} + {{ec_meter_id1}}
    cc_formula = models.CharField(max_length=500, null=True)

    # 项目水耗计算公式，如：{{ec_meter_id1}} + {{ec_meter_id1}}
    wc_formula = models.CharField(max_length=500, null=True)

    # # 风机盘管电耗json配置
    # fcu_pc_config = models.CharField(max_length=1000, null=True)
    #
    # # 风机盘管冷耗json配置
    # fcu_cc_config = models.CharField(max_length=1000, null=True)
    #
    # # 风机盘管冷耗json配置
    # fcu_wc_config = models.CharField(max_length=1000, null=True)

    class Meta:
        db_table = 'projects'
        ordering = ['-created_at']

    objects = ProjectManager()

    def __str__(self):
        return f'{self.id} => {self.name}'

    # 项目所在省份
    @property
    def province(self):
        provinceId = ''.join([str(self.admin_region_id)[0:2], '0000'])
        try:
            province = Region.objects.get(pk=provinceId, level=1)
        except exceptions.ObjectDoesNotExist:
            province = Region.objects.filter(level=1).first()

        return province

    # 项目所在城市
    @property
    def city(self):
        cityId = ''.join([str(self.admin_region_id)[0:4], '00'])
        try:
            city = Region.objects.get(pk=cityId, level=2)
        except exceptions.ObjectDoesNotExist:
            city = Region.objects.filter(level=2).first()

        return city

    # 项目所在区
    @property
    def district(self):
        try:
            district = Region.objects.get(pk=self.admin_region_id, level=3)
        except exceptions.ObjectDoesNotExist:
            district = Region.objects.filter(level=3).first()

        return district

    # 项目拥有的设备子分类
    def device_types(self):
        from saian_api.devdefine.models import DeviceType

        device_types = []
        for device in self.device_set.all():
            device_type = DeviceType.objects.get(pk=device.device_type_id)
            if device_type not in device_types:
                device_types.append(device_type)

        return device_types

    # 总在线设备数
    # project = Project.objects.get(pk=66)
    # project.total_online()
    def total_online(self):
        return self.device_set.filter(online=True).count()

    # 总离线设备数
    def total_offline(self):
        return self.device_set.filter(online=False).count()

    # 总运行的设备数
    def total_inrun(self):
        return self.device_set.filter(sw_on=True, online=True).count()

    # 报警的设备总数
    def total_inalarm(self):
        return self.device_set.filter(in_alarm=True, online=True).count()

    # 故障的设备总数
    def total_infault(self):
        return self.device_set.filter(in_fault=True, online=True).count()

    """
      echart数据，项目离在线数据统计，要与离线和在线总数对应
      [
        {on_value:2, name:'挂式空调'},
        {off_value:1, name:'挂式空调'},
    ]
    """
    def device_stat_data(self):
        data = []
        for device_type in self.device_types():
            online_devices = self.device_set.filter(online=True, device_prototype_id=device_type.id)
            on_data = {
                'on_value': online_devices.count(),
                'name': device_type.name
            }

            data.append(on_data)

            offline_devices = self.device_set.filter(online=False, device_prototype_id=device_type.id)

            off_data = {
                'off_value': offline_devices.count(),
                'name': device_type.name
            }

            data.append(off_data)

        return data

    # 统计需要维护的设备数量
    # 暂时先统计盘管
    def num_of_dev_need_maintenance(self):
        return self.device_set.filter(needs_m=True, device_type_id=14).count()

"""
  项目菜单关系表
"""
class ProjectWebMenu(models.Model):
    # 对应的项目
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    # 对应的菜单
    web_menu = models.ForeignKey(WebMenu, on_delete=models.CASCADE)

    class Meta:
        db_table = 'project_web_menus'


"""
  项目图表关系表
"""
class ProjectChart(models.Model):
    # 对应的项目
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    # 对应的图表
    web_chart = models.ForeignKey(WebChart, on_delete=models.CASCADE)
    # 对应仪表盘或看板的编号
    num = models.IntegerField()
    # 模板名，
    template = models.CharField(max_length=255, null=True)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'project_charts'
        # ordering = ['-created_at']


"""
    项目用户看板关系表
"""
class ProjectPanel(models.Model):
    # 对应的项目
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    # 对应的图表
    web_panel = models.ForeignKey(WebPanel, on_delete=models.CASCADE)
    # 对应仪表盘或看板的编号
    num = models.IntegerField()
    # 模板名，
    template = models.CharField(max_length=255, null=True)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'project_panels'
        # ordering = ['-created_at']


"""
    使用说明：PDF
"""
def upload_to(instance, filename):
    base, extension = os.path.splitext(filename.lower())
    return f'assets/files/{base}-{AuthUtils.generate_random_code(10)}{extension}'

class WebManual(models.Model):
    # 文件位置
    file = models.FileField(upload_to=upload_to)
    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'web_manuals'
