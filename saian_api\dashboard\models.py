import datetime
import logging

from django.db import models
from django.utils import timezone

from saian_api.project.models import Project
# Create your models here.
from saian_api.regions.models import Region
from saian_api.utils.httpapi.weather import WeatherApi

class EcCriteria(models.Model):
    """
      能耗基准年数据
    """
    # 对应的项目
    # project = models.ForeignKey(Project, on_delete=models.CASCADE)
    # 能耗类型，10-电，20-水，30-冷，40-气，50-汽油
    ec_type = models.IntegerField()
    # 年月
    year_month = models.CharField(max_length=6)
    # 名字
    name = models.CharField(max_length=36, null=True, default=None)
    # 值
    value = models.CharField(max_length=20)
    # 是否作为基准
    is_refer = models.BooleanField(default=False)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'ec_criteria'
        constraints = [
            models.UniqueConstraint(fields=['ec_type', 'name', 'year_month', ], name='Unique Month')
        ]

class EcTranscription(models.Model):
    """
        手动录入每月能耗
    """
    # 能耗类型，10-电，20-水，30-冷，40-气，50-汽油
    ec_type = models.IntegerField()
    # # 能耗对象，10-总能耗，20-盘管能耗
    # ec_object = models.IntegerField()
    # 年月
    year_month = models.CharField(max_length=6)
    # 值
    value = models.CharField(max_length=20)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'ec_transcription'
        ordering = ['-year_month']
        constraints = [
            models.UniqueConstraint(fields=['year_month', 'ec_type'], name='Unique Ec Transcription')
        ]

class ProjectWeatherManager(models.Manager):
    @staticmethod
    def create_record(project):
        region = Region.objects.using(project['db_name']).get(pk=project['region_code'])
        if region:
            result = WeatherApi.get_weather(region.weather_code)
            print('项目气温小时统计: %s >>> temp: %s , humidity: %s' % (
                project['project'].name, result.get('tem'), result.get('humidity')))
            temp = result.get('tem')
            humidity = result.get('humidity').replace('%', '')

            weather = ProjectWeather(
                project=project['project'],
                temp=temp,
                humidity=humidity,
                min_temp=temp,
                max_temp=temp,
                min_humidity=humidity,
                max_humidity=humidity,
                type='hr',
            )

            weather.save(using=project['db_name'])

    @classmethod
    def query_stats(cls, weathers, stat_type, from_at, till_at):
        if weathers is None:
            logging.error('weathers is required.')
            return []
        else:
            weathers = weathers.split(',')

        if stat_type is None:
            logging.info('time_type is required.')
            return []

        if from_at is None or till_at is None:
            if stat_type == 'di':
                from_at = datetime.datetime.now().strftime('%Y-%m-%d 00:00:00')
                till_at = datetime.datetime.now().strftime('%Y-%m-%d 23:59:59')
            else:
                from_at = datetime.datetime.now().strftime('%Y-%m-%d 00:00:00')
                till_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 格式化前端传来的时间字符串 “20220310000000” =》 "2022-03-10 00:00:00"
        if from_at:
            from_at = datetime.datetime.strptime(from_at, '%Y%m%d%H%M%S')
        if till_at:
            till_at = datetime.datetime.strptime(till_at, '%Y%m%d%H%M%S')

        result = []
        if stat_type == 'di':
            stat_type = 'hr'
        elif stat_type == 'mo':
            stat_type = 'di'
        elif stat_type == 'yr':
            stat_type = 'mth'
        queryset = list(ProjectWeather.objects.filter(type=stat_type, created_at__range=[from_at, till_at]).order_by('created_at'))

        for weather in weathers:
            if weather not in ['temp', 'humidity', 'wetbulb_temp']:
                continue
            name = '温度'
            unit = '°C'
            if weather == 'humidity':
                name = '湿度'
                unit = '%'
            elif weather == 'wetbulb_temp':
                name = '湿球温度'
            values = []
            for item in queryset:
                value = getattr(item, weather)
                created_at = item.created_at - datetime.timedelta(seconds=1)
                if created_at < from_at:
                    continue
                values.append({
                    'time': datetime.datetime.strftime(created_at, '%Y-%m-%d %H:%M:%S'),
                    'value': round(float(value), 2) if (value and value != '--') else '--',
                    # 'min': round(float(item.min), 2) if item.min != '--' else '--',
                    # 'max': round(float(item.max), 2) if item.max != '--' else '--',
                })

            result.append({
                'name': name,
                'label': name,
                'unit': unit,
                'values': values,
                'identifier': weather
            })

        return result

class ProjectWeather(models.Model):
    """
        项目所在地的温湿度
    """
    # 类型的可选值
    WEATHER_TYPES = (
        ('hr', '小时'),
        ('di', '天'),
        ('mth', '月'),
        ('yr', '年')
    )

    # 对应的项目
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    # 温度，如果类型不是 hour，则代表平均温度
    temp = models.FloatField()
    # 湿度，如果类型不是 hour，则代表平均湿度
    humidity = models.FloatField()
    # 最小温度
    min_temp = models.FloatField()
    # 最大温度
    max_temp = models.FloatField()
    # 最小湿度
    min_humidity = models.FloatField()
    # 最大湿度
    max_humidity = models.FloatField()
    # 湿球温度
    wetbulb_temp = models.FloatField(null=True, default=None)
    # 大气压, 单位 hPa（百帕）
    pressure = models.FloatField(null=True, default=None)
    # 天气情况
    wea = models.CharField(max_length=255, null=True, default=None)
    # 空气质量
    air = models.CharField(max_length=255, null=True, default=None)
    # 紫外线
    uv = models.CharField(max_length=255, null=True, default=None)
    # 类型，四种：hr，di，mth，yr 除 hr 外，其他类型根据前一类计算出来
    type = models.CharField(max_length=5, choices=WEATHER_TYPES)
    # 数据创建时间, 默认当前时间
    created_at = models.DateTimeField(default=timezone.now)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "web_project_weather"

    objects = ProjectWeatherManager()
