import threading

from django.conf import settings
from django.utils.deprecation import MiddlewareMixin

# 存放当前请求的项目ID
request_cfg = threading.local()


class RouterMiddleware(MiddlewareMixin):
    def process_request(self, request):
        is_local_deployment = settings.LOCAL_DEPLOYMENT
        if is_local_deployment:
            db_name = settings.LOCAL_DEPLOYMENT_DB
        else:
            # 读取请求的项目ID
            authorization = request.headers.get("Authorization", None)

            if authorization is not None and 'project=' in authorization:
                project_id = authorization.split("=")[-1]
            else:
                project_id = request.headers.get('project', None)

            db_name = None
            if project_id and project_id != 'null':
                db_name = f'prj{project_id}db'

        if db_name:
            request_cfg.cfg = db_name

    def process_response(self, request, response):
        if hasattr(request_cfg, 'cfg'):
            del request_cfg.cfg
        return response


class DatabaseRouter:
    """数据库路由"""

    def _default_db(self):

        if hasattr(request_cfg, 'cfg'):
            return request_cfg.cfg
        else:
            return None

    def db_for_read(self, model, **hints):
        return self._default_db()

    def db_for_write(self, model, **hints):
        return self._default_db()
