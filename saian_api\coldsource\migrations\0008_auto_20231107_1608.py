# Generated by Django 3.2.19 on 2023-11-07 16:08

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('coldsource', '0007_coldsource_uni_name'),
    ]

    operations = [
        # migrations.RenameField(
        #     model_name='cseeranalyse',
        #     old_name='eer',
        #     new_name='value',
        # ),
        # migrations.RemoveField(
        #     model_name='cseeranalyse',
        #     name='cold_source',
        # ),
        migrations.AddField(
            model_name='cseeranalyse',
            name='type',
            field=models.CharField(default='hr', max_length=10),
        ),
        migrations.CreateModel(
            name='CsCop',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('formula', models.Char<PERSON>ield(max_length=500, null=True)),
                ('identifier', models.Char<PERSON>ield(max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('cold_source', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='coldsource.coldsource')),
            ],
            options={
                'db_table': 'cs_cops',
            },
        ),
        migrations.AddField(
            model_name='cseeranalyse',
            name='cs_cop',
            field=models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, to='coldsource.cscop'),
        ),
    ]
