from rest_framework.exceptions import ErrorDetail
from rest_framework.renderers import JSONRenderer

class SyJSONRender(JSONRenderer):
    def render(self, data, accepted_media_type=None, renderer_context=None):
        response_data = ResponseUtils.format(data)

        return super(SyJSO<PERSON><PERSON>, self).render(response_data, accepted_media_type, renderer_context)


class ResponseUtils:
    @staticmethod
    def format(data, result_key='data'):
        status_code = 200

        # 出错时(主要是404)不返回默认的200
        if isinstance(data, dict):
            error_detail = data.get('detail', None)
            if error_detail is not None and isinstance(error_detail, ErrorDetail):
                status_code = 404 if error_detail.code == 'not_found' else 400

        response_data = {
            'status': status_code,
            'data': None
        }

        data_not_none = data is not None

        # if data_not_none and 'status' in data:
        #     response_data['status'] = data['status']

        if data_not_none and 'count' in data:
            response_data['total'] = data['count']
            data.pop('count')

        if data_not_none and 'per_page' in data:
            response_data['per_page'] = data['per_page']
            data.pop('per_page')
        if data_not_none and 'message' in data:
            response_data['message'] = data['message']
        if data_not_none and 'results' in data:
            response_data['data'] = data['results']
        elif data_not_none and 'data' in data:
            response_data['data'] = data['data']
        elif data_not_none and result_key == 'data':
            response_data['data'] = data
        elif not data_not_none:
            response_data['data'] = None
        else:
            response_data['data'] = {result_key: data}

        if data_not_none and 'error' in data:
            response_data['error'] = data['error']
            data.pop('error')
        if data_not_none and 'detail' in data:
            response_data['error'] = data['detail']
            data.pop('detail')

        if data_not_none and 'status' in data:
            response_data['status'] = data['status']
            data.pop('status')

        if data_not_none and isinstance(data, list):
            response_data['total'] = len(data)

        return response_data

    @staticmethod
    def custom_response_format(data, data_key='data', inner_data=False):
        if data is not None:
            response = {
                'status': 200
            }
            # 如果 data 是数组类型
            if isinstance(data, list):
                response['total'] = len(data)

            if inner_data:
                response['data'] = {
                    data_key: data
                }
            else:
                response[data_key] = data
            return response
        return None
