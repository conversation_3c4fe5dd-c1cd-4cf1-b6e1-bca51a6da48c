import logging
import os
import pickle

import redis


# redis_db = os.environ.get('PROJECT_ID', 66)
redis_pwd = os.environ["REDIS_PSW"]
# redis_host = 'prj.syense.cn'
redis_host = '************'
if os.environ.get('DRF_DEBUG', 'False') == 'True':
    redis_host = 'prj.syense.cn'

class RedisHelper:
    # 自动编码解码
    redis_native_instances = {}
    # 不编码，用于存取 pickle 处理后的对象
    redis_pickle_instances = {}

    native_pool = {}
    pickle_pool = {}

    @classmethod
    def create_redis_instance(cls, project_id, native=True):
        socket_options = {
            'socket_keepalive': True
        }
        if native:
            pool = cls.native_pool.get(project_id, None)
            if pool is None:
                pool = redis.ConnectionPool(host=redis_host, port=9399, db=project_id, password=redis_pwd, decode_responses=native,
                                            socket_keepalive_options=socket_options)
                cls.native_pool[project_id] = pool
        else:
            pool = cls.pickle_pool.get(project_id, None)
            if pool is None:
                pool = redis.ConnectionPool(host=redis_host, port=9399, db=project_id, password=redis_pwd, decode_responses=native,
                                            socket_keepalive_options=socket_options)
                cls.pickle_pool[project_id] = pool

        return redis.Redis(connection_pool=pool)

    @classmethod
    def redis_instance(cls, project_id, native=True):
        if native:
            instance = cls.redis_native_instances.get(project_id, None)
            if instance is None:
                instance = cls.create_redis_instance(project_id, native)
                cls.redis_native_instances[project_id] = instance
        else:
            instance = cls.redis_pickle_instances.get(project_id, None)
            if instance is None:
                instance = cls.create_redis_instance(project_id, native)
                cls.redis_pickle_instances[project_id] = instance
        return instance

    @classmethod
    def set_hvalue(cls, project_id, name, values=None, ex=3600):
        # 设置 hash 类型
        try:
            instance = cls.redis_instance(project_id)

            if values is not None:
                pipe = instance.pipeline()
                pipe.hmset(name, values)
                pipe.expire(name, ex)
                pipe.execute()
                pipe.close()
        except Exception as e:
            logging.error(f'Redis error, failed to set hash value, {e.__str__()}')

    @classmethod
    def get_hvalue(cls, project_id, name, instance=None):
        # 查询 hash 类型
        try:
            if instance is None:
                instance = cls.redis_instance(project_id)

            return instance.hgetall(name)
        except Exception as e:
            logging.error(f'Redis error, failed to get hash value, {e.__str__()}')
            return None

    @classmethod
    def get_list(cls, project_id, name, native):
        """查询list中的所有元素"""
        try:
            instance = cls.redis_instance(project_id, native)
            return instance.lrange(name, 0, -1)
        except Exception as e:
            logging.error(f'Redis error, failed to get list-{name}, {e.__str__()}')
            return []

    @classmethod
    def get_list_tail_items(cls, project_id, name, native, count=2):
        """从 Redis 列表中获取倒数 count 个元素"""
        try:
            instance = cls.redis_instance(project_id, native)
            # 获取倒数 count 个元素，相当于索引范围 -count 到 -1
            return instance.lrange(name, -count, -1)
        except Exception as e:
            logging.error(f"Redis error, failed to get last {count} items from list-{name}, {e.__str__()}")
            return []

    @classmethod
    def get_list_tail_item(cls, project_id, name, idx, native):
        """从 Redis 列表中获取倒数第 idx 个元素（idx=1 表示最后一个）"""
        try:
            instance = cls.redis_instance(project_id, native)
            return instance.lindex(name, -idx)
        except Exception as e:
            logging.error(f"Redis error, failed to get last {idx} item from list-{name}, {e.__str__()}")
            return None

    @classmethod
    def set_list_tail_item(cls, project_id, name, idx, value, native):
        """
        更新 Redis 列表中倒数第 idx 个元素的值
        idx=1 表示最后一个，idx=2 表示倒数第二个
        """
        try:
            instance = cls.redis_instance(project_id, native)

            if not instance.exists(name):
                # 用一个占位值初始化 list
                instance.rpush(name, value)
                return True
            # Redis 支持负索引，-1 是最后一个
            instance.lset(name, -idx, value)
            return True
        except Exception as e:
            logging.error(f"Redis error, failed to set value at tail index {idx} in list-{name}, {e.__str__()}")
            return False

    @classmethod
    def push_list(cls, project_id, name, value, native, ex=4800, limit=None):
        """往list添加新元素"""
        try:
            instance = cls.redis_instance(project_id, native)

            pipeline = instance.pipeline()

            # 插入新值
            if isinstance(value, list):
                pipeline.rpush(name, *value)
            else:
                pipeline.rpush(name, value)

            # 限制最大数量
            if isinstance(limit, int) and limit > 0:
                pipeline.ltrim(name, -limit, -1)

            # 设置过期时间
            pipeline.expire(name, ex)

            pipeline.execute()
        except Exception as e:
            logging.error(f'Redis error, failed to push value-{value} to list-{name}, {e.__str__()}')
            return None

    @classmethod
    def add_set(cls, project_id, name, value, native, ex=3600):
        """往set添加元素"""
        try:
            instance = cls.redis_instance(project_id, native)
            instance.sadd(name, value)
            instance.expire(name, ex)
        except Exception as e:
            logging.error(f'Redis error, failed to add value-{value} to set-{name}, {e.__str__()}')
            return None

    @classmethod
    def get_members(cls, project_id, name, native):
        """查询set中的所有元素"""
        try:
            instance = cls.redis_instance(project_id, native)
            return instance.smembers(name)
        except Exception as e:
            logging.error(f'Redis error, failed to get list-{name}, {e.__str__()}')
            return set()

    @classmethod
    def set_value(cls, project_id, name, value, native, ex=3600):
        # 设置字符串类型
        try:
            instance = cls.redis_instance(project_id, native)
            instance.set(name, value if native else pickle.dumps(value), ex=ex)
        except Exception as e:
            logging.error(f'Redis error, failed to set value, {e.__str__()}')

    @classmethod
    def get_value(cls, project_id, name, native):
        # 查询字符串类型
        try:
            instance = cls.redis_instance(project_id, native)
            result = instance.get(name)

            if result is not None and not native:
                return pickle.loads(result)

            return result
        except Exception as e:
            logging.error(f'Redis error: failed to get value, {e.__str__()}')
            return None

    @classmethod
    def get_multi_value(cls, project_id, names, native):
        # 批量获取
        try:
            instance = cls.redis_instance(project_id, native)
            results = instance.mget(names)
            if native:
                values = results
            else:
                values = [(pickle.loads(result) if result else None) for result in results]

            return values

        except Exception as e:
            logging.error(f'Redis error: failed to get multi value, {e.__str__()}')
            return []

    @classmethod
    def set_multi_value(cls, project_id, values: dict, native, ex=3600):
        # 批量设置值
        try:
            instance = cls.redis_instance(project_id, native)
            pipeline = instance.pipeline()

            for name, obj in values.items():
                value = obj
                if not native:
                    value = pickle.dumps(obj)

                pipeline.set(name, value, ex=ex)

            pipeline.execute()
            pipeline.close()

        except Exception as e:
            logging.error(f'Redis error: failed to set multi value, {e.__str__()}')

    @classmethod
    def scan_keys(cls, project_id, pattern, native):
        instance = cls.redis_instance(project_id, native)
        pattern_keys = []

        cursor = 0
        while True:
            cursor, keys = instance.scan(cursor, match=pattern, count=1000)
            if len(keys):
                pattern_keys += keys

            if cursor == 0:
                break

        return pattern_keys

    @classmethod
    def scan_items(cls, project_id, pattern, native):
        instance = cls.redis_instance(project_id, native)
        items = []

        cursor = 0
        while True:
            cursor, keys = instance.scan(cursor, match=pattern, count=1000)
            if len(keys):
                items += instance.mget(keys)

            if cursor == 0:
                break
        if native:
            return items
        else:
            return [pickle.loads(i) for i in items]

    @classmethod
    def get_wechat_token(cls):
        """查询微信token"""
        name = 'wechat:access_token'
        return cls.get_value(0, name, True)

    @classmethod
    def set_wechat_token(cls, token, ex=3600):
        """更新微信token"""
        name = 'wechat:access_token'
        cls.set_value(0, name, token, True, ex=ex)

    @classmethod
    def delete(cls, project_id, name):
        """从Redis中移除"""
        try:
            instance = cls.redis_instance(project_id)
            instance.delete(name)
        except Exception as e:
            logging.error(f'Redis error, failed to delete {name} in project {project_id}, {e.__str__()}')

    @classmethod
    def delete_multi_obj(cls, project_id, names):
        """从Redis中批量移除"""
        try:
            instance = cls.redis_instance(project_id)
            pipeline = instance.pipeline()
            for name in names:
                pipeline.delete(name)
            pipeline.execute()
            pipeline.close()
        except Exception as e:
            logging.error(f'Redis error, failed to delete multi in project {project_id}, {e.__str__()}')

    @classmethod
    def exists(cls, project_id, name):
        try:
            instance = cls.redis_instance(project_id)

            if name is not None:
                return instance.exists(name)

        except Exception as e:
            logging.error(f'Redis error, failed to query exists in project {project_id}, {e.__str__()}')
            return False
