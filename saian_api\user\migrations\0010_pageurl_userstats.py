# Generated by Django 3.2.19 on 2024-05-15 12:01

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0009_webuser_online_time'),
    ]

    operations = [
        migrations.CreateModel(
            name='PageUrl',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('page', models.CharField(max_length=255, null=True)),
                ('api_url', models.CharField(blank=True, max_length=255, null=True)),
                ('http_method', models.CharField(blank=True, max_length=255, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='UserStats',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('periods', models.IntegerField()),
                ('web_user_id', models.IntegerField()),
                ('user_name', models.CharField(max_length=255, null=True)),
                ('user_mobile', models.CharField(blank=True, max_length=255, null=True)),
                ('ranking', models.IntegerField()),
                ('acc_times', models.IntegerField()),
                ('online_time', models.IntegerField()),
                ('acc_pages', models.IntegerField()),
                ('target_dt', models.DateTimeField()),
                ('created_at', models.DateTimeField(auto_now=True)),
            ],
        ),
    ]
