import datetime
import logging
import random
import time

import requests


class OneLinkApi:
    domain = 'https://api.iot.10086.cn'

    def __init__(self, appid: str = '', password: str = '', instance_id=0):
        self.instance_id = instance_id
        self.appid = appid
        self.password = password
        self.__token = ""
        self.__lastUpdateTime = 0
        self.__transid = ""

    def create_transid(self):
        """ 创建 transid """
        dt = datetime.datetime.now()
        return f"{self.appid}{dt.strftime('%Y%m%d%H%M%S')}{random.randint(1, 99990000)}{self.instance_id}"

    def get_token_an_transid(self):
        """ 获取接口 token 和 transid """
        if time.time() < (self.__lastUpdateTime + 1800):
            return self.__token, self.__transid
        tid = self.create_transid()
        url = f'{self.domain}/v5/ec/get/token'
        params = {
            'appid': self.appid,
            'password': self.password,
            'transid': tid
        }

        result = requests.get(url=url, params=params)
        if result.status_code == 200:
            result = result.json()

            if result.get('status', None) == '0':
                token = result.get('result')[0].get('token')

                self.__token = token
                self.__transid = tid
                self.__lastUpdateTime = time.time()

                return self.__token, self.__transid
            else:
                return None, None
        else:
            return None, None

    def send_request(self, path, params):
        """ 发送请求 """
        url = f'{self.domain}/{path}'
        token, transid = self.get_token_an_transid()
        params = {
            **params,
            'token': token,
            'transid': transid
        }
        result = requests.get(url, params=params)
        if result.status_code == 200:
            result = result.json()
            if result.get('status') == '0':
                return result
            else:
                logging.info(f'onelink 错误: url-{url}, message: {result.get("message")}')
        else:
            logging.error(f"接口请求错误，status_code: {result.status_code}, {result.text}")
            return None

    def get_imei(self, **kwargs):
        """查询绑定的 sim 卡绑定的 imei """
        path = 'v5/ec/query/sim-imei'
        result = self.send_request(path, params=kwargs)

        if result is not None:
            imei = result.get('result')[0].get('imei')
            return imei

        return None

    def get_sim_basic_info(self, **kwargs):
        """ onelink 基础信息查询接口 """
        path = 'v5/ec/query/sim-basic-info'
        result = self.send_request(path, kwargs)

        if result is not None:
            return result.get('result')[0]
        return None

    def get_platform(self, **kwargs):
        """物联卡归属平台批量查询. 批量查询 多个号码用下划线分隔，最多 20 个"""
        path = 'v5/ec/query/sim-platform/batch'
        result = self.send_request(path, kwargs)

        if result is not None:
            return result.get('result')
        return None

    def get_sim_status(self, **kwargs):
        """
            单卡状态查询
            OneLink-PB: 00-正常; 01-单向停机; 02-停机; 03-预销号; 05-过户; 06-休眠; 07-待激活; 99-号码不存在
            OneLink-CT: 1:待激活 2:已激活 4:停机 6:可测试 7:库存 8:预销户
        """
        path = 'v5/ec/query/sim-status'
        result = self.send_request(path, kwargs)

        if result is not None:
            return result.get('result')[0].get('cardStatus')
        return None

    def get_stop_reason(self, **kwargs):
        """
            单卡停机原因查询
            OneLink-PB: 01:欠费停机 02:用户挂失 03:用户主动停机 11:管理型停机
                shutdownReasonDesc 当停机原因为“11:管理型停机”，此属性不为空; 其它停机原因，此属性可为空。
            OneLink-CT:
                000000000020:强制双向停机 000000002000:主动(申请)双向停机 000020000000:信控双向停机 200000000000:窄带网商品到期失效停机
                020000000000:机卡分离停机 000200000000:M2M 管理停机 000000000000:该卡当前不处于“已停机”或系统暂无停机原因
        """
        path = 'v5/ec/query/sim-stop-reason'
        result = self.send_request(path, kwargs)

        if result is not None:
            result = result.get('result')[0]
            return result.get('stopReason'), result.get('shutdownReasonDesc', None)
        return None, None

    def get_data_consumed(self, **kwargs):
        """
            单卡本月流量累计使用量查询
            PB 号段为截至前一天 24 点流量，CT 号段为实时流量。(单位:KB)。
        """
        path = 'v5/ec/query/sim-data-usage'
        result = self.send_request(path, kwargs)

        if result is not None:
            return result.get('result')[0].get('dataAmount')
        return None

    def change_status(self, **kwargs):
        """
            单卡状态变更
            operType:
                0:申请停机(已激活转已停机)
                1:申请复机(已停机转已激活)
                2:库存转已激活
                3:可测试转库存
                4:可测试转待激活
                5:可测试转已激活
                6:待激活转已激活
        """
        path = 'v5/ec/change/sim-status'
        result = self.send_request(path, kwargs)

        if result is not None:
            return result.get('result')[0].get('orderNum')
        return None


if __name__ == '__main__':
    api = OneLinkApi(appid="200340307200200000", password="uCmidktDs")
    base_info = api.get_platform(iccids="898604A2192181871920")
    if base_info is not None:
        print(base_info)
