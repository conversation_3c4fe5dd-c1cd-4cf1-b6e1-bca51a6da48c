"""
  设备分组定时任务-重复性任务
"""

from django.core.management.base import BaseCommand, CommandError

from saian_api.utils.tools import replenishment_time
from saian_api.group.models import ActionTimer, GroupAction, AcStrategies
from saian_api.project.models import Project
from saian_api.scheduledtask.utils import get_projects, hit_time, set_global_db

import datetime
import traceback
import json


class Command(BaseCommand):
    help = '设备分组定时任务-重复性任务'

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目列表
        projects = get_projects(self, options)

        now = datetime.datetime.now()

        for project_id in projects:
            try:
                # 存储满足条件的定时任务
                hit_timers = []

                # 设置全局数据库
                set_global_db(project_id)
                project = Project.objects.get(pk=project_id)

                self.stdout.write(f"执行分组重复定时任务: {project.name}", ending='\n')
                group_actions = GroupAction.objects.filter(
                    group__in=project.group_set.all())
                action_timers = ActionTimer.objects.filter(
                    group_action__in=group_actions, is_finished=False, enabled=True, repeat=True)
                for timer in action_timers:
                    try:

                        is_effective = False
                        if timer.time_ranges:
                            time_period_list = replenishment_time(now, json.loads(timer.time_ranges))
                            for item in time_period_list:
                                if item['from_at'] <= now < item['till_at']:
                                    is_effective = True
                                    break
                        else:
                            is_effective = True

                        if hit_time(self, timer.run_date, timer.run_time, int(timer.updated_at.timestamp()), timer.week, None, timer.repeat, now) and is_effective:
                            # 更新时间和状态，避免重复执行
                            timer.updated_at = datetime.datetime.now()
                            # timer.is_finished = True
                            timer.save()

                            # ac_strategy = AcStrategies.objects.get(st_type=10, st_id=timer.id)
                            # ac_strategy.status = 20
                            # ac_strategy.save()

                            hit_timers.append(timer)

                    except Exception as e:
                        self.stderr.write(
                            f"处理分组定时任务出错，action_timer_id = {timer.id}, err = {e.__str__()}", ending='\n')
                        self.stderr.write(traceback.format_exc(), ending='')
                        continue

                for timer in hit_timers:
                    try:
                        self.stdout.write(f"execute_job, id = {timer.id}")
                        if timer.group_action is not None:
                            timer.group_action.execute_by_timer(timer)

                            # 重复任务不会结束
                            # timer.is_finished = False
                            # timer.save()
                            #
                            # ac_strategy = AcStrategies.objects.get(st_type=10, st_id=timer.id)
                            # ac_strategy.status = 10
                            # ac_strategy.save()
                        else:
                            self.stdout('group_action is nil')
                    except Exception as e:
                        self.stderr.write(
                            f"执行分组定时任务出错，action_timer_id = {timer.id}, err = {e.__str__()}", ending='\n')
                        self.stderr.write(traceback.format_exc(), ending='')

                        # 异常也要恢复原样
                        # timer.is_finished = False
                        # timer.save()
                        #
                        # ac_strategy = AcStrategies.objects.get(st_type=10, st_id=timer.id)
                        # ac_strategy.status = 10
                        # ac_strategy.save()
                        continue
            except Exception as e:
                self.stderr.write(
                    f"执行分组重复定时任务出错，project_id = {project_id}, err = {e.__str__()}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
                continue
