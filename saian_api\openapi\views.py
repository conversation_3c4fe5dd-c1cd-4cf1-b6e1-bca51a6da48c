from rest_framework import status
from rest_framework import viewsets
from rest_framework.response import Response
from rest_framework import exceptions

from saian_api.utils.intapi_auth import IntapiAuth
from saian_api.device.models import Device, DeviceAttribute
from saian_api.devdefine.models import AttributePrototype

# Create your views here.
class DatapointsViewSet(viewsets.ViewSet):
    authentication_classes = (IntapiAuth,)

    def list(self, request, *args, **kwargs):
        device_id = kwargs.get('device_id', None)
        value_only = request.query_params.get('vo', None)
        pids = request.query_params.get('pids', None)

        if device_id is None:
            raise exceptions.ValidationError(detail='设备id不能为空！')

        device = Device.objects.filter(pk=device_id, project_id=request.user['project_id']).first()
        if device is None:
            raise exceptions.NotFound(detail='找不到设备！')

        # 是否只返回值
        if value_only is not None and value_only == '1':
            value_only = True
        else:
            value_only = False

        aps = AttributePrototype.objects.filter(device_prototype_id=device.device_prototype_id, en_entapi=True)

        # 返回特定的参数点
        if pids is not None and pids != '':
            pids = str(pids).split(',')
            aps = aps.filter(id__in=pids)
        
        data = []

        aps = list(aps)
        ap_ids = [ap.id for ap in aps]
        das = DeviceAttribute.objects.query_object_list(device, ap_ids)

        for ap in aps:
            da = next(filter(lambda x: x.attribute_prototype_id == ap.id, das), None)
            if da is None:
                da = DeviceAttribute.objects.query_object_by_ap(device, ap)

            if da is None:
                value = None
                at = None
            else:
                value = da.value
                at = int(da.updated_at.timestamp())
            data_item = {
                'id': ap.id,
                'value': value,
                'at': at
            }

            if not value_only:
                data_item['identifier'] = ap.identifier
                data_item['name'] = ap.name
                data_item['data_type'] = ap.data_type
                data_item['read_only'] = ap.read_only
                data_item['options'] = ap.options
                data_item['remark'] = ap.remark
                data_item['min_value'] = ap.min_value
                data_item['max_value'] = ap.max_value
                data_item['unit'] = ap.unit
                data_item['default_value'] = ap.default_value
                data_item['in_crement'] = ap.in_crement

            data.append(data_item)
        
        res_data = {
            'status': status.HTTP_200_OK,
            'total': len(aps),
            'data': data
        }

        return Response(res_data)

class DeviceCtrlsViewSet(viewsets.GenericViewSet):
    authentication_classes = (IntapiAuth,)

    def create(self, request):
        device_id = request.data.get('device_id', None)
        args = request.data.get('args', None)
        device = Device.objects.get(pk=device_id)
        device.send_ctrls({"name": "第三方调用"}, args)

        res_data = {
            'status': status.HTTP_200_OK,
            'data': None
        }

        return Response(res_data)

class DeviceViewSet(viewsets.GenericViewSet):
    authentication_classes = (IntapiAuth,)

    def list(self, request, *args, **kwargs):
        devices = Device.objects.filter(project_id=request.user['project_id'])
        data = []
        for device in devices:
            data.append({
                'id': device.id,
                'mac': device.mac,
                'online': device.online
            })

        res_data = {
            'status': status.HTTP_200_OK,
            'total': len(devices),
            'data': data,
            'error': 'Success'
        }

        return Response(res_data)
