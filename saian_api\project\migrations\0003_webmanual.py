# Generated by Django 3.2.8 on 2022-02-18 17:35

from django.db import migrations, models
import saian_api.project.models


class Migration(migrations.Migration):

    dependencies = [
        ('project', '0002_project_web_users'),
    ]

    operations = [
        migrations.CreateModel(
            name='WebManual',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to=saian_api.project.models.upload_to)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'web_manuals',
            },
        ),
    ]
