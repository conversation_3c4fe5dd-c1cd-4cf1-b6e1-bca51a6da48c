# Generated by Django 3.2.8 on 2022-05-10 14:28

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0004_webuser_email'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserSearches',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key_words', models.CharField(max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('web_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='user.webuser')),
            ],
            options={
                'db_table': 'user_searches',
            },
        ),
    ]
