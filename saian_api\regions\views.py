import re

from rest_framework import status
# Create your views here.
from rest_framework.response import Response
from rest_framework.views import APIView

from saian_api.regions.models import Region
from saian_api.regions.serializers import RegionSerializer

class RegionList(APIView):
    def get(self, request):

        pid = request.query_params.get('pid')
        if not pid or pid == '0':
            pid = '000000'
        search_result = re.search(r'^(\d{2})(\d{2})(\d{2})$', pid, re.M | re.I)
        province = search_result.group(1)
        city = search_result.group(2)
        # 省市区各占两位，提取省市，依次判断
        # district = search_result.group(3)
        # regions = []
        # 如果省代号是 00，则返回省级的地区
        if province == '00':
            regions = Region.objects.raw("select * from web_regions where id %% 10000 = 0", )

        elif city == '00':
            startCity = int(province) * 10000
            endCity = startCity + 9900
            regions = Region.objects.raw(
                "select * from web_regions where id > %s && id < %s && id %% 100 = 0 ", [startCity, endCity]
            )

        else:
            startDistrict = int(province) * 10000 + int(city) * 100
            endDistrict = startDistrict + 99
            regions = Region.objects.raw(
                "select * from web_regions where id > %s && id < %s ", [startDistrict, endDistrict]
            )

        serializer = RegionSerializer(regions, many=True)
        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'regions': serializer.data
            }
        })


    def post(self, request):
        serializer = RegionSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(status=status.HTTP_400_BAD_REQUEST)
