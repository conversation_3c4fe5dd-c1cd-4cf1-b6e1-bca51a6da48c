import base64
import random
import string
from copy import copy
from datetime import datetime
from io import BytesIO

from captcha.image import ImageCaptcha
from django.conf import settings
from openpyxl.styles import Border, Side, Font
from openpyxl.utils import get_column_letter
from openpyxl.workbook import Workbook


class DatetimeUtils:
    far_away_date = None

    @staticmethod
    def get_date_time_from_string(datetime_str):
        # search_result = re.search(r'^(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})$', datetime_str, re.M | re.I)
        # year = int(search_result.group(1))
        # month = int(search_result.group(2))
        # day = int(search_result.group(3))
        # hour = int(search_result.group(4))
        # minute = int(search_result.group(5))
        # second = int(search_result.group(6))

        # return datetime(year, month, day, hour, minute, second)
        return datetime.strptime(datetime_str, '%Y%m%d%H%M%S')

    @classmethod
    def get_far_away_date(cls):
        if not cls.far_away_date:
            cls.far_away_date = datetime(1970, 1, 1, 0, 0, 0)
        return cls.far_away_date

class CeleryTaskUtils:
    @staticmethod
    def get_all_project():
        from saian_api.project.models import Project
        projects = settings.TASK_PROJECTS
        result = []
        for project in projects:
            db_name = f'prj{project}db'
            region_code = Project.objects.using(db_name).get(pk=project).admin_region_id
            result.append({
                'project': Project.objects.using(db_name).get(pk=project),
                'db_name': db_name,
                'region_code': region_code,
                'project_id': project
            })
        return result

class AuthUtils:
    @staticmethod
    def generate_random_char(length=8):
        """生成随机字符"""
        result = ''
        for i in range(length):
            ch = chr(random.randrange(ord('0'), ord('z') + 1))
            result += ch
        return result

    @staticmethod
    def generate_random_comm_char(length=8):
        """生成随机字符串(大小写英文字母、数字组成)"""
        return ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(length))

    @staticmethod
    def generate_random_code(length=6):
        """生成随机数字"""
        result = ''
        for i in range(length):
            ch = chr(random.randrange(ord('0'), ord('9') + 1))
            result += ch
        return result

    @staticmethod
    def generate_captcha(length=4):
        """生成登陆验证码"""
        captcha_string = ''.join(random.choice('ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ') for i in range(length))
        image = ImageCaptcha().generate_image(captcha_string)
        buffer = BytesIO()
        image.save(buffer, format='PNG')
        data = buffer.getvalue()
        b64_image = 'data:image/png;base64,' + base64.b64encode(data).decode()

        return [captcha_string, b64_image]

    @staticmethod
    def has_permissions(user_id, idx):
        from saian_api.user.models import WebUser

        """ 检查用户是否有权限；idx=0, 代表 permissions 第一位 """
        web_user = WebUser.objects.get(pk=user_id)

        permissions = web_user.permissions
        if not permissions or len(permissions) < idx + 1:
            return False
        return permissions[idx] == '1'


class ExcelUtils:
    @classmethod
    def make_cell_center(cls, target_cell):
        alignment_obj = copy(target_cell.alignment)
        alignment_obj.horizontal = 'center'
        alignment_obj.vertical = 'center'
        target_cell.alignment = alignment_obj

    @classmethod
    def generate_work_book(cls, multi_series, filename, no_mma=True):
        # 参数 no_mma 是否不显示 "最大值", "最小值", "统计值"，这一行，并移除“最大值”，“最小值”
        series_label = ''
        bold_font = Font(bold=True)
        wb = Workbook()
        ws = wb.active
        if not no_mma:
            ws.merge_cells('A1:A2')
        ws['A1'] = '时间'
        ws['A1'].font = bold_font
        # ws['A1'].style = ws['A1'].style.copy(alignment=Alignment(horizontal='center', vertical='center'))
        cls.make_cell_center(ws['A1'])

        times = set()
        for series in multi_series:
            for v in series['values']:
                times.add(v['time'])

        times = list(times)
        times.sort()

        cell_border = Border(left=Side(style='thin'))

        for series_idx, series in enumerate(multi_series):
            if not no_mma:
                series_begin_idx = series_idx * 3 + 2
                series_begin_col = get_column_letter(series_begin_idx)
                series_end_col = get_column_letter(series_idx * 3 + 4)
                ws.merge_cells(f'{series_begin_col}1:{series_end_col}1')
            else:
                series_begin_idx = series_idx + 2
                series_begin_col = get_column_letter(series_begin_idx)
            column_title = f"{series.get('label', '')}"
            if series.get('unit', ""):
                column_title += f" - {series.get('unit', '')}"
            ws[f'{series_begin_col}1'] = column_title
            ws[f'{series_begin_col}1'].font = bold_font
            cls.make_cell_center(ws[f'{series_begin_col}1'])
            if len(series_label) < 30:
                series_label += f"{series.get('label', '')},"
            elif not series_label.endswith(".."):
                series_label += "..."

            ws[f'{series_begin_col}1'].border = cell_border
            if not no_mma:
                ws[f'{get_column_letter(series_idx * 3 + 2)}2'].border = cell_border
                ws[f'{get_column_letter(series_idx * 3 + 2)}2'] = '最小值'
                ws[f'{get_column_letter(series_idx * 3 + 3)}2'] = '最大值'
                ws[f'{get_column_letter(series_idx * 3 + 4)}2'] = '统计值'
                cls.make_cell_center(ws[f'{get_column_letter(series_idx * 3 + 2)}2'])
                cls.make_cell_center(ws[f'{get_column_letter(series_idx * 3 + 3)}2'])
                cls.make_cell_center(ws[f'{get_column_letter(series_idx * 3 + 4)}2'])

        for time_idx, time in enumerate(times):
            ws[f'A{time_idx + (3 if not no_mma else 2)}'] = time
            for series_idx, series in enumerate(multi_series):
                if not no_mma:
                    series_begin_idx = series_idx * 3 + 2
                    row_idx = time_idx + 3
                else:
                    series_begin_idx = series_idx + 2
                    row_idx = time_idx + 2
                if series['values'] and len(series['values']) > time_idx and series['values'][time_idx]['time'] == time:
                    value = series['values'][time_idx]
                else:
                    value = next((x for x in series['values'] if x['time'] == time), None)
                ws[f'{get_column_letter(series_begin_idx)}{row_idx}'].border = cell_border
                if value:
                    if not no_mma:
                        ws[f'{get_column_letter(series_begin_idx)}{row_idx}'] = value.get('min', '--')
                        ws[f'{get_column_letter(series_begin_idx + 1)}{row_idx}'] = value.get('max', '--')
                        ws[f'{get_column_letter(series_begin_idx + 2)}{row_idx}'] = value.get('value', '--')
                    else:
                        ws[f'{get_column_letter(series_begin_idx)}{row_idx}'] = value.get('value', '--')

        # 调整宽度
        prev_cell_length = 9
        for idx, col in enumerate(ws.columns):
            max_length = 9

            if idx:
                column_letter = col[0].column_letter  # Get the column name
                if (idx - 1) % (3 if not no_mma else 1) == 0:
                    try:  # Necessary to avoid error on empty cells
                        label_cell_length = len(str(col[0].value).encode('gbk')) / (3 if not no_mma else 1)
                        idf_cell_length = len(str(col[1].value).encode('gbk'))

                        cell_length = max(label_cell_length, idf_cell_length)
                        if cell_length > max_length:
                            max_length = cell_length

                    except:
                        pass
                    prev_cell_length = max_length
                else:
                    max_length = prev_cell_length
            else:
                column_letter = "A"
                max_length = 20

            adjusted_width = max_length
            ws.column_dimensions[column_letter].width = adjusted_width
        filename = f'{filename}({series_label[:-1]})__{AuthUtils.generate_random_comm_char(9)}'
        # 移除文件名中的特殊符号(#)
        filename = filename.replace('#', '')
        path = f'drf-assets/files/{filename}.xlsx'
        wb.save(filename=path)

        return path, filename

    @classmethod
    def generate_ec_report(cls, stats, time_range, filename):
        wb = Workbook()
        ws = wb.active
        bold_font = Font(bold=True)
        ws.merge_cells('A1:A2')
        ws['A1'].value = '电表名字'
        ws['A1'].font = bold_font
        cls.make_cell_center(ws['A1'])

        cell_border = Border(left=Side(style='thin'))

        for time_idx, time in enumerate(time_range):
            col = get_column_letter(time_idx + 1 + 1)
            column_title = time
            ws[f'{col}1'].value = column_title
            ws[f'{col}1'].font = bold_font
            ws[f'{col}2'].value = '能耗（KWh）'
            ws[f'{col}2'].font = bold_font

            cls.make_cell_center(ws[f'{col}1'])
            cls.make_cell_center(ws[f'{col}2'])

        for stat_idx, stat in enumerate(stats):
            ws[f'A{stat_idx + 3}'] = stat['name']
            cls.make_cell_center(ws[f'A{stat_idx + 3}'])

            for time_idx, time in enumerate(time_range):
                cell = ws[f'{get_column_letter(time_idx + 1 + 1)}{stat_idx + 3}']
                cell.border = cell_border
                value = stat['data'][time_idx]
                cell.value = value
                cls.make_cell_center(cell)

        # 调整宽度
        for idx, col in enumerate(ws.columns):
            max_length = 9

            if idx:
                column_letter = col[0].column_letter  # Get the column name
                try:  # Necessary to avoid error on empty cells
                    cell_length = max([len(str(row.value).encode('gbk')) for row in col])
                    if cell_length > max_length:
                        max_length = cell_length + 3
                except:
                    pass

            else:
                column_letter = "A"
                max_length = 30

            adjusted_width = max_length
            ws.column_dimensions[column_letter].width = adjusted_width
        filename = f'{filename}__{AuthUtils.generate_random_comm_char(9)}'
        path = f'drf-assets/files/{filename}.xlsx'
        wb.save(filename=path)

        return path, filename

    @classmethod
    def generate_plain_sheet(cls, records, filename):
        """ 将列表(字典或对象)导出为 excel """
        from saian_api.utils.tools import object_to_dict

        wb = Workbook()
        ws = wb.active
        bold_font = Font(bold=True)
        cell_border = Border(left=Side(style='thin'))

        # sheet data
        tree_data = []

        for idx, record in enumerate(records):
            if not isinstance(record, dict):
                record_dict = object_to_dict(record)
            else:
                record_dict = record
            if idx == 0:
                tree_data.append(list(record_dict.keys()))
            tree_data.append(list(record_dict.values()))

        for row in tree_data:
            ws.append(row)

        # 样式调整
        for row in ws['A1:Z1']:
            for cell in row:
                cls.make_cell_center(cell)
                cell.font = bold_font
                cell.border = cell_border

        # 调整宽度
        for idx, col in enumerate(ws.columns):
            max_length = 9

            column_letter = col[0].column_letter  # Get the column name
            try:  # Necessary to avoid error on empty cells
                cell_length = max([len(str(row.value).encode('gbk')) for row in col])
                if cell_length > max_length:
                    max_length = cell_length + 3
            except:
                pass

            adjusted_width = max_length
            ws.column_dimensions[column_letter].width = adjusted_width

        # filename = f'{filename}__{AuthUtils.generate_random_comm_char(9)}'
        # path = f'/data/bulk_tasks/{filename}.xlsx'
        path = f'drf-assets/files/{filename}.xlsx'
        wb.save(filename=path)

        return path, wb
