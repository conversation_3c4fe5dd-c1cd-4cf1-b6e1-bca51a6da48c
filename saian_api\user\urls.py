from django.urls import path

from .views import (WebUserViewSet, MyInfoView, WebUserSession, WebUserResetPassword,
                    WebRoleView, WebUserRoleView, LoginCaptchaView, WebRoleDetailView, UserDeviceStatViewSet, EcLoginView, UserSearchesView,
                    UserDeviceViewSet, LegacyAuthView, UserLogView, UserManualViewSet, ActiveUserView, UserStatView, UserDimensionView)

user_device_stat = UserDeviceStatViewSet.as_view({
    'get': 'list'
})

web_user_list = WebUserViewSet.as_view({
    'get': 'list',
    'post': 'create'
})

web_user_detail = WebUserViewSet.as_view({
    'get': 'retrieve',
    'put': 'update',
    'delete': 'destroy'
})

user_searches_list = UserSearchesView.as_view({
    'get': 'list',
    'delete': 'destroy'
})

user_device = UserDeviceViewSet.as_view({
    'get': 'list',
    'post': 'create',
    'delete': 'destroy'
})

use_logs = UserLogView.as_view({
    'get': 'list',
    'post': 'create'
})

user_manual = UserManualViewSet.as_view({
    'get': 'list'
})

active_users = ActiveUserView.as_view({
    'get': 'list'
})

user_stats = UserStatView.as_view({
    'get': 'list'
})

user_dimensions = UserDimensionView.as_view({
    'get': 'list',
    'post': 'create'
})

urlpatterns = [
    path('saianapi/v1/web_users', web_user_list),
    path('saianapi/v1/web_users/<int:pk>', web_user_detail),
    path('saianapi/v1/web_users/my', MyInfoView.as_view()),
    path('saianapi/v1/web_users/legacy_auth', LegacyAuthView.as_view()),
    path('saianapi/v1/web_user_sessions', WebUserSession.as_view()),
    path('saianapi/v1/local_forgot_passwords', WebUserResetPassword.as_view()),

    path('saianapi/v1/web_roles', WebRoleView.as_view()),
    path('saianapi/v1/web_roles/<int:pk>', WebRoleDetailView.as_view()),
    path('saianapi/v1/web_user_roles', WebUserRoleView.as_view()),
    path('saianapi/v1/web_user_roles/<int:pk>', WebUserRoleView.as_view()),
    path('saianapi/v1/login_captcha', LoginCaptchaView.as_view()),
    path('saianapi/v1/user_device_stats', user_device_stat, name='user_device_stat'),
    path('saianapi/v1/ec_logins', EcLoginView.as_view()),

    path('saianapi/v1/user_manuals/mini', user_manual, name="user_manual"),

    path('saianapi/v1/user_searches', user_searches_list),
    path('saianapi/v1/user_searches/<int:pk>', user_searches_list),
    path('saianapi/v1/user_devices', user_device),
    path('saianapi/v1/user_devices/<int:pk>', user_device),
    # 用户操作日志
    path('saianapi/v5/user_logs', use_logs, name="user_logs"),
    # 用户活跃排名
    path('saianapi/v5/active_users', active_users, name="active_users"),

    path('saianapi/v5/my_stats', user_stats, name="user_stats"),
    # 用户所属维度
    path('saianapi/v5/user_dimensions', user_dimensions, name="user_dimensions")
]
