"""
    去除冷源平面图的重复元素
"""
import datetime
import json
import traceback

from django.core.management import BaseCommand, CommandError

from saian_api.coldsource.models import CsCop, CsEerAnalyse, ColdSource
from saian_api.dashboard.models import ProjectWeather
from saian_api.project.models import Project
from saian_api.regions.models import Region
from saian_api.scheduledtask.utils import get_projects, set_global_db
from saian_api.utils.httpapi.weather import WeatherApi

class Command(BaseCommand):
    help = "去除冷源平面图的重复元素"

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目ID列表
        projects = get_projects(self, options)

        now = datetime.datetime.now()

        for project_id in projects:
            try:
                # 设置全局数据库
                set_global_db(project_id)

                cs = ColdSource.objects.get(pk=1)

                clear_arr = []

                cs_json = json.loads(cs.coords_arr)
                for item in cs_json:
                    mac = item.get('mac', None)
                    identifier = item.get('identifier', None)
                    left = item.get('left', None)
                    top = item.get('right', None)
                    data_type = item.get('data_type', None)
                    prefix = item.get('prefix', None)
                    idx = item.get('idx', None)
                    font_color = item.get('font_color', None)
                    value = item.get('value', None)
                    type = item['type']

                    queryset = filter(lambda x: x['type'] == type, clear_arr)
                    if mac is not None:
                        queryset = filter(lambda x: x.get('mac', None) == mac, queryset)
                    if identifier is not None:
                        queryset = filter(lambda x: x.get('identifier', None) == identifier, queryset)
                    if left is not None:
                        queryset = filter(lambda x: x.get('left', None) == left, queryset)
                    if top is not None:
                        queryset = filter(lambda x: x.get('top', None) == top, queryset)
                    if data_type is not None:
                        queryset = filter(lambda x: x.get('data_type', None) == data_type, queryset)
                    if prefix is not None:
                        queryset = filter(lambda x: x.get('prefix', None) == prefix, queryset)
                    if idx is not None:
                        queryset = filter(lambda x: x.get('idx', None) == idx, queryset)
                    if font_color is not None:
                        queryset = filter(lambda x: x.get('font_color', None) == font_color, queryset)
                    if value is not None:
                        queryset = filter(lambda x: x.get('value', None) == value, queryset)

                    if len(list(queryset)) == 0:
                        clear_arr.append(item)

                print(f'original item count: {len(cs_json)}, clear item count: {len(clear_arr)}')
                cs.coords_arr = json.dumps(clear_arr)
                cs.save()

            except CommandError:
                self.stderr.write(f"运行'去除冷源平面图的重复元素'任务失败，项目ID：{project_id}, 命令参数不合法！", ending='\n')
                continue
            except Exception as e:
                self.stderr.write(f"运行'去除冷源平面图的重复元素'任务失败，项目ID：{project_id}, error: {e.__str__()}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
                continue
