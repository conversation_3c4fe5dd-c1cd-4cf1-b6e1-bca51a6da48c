import calendar
import datetime
import logging
import os
from collections import defaultdict
from copy import copy
import json
import re
from decimal import Decimal
from operator import itemgetter

from chinese_calendar import is_workday

from pathlib import Path

from django.db.models import Q, Sum
from django.shortcuts import get_object_or_404

from saian_api.dimension.models import (Dimension, DimensionAttribute, DimensionHourlyStat, DimensionDailyStat, DimensionWeeklyStat,
                                        DimensionMonthlyStat, DimensionYearlyStat)
from saian_api.dashboard.models import EcCriteria, ProjectWeather
from saian_api.terminal.models import Terminal, TerminalAttribute

from openpyxl import Workbook, load_workbook
from openpyxl.utils import get_column_letter
from openpyxl.styles import Border, Side

from django.contrib.contenttypes.models import ContentType
from rest_framework import exceptions
from rest_framework import viewsets, status
from rest_framework.response import Response

from saian_api.building.models import ActiveRoom, Building
from saian_api.devdefine.models import DevicePrototype, AttributePrototype, DevicePrototypeRatio
from saian_api.device.models import Device, <PERSON>ceEvent, <PERSON>Device, DeviceAttribute
from saian_api.report.models import (DeviceDailyStat, DeviceHourlyStat, ManualRecord, ReportConfigurer, DlTask, StatReport, DeviceMonthlyStat)
from .serializers import (ReportSerializer, SimpleDeviceSerializer, SimpleRoomSerializer, UserChartSerializer, UserAnalyseSerializer,
                          ManualRecordSerializer, DlTaskSerializer, StatReportSerializer,
                          SettlePartySerializer, SettleReferSerializer, SettleRecordSerializer, DevstlRecordSerializer)
from saian_api.utils.sy_jsonrenderer import SyJSONRender
from saian_api.utils.tools import get_prefix, get_qty, is_number, gran_to_Str, ec_type_to_unit, sanitize_filename
from saian_api.utils.utils import AuthUtils
from saian_api.device.views import DeviceAttributeViewV5

from .models import DeviceMinuteStat, UserChart, UserAnalyse, UserAnalysisCharts, SettleParty, SettleRefer, SettleRecord, DevstlRecord
from .. import settings
from ..dashboard.serializers import ProjectWeatherSerializer
from ..user.models import WebUser
from ..utils.httpapi.weather import WeatherApi

# Create your views here.


class ReportViewSet(viewsets.ModelViewSet):
    renderer_classes = (SyJSONRender,)
    serializer_class = ReportSerializer

    # 小程序请求的报表配置
    def get_queryset(self):
        device_id = self.request.query_params.get('device_id')
        device_prototype_id = self.request.query_params.get('device_prototype_id')
        active_room_id = self.request.query_params.get('active_room_id')
        queryset = ReportConfigurer.objects.all()

        # 中间变量
        con = None
        target_type = None
        device = None

        # 查询设备类型的报表
        if device_id:
            device = Device.objects.get(pk=device_id)
            device_prototype_id = device.device_prototype_id
            con = device.device_prototype

        if device_prototype_id is not None:
            con = DevicePrototype.objects.get(pk=device_prototype_id)

        # 查询房间的报表
        if active_room_id is not None:
            con = ActiveRoom.objects.get(pk=active_room_id)

        if con is not None:
            target_type = ContentType.objects.get_for_model(con)

        if target_type is not None:
            queryset = queryset.filter(target_id=con.id, target_type_id=target_type.id)

            # 关键词对图表名称模糊搜索
            search = self.request.query_params.get('search')
            if search is not None:
                queryset = queryset.filter(name__icontains=search)

            idx = self.request.query_params.get('idx')
            prefix = self.request.query_params.get('prefix')

            if idx is not None and prefix is not None:
                queryset = queryset.filter(
                    device_idx=idx,
                    identifier__startswith=str(prefix) + '_'
                ).order_by('seq')

            # 冷源和板换只返回系统级的报表配置
            elif device is not None:
                device_type = device.device_type
                if device_type.parent_id is not None:
                    device_type = device_type.parent

                if device_type.uni_name == '冷源':
                    queryset = queryset.filter(device_idx=0)

        # 设备只查询数值类型
        if active_room_id is None:
            ids = []
            for rc in queryset:
                aps = AttributePrototype.objects.filter(device_prototype_id=rc.target_id, identifier=rc.identifier)
                if aps.exists():
                    ap = aps.last()
                    if ap.data_type == 20:
                        ids.append(rc.id)
            queryset = queryset.filter(id__in=ids)

        return queryset.order_by('seq')

    def list(self, request, *args, **kwargs):
        result = super(ReportViewSet, self).list(request, *args, **kwargs).data
        reports = result['results']
        device_id = request.query_params.get('device_id', None)

        if device_id is not None:
            device = Device.objects.get(pk=device_id)
            target_type = ContentType.objects.get_for_model(DevicePrototype)

            identifiers = [report['identifier'] for report in reports if report['target_type'] == target_type.id]
            aps = list(AttributePrototype.objects.filter(device_prototype_id=device.device_prototype_id, identifier__in=identifiers))

            for report in reports:
                # 处理 ap
                report['attribute_prototype'] = None
                ap = next(filter(lambda x: x.identifier == report['identifier'], aps), None)
                if ap is not None:
                    report['attribute_prototype'] = {
                        'id': ap.id,
                        'name': ap.name,
                        'label': ap.label
                    }

                del report['target_id']
                del report['target_type']

        res_data = {
            'reports': reports
        }

        return Response(res_data)


class WebReportViewSet(viewsets.ModelViewSet):
    renderer_classes = (SyJSONRender,)
    serializer_class = ReportSerializer

    # web请求的报表配置
    def get_queryset(self):
        device_id = self.request.query_params.get('device_id')
        device_prototype_id = self.request.query_params.get('device_prototype_id')
        active_room_id = self.request.query_params.get('active_room_id')

        con = None
        queryset = None
        target_type = None
        device = None
        # 查询设备类型的报表
        if device_id is not None:
            device = Device.objects.get(pk=device_id)
            con = device.device_prototype

        if device_prototype_id is not None:
            con = DevicePrototype.objects.get(pk=device_prototype_id)

        # 查询房间的报表
        if active_room_id is not None:
            con = ActiveRoom.objects.get(pk=active_room_id)

        if con is not None:
            target_type = ContentType.objects.get_for_model(con)

        if target_type is not None:
            queryset = ReportConfigurer.objects.filter(target_id=con.id, target_type_id=target_type.id).order_by('seq')

            idx = self.request.query_params.get('idx')
            prefix = self.request.query_params.get('prefix')

            if idx is not None and prefix is not None:
                queryset = ReportConfigurer.objects.filter(
                    target_id=con.id,
                    target_type_id=target_type.id,
                    device_idx=idx,
                    identifier__startswith=str(prefix)
                ).order_by('seq')
            elif idx is None and prefix is not None:
                queryset = ReportConfigurer.objects.filter(
                    target_id=con.id,
                    target_type_id=target_type.id,
                    identifier__startswith=str(prefix)
                ).order_by('seq')
            else:
                # 由于web还没把子设备的报表和设备区分开，而是全部返回
                # 所以需要根据子设备数量的配置，只返回对应的报表
                if device is not None:
                    ids = []
                    try:
                        for rc in queryset:
                            prefix = get_prefix(rc.identifier)
                            qty = get_qty(device, prefix)

                            if qty is not None and qty != 0:
                                if rc.device_idx != 0 and rc.device_idx <= qty:
                                    ids.append(rc.id)
                                # else:
                                #     ids.append(rc.id)
                            else:
                                ids.append(rc.id)

                        # 重新查询queryset
                        queryset = ReportConfigurer.objects.filter(id__in=ids).order_by('seq', 'device_idx')

                    except ValueError as e:
                        logging.error(e.with_traceback)

        return queryset

    def list(self, request, *args, **kwargs):
        result = super(WebReportViewSet, self).list(request, *args, **kwargs).data
        reports = result['results']

        device_id = request.query_params.get('device_id', None)
        if device_id is not None:
            device = Device.objects.get(pk=device_id)
            target_type = ContentType.objects.get_for_model(DevicePrototype)

            identifiers = [report['identifier'] for report in reports if report['target_type'] == target_type.id]
            aps = list(AttributePrototype.objects.filter(device_prototype_id=device.device_prototype_id, identifier__in=identifiers))
            rcs = list(ReportConfigurer.objects.filter(target_type=target_type, target_id=device.device_prototype_id, identifier__in=identifiers))

            for report in reports:
                # 处理 ap
                report['attribute_prototype'] = None
                ap = next(filter(lambda x: x.identifier == report['identifier'], aps), None)
                if ap is not None:
                    report['attribute_prototype'] = {
                        'id': ap.id,
                        'name': ap.name,
                        'label': ap.label
                    }

                # 处理 label
                rc = next(filter(lambda x: x.identifier == report['identifier'], rcs), None)
                idf_attr = report.get('attribute_prototype', None)

                if rc is not None:
                    report['label'] = ReportConfigurer.objects.get_report_label(device, rc, idf_attr.get('label', None) if idf_attr else None)
                else:
                    report['label'] = idf_attr.get('label', None) if idf_attr is not None else None

                del report['target_id']
                del report['target_type']

        res_data = {
            'reports': reports,
            'count': result['count']
        }

        return Response(res_data)


class DeviceStatViewSet(viewsets.ViewSet):
    @classmethod
    def query_stats(cls, device_id, name, stat_type, from_at, till_at, names):
        rc, label = None, None

        values, multiSeries, total = [], [], 0

        if device_id is not None:
            device = Device.objects.get(pk=device_id)
        else:
            raise exceptions.ValidationError(detail={'detail': 'Did is required!'})

        if stat_type is None:
            raise exceptions.ValidationError(detail={'detail': 'Type is required!'})

        if name is None and names is None:
            raise exceptions.ValidationError(detail={'detail': 'Name or names is required!'})

        if from_at is None or till_at is None:
            if stat_type == 'di':
                from_at = datetime.datetime.now().strftime('%Y-%m-%d 00:00:00')
                till_at = datetime.datetime.now().strftime('%Y-%m-%d 23:59:59')
            else:
                from_at = datetime.datetime.now().strftime('%Y-%m-%d 00:00:00')
                till_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 格式化前端传来的时间字符串 “20220310000000” =》 "2022-03-10 00:00:00"
        if from_at:
            from_at = datetime.datetime.strptime(from_at, '%Y%m%d%H%M%S').strftime('%Y-%m-%d %H:%M:%S')
        if till_at:
            till_at = datetime.datetime.strptime(till_at, '%Y%m%d%H%M%S').strftime('%Y-%m-%d %H:%M:%S')

        device_prototype = DevicePrototype.objects.get(pk=device.device_prototype_id)

        target_type = ContentType.objects.get_for_model(device_prototype)

        if name is not None:
            rc = ReportConfigurer.objects.filter(target_type=target_type, target_id=device_prototype.id, name=name).first()
            if rc is not None:
                attr = AttributePrototype.objects.filter(identifier=rc.identifier, device_prototype_id=device.device_prototype_id).first()
                if attr is not None:
                    label = ''.join([device.nick_name, attr.name])
                    values = cls.single_series(device, rc, stat_type, from_at, till_at)

        if names is not None:
            rcs = ReportConfigurer.objects.filter(target_type=target_type, target_id=device_prototype.id, name__in=names.split(','))
            if rcs.count() > 0:
                multiSeries, total = cls.multiple_series(device, rcs, stat_type, from_at, till_at)

        return rc, label, values, multiSeries, total

    # 根据条件查询设备的报表格式数据
    def list(self, request, *args, **kwargs):
        device_id = request.query_params.get('did', None)
        name = request.query_params.get('name', None)
        stat_type = request.query_params.get('type', None)
        from_at = request.query_params.get('from', None)
        till_at = request.query_params.get('till', None)
        names = request.query_params.get('names', None)

        rc, label, values, multiSeries, total = self.query_stats(device_id, name, stat_type, from_at, till_at, names)

        res_data = {
            'data': {
                'status': status.HTTP_200_OK,
                'name': rc.name if rc is not None else '',
                'label': label if label is not None else '',
                'unit': rc.unit if rc is not None else '',
                'refer_point': rc.refer_point if rc is not None else '',
                'values': values,
                'total': total,
                'multiSeries': multiSeries
            },
            "status": 200
        }

        return Response(res_data)

    # 单个系列的数据
    @classmethod
    def single_series(cls, device, rc, stat_type, from_at, till_at):
        values = []

        # 实时数据
        if stat_type == 'rt':
            values = DeviceEvent.objects.get_data_by_idf(device.id, rc.identifier, from_at, till_at)

        # 分钟统计数据
        if stat_type == 'min':
            values = DeviceMinuteStat.objects.get_report_stat(device, rc.identifier, from_at, till_at, 1)

        # 分钟统计数据,间隔为5
        if stat_type == '5min':
            values = DeviceMinuteStat.objects.get_report_stat(device, rc.identifier, from_at, till_at, 5)

        # 小时统计数据
        if stat_type == 'hr':
            values = DeviceHourlyStat.objects.get_report_stat(device, rc.identifier, from_at, till_at)

        # 日统计数据
        if stat_type == 'di':
            values = DeviceDailyStat.objects.get_report_stat(device, rc.identifier, from_at, till_at)

        # 月统计数据
        if stat_type == 'mo' or stat_type == 'mth':
            values = DeviceMonthlyStat.objects.get_report_stat(device, rc.identifier, from_at, till_at)

        return values

    # 多个系列的数据
    @classmethod
    def multiple_series(cls, device, rcs, stat_type, from_at, till_at):
        data = []
        total = 0

        for rc in rcs:
            item = {
                'name': rc.name
            }
            idf_attr = AttributePrototype.objects.get_by_idf(device, rc.identifier)
            if idf_attr is not None:
                item['label'] = ReportConfigurer.objects.get_report_label(device, rc, idf_attr.label)
                item['options'] = idf_attr.options
                item['data_type'] = idf_attr.data_type
                item['unit'] = rc.unit
                item['refer_point'] = rc.refer_point
                item['values'] = cls.single_series(device, rc, stat_type, from_at, till_at)
                item['identifier'] = rc.identifier

                total += len(item['values'])

                data.append(item)

        return data, total

    def create(self, request, *args, **kwargs):

        def make_cell_center(target_cell):
            alignment_obj = copy(target_cell.alignment)
            alignment_obj.horizontal = 'center'
            alignment_obj.vertical = 'center'
            target_cell.alignment = alignment_obj

        device_id = request.data.get('did', None)
        name = request.data.get('name', None)
        stat_type = request.data.get('type', None)
        from_at = request.data.get('from', None)
        till_at = request.data.get('till', None)
        names = request.data.get('names', None)

        filename = ''
        series_label = ''
        if device_id is not None:
            device = Device.objects.get(pk=device_id)
            filename = device.nick_name

        _, _, _, multiSeries, _ = self.query_stats(device_id, name, stat_type, from_at, till_at, names)

        if multiSeries:
            wb = Workbook()
            ws = wb.active
            ws.merge_cells('A1:A2')
            ws['A1'] = '时间'
            # ws['A1'].style = ws['A1'].style.copy(alignment=Alignment(horizontal='center', vertical='center'))
            make_cell_center(ws['A1'])

            times = set()
            for series in multiSeries:
                for v in series['values']:
                    times.add(v['time'])

            times = list(times)
            times.sort()

            cell_border = Border(left=Side(style='thin'))

            for series_idx, series in enumerate(multiSeries):
                series_begin_col = get_column_letter(series_idx * 3 + 2)
                series_end_col = get_column_letter(series_idx * 3 + 4)
                ws.merge_cells(f'{series_begin_col}1:{series_end_col}1')
                ws[f'{series_begin_col}1'] = f"{series.get('label', '')} - {series.get('unit', '')}"
                make_cell_center(ws[f'{series_begin_col}1'])
                if len(series_label) < 30:
                    series_label += f"{series.get('label', '')},"
                elif not series_label.endswith(".."):
                    series_label += "..."

                ws[f'{series_begin_col}1'].border = cell_border
                ws[f'{get_column_letter(series_idx * 3 + 2)}2'].border = cell_border

                ws[f'{get_column_letter(series_idx * 3 + 2)}2'] = '最小值'
                ws[f'{get_column_letter(series_idx * 3 + 3)}2'] = '最大值'
                ws[f'{get_column_letter(series_idx * 3 + 4)}2'] = '统计值'
                make_cell_center(ws[f'{get_column_letter(series_idx * 3 + 2)}2'])
                make_cell_center(ws[f'{get_column_letter(series_idx * 3 + 3)}2'])
                make_cell_center(ws[f'{get_column_letter(series_idx * 3 + 4)}2'])

            for time_idx, time in enumerate(times):
                ws[f'A{time_idx + 3}'] = time
                for series_idx, series in enumerate(multiSeries):
                    if series['values'] and len(series['values']) > time_idx and series['values'][time_idx]['time'] == time:
                        value = series['values'][time_idx]
                    else:
                        value = next((x for x in series['values'] if x['time'] == time), None)
                    ws[f'{get_column_letter(series_idx * 3 + 2)}{time_idx + 3}'].border = cell_border
                    if value:
                        ws[f'{get_column_letter(series_idx * 3 + 2)}{time_idx + 3}'] = value.get('min', None)
                        ws[f'{get_column_letter(series_idx * 3 + 3)}{time_idx + 3}'] = value.get('max', None)
                        ws[f'{get_column_letter(series_idx * 3 + 4)}{time_idx + 3}'] = value.get('value', None)

            # 调整宽度
            prev_cell_length = 9
            for idx, col in enumerate(ws.columns):
                max_length = 9

                if idx:
                    column_letter = col[1].column_letter  # Get the column name
                    if (idx - 1) % 3 == 0:
                        try:  # Necessary to avoid error on empty cells
                            label_cell_length = len(str(col[0].value).encode('gbk')) / 3
                            idf_cell_length = len(str(col[1].value).encode('gbk'))

                            cell_length = max(label_cell_length, idf_cell_length)
                            if cell_length > max_length:
                                max_length = cell_length

                        except:
                            pass
                        prev_cell_length = max_length
                    else:
                        max_length = prev_cell_length
                else:
                    column_letter = "A"
                    max_length = 20

                adjusted_width = max_length
                ws.column_dimensions[column_letter].width = adjusted_width
            filename = f'{filename}({series_label[:-1]})__{AuthUtils.generate_random_comm_char(9)}'
            # 替换掉非法字符
            filename = sanitize_filename(filename)
            path = f'drf-assets/files/{filename}.xlsx'
            wb.save(filename=path)
            return Response({
                'status': status.HTTP_200_OK,
                'data': {
                    'file_url': path,
                    'filename': filename
                }
            })
        else:
            return Response({
                'status': status.HTTP_200_OK,
                'data': {
                    'file_url': None,
                    'filename': filename
                }
            })


class EcApportionView(viewsets.ModelViewSet):
    renderer_classes = (SyJSONRender,)

    def list(self, request, *args, **kwargs):
        time_from = self.request.query_params.get('from', None)
        time_till = self.request.query_params.get('till', None)
        building_id = self.request.query_params.get('building_id', None)
        # 10-基于设备统计， 20-基于房间统计
        stats_type = self.request.query_params.get('stats_type', '10')

        device_queryset = Device.objects.filter(project__isnull=False)

        # TODO 嘉瑞禾使用软运行时间
        dp_id = request.query_params.get('dp_id', None)
        if dp_id is not None:
            device_queryset = device_queryset.filter(device_prototype_id=dp_id)
        else:
            device_queryset = device_queryset.filter(device_prototype_id__in=[ratio.device_prototype.id for ratio in DevicePrototypeRatio.
                                                     objects.filter(params__isnull=False).exclude(params='')])

        if building_id is not None:
            device_queryset = device_queryset.filter(activeroom__building_id=building_id)

        device_ids = device_queryset.values_list('id', flat=True)
        runtime_queryset = DeviceHourlyStat.objects.filter(device_id__in=device_ids, identifier__endswith='SoftRuntime')
        if time_from is not None:
            runtime_queryset = runtime_queryset.filter(created_at__gte=datetime.datetime.strptime(time_from, '%Y%m%d%H%M%S'))

        if time_till is not None:
            runtime_queryset = runtime_queryset.filter(created_at__lte=datetime.datetime.strptime(time_till, '%Y%m%d%H%M%S'))

        results = []
        count = 0

        if stats_type == '10':
            count = device_queryset.count()
            device_queryset = self.paginate_queryset(device_queryset)
            for device in device_queryset:
                runtimes = runtime_queryset.filter(device_id=device.id)
                sum_time = sum([float(time.avg) for time in runtimes])
                room = device.active_room
                if room is not None:
                    room = SimpleRoomSerializer(room).data

                # rated_power = DeviceAttribute.objects.get_by_idf(device, 'RatedPower')
                rated_power = DeviceAttribute.objects.query_object_by_idf(device, 'RatedPower')
                results.append({
                    'id': device.id,
                    'mac': device.mac,
                    'nick_name': device.nick_name,
                    'runtime': {
                        'value': sum_time,
                        'unit': 'minute'
                    },
                    'room': room,
                    'rated_power': {
                        'value': rated_power.value if rated_power else None,
                        'unit': rated_power.attribute_prototype.unit if rated_power else None
                    },
                    'power_cons': {
                        'value': (float(rated_power.value) / 1000) * (sum_time / 60) if (rated_power and is_number(rated_power.value)) else None,
                        'unit': 'kWh'
                    }
                })
        elif stats_type == '20':
            device_ids = device_queryset.values_list('id', flat=True)
            device_model = ContentType.objects.get_for_model(Device)
            room_ids = set(RoomDevice.objects.filter(object_id__in=device_ids, content_type=device_model).values_list('active_room', flat=True))
            rooms = ActiveRoom.objects.filter(id__in=room_ids)
            count = rooms.count()
            rooms = self.paginate_queryset(rooms)
            for room in rooms:
                room_devices = RoomDevice.objects.filter(active_room=room, content_type=device_model)
                devices = []
                sum_runtime = 0
                power_cons = 0
                for device in Device.objects.filter(id__in=set(room_devices.values_list('object_id', flat=True))):
                    # rated_power = DeviceAttribute.objects.get_by_idf(device, 'RatedPower')
                    rated_power = DeviceAttribute.objects.query_object_by_idf(device, 'RatedPower')
                    runtimes = runtime_queryset.filter(device_id=device.id)
                    device_runtime = sum(map(lambda x: float(x), runtimes.values_list('avg', flat=True)))
                    device_power_cons = (float(rated_power.value) / 1000) * (device_runtime / 60) if (
                            rated_power and is_number(rated_power.value)) else 0
                    power_cons += device_power_cons
                    sum_runtime += device_runtime
                    devices.append({
                        'id': device.id,
                        'nick_name': device.nick_name,
                        'mac': device.mac,
                        'runtime': device_runtime,
                        'rated_power': rated_power.value if rated_power else None,
                        'power_cons': device_power_cons
                    })

                results.append({
                    "id": room.id,
                    "name": room.name,
                    "devices": devices,
                    "runtime": {
                        "value": sum_runtime,
                        'unit': 'minute',
                    },
                    "power_cons": {
                        "value": power_cons,
                        "unit": 'kWh'
                    }
                })

        return Response({
            'status': status.HTTP_200_OK,
            'data': results,
            'count': count
        })


class ChartDataViewSet(viewsets.GenericViewSet):
    def list(self, request, *args, **kwargs):
        from saian_api.device.models import Terminal

        uni_name = request.query_params.get('uni_name', None)
        chart_type = request.query_params.get('chart_type', None)
        if chart_type is not None:
            chart_type = int(chart_type)
        data_src = request.query_params.get('data_src', None)
        data_id = request.query_params.get('data_id', None)
        data_name = request.query_params.get('data_name', None)
        from_at = request.query_params.get('from', None)
        till_at = request.query_params.get('till', None)
        gran = request.query_params.get('gran', None)
        ec_type = request.query_params.get('ec_type', None)

        if not data_src:
            raise exceptions.ValidationError('data_src must be present!')

        # if not uni_name and not data_name:
        #     raise exceptions.ValidationError('uni_name or data_names must be present!')

        count = 0
        series = None
        pie_data = None
        table_data = None
        single_data = None
        scatter_data = None

        # 定制数据
        if int(data_src) == 10:
            # 需要抽出定制图表处理的代码
            pass

        # 设备原始数据
        if int(data_src) == 20:
            if not data_name or not data_id:
                raise exceptions.ValidationError('data_name and data_id must be present!')

            terminal = Terminal.objects.filter(pk=data_id).last()
            if terminal is not None:
                dp_id = terminal.device.device_prototype_id
                ap = AttributePrototype.objects.get(name=data_name, device_prototype_id=dp_id)
                ta = TerminalAttribute.objects.get(terminal=terminal, attribute_prototype_id=ap.id)

                series, count = DeviceAttributeViewV5.query_stats(self, ta, from_at, till_at, None, None, 0)

                if chart_type == 60:
                    # 原始数据以分钟计
                    data_dict = {item['time'].replace(second=0, microsecond=0): item['value'] for item in series['values']}

                    if from_at:
                        current_time = datetime.datetime.strptime(from_at, '%Y%m%d%H%M%S').replace(second=0, microsecond=0)
                    else:
                        current_time = series['values'][0]['time'].replace(second=0, microsecond=0)
                    if till_at:
                        end_at = datetime.datetime.strptime(till_at, '%Y%m%d%H%M%S').replace(second=0, microsecond=0)
                    else:
                        end_at = series['values'][-1]['time'].replace(second=0, microsecond=0)

                    scatter_values = []
                    idx = 0
                    while current_time <= end_at:
                        value = data_dict.get(current_time, None)
                        scatter_values.append([value, idx])

                        idx += 1
                        current_time = current_time + datetime.timedelta(minutes=1)

                    series['values'] = scatter_values
                    scatter_data = copy(series)
                    series = None

        # 报表配置数据
        if int(data_src) == 30:
            if not data_name or not data_id or not gran:
                raise exceptions.ValidationError('data_name, data_id and gran must be present!')

            terminal = Terminal.objects.get(pk=data_id)
            target_type = ContentType.objects.get_for_model(DevicePrototype)
            rcs = ReportConfigurer.objects.filter(target_id=terminal.device.device_prototype_id, target_type=target_type)

            if terminal.idx:
                rcs = rcs.filter(device_idx=terminal.idx)

            rc = rcs.get(name=data_name)

            # 格式化前端传来的时间字符串 “20220310000000” =》 "2022-03-10 00:00:00"
            if from_at:
                from_at = datetime.datetime.strptime(from_at, '%Y%m%d%H%M%S').strftime('%Y-%m-%d %H:%M:%S')
            if till_at:
                till_at = datetime.datetime.strptime(till_at, '%Y%m%d%H%M%S').strftime('%Y-%m-%d %H:%M:%S')

            values = DeviceStatViewSet.single_series(terminal.device, rc, gran_to_Str(gran), from_at, till_at)

            # 处理报表配置的单位。例如，将 "摄氏度·℃" 处理为 "℃"
            unit = rc.unit
            if '·' in unit and unit.index('·') + 1 != len(unit):
                unit = unit.split('·')[1]

            series = {
                'name': data_name,
                'unit': unit,
                'values': values
            }

            count = len(values)

            # 设备统计数据，支持小时统计,日统计,月统计
            gran_name = gran_to_Str(gran)
            if chart_type == 60 and gran_name in ['hr', 'di', 'mo']:
                if gran_name == 'hr':  # 小时统计
                    data_dict = {datetime.datetime.fromisoformat(item['time']).replace(minute=0, second=0, microsecond=0)
                                 : item['value'] for item in values}
                elif gran_name == 'di':  # 日统计
                    data_dict = {datetime.datetime.fromisoformat(item['time']).date(): item['value'] for item in values}
                else:  # 月统计
                    data_dict = {datetime.datetime.fromisoformat(item['time']).date().replace(day=1): item['value'] for item in values}

                if from_at:
                    current_time = datetime.datetime.fromisoformat(from_at)
                else:
                    first_time = values[0]['time']
                    current_time = datetime.datetime.fromisoformat(first_time)

                if till_at:
                    end_at = datetime.datetime.fromisoformat(till_at)
                else:
                    last_time = values[-1]['time']
                    end_at = datetime.datetime.fromisoformat(last_time)

                if gran_name == 'hr':
                    current_time = current_time.replace(minute=0, second=0, microsecond=0)
                    end_at = end_at.replace(minute=0, second=0, microsecond=0)
                elif gran_name == 'di':
                    current_time = current_time.date()
                    end_at = end_at.date()
                else:
                    current_time = current_time.date().replace(day=1)
                    end_at = end_at.date().replace(day=1)

                scatter_values = []
                idx = 0
                while current_time <= end_at:
                    value = data_dict.get(current_time, None)
                    scatter_values.append([value, idx])

                    idx += 1
                    if gran_name == 'hr':
                        current_time = current_time + datetime.timedelta(hours=1)
                    elif gran_name == 'di':
                        current_time = current_time + datetime.timedelta(days=1)
                    else:
                        monthrange = calendar.monthrange(current_time.year, current_time.month)[1]
                        current_time = current_time + datetime.timedelta(days=monthrange)

                scatter_data = {
                    'name': data_name,
                    'unit': unit,
                    'values': scatter_values
                }
                count = len(scatter_values)
                series = None

        # 能耗录入数据，基准能耗数据
        if int(data_src) == 40 or int(data_src) == 50:
            if not ec_type:
                raise exceptions.ValidationError('ec_type must be present!')

            name = '能耗录入数据'

            if int(data_src) == 40 and not data_name:
                raise exceptions.ValidationError('data_name must be present!')

            if int(data_src) == 50:
                name = '基准能耗数据'
                ec_criterias = EcCriteria.objects.filter(is_refer=True, ec_type=ec_type)
            else:
                name = f'能耗录入数据-{data_name}'
                ec_criterias = EcCriteria.objects.filter(name__contains=data_name, ec_type=ec_type)

            data = []
            for d in ec_criterias:
                data.append({
                    'time': d.year_month,
                    'value': d.value
                })

            series = {
                'name': name,
                'unit': ec_type_to_unit(ec_type),
                'values': data
            }

            count = len(data)

        # 人工录入数据
        if int(data_src) == 60:
            if not data_name:
                raise exceptions.ValidationError('data_name must be present!')

            record = ManualRecord.objects.get(name=data_name, use_for=10)

            # 趋势数据
            if record.data_type == 10:
                series = {
                    'name': data_name,
                    'unit': record.unit,
                    'values': record.data
                }

            # 占比（饼图）数据
            if record.data_type == 20:
                pie_data = record.data

            # 表格数据
            if record.data_type == 30:
                table_data = record.data

            # 单项数据
            if record.data_type == 40:
                single_data = record.data

            count = len(json.loads(record.data)) if isinstance(json.loads(record.data), list) else 1

        # 设备实时数据
        if int(data_src) == 70:
            if not data_id or not data_name:
                raise exceptions.ValidationError('data_name and data_id must be present!')

            if AttributePrototype.objects.filter(name=data_name).exists():
                data_name = AttributePrototype.objects.filter(name=data_name).first().identifier

            if not TerminalAttribute.objects.filter(terminal_id=data_id, identifier=data_name).exists():
                raise exceptions.NotFound('参数不存在！')

            terminal = Terminal.objects.get(pk=data_id)
            ta = TerminalAttribute.objects.get(terminal_id=data_id, identifier=data_name)
            ap = AttributePrototype.objects.get(pk=ta.attribute_prototype_id)
            single_data = {
                'name': ap.name,
                'value': ta.value,
                'unit': ap.unit,
                'icon': ap.icon
            }

            count = 1

        # 维度统计数据
        if int(data_src) == 80:
            if not data_id or not data_name and not gran:
                raise exceptions.ValidationError('data_name, gran and data_id must be present!')

            if not from_at or not till_at:
                raise exceptions.ValidationError('from and till must be present!')

            # 格式化前端传来的时间字符串 “20220310000000” =》 "2022-03-10 00:00:00"
            if from_at:
                from_at = datetime.datetime.strptime(from_at, '%Y%m%d%H%M%S')
            if till_at:
                till_at = datetime.datetime.strptime(till_at, '%Y%m%d%H%M%S')

            dim = Dimension.objects.get(pk=data_id)
            da = DimensionAttribute.objects.get(name=data_name)
            stats = []

            # 小时统计
            if int(gran) == 20:
                stats = DimensionHourlyStat.objects.filter(dimension_attribute_id=da.id,
                                                           created_at__range=(from_at, till_at)).order_by('created_at')

            # 日统计
            if int(gran) == 30:
                stats = DimensionDailyStat.objects.filter(dimension_attribute_id=da.id,
                                                          created_at__range=(from_at, till_at)).order_by('created_at')

            # 周统计
            if int(gran) == 40:
                stats = DimensionWeeklyStat.objects.filter(dimension_attribute_id=da.id,
                                                           created_at__range=(from_at, till_at)).order_by('created_at')

            # 月统计
            if int(gran) == 50:
                stats = DimensionMonthlyStat.objects.filter(dimension_attribute_id=da.id,
                                                            created_at__range=(from_at, till_at)).order_by('created_at')

            values = []
            for stat in stats:
                values.append({
                    'time': stat.created_at,
                    'value': round(float(stat.avg), 2) if stat.avg else stat.avg
                })

            series = {
                'name': data_name,
                'unit': ec_type_to_unit(da.ec_type),
                'values': values
            }

            count = len(values)

            # 维度统计数据，支持小时统计,日统计,月统计
            gran_name = gran_to_Str(gran)
            if chart_type == 60 and gran_name in ['hr', 'di', 'mo']:
                if gran_name == 'hr':  # 小时统计
                    data_dict = {item['time'].replace(minute=0, second=0, microsecond=0): item['value'] for item in values}
                elif gran_name == 'di':  # 日统计
                    data_dict = {item['time'].date(): item['value'] for item in values}
                else:  # 月统计
                    data_dict = {item['time'].date().replace(day=1): item['value'] for item in values}

                current_time = from_at
                end_at = till_at

                if gran_name == 'hr':
                    current_time = current_time.replace(minute=0, second=0, microsecond=0)
                    end_at = end_at.replace(minute=0, second=0, microsecond=0)
                elif gran_name == 'di':
                    current_time = current_time.date()
                    end_at = end_at.date()
                else:
                    current_time = current_time.date().replace(day=1)
                    end_at = end_at.date().replace(day=1)

                scatter_values = []
                idx = 0
                while current_time <= end_at:
                    value = data_dict.get(current_time, None)
                    scatter_values.append([value, idx])

                    idx += 1
                    if gran_name == 'hr':
                        current_time = current_time + datetime.timedelta(hours=1)
                    elif gran_name == 'di':
                        current_time = current_time + datetime.timedelta(days=1)
                    else:
                        monthrange = calendar.monthrange(current_time.year, current_time.month)[1]
                        current_time = current_time + datetime.timedelta(days=monthrange)

                scatter_data = {
                    'name': data_name,
                    'unit': ec_type_to_unit(da.ec_type),
                    'values': scatter_values
                }
                count = len(scatter_values)
                series = None

        # 维度实时数据
        if int(data_src) == 90:
            if not data_id or not data_name:
                raise exceptions.ValidationError('data_name and data_id must be present!')

            dim = Dimension.objects.get(pk=data_id)
            da = DimensionAttribute.objects.get(name=data_name)

            single_data = {
                'name': data_name,
                'value': round(da.value, 2),
                'unit': ec_type_to_unit(da.ec_type),
                'icon': None
            }

            count = 1

        # 天气数据
        if int(data_src) == 100:
            from saian_api.dashboard.models import ProjectWeather

            if not data_name or not from_at or not till_at or not gran:
                raise exceptions.ValidationError('data_name, from, till and gran must be present!')

            # 格式化前端传来的时间字符串 “20220310000000” =》 "2022-03-10 00:00:00"
            if from_at:
                from_at = datetime.datetime.strptime(from_at, '%Y%m%d%H%M%S')
            if till_at:
                till_at = datetime.datetime.strptime(till_at, '%Y%m%d%H%M%S')

            stats = ProjectWeather.objects.filter(created_at__range=(from_at, till_at), type=gran_to_Str(gran)).order_by('created_at')
            values = []
            if data_name == '温度':
                for stat in stats:
                    values.append({
                        'time': stat.created_at,
                        'value': stat.temp
                    })

            if data_name == '湿度':
                for stat in stats:
                    values.append({
                        'time': stat.created_at,
                        'value': stat.humidity
                    })

            series = {
                'name': data_name,
                'unit': '℃' if data_name == '温度' else '%',
                'values': values
            }

            count = len(values)

            # 天气统计数据，支持小时统计,日统计,月统计
            gran_name = gran_to_Str(gran)
            if chart_type == 60 and gran_name in ['hr', 'di', 'mo']:
                if gran_name == 'hr':  # 小时统计
                    data_dict = {item['time'].replace(minute=0, second=0, microsecond=0): item['value'] for item in values}
                elif gran_name == 'di':  # 日统计
                    data_dict = {item['time'].date(): item['value'] for item in values}
                else:  # 月统计
                    data_dict = {item['time'].date().replace(day=1): item['value'] for item in values}

                current_time = from_at
                end_at = till_at

                if gran_name == 'hr':
                    current_time = current_time.replace(minute=0, second=0, microsecond=0)
                    end_at = end_at.replace(minute=0, second=0, microsecond=0)
                elif gran_name == 'di':
                    current_time = current_time.date()
                    end_at = end_at.date()
                else:
                    current_time = current_time.date().replace(day=1)
                    end_at = end_at.date().replace(day=1)

                scatter_values = []
                idx = 0
                while current_time <= end_at:
                    value = data_dict.get(current_time, None)
                    scatter_values.append([value, idx])

                    idx += 1
                    if gran_name == 'hr':
                        current_time = current_time + datetime.timedelta(hours=1)
                    elif gran_name == 'di':
                        current_time = current_time + datetime.timedelta(days=1)
                    else:
                        monthrange = calendar.monthrange(current_time.year, current_time.month)[1]
                        current_time = current_time + datetime.timedelta(days=monthrange)

                scatter_data = {
                    'name': data_name,
                    'unit': '℃' if data_name == '温度' else '%',
                    'values': scatter_values
                }
                count = len(scatter_values)
                series = None

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'line_data': series,
                'pie_data': pie_data,
                'table_data': table_data,
                'single_data': single_data,
                'scatter_data': scatter_data
            },
            'count': count
        })


class UserChartViewSet(viewsets.ModelViewSet):
    serializer_class = UserChartSerializer

    def get_queryset(self):
        return UserChart.objects.filter(web_user_id=self.request.user['id']).order_by('-id')

    def get_object(self):
        return get_object_or_404(UserChart, pk=self.kwargs['pk'], web_user_id=self.request.user['id'])

    def list(self, request, *args, **kwargs):
        charts = super().list(request, *args, **kwargs).data

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'user_charts': charts['results']
            },
            'total': charts['count']
        })

    def create(self, request, *args, **kwargs):
        request.data['web_user'] = request.user['id']
        chart = super().create(request, *args, **kwargs).data

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'user_chart': chart
            }
        })

    def partial_update(self, request, *args, **kwargs):
        chart = super().partial_update(request, *args, **kwargs).data

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'user_chart': chart
            }
        })

    def retrieve(self, request, *args, **kwargs):
        chart = super().retrieve(request, *args, **kwargs).data

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'user_chart': chart
            }
        })

    def destroy(self, request, *args, **kwargs):
        chart = self.get_object()

        chart.destroy_cus()

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })


class UserAnalyseViewSet(viewsets.ModelViewSet):
    serializer_class = UserAnalyseSerializer

    def get_queryset(self):
        return UserAnalyse.objects.filter(web_user_id=self.request.user['id']).order_by('-id')

    def get_object(self):
        return get_object_or_404(UserAnalyse, pk=self.kwargs['pk'], web_user_id=self.request.user['id'])

    def list(self, request, *args, **kwargs):
        user_analysis = super().list(request, *args, **kwargs).data

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'user_analysis': user_analysis['results']
            },
            'total': user_analysis['count']
        })

    def create(self, request, *args, **kwargs):
        request.data['web_user'] = request.user['id']
        user_analyse = super().create(request, *args, **kwargs).data

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'user_analyse': user_analyse
            }
        })

    def partial_update(self, request, *args, **kwargs):
        user_analyse = super().partial_update(request, *args, **kwargs).data

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'user_analyse': user_analyse
            }
        })

    def retrieve(self, request, *args, **kwargs):
        user_analyse = super().retrieve(request, *args, **kwargs).data

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'user_analyse': user_analyse
            }
        })

    def destroy(self, request, *args, **kwargs):
        super().destroy(request, *args, **kwargs)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })


class UserAnalyseChartViewSet(viewsets.GenericViewSet):
    def create(self, request, *args, **kwargs):
        user_analyse_id = request.data.get('analyse_id', None)
        chart_id = request.data.get('chart_id', None)
        content_type = request.data.get('content_type', None)

        seq = request.data.get('seq', 0)

        if not user_analyse_id or not chart_id or not content_type:
            raise exceptions.ValidationError('analyse_id, chart_id and content_type must be present!')

        content_type_id = ContentType.objects.get(model='userchart').id

        if content_type == 'web-chart':
            content_type_id = ContentType.objects.get(model='webchart').id

        if UserAnalysisCharts.objects.filter(user_analyse_id=user_analyse_id, content_id=chart_id, content_type_id=content_type_id).exists():
            raise exceptions.ValidationError('Chart already exists!')

        uac = UserAnalysisCharts.objects.create(user_analyse_id=user_analyse_id, content_id=chart_id, content_type_id=content_type_id, seq=seq)

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'analyse_chart': {
                    'id': uac.id,
                    'chart': {
                        'id': uac.content.id,
                        'content_type': uac.content_type.model,
                        'name': uac.content.name,
                        'uni_name': uac.content.uni_name,
                        'chart_type': uac.content.chart_type,
                        'data_src': uac.content.data_src,
                        'query_time': uac.content.query_time,
                        'gran': uac.content.gran
                    },
                    'seq': uac.seq
                }
            }
        })

    def partial_update(self, request, *args, **kwargs):
        analyse_id = request.data.get('analyse_id', None)
        chart_id = request.data.get('chart_id', None)
        content_type = request.data.get('content_type', None)
        seq = request.data.get('seq', None)

        if analyse_id and chart_id and seq and content_type and UserAnalyse.objects.filter(web_user_id=request.user['id'], pk=analyse_id).exists():
            content_type_id = ContentType.objects.get(model='userchart').id

            if content_type == 'web-chart':
                content_type_id = ContentType.objects.get(model='webchart').id

            uac = UserAnalysisCharts.objects.get(user_analyse_id=analyse_id, content_id=chart_id, content_type_id=content_type_id)
            uac.seq = seq
            uac.save()
        else:
            raise exceptions.ValidationError('Validate error!')

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def destroy(self, request, *args, **kwargs):
        user_analyse_id = request.data.get('user_analyse_id', None)
        chart_id = request.data.get('chart_id', None)
        content_type = request.data.get('content_type', None)

        content_type_id = ContentType.objects.get(model='userchart').id

        if content_type == 'web-chart':
            content_type_id = ContentType.objects.get(model='webchart').id

        uac = UserAnalysisCharts.objects.filter(user_analyse_id=user_analyse_id, content_id=chart_id, content_type_id=content_type_id)

        uac.delete()

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })


class ManualRecordViewSet(viewsets.ModelViewSet):
    serializer_class = ManualRecordSerializer

    def get_queryset(self):
        query_set = ManualRecord.objects.all()

        name = self.request.query_params.get('name', None)
        if name is not None:
            query_set = query_set.filter(name=name)

        search = self.request.query_params.get('search', None)
        if search is not None:
            query_set = query_set.filter(name__icontains=search)

        use_for = self.request.query_params.get('use_for', None)
        if use_for is not None:
            query_set = query_set.filter(use_for=use_for)

        gran = self.request.query_params.get('gran', None)
        if gran is not None:
            query_set = query_set.filter(gran=gran)

        data_year = self.request.query_params.get('data_year', None)
        if data_year is not None:
            query_set = query_set.filter(data_time__year=data_year)

        data_month = self.request.query_params.get('data_month', None)
        if data_month is not None:
            query_set = query_set.filter(data_time__month=data_month)

        data_time = self.request.query_params.get('data_time', None)
        if data_time is not None:
            dt = datetime.datetime.strptime(data_time, '%Y%m%d%H%M%S')
            query_set = query_set.filter(data_time=dt)

        return query_set.order_by('id')

    def list(self, request, *args, **kwargs):
        records = super().list(request, *args, **kwargs).data

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'records': records['results']
            },
            'total': records['count']
        })

    def create(self, request, *args, **kwargs):
        data = request.data
        data_at = data.get('data_time', None)
        if data_at is not None:
            data['data_time'] = datetime.datetime.strptime(data_at, '%Y%m%d%H%M%S')
        record = super().create(request, *args, **kwargs).data

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'record': record
            }
        })

    def partial_update(self, request, *args, **kwargs):
        data = request.data
        if 'data_time' in data:
            data['data_time'] = datetime.datetime.strptime(data['data_time'], '%Y%m%d%H%M%S')

        instance = self.get_object()
        serializer = self.get_serializer(instance, data=data, partial=True)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'record': serializer.data
            }
        })

    def destroy(self, request, *args, **kwargs):
        from saian_api.project.models import WebChart

        record = self.get_object()

        if UserChart.objects.filter(data_name=record.name).exists() or WebChart.objects.filter(data_name=record.name).exists():
            raise exceptions.ValidationError('有图表正在使用该数据，请先在图表停止使用该数据！')

        super().destroy(request, *args, **kwargs)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })


class DlTasksView(viewsets.ModelViewSet):
    renderer_classes = (SyJSONRender,)
    serializer_class = DlTaskSerializer

    def get_queryset(self):
        queryset = DlTask.objects.all()

        project_id = self.request.user.get('project_id', None)
        if project_id is not None:
            queryset = queryset.filter(project_id=project_id)

        web_user_id = self.request.query_params.get('user_id', None)
        if web_user_id is not None:
            queryset = queryset.filter(web_user_id=web_user_id)

        device_id = self.request.query_params.get('device_id', None)
        if device_id is not None:
            queryset = queryset.filter(device_id=device_id)

        task_status = self.request.query_params.get('status', None)
        if task_status is not None:
            queryset = queryset.filter(status=task_status)

        search = self.request.query_params.get('search', None)
        if search is not None:
            queryset = queryset.filter(Q(name__icontains=search) | Q(file_path__icontains=search))

        return queryset

    def get_object(self):
        return get_object_or_404(DlTask, pk=self.kwargs['pk'])

    def list(self, request, *args, **kwargs):
        data = super().list(request, *args, **kwargs).data

        results = data['results']

        device_cache = {}
        user_cache = {}

        for task in results:
            web_user_id = task.get('web_user_id', None)
            if web_user_id is not None:
                web_user = user_cache.get(web_user_id, None)
                if web_user is None:
                    web_user = WebUser.objects.get(pk=web_user_id)
                    user_cache[web_user_id] = web_user
                task['web_user'] = {
                    'id': web_user.id,
                    'name': web_user.name
                }
            device_id = task.get('device_id', None)
            if device_id is not None:
                device = device_cache.get(device_id, None)
                if device is None:
                    device = Device.objects.get(pk=device_id)
                    device_cache[device_id] = device
                task['device'] = {
                    'id': device.id,
                    'mac': device.mac,
                    'nick_name': device.nick_name
                }

        return Response({
            'status': status.HTTP_200_OK,
            'dl_tasks': results,
            'count': data['count']
        })

class StatReportView(viewsets.ModelViewSet):
    serializer_class = StatReportSerializer
    renderer_classes = (SyJSONRender,)

    def get_queryset(self):
        queryset = StatReport.objects.all()

        period = self.request.query_params.get('period', None)
        if period is not None:
            queryset = queryset.filter(periods=period)

        from_at = self.request.query_params.get('from', None)
        if from_at is not None:
            from_dt = datetime.datetime.strptime(from_at, '%Y%m%d%H%M%S')
            queryset = queryset.filter(report_dt__gte=from_dt)

        till_at = self.request.query_params.get('till', None)
        if till_at is not None:
            till_dt = datetime.datetime.strptime(till_at, '%Y%m%d%H%M%S')
            queryset = queryset.filter(report_dt__lte=till_dt)

        return queryset.order_by('-created_at')

    def list(self, request, *args, **kwargs):

        data = super().list(request, *args, **kwargs).data

        return Response({
            'status': status.HTTP_200_OK,
            'stat_reports': data['results'],
            'count': data['count']
        })

    def retrieve(self, request, *args, **kwargs):
        report_id = kwargs.get('pk')
        stat_report = StatReport.objects.get(pk=report_id)
        dt = stat_report.report_dt

        # 天气
        weather = {}
        # 用电量数据
        power_cons = {}

        # - 总用电维度属性
        total_power_da = None
        # - 其他用电维度属性
        other_das = []
        other_da_ids = []

        root_dimension = Dimension.objects.filter(type_name__icontains='分项', parent__isnull=True).values_list('id', flat=True)
        root_power_das = DimensionAttribute.objects.filter(Q(name__icontains='总') & Q(name__icontains='用电'),
                                                           dimension_id__in=root_dimension, ec_type=10).exclude(name__contains='热水')
        if root_power_das.count() == 1:
            total_power_da = root_power_das.last()
            other_das = list(DimensionAttribute.objects.filter(ec_type=10, dimension__type_name='分项')
                             .exclude(id=total_power_da.id)
                             .exclude(dimension_id__in=root_dimension)
                             .exclude(Q(name__icontains='总用电') | Q(name__contains='热水')))
            other_da_ids = [da.id for da in other_das]

        elif root_power_das.count() > 1:
            logging.error(f'找到多个总用电分项！')

        # 用气量
        gas_cons = {}
        total_gas_da = None
        root_gas_das = DimensionAttribute.objects.filter(Q(name__icontains='总') & Q(name__icontains='用气'),
                                                         dimension_id__in=root_dimension, ec_type=40)
        if root_gas_das.count() == 1:
            total_gas_da = root_gas_das.first()
        elif root_gas_das.count() > 1:
            logging.error(f'找到多个总气用分项！')

        # 用水量
        water_cons = {}
        total_water_da = None
        root_water_das = DimensionAttribute.objects.filter(Q(name__icontains='总') & Q(name__icontains='用水'),
                                                           dimension_id__in=root_dimension, ec_type=20)
        if root_water_das.count() == 1:
            total_water_da = root_water_das.first()
        elif root_water_das.count() > 1:
            logging.error(f'找到多个总水用分项！')

        wea_type = 'di'
        data_from = DimensionDailyStat.objects

        if stat_report.periods == 10:
            wea_type = 'di'
            dt = (dt + datetime.timedelta(days=1)) - datetime.timedelta(seconds=1)
            prev_dt = dt - datetime.timedelta(days=1)
            prev_label = prev_dt.strftime("%Y年%m月%d日")
            data_from = DimensionDailyStat.objects
        elif stat_report.periods == 20:
            wea_type = 'wk'
            dt = (dt + datetime.timedelta(days=7)) - datetime.timedelta(seconds=1)
            prev_dt = dt - datetime.timedelta(days=7)
            prev_calendar = prev_dt.isocalendar()
            prev_label = f'{prev_calendar.year}年{prev_calendar.week}周'
            data_from = DimensionWeeklyStat.objects
        elif stat_report.periods == 30:
            wea_type = 'mth'
            monthrange = calendar.monthrange(dt.year, dt.month)[1]
            dt = (dt + datetime.timedelta(days=monthrange)) - datetime.timedelta(seconds=1)

            prev_month = dt.month - 1
            prev_year = dt.year
            if dt.month == 1:
                prev_month = 12
                prev_year = dt.year - 1
            prev_monthrange = calendar.monthrange(prev_year, prev_month)[1]
            prev_dt = datetime.datetime(prev_year, prev_month, prev_monthrange, 23, 59, 59)
            prev_label = f'{prev_dt.year}年{prev_dt.month}月'
            data_from = DimensionMonthlyStat.objects
        else:
            wea_type = 'yr'
            dt = dt.replace(month=12, day=31, hour=23, minute=59, second=59)
            prev_dt = dt.replace(year=dt.year - 1)
            prev_label = f'{prev_dt.year}年'
            data_from = DimensionYearlyStat.objects

        # 天气
        di_weather = ProjectWeather.objects.filter(created_at__year=dt.year,
                                                   created_at__month=dt.month,
                                                   created_at__day=dt.day,
                                                   type=wea_type).last()
        if di_weather is not None:
            weather = ProjectWeatherSerializer(di_weather).data
            weather['wea_img'] = WeatherApi.wea_to_img(weather.get('wea', None))

            # prev_dt = dt - datetime.timedelta(days=1)
            prev_weather = ProjectWeather.objects.filter(created_at__year=prev_dt.year,
                                                         created_at__month=prev_dt.month,
                                                         created_at__day=prev_dt.day,
                                                         type=wea_type).last()
            if prev_weather is not None:
                weather['last_period'] = ProjectWeatherSerializer(prev_weather).data
                weather['last_period']['wea_img'] = WeatherApi.wea_to_img(weather['last_period'].get('wea', None))
            else:
                weather['last_period'] = {}

        # 总用电
        if total_power_da is not None:
            power_cons['id'] = total_power_da.id
            stat = data_from.filter(dimension_attribute_id=total_power_da.id, created_at=dt).last()
            if stat is not None:
                power_cons['total'] = stat.avg

            prev_daily_da = data_from.filter(dimension_attribute_id=total_power_da.id, created_at=prev_dt).last()
            if prev_daily_da is not None:
                power_cons['last_total'] = prev_daily_da.avg

            # 分项用电数据
            dds = list(data_from.filter(dimension_attribute_id__in=other_da_ids, created_at=dt).order_by('-updated_at'))
            prev_dds = list(data_from.filter(dimension_attribute_id__in=other_da_ids, created_at=prev_dt))
            dim_cons = []
            for stat in dds:
                da = next(filter(lambda x: x.id == stat.dimension_attribute_id, other_das), None)
                if da is None:
                    continue
                prev_stat = next(filter(lambda x: x.dimension_attribute_id == da.id, prev_dds), None)
                dim_cons.append({
                    "name": da.name,
                    "value": stat.avg,
                    "last_value": prev_stat.avg if prev_stat is not None else None
                })
            power_cons['dim_cons'] = dim_cons

        # 总用气
        if total_gas_da is not None:
            gas_cons['id'] = total_gas_da.id
            stat = data_from.filter(dimension_attribute_id=total_gas_da.id, created_at=dt).last()
            if stat is not None:
                gas_cons['total'] = stat.avg

            prev_daily_da = data_from.filter(dimension_attribute_id=total_gas_da.id, created_at=prev_dt).last()
            if prev_daily_da is not None:
                gas_cons['last_total'] = prev_daily_da.avg

        # 总用气
        if total_water_da is not None:
            water_cons['id'] = total_water_da.id
            stat = data_from.filter(dimension_attribute_id=total_water_da.id, created_at=dt).last()
            if stat is not None:
                water_cons['total'] = stat.avg

            prev_daily_da = data_from.filter(dimension_attribute_id=total_water_da.id, created_at=prev_dt).last()
            if prev_daily_da is not None:
                water_cons['last_total'] = prev_daily_da.avg

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'period_label': {
                    'current': stat_report.title[5:-1],
                    'last': prev_label
                },
                'weather': weather,
                'power_cons': power_cons,
                'water_cons': water_cons,
                'gas_cons': gas_cons
            }
        })

class DerivedDataViewSet(viewsets.ModelViewSet):

    def create(self, request, *args, **kwargs):
        # 文件名称
        name = request.data.get('name', None)
        # 文件模板（10：按时间；20：按参数）
        template = request.data.get('template', None)
        if template:
            template = int(template)
        # 数据系列
        data_params = request.data.get('data_params', None)
        if data_params:
            data_params = json.loads(data_params)
        
        period_list = {10: '实时', 60: '分钟', 70: '5分钟', 20: '小时', 30: '日', 50: '月', 80: '年'}

        def replenish_time(period, incomplete_time):
            start_time = datetime.datetime.now()
            end_time = datetime.datetime.now()
            if int(period) == 20:
                start_time = (datetime.datetime.strptime(incomplete_time + ':00', '%Y-%m-%d %H:%M:%S')).replace(minute=0, second=0, microsecond=0)
                end_time = (datetime.datetime.strptime(incomplete_time + ':00', '%Y-%m-%d %H:%M:%S')).replace(minute=59, second=59, microsecond=0)
            if int(period) == 30:
                start_time = (datetime.datetime.strptime(incomplete_time + ' 00:00:00', '%Y-%m-%d %H:%M:%S')).replace(hour=0, minute=0, second=0, microsecond=0)
                end_time = (datetime.datetime.strptime(incomplete_time + ' 00:00:00', '%Y-%m-%d %H:%M:%S')).replace(hour=23, minute=59, second=59, microsecond=0)
            if int (period) == 50:
                year = int(incomplete_time.split("-")[0])
                month = int(incomplete_time.split("-")[1])
                start_time = datetime.datetime(year, month, 1, 0, 0, 0)
                day = calendar.monthrange(year, month)[1]
                end_time = datetime.datetime(year, month, day, 23, 59, 59)
            if int(period) == 80:
                year = int(incomplete_time.split("年")[0])
                start_time = datetime.datetime(year, 1, 1, 0, 0, 0)
                end_time = datetime.datetime(year, 12, 31, 23, 59, 59)
            return start_time, end_time

        def calculate_time_period(data_src, period, time):
            start_time = time
            end_time = time
            if data_src == 30 and period == 60:
                end_time = time + datetime.timedelta(seconds=59) 
            if data_src == 30 and period == 70:
                end_time = time + datetime.timedelta(minutes=4) + datetime.timedelta(seconds=59)
            if period == 20:
                start_time = time.replace(minute=0, second=0, microsecond=0)
                end_time = time.replace(minute=59, second=59, microsecond=0)
            if period == 30:
                start_time = time.replace(hour=0, minute=0, second=0, microsecond=0)
                end_time = time.replace(hour=23, minute=59, second=59, microsecond=0)
            if period == 50:
                start_time = time.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
                day = calendar.monthrange(start_time.year, start_time.month)[1]
                end_time = time.replace(day=day, hour=23, minute=59, second=59, microsecond=0)
            return start_time, end_time

        def change_data_format(data_type, options, unadjusted_data):
            adjusted_data = '--'
            data_type = int(data_type)
            if data_type == 10:
                adjusted_data = True if unadjusted_data else False
            elif data_type == 20:
                if is_number(unadjusted_data):
                    adjusted_data = round(float(unadjusted_data), 2)
                else:
                    adjusted_data = unadjusted_data if unadjusted_data else '--'
            elif data_type == 30:
                options = options.split(',') if options else []
                if is_number(unadjusted_data) and int(unadjusted_data) < len(options):
                    adjusted_data = options[int(unadjusted_data)]
                else:
                    adjusted_data = unadjusted_data if unadjusted_data else '--'
            else:
                adjusted_data = unadjusted_data if unadjusted_data else '--'
            return adjusted_data

        data_list = []

        for item in data_params:

            # 设备原始数据
            if item['data_src'] == 20:
                from saian_api.device.models import Terminal
                terminal = Terminal.objects.filter(pk=item['terminal_id']).last()
                if terminal is not None:
                    device_prototype_id = terminal.device.device_prototype_id
                    attribute_prototype = AttributePrototype.objects.get(name=item['data_name'], device_prototype_id=device_prototype_id)
                    terminal_attribute = TerminalAttribute.objects.get(terminal=terminal, attribute_prototype_id=attribute_prototype.id)
                    series, _ = DeviceAttributeViewV5.query_stats(self, terminal_attribute, item['from'], item['till'], None, None, 0)
                    stats = series['values']
                    data_item = {
                        'data_src': 20,
                        'parent_name': terminal.device.nick_name,
                        'name': item['data_name'],
                        'period': item['period'],
                        'unit': series['unit'] if series['unit'] else '--',
                        'data': []
                    }
                    for stat in stats:
                        data_item['data'].append({
                            'time': stat['time'],
                            'value': change_data_format(attribute_prototype.data_type, attribute_prototype.options, stat['value']),
                            'max': '--',
                            'min': '--'
                        })
                    data_list.append(data_item)
            
            # 设备统计数据
            if item['data_src'] == 30:
                from saian_api.device.models import Terminal
                terminal = Terminal.objects.get(pk=item['terminal_id'])
                target_type = ContentType.objects.get_for_model(DevicePrototype)
                rcs = ReportConfigurer.objects.filter(target_id=terminal.device.device_prototype_id, target_type=target_type)
                if terminal.idx:
                    rcs = rcs.filter(device_idx=terminal.idx)
                rc = rcs.get(name=item['data_name'])
                device_statistical_from_at = datetime.datetime.strptime(item['from'], '%Y%m%d%H%M%S').strftime('%Y-%m-%d %H:%M:%S')
                device_statistical_till_at = datetime.datetime.strptime(item['till'], '%Y%m%d%H%M%S').strftime('%Y-%m-%d %H:%M:%S')
                stats = DeviceStatViewSet.single_series(terminal.device, rc, gran_to_Str(item['period']), device_statistical_from_at, device_statistical_till_at)
                data_item = {
                    'data_src': 30,
                    'parent_name': terminal.device.nick_name,
                    'name': item['data_name'],
                    'period': item['period'],
                    'unit': rc.unit.split('·')[1] if '·' in rc.unit and rc.unit.index('·') + 1 != len(rc.unit) else rc.unit,
                    'data': []
                }
                compare_from_at = datetime.datetime.strptime(item['from'], '%Y%m%d%H%M%S')
                compare_till_at = datetime.datetime.strptime(item['till'], '%Y%m%d%H%M%S')
                for stat in stats:                    
                    time_item = calculate_time_period(30, item['period'], datetime.datetime.strptime(stat['time'], '%Y-%m-%d %H:%M:%S'))
                    if time_item[0] >= compare_from_at and time_item[1] <= compare_till_at:
                        data_item['data'].append({
                            'time': time_item[0],
                            'value': change_data_format(20, None, stat['value']),
                            'min': change_data_format(20, None, stat.get('min', '')),
                            'max': change_data_format(20, None, stat.get('max', ''))
                        })
                data_list.append(data_item)

            # 人工输入数据
            if item['data_src'] == 60:
                manual_entry_from_at = datetime.datetime.strptime(item['from'], '%Y%m%d%H%M%S')
                manual_entry_till_at = datetime.datetime.strptime(item['till'], '%Y%m%d%H%M%S')
                record = ManualRecord.objects.get(name=item['data_name'], use_for=10)
                stats = json.loads(record.data) if record.data else []
                data_item = {
                    'data_src': 60,
                    'parent_name': '',
                    'name': item['data_name'],
                    'period': item['period'],
                    'unit': record.unit if record.unit else '--',
                    'data': []
                }
                for stat in stats:
                    time_item = replenish_time(item['period'], stat['time'])
                    if time_item[0] >= manual_entry_from_at and time_item[1] <= manual_entry_till_at:
                        data_item['data'].append({
                            'time': time_item[0],
                            'value': change_data_format(20, None, stat['value']),
                            'min': '--',
                            'max': '--'
                        })
                data_list.append(data_item)
            
            # 维度统计数据
            if item['data_src'] == 80:
                dimension = Dimension.objects.get(pk=item['dimension_id'])
                dimension_attribute = DimensionAttribute.objects.get(name=item['data_name'])
                dimension_statistical_from_at = datetime.datetime.strptime(item['from'], '%Y%m%d%H%M%S')
                dimension_statistical_till_at = datetime.datetime.strptime(item['till'], '%Y%m%d%H%M%S')
                stats = []
                if int(item['period']) == 20:
                    stats = DimensionHourlyStat.objects.filter(dimension_attribute_id=dimension_attribute.id,
                                                           created_at__range=(dimension_statistical_from_at, dimension_statistical_till_at)).order_by('created_at')
                if int(item['period']) == 30:
                    stats = DimensionDailyStat.objects.filter(dimension_attribute_id=dimension_attribute.id,
                                                           created_at__range=(dimension_statistical_from_at, dimension_statistical_till_at)).order_by('created_at')
                if int(item['period']) == 50:
                    stats = DimensionMonthlyStat.objects.filter(dimension_attribute_id=dimension_attribute.id,
                                                           created_at__range=(dimension_statistical_from_at, dimension_statistical_till_at)).order_by('created_at')
                data_item = {
                    'data_src': 80,
                    'parent_name': dimension.name,
                    'name': item['data_name'],
                    'period': item['period'],
                    'unit': ec_type_to_unit(dimension_attribute.ec_type) if ec_type_to_unit(dimension_attribute.ec_type) else '--',
                    'data': []
                }
                for stat in stats:
                    time_item = calculate_time_period(80, item['period'], stat.created_at)
                    if time_item[0] >= dimension_statistical_from_at and time_item[1] <= dimension_statistical_till_at:
                        data_item['data'].append({
                            'time': time_item[0],
                            'value': change_data_format(20, None, stat.avg),
                            'max': change_data_format(20, None, stat.max),
                            'min': change_data_format(20, None, stat.min)
                        })
                data_list.append(data_item)
            
            # 天气数据
            if item['data_src'] == 100:
                from saian_api.project.models import Project
                from saian_api.dashboard.models import ProjectWeather

                project_id = request.user.get('project_id')
                try:
                    project = Project.objects.get(pk=project_id)
                except Project.DoesNotExist:
                    return Response(status=status.HTTP_400_BAD_REQUEST)
                weather_from_at = datetime.datetime.strptime(item['from'], '%Y%m%d%H%M%S')
                weather_till_at = datetime.datetime.strptime(item['till'], '%Y%m%d%H%M%S')
                type_list = {
                    20: 'hr',
                    30: 'di',
                    50: 'mth'
                }
                stats = ProjectWeather.objects.filter(created_at__range=[weather_from_at, weather_till_at], project=project, type=type_list[item['period']]).order_by('created_at')

                data_item = {
                    'data_src': 100,
                    'parent_name': '',
                    'name': item['data_name'],
                    'period': item['period'],
                    'unit': '%' if item['data_name'] == '室外湿度' else '℃',
                    'data': []
                }
                if item['data_name'] == '室外温度':
                    for stat in stats:
                        time_item = calculate_time_period(100, item['period'], stat.created_at)
                        if time_item[0] >= weather_from_at and time_item[1] <= weather_till_at:
                            data_item['data'].append({
                                'time': time_item[0],
                                'value': change_data_format(20, None, stat.temp),
                                'max': change_data_format(20, None, stat.max_temp),
                                'min': change_data_format(20, None, stat.min_temp)
                            })
                if item['data_name'] == '室外湿度':
                    for stat in stats:
                        time_item = calculate_time_period(100, item['period'], stat.created_at)
                        if time_item[0] >= weather_from_at and time_item[1] <= weather_till_at:
                            data_item['data'].append({
                                'time': time_item[0],
                                'value': change_data_format(20, None, stat.humidity),
                                'max': change_data_format(20, None, stat.max_humidity),
                                'min': change_data_format(20, None, stat.min_humidity)
                            })
                if item['data_name'] == '湿球温度':
                    for stat in stats:
                        time_item = calculate_time_period(100, item['period'], stat.created_at)
                        if time_item[0] >= weather_from_at and time_item[1] <= weather_till_at:
                            data_item['data'].append({
                                'time': time_item[0],
                                'value': change_data_format(20, None, stat.wetbulb_temp),
                                'max': '--',
                                'min': '--'
                            })
                data_list.append(data_item)
            
        # 开始编写excel文档
        def make_cell_center(target_cell):
            alignment_obj = copy(target_cell.alignment)
            alignment_obj.horizontal = 'center'
            alignment_obj.vertical = 'center'
            target_cell.alignment = alignment_obj
        cell_border = Border(left=Side(style='thin'))

        if template == 10 and len(data_list):
            wb = Workbook()
            ws = wb.active
            ws.merge_cells('A1:A2')
            ws['A1'] = '时间'
            make_cell_center(ws['A1'])
            object_list = []
            for data_item in data_list:
                object_list = object_list + data_item['data']
            untreated_times = [element['time'] for element in object_list]
            processed_times = sorted(set(untreated_times), key=lambda x: x)
    
            for data_index, data_item in enumerate(data_list):
                three_count = sum(1 for item in data_list[:data_index] if item['data_src'] == 60)
                four_count = sum(1 for item in data_list[:data_index] if item['data_src'] == 20)
                five_count = sum(1 for item in data_list[:data_index] if item['data_src'] == 100)
                six_count = sum(1 for item in data_list[:data_index] if item['data_src'] == 30 or item['data_src'] == 80)

                cell_number = 6
                if data_item['data_src'] == 100:
                    cell_number = 5
                if data_item['data_src'] == 20:
                    cell_number = 4
                if data_item['data_src'] == 60:
                    cell_number = 3

                item_begin_col = int(three_count * 3 + four_count * 4 + five_count * 5 + six_count * 6 + 2)
                item_end_col = int(three_count * 3 + four_count * 4 + five_count * 5 + six_count * 6 + 1 + cell_number)

                ws[f'{get_column_letter(item_begin_col)}1'].border = cell_border
                ws.merge_cells(f'{get_column_letter(item_begin_col)}1:{get_column_letter(item_end_col)}1')
                ws[f'{get_column_letter(item_begin_col)}1'] = f"{data_item.get('name', '')}"
                make_cell_center(ws[f'{get_column_letter(item_begin_col)}1'])
                
                ws[f'{get_column_letter(item_begin_col)}2'].border = cell_border
                if data_item['data_src'] == 20:
                    ws[f'{get_column_letter(item_begin_col)}2'] = '设备名称'
                    ws[f'{get_column_letter(item_begin_col + 1)}2'] = '数据粒度'
                    ws[f'{get_column_letter(item_begin_col + 2)}2'] = '数据值'
                    ws[f'{get_column_letter(item_begin_col + 3)}2'] = '单位'
                    make_cell_center(ws[f'{get_column_letter(item_begin_col)}2'])
                    make_cell_center(ws[f'{get_column_letter(item_begin_col + 1)}2'])
                    make_cell_center(ws[f'{get_column_letter(item_begin_col + 2)}2'])
                    make_cell_center(ws[f'{get_column_letter(item_begin_col + 3)}2'])

                if data_item['data_src'] == 30:
                    ws[f'{get_column_letter(item_begin_col)}2'] = '设备名称'
                    ws[f'{get_column_letter(item_begin_col + 1)}2'] = '数据粒度'
                    ws[f'{get_column_letter(item_begin_col + 2)}2'] = '最大值'
                    ws[f'{get_column_letter(item_begin_col + 3)}2'] = '最小值'
                    ws[f'{get_column_letter(item_begin_col + 4)}2'] = '数据值'
                    ws[f'{get_column_letter(item_begin_col + 5)}2'] = '单位'
                    make_cell_center(ws[f'{get_column_letter(item_begin_col)}2'])
                    make_cell_center(ws[f'{get_column_letter(item_begin_col + 1)}2'])
                    make_cell_center(ws[f'{get_column_letter(item_begin_col + 2)}2'])
                    make_cell_center(ws[f'{get_column_letter(item_begin_col + 3)}2'])
                    make_cell_center(ws[f'{get_column_letter(item_begin_col + 4)}2'])
                    make_cell_center(ws[f'{get_column_letter(item_begin_col + 5)}2'])

                if data_item['data_src'] == 60:
                    ws[f'{get_column_letter(item_begin_col)}2'] = '数据粒度'
                    ws[f'{get_column_letter(item_begin_col + 1)}2'] = '数据值'
                    ws[f'{get_column_letter(item_begin_col + 2)}2'] = '单位'
                    make_cell_center(ws[f'{get_column_letter(item_begin_col)}2'])
                    make_cell_center(ws[f'{get_column_letter(item_begin_col + 1)}2'])
                    make_cell_center(ws[f'{get_column_letter(item_begin_col + 2)}2'])

                if data_item['data_src'] == 80:
                    ws[f'{get_column_letter(item_begin_col)}2'] = '维度名称'
                    ws[f'{get_column_letter(item_begin_col + 1)}2'] = '数据粒度'
                    ws[f'{get_column_letter(item_begin_col + 2)}2'] = '最大值'
                    ws[f'{get_column_letter(item_begin_col + 3)}2'] = '最小值'
                    ws[f'{get_column_letter(item_begin_col + 4)}2'] = '数据值'
                    ws[f'{get_column_letter(item_begin_col + 5)}2'] = '单位'
                    make_cell_center(ws[f'{get_column_letter(item_begin_col)}2'])
                    make_cell_center(ws[f'{get_column_letter(item_begin_col + 1)}2'])
                    make_cell_center(ws[f'{get_column_letter(item_begin_col + 2)}2'])
                    make_cell_center(ws[f'{get_column_letter(item_begin_col + 3)}2'])
                    make_cell_center(ws[f'{get_column_letter(item_begin_col + 4)}2'])
                    make_cell_center(ws[f'{get_column_letter(item_begin_col + 5)}2'])
                
                if data_item['data_src'] == 100:
                    ws[f'{get_column_letter(item_begin_col)}2'] = '数据粒度'
                    ws[f'{get_column_letter(item_begin_col + 1)}2'] = '最大值'
                    ws[f'{get_column_letter(item_begin_col + 2)}2'] = '最小值'
                    ws[f'{get_column_letter(item_begin_col + 3)}2'] = '数据值'
                    ws[f'{get_column_letter(item_begin_col + 4)}2'] = '单位'
                    make_cell_center(ws[f'{get_column_letter(item_begin_col)}2'])
                    make_cell_center(ws[f'{get_column_letter(item_begin_col + 1)}2'])
                    make_cell_center(ws[f'{get_column_letter(item_begin_col + 2)}2'])
                    make_cell_center(ws[f'{get_column_letter(item_begin_col + 3)}2'])
                    make_cell_center(ws[f'{get_column_letter(item_begin_col + 4)}2'])
                
            for time_index, time_item in enumerate(processed_times):
                ws[f'A{time_index + 3}'] = time_item.strftime('%Y-%m-%d %H:%M:%S')
                for data_index, data_item in enumerate(data_list):
                    three_count = sum(1 for item in data_list[:data_index] if item['data_src'] == 60)
                    four_count = sum(1 for item in data_list[:data_index] if item['data_src'] == 20)
                    five_count = sum(1 for item in data_list[:data_index] if item['data_src'] == 100)
                    six_count = sum(1 for item in data_list[:data_index] if item['data_src'] == 30 or item['data_src'] == 80)

                    cell_number = 6
                    if data_item['data_src'] == 100:
                        cell_number = 5
                    if data_item['data_src'] == 20:
                        cell_number = 4
                    if data_item['data_src'] == 60:
                        cell_number = 3

                    item_begin_col = int(three_count * 3 + four_count * 4 + five_count * 5 + six_count * 6 + 2)
                    item_end_col = int(three_count * 3 + four_count * 4 + five_count * 5 + six_count * 6 + 1 + cell_number)

                    ws[f'{get_column_letter(item_begin_col)}{time_index + 3}'].border = cell_border
                    value_list = [element for element in data_item['data'] if element['time'] == time_item]
                    if data_item['data_src'] == 20:
                        ws[f'{get_column_letter(item_begin_col)}{time_index + 3}'] = (data_item['parent_name'] if len(value_list) else ' ')
                        ws[f'{get_column_letter(item_begin_col + 1)}{time_index + 3}'] = (period_list[data_item['period']] if len(value_list) else ' ')
                        ws[f'{get_column_letter(item_begin_col + 2)}{time_index + 3}'] = (value_list[0].get('value', ' ') if len(value_list) else ' ')
                        ws[f'{get_column_letter(item_begin_col + 3)}{time_index + 3}'] = (data_item['unit'] if len(value_list) else ' ')
                        make_cell_center(ws[f'{get_column_letter(item_begin_col)}{time_index + 3}'])
                        make_cell_center(ws[f'{get_column_letter(item_begin_col + 1)}{time_index + 3}'])
                        make_cell_center(ws[f'{get_column_letter(item_begin_col + 2)}{time_index + 3}'])
                        make_cell_center(ws[f'{get_column_letter(item_begin_col + 3)}{time_index + 3}'])

                    if data_item['data_src'] == 30 or data_item['data_src'] == 80:
                        ws[f'{get_column_letter(item_begin_col)}{time_index + 3}'] = (data_item['parent_name'] if len(value_list) else ' ')
                        ws[f'{get_column_letter(item_begin_col + 1)}{time_index + 3}'] = (period_list[data_item['period']] if len(value_list) else ' ')
                        ws[f'{get_column_letter(item_begin_col + 2)}{time_index + 3}'] = (value_list[0].get('max', ' ') if len(value_list) else ' ')
                        ws[f'{get_column_letter(item_begin_col + 3)}{time_index + 3}'] = (value_list[0].get('min', ' ') if len(value_list) else ' ')
                        ws[f'{get_column_letter(item_begin_col + 4)}{time_index + 3}'] = (value_list[0].get('value', ' ') if len(value_list) else ' ')
                        ws[f'{get_column_letter(item_begin_col + 5)}{time_index + 3}'] = (data_item['unit'] if len(value_list) else ' ')
                        make_cell_center(ws[f'{get_column_letter(item_begin_col)}{time_index + 3}'])
                        make_cell_center(ws[f'{get_column_letter(item_begin_col + 1)}{time_index + 3}'])
                        make_cell_center(ws[f'{get_column_letter(item_begin_col + 2)}{time_index + 3}'])
                        make_cell_center(ws[f'{get_column_letter(item_begin_col + 3)}{time_index + 3}'])
                        make_cell_center(ws[f'{get_column_letter(item_begin_col + 4)}{time_index + 3}'])
                        make_cell_center(ws[f'{get_column_letter(item_begin_col + 5)}{time_index + 3}'])

                    if data_item['data_src'] == 60:
                        ws[f'{get_column_letter(item_begin_col)}{time_index + 3}'] = (period_list[data_item['period']] if len(value_list) else ' ')
                        ws[f'{get_column_letter(item_begin_col + 1)}{time_index + 3}'] = (value_list[0].get('value', ' ') if len(value_list) else ' ')
                        ws[f'{get_column_letter(item_begin_col + 2)}{time_index + 3}'] = (data_item['unit'] if len(value_list) else ' ')
                        make_cell_center(ws[f'{get_column_letter(item_begin_col)}{time_index + 3}'])
                        make_cell_center(ws[f'{get_column_letter(item_begin_col + 1)}{time_index + 3}'])
                        make_cell_center(ws[f'{get_column_letter(item_begin_col + 2)}{time_index + 3}'])

                    if data_item['data_src'] == 100:
                        ws[f'{get_column_letter(item_begin_col)}{time_index + 3}'] = (period_list[data_item['period']] if len(value_list) else ' ')
                        ws[f'{get_column_letter(item_begin_col + 1)}{time_index + 3}'] = (value_list[0].get('max', ' ') if len(value_list) else ' ')
                        ws[f'{get_column_letter(item_begin_col + 2)}{time_index + 3}'] = (value_list[0].get('min', ' ') if len(value_list) else ' ')
                        ws[f'{get_column_letter(item_begin_col + 3)}{time_index + 3}'] = (value_list[0].get('value', ' ') if len(value_list) else ' ')
                        ws[f'{get_column_letter(item_begin_col + 4)}{time_index + 3}'] = (data_item['unit'] if len(value_list) else ' ')
                        make_cell_center(ws[f'{get_column_letter(item_begin_col)}{time_index + 3}'])
                        make_cell_center(ws[f'{get_column_letter(item_begin_col + 1)}{time_index + 3}'])
                        make_cell_center(ws[f'{get_column_letter(item_begin_col + 2)}{time_index + 3}'])
                        make_cell_center(ws[f'{get_column_letter(item_begin_col + 3)}{time_index + 3}'])
                        make_cell_center(ws[f'{get_column_letter(item_begin_col + 4)}{time_index + 3}'])

            for idx, col in enumerate(ws.columns):
                ws.column_dimensions[col[1].column_letter if idx else "A"].width = (10 if idx else 20)
            
            file_name = f'导出数据文件__{AuthUtils.generate_random_comm_char(9)}'
            file_url = f'drf-assets/files/{file_name}.xlsx'
            wb.save(filename=file_url)
            return Response({
                'status': status.HTTP_200_OK,
                'data': {
                    'file_url': file_url,
                    'file_name': file_name
                }
            })

        if template == 20 and len(data_list):
            wb = Workbook()
            ws = wb.active
            title = ['序号', '数据名称', '数据粒度', '时间', '数据值', '单位']
            # 数据名称的位置
            data_name_position = 2
            # 设备原始数据 || 设备统计数据
            if list(filter(lambda x: x['data_src'] == 20 or x['data_src'] == 30, data_list)):
                title.insert(1, '设备名称')
                data_name_position += 1
            # 维度统计数据
            if list(filter(lambda x: x['data_src'] == 80, data_list)):
                title.insert(1, '维度名称')
                data_name_position += 1
            # 数据值的位置
            data_value_position = data_name_position + 3
            if list(filter(lambda x: x['data_src'] == 30 or x['data_src'] == 80 or x['data_src'] == 100, data_list)):
                title.insert(-2, '最大值')
                title.insert(-2, '最小值')
                data_value_position += 2
            # 将标题整合到excel表中
            for title_index, title_item in enumerate(title):
                position = get_column_letter(title_index * 1 + 1)
                ws[f'{position}1'] = f"{title_item}"
                make_cell_center(ws[f'{position}1'])
            # 将数据整合到excel表中
            for data_index, data_item in enumerate(data_list):
                for subdata_index, subdata_item in enumerate(data_item['data']):
                    # 序号
                    rows = int(ws.max_row)
                    ws[f'A{rows + 1}'] = rows
                    make_cell_center(ws[f'A{rows + 1}'])
                    # 设备名称
                    if list(filter(lambda x: x['data_src'] == 20 or x['data_src'] == 30, data_list)):
                        device_name = data_item['parent_name'] if data_item['data_src'] == 20 or data_item['data_src'] == 30 else '--'
                        ws[f'{get_column_letter(data_name_position - 1)}{rows + 1}'] = device_name
                        make_cell_center(ws[f'{get_column_letter(data_name_position - 1)}{rows + 1}'])
                    # 维度名称
                    if list(filter(lambda x: x['data_src'] == 80, data_list)):
                        dimension_name = data_item['parent_name'] if data_item['data_src'] == 80 else '--'
                        ws[f'B{rows + 1}'] = dimension_name
                        make_cell_center(ws[f'B{rows + 1}'])
                    # 数据名称
                    ws[f'{get_column_letter(data_name_position)}{rows + 1}'] = data_item['name']
                    make_cell_center(ws[f'{get_column_letter(data_name_position)}{rows + 1}'])
                    # 数据粒度
                    ws[f'{get_column_letter(data_name_position + 1)}{rows + 1}'] = period_list[data_item['period']]
                    make_cell_center(ws[f'{get_column_letter(data_name_position + 1)}{rows + 1}'])
                    # 时间
                    ws[f'{get_column_letter(data_name_position + 2)}{rows + 1}'] = subdata_item['time'].strftime('%Y-%m-%d %H:%M:%S')
                    make_cell_center(ws[f'{get_column_letter(data_name_position + 2)}{rows + 1}'])
                    # 最大值、最小值
                    if list(filter(lambda x: x['data_src'] == 30 or x['data_src'] == 80 or x['data_src'] == 100, data_list)):
                        ws[f'{get_column_letter(data_value_position - 2)}{rows + 1}'] = subdata_item['max'] if subdata_item['max'] else '--'
                        make_cell_center(ws[f'{get_column_letter(data_value_position - 2)}{rows + 1}'])
                        ws[f'{get_column_letter(data_value_position - 1)}{rows + 1}'] = subdata_item['min'] if subdata_item['min'] else '--'
                        make_cell_center(ws[f'{get_column_letter(data_value_position - 1)}{rows + 1}'])
                    # 数据值
                    ws[f'{get_column_letter(data_value_position)}{rows + 1}'] = subdata_item['value']
                    make_cell_center(ws[f'{get_column_letter(data_value_position)}{rows + 1}'])
                    # 单位
                    ws[f'{get_column_letter(data_value_position + 1)}{rows + 1}'] = data_item['unit'] if data_item['unit'] else '--'
                    make_cell_center(ws[f'{get_column_letter(data_value_position + 1)}{rows + 1}'])

            for idx, col in enumerate(ws.columns):
                ws.column_dimensions[f'{col[0].column_letter}'].width = (20 if idx == data_name_position + 1 else 10)
            
            file_name = f'导出数据文件__{AuthUtils.generate_random_comm_char(9)}'
            file_url = f'drf-assets/files/{file_name}.xlsx'
            wb.save(filename=file_url)
            return Response({
                'status': status.HTTP_200_OK,
                'data': {
                    'file_url': file_url,
                    'file_name': file_name
                }
            })

        return Response({
            'status': status.HTTP_404_NOT_FOUND,
            'data': None
        })

class SettlePartyViewSet(viewsets.ModelViewSet):
    serializer_class = SettlePartySerializer

    def get_queryset(self):
        queryset = SettleParty.objects.all()
        search = self.request.query_params.get('search', None)
        if search is not None:
            queryset = queryset.filter(name__icontains=search)
        return queryset
    
    def create(self, request, *args, **kwargs):
        from saian_api.utils.db.Redis import RedisHelper
        from django.db.models.functions import Cast
        project_id = request.user.get('project_id')
        # 结算对象的设备不允许重复
        if SettleParty.objects.exists():
            # 当前请求创建的结算对象的设备
            request_terminal_list = Terminal.objects.filter(show_en=True)
            request_device_filter = request.data.get('device_filter', None)
            if request_device_filter is not None:
                request_device_filter = json.loads(request_device_filter)
                if request_device_filter['device_prototype_ids']:
                    request_terminal_list = request_terminal_list.filter(device_prototype_id__in=request_device_filter['device_prototype_ids'].split(','))
                parameter_screening_list = [item for item in list(request_device_filter.keys()) if item != 'device_prototype_ids']
                if parameter_screening_list:
                    for device_prototype_id in request_device_filter['device_prototype_ids'].split(','):
                        for parameter_screening_item in parameter_screening_list:
                            attribute_prototype = AttributePrototype.objects.filter(identifier=parameter_screening_item, device_prototype_id=device_prototype_id).order_by('created_at').last()
                            if attribute_prototype:
                                # 缓存数据
                                request_cache_terminal_attribute = RedisHelper.scan_items(project_id, f'terminal_attribute:*_{attribute_prototype.id}', False)
                                request_cache_terminal_ids = [terminal_attribute.terminal_id for terminal_attribute in request_cache_terminal_attribute]
                                # 数据库数据
                                request_archive_terminal_ids = request_terminal_list.exclude(id__in=request_cache_terminal_ids).values_list('id', flat=True)
                                request_archive_terminal_attribute = TerminalAttribute.objects.filter(terminal_id__in=request_archive_terminal_ids, attribute_prototype_id=attribute_prototype.id)

                                request_terminal_attribute_list = request_cache_terminal_attribute + list(request_archive_terminal_attribute)

                                if int(attribute_prototype.data_type) == 20:
                                    request_eligible_terminal_attribute = list(filter(lambda item: float(item.value) == float(request_device_filter[parameter_screening_item]), request_terminal_attribute_list))
                                    request_eligible_terminal_ids = [item.terminal_id for item in request_eligible_terminal_attribute]
                                    request_terminal_list = request_terminal_list.filter(id__in=request_eligible_terminal_ids)
                                else:
                                    request_eligible_terminal_attribute = list(filter(lambda item: item.value == request_device_filter[parameter_screening_item], request_terminal_attribute_list))
                                    request_eligible_terminal_ids = [item.terminal_id for item in request_eligible_terminal_attribute]
                                    request_terminal_list = request_terminal_list.filter(id__in=request_eligible_terminal_ids)
                            else:
                                request_terminal_list = request_terminal_list.exclude(device_prototype_id=device_prototype_id)
            request_terminal_list = set([item.id for item in request_terminal_list])
            # 数据库中所有结算对象的设备
            settle_ways = request.data.get('settle_ways', None)
            database_terminal_list = set([])
            settle_party_list = SettleParty.objects.all()
            if settle_ways == '10' or settle_ways == '20':
                settle_party_list = SettleParty.objects.filter(settle_ways__icontains=settle_ways)
            for settle_party_item in settle_party_list:
                database_terminal_item = Terminal.objects.filter(show_en=True)
                if settle_party_item.device_filter:
                    database_device_filter = json.loads(settle_party_item.device_filter)
                    if database_device_filter['device_prototype_ids']:
                        database_terminal_item = database_terminal_item.filter(device_prototype_id__in=database_device_filter['device_prototype_ids'].split(','))
                    parameter_screening_list = [item for item in list(database_device_filter.keys()) if item != 'device_prototype_ids']
                    if parameter_screening_list:
                        for device_prototype_id in database_device_filter['device_prototype_ids'].split(','):
                            for parameter_screening_item in parameter_screening_list:
                                attribute_prototype = AttributePrototype.objects.filter(identifier=parameter_screening_item, device_prototype_id=device_prototype_id).order_by('created_at').last()
                                if attribute_prototype:
                                    # 缓存数据
                                    cache_terminal_attribute = RedisHelper.scan_items(project_id, f'terminal_attribute:*_{attribute_prototype.id}', False)
                                    cache_terminal_ids = [terminal_attribute.terminal_id for terminal_attribute in cache_terminal_attribute]
                                    # 数据库数据
                                    archive_terminal_ids = database_terminal_item.exclude(id__in=cache_terminal_ids).values_list('id', flat=True)
                                    archive_terminal_attribute = TerminalAttribute.objects.filter(terminal_id__in=archive_terminal_ids, attribute_prototype_id=attribute_prototype.id)

                                    terminal_attribute_list = cache_terminal_attribute + list(archive_terminal_attribute)

                                    if int(attribute_prototype.data_type) == 20:
                                        eligible_terminal_attribute = list(filter(lambda item: float(item.value) == float(database_device_filter[parameter_screening_item]), terminal_attribute_list))
                                        eligible_terminal_ids = [item.terminal_id for item in eligible_terminal_attribute]
                                        database_terminal_item = database_terminal_item.filter(id__in=eligible_terminal_ids)
                                    else:
                                        eligible_terminal_attribute = list(filter(lambda item: item.value == database_device_filter[parameter_screening_item], terminal_attribute_list))
                                        eligible_terminal_ids = [item.terminal_id for item in eligible_terminal_attribute]
                                        database_terminal_item = database_terminal_item.filter(id__in=eligible_terminal_ids)
                                else:
                                    database_terminal_item = database_terminal_item.exclude(device_prototype_id=device_prototype_id)
                database_terminal_list = database_terminal_list.union(set([item.id for item in database_terminal_item]))
            if bool(database_terminal_list.intersection(request_terminal_list)):
                return Response({
                    'status': status.HTTP_400_BAD_REQUEST,
                    'error': '结算对象设备重叠'
                })
        settle_party = super().create(request, *args, **kwargs).data
        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'settle_party': settle_party
            }
        })
    
    def destroy(self, request, *args, **kwargs):
        # 删除结算对象
        super().destroy(request, *args, **kwargs)
        # 删除结算对象相关的结算基准
        SettleRefer.objects.filter(settle_party_id=kwargs['pk']).delete()
        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })
    
    def partial_update(self, request, *args, **kwargs):
        settle_party = super().partial_update(request, *args, **kwargs).data
        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'settle_party': settle_party
            }
        })

    def list(self, request, *args, **kwargs):
        data = super().list(request, *args, **kwargs).data
        settle_parties = data['results']
        count = data['count']
        return Response({
            'status': status.HTTP_200_OK,
            'settle_parties': settle_parties,
            'count': count
        })
    
    def retrieve(self, request, *args, **kwargs):    
        settle_party = super().retrieve(request, *args, **kwargs).data
        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'settle_party': settle_party
            }
        })
    
class SettleReferViewSet(viewsets.ModelViewSet):
    serializer_class = SettleReferSerializer

    def get_queryset(self):
        queryset = SettleRefer.objects.all()
        spid = self.request.query_params.get('spid', None)
        if spid is not None:
            queryset = queryset.filter(settle_party_id=spid)
        return queryset

    def create(self, request, *args, **kwargs):
        settle_party_id = request.query_params.get('spid', None)
        param_name = request.data.get('param_name', None)
        param_idf = request.data.get('param_idf', None)
        param_value = request.data.get('param_value', None)
        param_unit = request.data.get('param_unit', None)
        run_params = request.data.get('run_params', None)
        settle_party_list= SettleParty.objects.filter(id=settle_party_id)
        if settle_party_list:
            settle_party_item = settle_party_list[0]
            if settle_party_item.ec_src == 'deviceself' and settle_party_item.rt_stat == 'run_params' and settle_party_item.settle_ways:
                settle_way_list = [int(item) for item in settle_party_list[0].settle_ways.split(',')]
                if not ((10 in settle_way_list and param_idf is None) 
                        or (20 in settle_way_list and (param_value is None or param_unit is None or param_name is None))):
                    settle_refer = SettleRefer.objects.create(
                        settle_party_id=settle_party_id,
                        param_name=param_name,
                        param_idf=param_idf,
                        param_value=param_value,
                        param_unit=param_unit,
                        run_params=run_params
                    )
                    return Response({
                        'status': status.HTTP_200_OK,
                        'data': {
                            'settle_refer': SettleReferSerializer(settle_refer).data
                        }
                    })
        return Response({
            'status': status.HTTP_400_BAD_REQUEST,
            'error': 'missing necessary parameters'
        })
    
    def destroy(self, request, *args, **kwargs):
        super().destroy(request, *args, **kwargs)
        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })
    
    def partial_update(self, request, *args, **kwargs):
        settle_refer = super().partial_update(request, *args, **kwargs).data
        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'settle_refer': settle_refer
            }
        })

    def list(self, request, *args, **kwargs):
        data = super().list(request, *args, **kwargs).data
        settle_references = data['results']
        count = data['count']
        return Response({
            'status': status.HTTP_200_OK,
            'settle_references': settle_references,
            'count': count
        })
    
    def retrieve(self, request, *args, **kwargs):
        settle_refer = super().retrieve(request, *args, **kwargs).data
        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'settle_refer': settle_refer
            }
        })
    
class SettleRecordViewSet(viewsets.ModelViewSet):
    serializer_class = SettleRecordSerializer

    def get_queryset(self):
        queryset = SettleRecord.objects.all().order_by('-settle_dt', '-period')

        # 结算对象
        settle_party_id = self.request.query_params.get('settle_party_id', None)
        if settle_party_id is not None:
            queryset = queryset.filter(settle_party_id=settle_party_id)

        # 结算周期
        period = self.request.query_params.get('period', None)
        if period is not None:
            queryset = queryset.filter(period__icontains=period)

        # 开始时间
        from_at = self.request.query_params.get('from', None)
        if from_at is not None:
            from_at = datetime.datetime.strptime(from_at, '%Y%m%d%H%M%S')
            queryset = queryset.filter(settle_dt__gte=from_at)

        # 结束时间
        till_at = self.request.query_params.get('till', None)
        if till_at is not None:
            till_at = datetime.datetime.strptime(till_at, '%Y%m%d%H%M%S')
            queryset = queryset.filter(settle_dt__lte=till_at)
        
        return queryset
    
    def change_date_format(self, period, settle_dt):
        settle_dt = datetime.datetime.strptime(settle_dt, "%Y-%m-%dT%H:%M:%S")

        year = str(settle_dt.year)
        month = f'{settle_dt.month:02}'
        day = f'{settle_dt.day:02}'
        hour = f'{settle_dt.hour:02}'

        year_, week_number, _ = settle_dt.isocalendar()

        four_quarter_time = [{'begin': 1, 'end': 3}, {'begin': 4, 'end': 6}, {'begin': 7, 'end': 9}, {'begin': 10, 'end': 12}]
        quarter = ['第一季度', '第二季度', '第三季度', '第四季度']
        index = next((i for i, item in enumerate(four_quarter_time) if item['begin'] <= int(month) <= item['end']), None)

        if period == 10:
            return year + '年' + month + '月' + day + '日' + hour + '时'
        elif period == 20:
            return year + '年' + month + '月' + day + '日'
        elif period == 30:
            return str(year_) + '年' + str(week_number) + '周'
        elif period == 40:
            return year + '年' + month + '月'
        elif period == 50:
            return year + '年' + quarter[index]
        else:
            return year + '年'

    def list(self, request, *args, **kwargs):    
        data = super().list(request, *args, **kwargs).data
        settle_record_list = data['results']
        count = data['count']
        for settle_record_item in settle_record_list:
            settle_party_list= SettleParty.objects.filter(id=settle_record_item['settle_party_id'])
            settle_record_item['settle_party'] = {}
            settle_record_item['settle_party']['id'] = settle_record_item['settle_party_id']
            settle_record_item['settle_party']['name'] = settle_party_list[0].name if settle_party_list else '--'
            del settle_record_item['settle_party_id']
            es_quantity = float(round(float(settle_record_item['conver_ec']) - float(settle_record_item['actual_ec']), 2))
            es_quantity = int(es_quantity) if int(es_quantity) == es_quantity else es_quantity
            settle_record_item['es_quantity'] = es_quantity
            settle_record_item['settle_dt'] = self.change_date_format(settle_record_item['period'], settle_record_item['settle_dt'])
        return Response({
            'status': status.HTTP_200_OK,
            'settle_records': settle_record_list,
            'count': count
        })
    
class DevstlRecordViewSet(viewsets.ModelViewSet):
    serializer_class = DevstlRecordSerializer

    def get_queryset(self):
        queryset = DevstlRecord.objects.all().order_by('-settle_dt', '-period')

        # 设备昵称、mac
        search = self.request.query_params.get('search', None)
        if search is not None:
            device_ids = Device.objects.filter(Q(mac__icontains=search)).values_list('id', flat=True)
            terminal_ids = Terminal.objects.filter(Q(show_en=True) & (Q(nick_name__icontains=search) | Q(device_id__in=device_ids))).values_list('id', flat=True)
            queryset = queryset.filter(terminal_id__in=terminal_ids)

        # 结算对象
        settle_party_id = self.request.query_params.get('settle_party_id', None)
        if settle_party_id is not None:
            queryset = queryset.filter(settle_party_id=settle_party_id)
        
        # 结算周期
        period = self.request.query_params.get('period', None)
        if period is not None:
            queryset = queryset.filter(period__icontains=period)

        # 开始时间
        from_at = self.request.query_params.get('from', None)
        if from_at is not None:
            from_at = datetime.datetime.strptime(from_at, '%Y%m%d%H%M%S')
            queryset = queryset.filter(settle_dt__gte=from_at)

        # 结束时间
        till_at = self.request.query_params.get('till', None)
        if till_at is not None:
            till_at = datetime.datetime.strptime(till_at, '%Y%m%d%H%M%S')
            queryset = queryset.filter(settle_dt__lte=till_at)
        
        return queryset
    
    def change_date_format(self, period, settle_dt):
        settle_dt = datetime.datetime.strptime(settle_dt, "%Y-%m-%dT%H:%M:%S")

        year = str(settle_dt.year)
        month = f'{settle_dt.month:02}'
        day = f'{settle_dt.day:02}'
        hour = f'{settle_dt.hour:02}'

        year_, week_number, _ = settle_dt.isocalendar()

        four_quarter_time = [{'begin': 1, 'end': 3}, {'begin': 4, 'end': 6}, {'begin': 7, 'end': 9}, {'begin': 10, 'end': 12}]
        quarter = ['第一季度', '第二季度', '第三季度', '第四季度']
        index = next((i for i, item in enumerate(four_quarter_time) if item['begin'] <= int(month) <= item['end']), None)

        if period == 10:
            return year + '年' + month + '月' + day + '日' + hour + '时'
        elif period == 20:
            return year + '年' + month + '月' + day + '日'
        elif period == 30:
            return str(year_) + '年' + str(week_number) + '周'
        elif period == 40:
            return year + '年' + month + '月'
        elif period == 50:
            return year + '年' + quarter[index]
        else:
            return year + '年'

    def list(self, request, *args, **kwargs):
        data = super().list(request, *args, **kwargs).data
        devstl_record_list = data['results']
        count = data['count']
        for devstl_record_item in devstl_record_list:
            terminal_list = Terminal.objects.filter(id=devstl_record_item['terminal_id'])
            device_list = Device.objects.filter(id=terminal_list[0].device_id) if terminal_list else None
            devstl_record_item['terminal'] = {}
            devstl_record_item['terminal']['id'] = devstl_record_item['terminal_id']
            devstl_record_item['terminal']['name'] = terminal_list[0].nick_name if terminal_list else '--'
            devstl_record_item['terminal']['mac'] = device_list[0].mac if device_list else '--'
            del devstl_record_item['terminal_id']
            devstl_record_item['detail'] = []
            devstl_record_item['con_rt'] = json.loads(devstl_record_item['con_rt'])
            run_time_total = sum(devstl_record_item['con_rt'].values())
            settle_refer_list = []
            settle_party_list = SettleParty.objects.filter(id=devstl_record_item['settle_party_id'])
            if settle_party_list:
                settle_party_item = settle_party_list[0]
                settle_refer_list = SettleRefer.objects.filter(settle_party_id=settle_party_item.id)
                for settle_refer_item in settle_refer_list:
                    settle_refer_item.run_params = json.loads(settle_refer_item.run_params)
            for rt_idf_item in devstl_record_item['con_rt'].keys():
                homologous_settle_refer_item = None
                homologous_settle_refer_list = list(filter(lambda item: item.run_params['rt_idf'] == rt_idf_item, settle_refer_list))
                if homologous_settle_refer_list:
                    homologous_settle_refer_item = homologous_settle_refer_list[0]
                item = {}
                item['name'] = homologous_settle_refer_item.run_params.get('name', '') + homologous_settle_refer_item.run_params.get('value', '') if homologous_settle_refer_item else ''
                item['name'] = item['name'] if item['name'] else rt_idf_item
                item['run_time'] = devstl_record_item['con_rt'][rt_idf_item]
                economize_energy = 0
                es_value = float(devstl_record_item['es_value'])
                if run_time_total and devstl_record_item['con_rt'][rt_idf_item]:
                    economize_energy = round(devstl_record_item['con_rt'][rt_idf_item] / run_time_total * es_value * 10000, 2) / 10000
                economize_energy = float(round(economize_energy, 2))
                economize_energy = int(economize_energy) if economize_energy == int(economize_energy) else economize_energy
                item['economize_energy'] = economize_energy
                devstl_record_item['detail'].append(item)                
            del devstl_record_item['con_rt']
            devstl_record_item['settle_dt'] = self.change_date_format(devstl_record_item['period'], devstl_record_item['settle_dt'])
        return Response({
            'status': status.HTTP_200_OK,
            'devstl_records': devstl_record_list,
            'count': count
        })


class GuananMonthlyReportView(viewsets.ModelViewSet):

    def calc_mom_yoy(self, da_id, year, month, prev_year, prev_month):
        cur = DimensionDailyStat.objects.filter(
            dimension_attribute_id=da_id,
            created_at__year=year,
            created_at__month=month
        ).aggregate(total=Sum('avg'))['total'] or 0

        prev = DimensionDailyStat.objects.filter(
            dimension_attribute_id=da_id,
            created_at__year=prev_year,
            created_at__month=prev_month
        ).aggregate(total=Sum('avg'))['total'] or 0

        prev_year_val = DimensionDailyStat.objects.filter(
            dimension_attribute_id=da_id,
            created_at__year=year - 1,
            created_at__month=month
        ).aggregate(total=Sum('avg'))['total'] or 0

        mom = (Decimal(cur) - Decimal(prev)) / Decimal(prev) if prev else None
        yoy = (Decimal(cur) - Decimal(prev_year_val)) / Decimal(prev_year_val) if prev_year_val else None

        return cur, prev, prev_year_val, mom, yoy

    def create(self, request, *args, **kwargs):
        template_path = f'saian_api/report/templates/monthly_analysis_template.xlsx'
        file_path = os.path.join(settings.BASE_DIR, template_path)
        file = Path(file_path)
        now = datetime.datetime.now()

        year = request.data.get('year', now.year)
        month = request.data.get('month', now.month)

        if month == 12:
            prev_year = year - 1
            prev_month = 1
        else:
            prev_year = year
            prev_month = month - 1

        if not file.exists() or not file.is_file():
            return Response({
                'status': status.HTTP_404_NOT_FOUND,
                'detail': '模板文件不存在'
            })

        wb = load_workbook(file_path)

        # 用能统计
        ws = wb[wb.sheetnames[0]]
        title = f'{month}月用能统计'
        ws.title = title
        power_da_id = 7
        water_da_id = 83
        gas_da_id = 84
        cur_power_cons, prev_power_cons, prev_year_power_cons, mom_ratio, yoy_ratio = self.calc_mom_yoy(power_da_id, year, month, prev_year,
                                                                                                        prev_month)
        cur_water_cons, prev_water_cons, prev_year_water_cons, water_mom_ratio, water_yoy_ratio = self.calc_mom_yoy(water_da_id, year, month,
                                                                                                                    prev_year, prev_month)
        cur_gas_cons, prev_gas_cons, prev_year_gas_cons, gas_mom_ratio, gas_yoy_ratio = self.calc_mom_yoy(gas_da_id, year, month, prev_year,
                                                                                                          prev_month)

        ws['A1'] = title
        ws['A4'] = f'{year}年{month}月'
        ws['A5'] = f'{prev_year}年{prev_month}月'
        ws['A6'] = f'{year - 1}年{month}月'
        ws['A7'] = f"环比（较{ws['A5'].value}）"
        ws['A8'] = f"同比（较{ws['A6'].value}）"

        ws['B4'] = cur_power_cons
        ws['B5'] = prev_power_cons if prev_power_cons else '-'
        ws['B6'] = prev_year_power_cons if prev_year_power_cons else '-'
        ws['B7'] = f'{round(mom_ratio * 100, 2)}%' if mom_ratio is not None else '-'
        ws['B8'] = f'{round(yoy_ratio * 100, 2)}%' if yoy_ratio is not None else '-'

        # 用水
        ws['C4'] = cur_water_cons
        ws['C5'] = prev_water_cons if prev_water_cons else '-'
        ws['C6'] = prev_year_water_cons if prev_year_water_cons else '-'
        ws['C7'] = f'{round(water_mom_ratio * 100, 2)}%' if water_mom_ratio is not None else '-'
        ws['C8'] = f'{round(water_yoy_ratio * 100, 2)}%' if water_yoy_ratio is not None else '-'

        # 用气
        ws['D4'] = cur_gas_cons
        ws['D5'] = prev_gas_cons if prev_gas_cons else '-'
        ws['D6'] = prev_year_gas_cons if prev_year_gas_cons else '-'
        ws['D7'] = f'{round(gas_mom_ratio * 100, 2)}%' if gas_mom_ratio is not None else '-'
        ws['D8'] = f'{round(gas_yoy_ratio * 100, 2)}%' if gas_yoy_ratio is not None else '-'

        # 用气量统计
        ws = wb[wb.sheetnames[1]]
        title = f'{month}月用水量统计'
        ws.title = title
        water_da_id = 83
        canteen_water_da_ids = [126, 127]
        building_water_da_ids = [125]

        # - 食堂用水量
        canteen_water_cons = DimensionDailyStat.objects.filter(dimension_attribute_id__in=canteen_water_da_ids,
                                                               created_at__year=year,
                                                               created_at__month=month).aggregate(total=Sum('avg'))['total']
        prev_canteen_water_cons = DimensionDailyStat.objects.filter(dimension_attribute_id__in=canteen_water_da_ids,
                                                                    created_at__year=prev_year,
                                                                    created_at__month=prev_month).aggregate(total=Sum('avg'))['total']
        canteen_water_mom = (Decimal(canteen_water_cons) - Decimal(prev_canteen_water_cons)) / Decimal(
            prev_canteen_water_cons) if prev_canteen_water_cons else None

        # - 办公大楼用水量
        building_water_cons = DimensionDailyStat.objects.filter(
            dimension_attribute_id__in=building_water_da_ids,
            created_at__year=year,
            created_at__month=month
        ).aggregate(total=Sum('avg'))['total']

        prev_building_water_cons = DimensionDailyStat.objects.filter(
            dimension_attribute_id__in=building_water_da_ids,
            created_at__year=prev_year,
            created_at__month=prev_month
        ).aggregate(total=Sum('avg'))['total']

        building_water_mom = (
                (Decimal(building_water_cons) - Decimal(prev_building_water_cons)) / Decimal(prev_building_water_cons)
        ) if prev_building_water_cons else None

        total_water_cons = canteen_water_cons + building_water_cons
        prev_total_water_cons = prev_canteen_water_cons + prev_building_water_cons
        total_water_mom = (Decimal(total_water_cons) - Decimal(prev_total_water_cons)) / Decimal(
            prev_total_water_cons) if prev_total_water_cons else None

        ws['A1'] = title
        ws['B4'] = building_water_cons
        ws['B5'] = canteen_water_cons
        ws['D4'] = f'{round(building_water_mom * 100, 2)}%' if building_water_mom else '-'
        ws['D5'] = f'{round(canteen_water_mom * 100, 2)}%' if canteen_water_mom else '-'
        ws['D6'] = f'{round(total_water_mom * 100, 2)}%' if total_water_mom else '-'

        # 用气量统计
        ws = wb[wb.sheetnames[2]]
        title = f'{month}月用气量统计'
        ws.title = title
        government_canteen_gas_id = 128
        audit_canteen_gas_id = 129
        total_gas_id = 84

        # 政府办用气量
        cur_gov_gas_cons, prev_gov_gas_cons, prev_year_gov_gas_cons, gov_gas_mom, gov_gas_yoy = self.calc_mom_yoy(
            government_canteen_gas_id, year, month, prev_year, prev_month
        )

        # 审计局用气量
        cur_audit_gas_cons, prev_audit_gas_cons, prev_year_audit_gas_cons, audit_gas_mom, audit_gas_yoy = self.calc_mom_yoy(
            audit_canteen_gas_id, year, month, prev_year, prev_month
        )

        # 总用气量
        cur_total_gas_cons, prev_total_gas_cons, prev_year_total_gas_cons, total_gas_mom, total_gas_yoy = self.calc_mom_yoy(
            total_gas_id, year, month, prev_year, prev_month
        )

        ws['A1'] = title
        ws['B4'] = cur_gov_gas_cons
        ws['B5'] = cur_audit_gas_cons
        ws['D4'] = f'{round(gov_gas_mom * 100, 2)}%' if gov_gas_mom else '-'
        ws['D5'] = f'{round(audit_gas_mom * 100, 2)}%' if audit_gas_mom else '-'
        ws['D6'] = f'{round(total_gas_mom * 100, 2)}%' if total_gas_mom else '-'
        ws['E4'] = f'{round(gov_gas_yoy * 100, 2)}%' if gov_gas_yoy else '-'
        ws['E5'] = f'{round(audit_gas_yoy * 100, 2)}%' if audit_gas_yoy else '-'
        ws['E6'] = f'{round(total_gas_yoy * 100, 2)}%' if total_gas_yoy else '-'

        # 电耗温度数据
        ws = wb[wb.sheetnames[3]]

        power_da_id = 7

        daily_power_stats = DimensionDailyStat.objects.filter(dimension_attribute_id=power_da_id, created_at__year=year, created_at__month=month)
        daily_power_stats_map = {stat.created_at.day: stat for stat in daily_power_stats}

        daily_temp_stats = ProjectWeather.objects.filter(type='di', created_at__year=year, created_at__month=month).only('temp')
        daily_temp_stats_map = {stat.created_at.day: stat for stat in daily_temp_stats}

        for day in range(1, 32):
            cur_day_power = daily_power_stats_map.get(day, None)
            cur_day_temp = daily_temp_stats_map.get(day, None)

            ws[f'B{day + 1}'] = float(cur_day_power.avg) if cur_day_power else ''
            ws[f'C{day + 1}'] = float(cur_day_temp.temp) if cur_day_temp else ''

        # 逐日用水量
        ws = wb[wb.sheetnames[4]]

        water_da_id = 83

        daily_water_stats = DimensionDailyStat.objects.filter(dimension_attribute_id=water_da_id, created_at__year=year, created_at__month=month)
        daily_water_stats_map = {stat.created_at.day: stat for stat in daily_water_stats}
        for day in range(1, 32):
            cur_day_water = daily_water_stats_map.get(day, None)

            ws[f'B{day + 1}'] = float(cur_day_water.avg) if cur_day_water else ''

        # 逐日用气量
        ws = wb[wb.sheetnames[5]]

        gas_da_id = 84

        daily_gas_stats = DimensionDailyStat.objects.filter(dimension_attribute_id=gas_da_id, created_at__year=year, created_at__month=month)
        daily_gas_stats_map = {stat.created_at.day: stat for stat in daily_gas_stats}
        for day in range(1, 32):
            cur_day_gas = daily_gas_stats_map.get(day, None)

            ws[f'B{day + 1}'] = float(cur_day_gas.avg) if cur_day_gas else ''

        # 各单位用电量(以及用气量)
        ws = wb[wb.sheetnames[6]]

        # - 总用气
        gas_cons = cur_gas_cons
        gas_cons = float(gas_cons) if gas_cons else 0
        prev_gas_cons = float(prev_gas_cons) if prev_gas_cons else 0
        unit_gas_cons = gas_cons / 8
        prev_unit_gas_cons = prev_gas_cons / 8

        # - 总用水
        water_cons = cur_water_cons
        water_cons = float(water_cons) if water_cons else 0
        prev_water_cons = float(prev_water_cons) if prev_water_cons else 0

        unit_ranks = []
        unit_da_ids = [[32, 75], 33, 34, 35, 36, 37, 38, 39]
        for da_id in unit_da_ids:
            if isinstance(da_id, list):
                unit_persons = Dimension.objects.filter(dimensionattribute__in=da_id).aggregate(total=Sum('ec_persons'))['total']
                unit_area = Dimension.objects.filter(dimensionattribute__in=da_id).aggregate(total=Sum('unit_area'))['total']

                unit_power = DimensionDailyStat.objects.filter(dimension_attribute_id__in=da_id,
                                                               created_at__year=year,
                                                               created_at__month=month).aggregate(total=Sum('avg'))['total']
                prev_unit_power = DimensionDailyStat.objects.filter(dimension_attribute_id__in=da_id,
                                                                    created_at__year=prev_year,
                                                                    created_at__month=prev_month).aggregate(total=Sum('avg'))['total']

                name = '市政府办用电量（含国动办）'
            else:
                unit_persons = Dimension.objects.filter(dimensionattribute=da_id).aggregate(total=Sum('ec_persons'))['total']
                unit_area = Dimension.objects.filter(dimensionattribute=da_id).aggregate(total=Sum('unit_area'))['total']

                unit_power = DimensionDailyStat.objects.filter(dimension_attribute_id=da_id,
                                                               created_at__year=year,
                                                               created_at__month=month).aggregate(total=Sum('avg'))['total']
                prev_unit_power = DimensionDailyStat.objects.filter(dimension_attribute_id=da_id,
                                                                    created_at__year=prev_year,
                                                                    created_at__month=prev_month).aggregate(total=Sum('avg'))['total']
                name = DimensionAttribute.objects.get(pk=da_id).name

            kgce = unit_power * 0.1229 + unit_gas_cons * 1.33
            prev_kgce = prev_unit_power * 0.1229 + prev_unit_gas_cons * 1.33

            kgce_person = kgce / unit_persons
            kgce_area = kgce / unit_area

            prev_kgce_person = prev_kgce / unit_persons
            prev_kgce_area = prev_kgce / unit_area

            unit_water_cons = water_cons / unit_persons
            prev_unit_water_cons = prev_water_cons / unit_persons

            ratio = (Decimal(unit_power) - Decimal(prev_unit_power)) / Decimal(prev_unit_power) if prev_unit_power else None
            unit_ranks.append({
                'name': name,
                'value': float(unit_power) if unit_power else 0,
                'prev_value': float(prev_unit_power) if prev_unit_power else 0,
                'ratio': f'{round(ratio * 100, 2)}%' if ratio else '-',
                'kgce': kgce,
                'prev_kgce': prev_kgce,
                'kgce_person': kgce_person,
                'prev_kgce_person': prev_kgce_person,
                'kgce_area': kgce_area,
                'prev_kgce_area': prev_kgce_area,
                'water_cons': unit_water_cons,
                'prev_water_cons': prev_unit_water_cons
            })

        unit_ranks = sorted(unit_ranks, key=lambda obj: -obj['value'])

        ws['B1'] = f'{prev_month}月用电量'
        ws['C1'] = f'{month}月用电量'

        for idx, unit_power_stat, in enumerate(unit_ranks):
            ws[f'A{idx + 2}'] = unit_power_stat['name']
            ws[f'B{idx + 2}'] = unit_power_stat['prev_value']
            ws[f'C{idx + 2}'] = unit_power_stat['value']
            ws[f'D{idx + 2}'] = unit_power_stat['ratio']

        # 用电构成
        ws = wb[wb.sheetnames[7]]
        title = f'{month}月用电构成'
        ws.title = title
        ws['A1'] = title

        # - 照明用电(照明、空调、插座、电梯、会议室、食堂、其他检测设备)
        total_item_id = 7
        item_ids = [69, 49, 59, 70, 97, 101, 71]
        item_sum = 0
        pre_item_sum = 0
        for idx, item_id in enumerate(item_ids):
            item_value = DimensionDailyStat.objects.filter(dimension_attribute_id=item_id,
                                                           created_at__year=year,
                                                           created_at__month=month).aggregate(total=Sum('avg'))['total']
            pre_item_value = DimensionDailyStat.objects.filter(dimension_attribute_id=item_id,
                                                               created_at__year=prev_year,
                                                               created_at__month=prev_month).aggregate(total=Sum('avg'))['total']
            ws[f'C{idx + 3}'] = round(float(item_value) / 10000, 2) if item_value else 0
            ws[f'E{idx + 3}'] = round(float(item_value - pre_item_value) / 10000, 2) if pre_item_value else 0
            if item_value:
                item_sum += float(item_value)
            if pre_item_value:
                pre_item_sum += float(pre_item_value)

        total_value = DimensionDailyStat.objects.filter(dimension_attribute_id=total_item_id,
                                                        created_at__year=year,
                                                        created_at__month=month).aggregate(total=Sum('avg'))['total']
        pre_total_value = DimensionDailyStat.objects.filter(dimension_attribute_id=total_item_id,
                                                            created_at__year=prev_year,
                                                            created_at__month=prev_month).aggregate(total=Sum('avg'))['total']
        remain_value = 0
        if total_value:
            remain_value = total_value - item_sum
        pre_remain_value = 0
        if pre_total_value:
            pre_remain_value = pre_total_value - pre_item_sum

        ws['c10'] = round(float(remain_value) / 10000, 2) if remain_value else 0
        ws['e10'] = round(float(pre_remain_value) / 10000, 2) if pre_remain_value else 0

        # 用电合理性分析
        ws = wb[wb.sheetnames[8]]

        # 设备 ID
        ac_item_id = 49  # 空调
        lighting_item_id = 69  # 照明
        socket_item_id = 59  # 插座
        meeting_item_id = 97  # 会议室

        item_power_stats = defaultdict(lambda: defaultdict(float))
        # 结构示例：
        # item_power_stats['workday_off']['ac'] = 123.45
        # item_power_stats['holiday_all']['lighting'] = 88.1

        calc_dt = datetime.datetime(year, month, 1)

        # 拉取当月所有小时数据（一次性，减少 SQL 查询）
        ac_hourly = DimensionHourlyStat.objects.filter(dimension_attribute_id=ac_item_id,
                                                       created_at__year=year,
                                                       created_at__month=month)
        lighting_hourly = DimensionHourlyStat.objects.filter(dimension_attribute_id=lighting_item_id,
                                                             created_at__year=year,
                                                             created_at__month=month)
        socket_hourly = DimensionHourlyStat.objects.filter(dimension_attribute_id=socket_item_id,
                                                           created_at__year=year,
                                                           created_at__month=month)
        meeting_hourly = DimensionHourlyStat.objects.filter(dimension_attribute_id=meeting_item_id,
                                                            created_at__year=year,
                                                            created_at__month=month)

        # 把数据按日期+小时组织成 dict，避免循环里查数据库
        def build_map(qs):
            mapping = defaultdict(dict)
            for rec in qs:
                day = rec.created_at.date()
                hour = rec.created_at.hour
                mapping[day][hour] = float(rec.avg) if rec.avg else 0
            return mapping

        ac_map = build_map(ac_hourly)
        lighting_map = build_map(lighting_hourly)
        socket_map = build_map(socket_hourly)
        meeting_map = build_map(meeting_hourly)

        while calc_dt.month == month:
            day = calc_dt.date()
            is_work = is_workday(calc_dt)
            # 获取当天温度
            temp = 26
            weather = ProjectWeather.objects.filter(
                type='di',
                created_at__year=year,
                created_at__month=month,
                created_at__day=day.day
            ).last()
            if weather:
                temp = weather.temp

            # 遍历 0-23 点
            for hour in range(24):
                # 取四类设备的用电
                ac_val = ac_map[day].get(hour, 0)
                lighting_val = lighting_map[day].get(hour, 0)
                socket_val = socket_map[day].get(hour, 0)
                meeting_val = meeting_map[day].get(hour, 0)

                if is_work:
                    # 工作日
                    if 9 <= hour < 19:  # 工作时间
                        if 10 <= temp <= 26:
                            key = "workday_work_normal"  # 温度正常
                        else:
                            key = "workday_work_extreme"  # 温度异常
                    else:
                        key = "workday_off"
                else:
                    # 非工作日
                    key = "holiday_all"

                # 累加到 item_power_stats
                item_power_stats[key]['ac'] += ac_val
                item_power_stats[key]['lighting'] += lighting_val
                item_power_stats[key]['socket'] += socket_val
                item_power_stats[key]['meeting'] += meeting_val

            calc_dt = calc_dt + datetime.timedelta(days=1)

        ws['B3'] = item_power_stats['workday_off']['ac']
        ws['B4'] = item_power_stats['workday_off']['lighting']
        ws['B5'] = item_power_stats['workday_off']['socket']
        ws['B6'] = item_power_stats['workday_off']['meeting']

        ws['C3'] = item_power_stats['holiday_all']['ac']
        ws['C4'] = item_power_stats['holiday_all']['lighting']
        ws['C5'] = item_power_stats['holiday_all']['socket']
        ws['C6'] = item_power_stats['holiday_all']['meeting']

        ws['D3'] = item_power_stats['workday_work_normal']['ac']
        ws['E3'] = item_power_stats['workday_work_extreme']['ac']

        ws['D4'] = item_power_stats['workday_work_normal']['lighting'] + item_power_stats['workday_work_extreme']['lighting']
        ws['D5'] = item_power_stats['workday_work_normal']['socket'] + item_power_stats['workday_work_extreme']['socket']
        ws['D6'] = item_power_stats['workday_work_normal']['meeting'] + item_power_stats['workday_work_extreme']['meeting']

        ac_total = (item_power_stats['workday_off']['ac'] +
                    item_power_stats['holiday_all']['ac'] +
                    item_power_stats['workday_work_normal']['ac'] +
                    item_power_stats['workday_work_extreme']['ac'])
        ac_eco = ac_total - item_power_stats['workday_work_extreme']['ac']

        ws['F3'] = round(ac_total / 10000, 2) if ac_total else 0
        ws['G3'] = round(ac_eco / 10000, 2) if ac_eco else 0

        lighting_total = (item_power_stats['workday_off']['lighting'] +
                          item_power_stats['holiday_all']['lighting'] +
                          item_power_stats['workday_work_normal']['lighting'] +
                          item_power_stats['workday_work_extreme']['lighting'])
        lighting_eco = (item_power_stats['workday_off']['lighting'] +
                        item_power_stats['holiday_all']['lighting'])

        ws['F4'] = round(lighting_total / 10000, 2) if lighting_total else 0
        ws['G4'] = round(lighting_eco / 10000, 2) if lighting_eco else 0

        socket_total = (item_power_stats['workday_off']['socket'] +
                        item_power_stats['holiday_all']['socket'] +
                        item_power_stats['workday_work_normal']['socket'] +
                        item_power_stats['workday_work_extreme']['socket'])
        socket_eco = (item_power_stats['workday_off']['socket'] +
                      item_power_stats['holiday_all']['socket'])

        ws['F5'] = round(socket_total / 10000, 2) if socket_total else 0
        ws['G5'] = round(socket_eco / 10000, 2) if socket_eco else 0

        meeting_total = (item_power_stats['workday_off']['meeting'] +
                         item_power_stats['holiday_all']['meeting'] +
                         item_power_stats['workday_work_normal']['meeting'] +
                         item_power_stats['workday_work_extreme']['meeting'])
        meeting_eco = (item_power_stats['workday_off']['meeting'] +
                       item_power_stats['holiday_all']['meeting'])

        ws['F6'] = round(meeting_total / 10000, 2) if meeting_total else 0
        ws['G6'] = round(meeting_eco / 10000, 2) if meeting_eco else 0

        # 各单位能耗排名
        ws = wb[wb.sheetnames[9]]
        title = f'{month}月第二办公区各单位能耗排名'
        ws['A1'] = title

        def rank_unit_data_by_kgce_area(data):
            """
            根据 kgce_area 排序，同时计算 kgce_area、kgce_person、water_cons 的排名和变化。
            data: 字典列表，每个字典包含 kgce_area、kgce_person、water_cons 及对应 prev_ 前缀字段
            """
            metrics = ['kgce_area', 'kgce_person', 'water_cons']

            # 计算每个指标的排名和排名变化
            for metric in metrics:
                prev_metric = 'prev_' + metric
                prev_rank_key = f'prev_{metric}_rank'
                rank_key = metric + '_rank'
                rank_change_key = metric + '_rank_change'

                # 本月排名
                sorted_curr = sorted(data, key=itemgetter(metric))
                curr_rank = {id(d): i + 1 for i, d in enumerate(sorted_curr)}

                # 上月排名
                sorted_prev = sorted(data, key=itemgetter(prev_metric))
                prev_rank = {id(d): i + 1 for i, d in enumerate(sorted_prev)}

                # 保存排名
                for d in data:
                    d[rank_key] = curr_rank[id(d)]
                    d[prev_rank_key] = prev_rank[id(d)]
                    d[rank_change_key] = prev_rank[id(d)] - curr_rank[id(d)]

            # 最终按照 kgce_area 本月排名排序输出
            sorted_final = sorted(data, key=lambda d: d['kgce_area_rank'])
            return sorted_final

        unit_ec_rank = rank_unit_data_by_kgce_area(unit_ranks)

        for idx, item, in enumerate(unit_ec_rank):
            # 单位面积综合能耗
            area_rank_change = item['kgce_area_rank_change']
            area_rank_change_symbol = '（--）'
            if area_rank_change > 0:
                area_rank_change_symbol = f'（↑{area_rank_change}）'
            elif area_rank_change < 0:
                area_rank_change_symbol = f'（↓{area_rank_change}）'

            ws[f'A{idx + 4}'] = item['name'].replace('用电量', '')
            ws[f'B{idx + 4}'] = round(item['kgce_area'], 2)
            ws[f'C{idx + 4}'] = f"{item['kgce_area_rank']}{area_rank_change_symbol}"

            # 人均综合能耗
            person_rank_change = item['kgce_person_rank_change']
            person_rank_change_symbol = '（--）'
            if person_rank_change > 0:
                person_rank_change_symbol = f'（↑{person_rank_change}）'
            elif person_rank_change < 0:
                person_rank_change_symbol = f'（↓{person_rank_change}）'

            ws[f'D{idx + 4}'] = round(item['kgce_person'], 2)
            ws[f'E{idx + 4}'] = f"{item['kgce_person_rank']}{person_rank_change_symbol}"

            # 人均用水量
            water_rank_change = item['water_cons_rank_change']
            water_rank_change_symbol = '（--）'
            if water_rank_change > 0:
                water_rank_change_symbol = f'（↑{water_rank_change}）'
            elif water_rank_change < 0:
                water_rank_change_symbol = f'（↓{water_rank_change}）'

            ws[f'F{idx + 4}'] = round(item['water_cons'], 2)
            ws[f'G{idx + 4}'] = f"{item['water_cons_rank']}{water_rank_change_symbol}"

        filename = f'广安{year}年{month}月_月度分析__{AuthUtils.generate_random_comm_char(9)}'
        # 移除文件名中的特殊符号(#)
        filename = filename.replace('#', '')
        path = f'drf-assets/files/{filename}.xlsx'
        wb.save(filename=path)

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'filename': filename,
                'filepath': path
            }
        })
