"""
新建快捷操作表
"""
import traceback
from datetime import datetime, timedelta

from django.core.management import BaseCommand, CommandError

from saian_api.device.models import DeviceCtrlLog
from saian_api.group.models import ActionLog
from saian_api.project.models import Project
from saian_api.scheduledtask.utils import get_projects, set_global_db


class Command(BaseCommand):
    help = "更新设备执行记录信息"

    # def add_arguments(self, parser):
    #     parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目列表
        # projects = get_projects(self, options)

        projects = [48, 58, 66, 67, 68, 69, 70, 71]
        for project_id in projects:
            # project_id = 58
            try:
                set_global_db(project_id)
                project = Project.objects.get(pk=project_id)
                self.stdout.write(f"更新设备执行记录: {project.name}", ending='\n')

                executor_types = ['WebUser', 'web user', 'LinkageRule',  'LinkageTrigger', 'linkage trigger', 'ActionTimer', 'action timer']
                webuser, linkage, action = ['WebUser', 'web user'], ['LinkageRule',
                                                                     'LinkageTrigger', 'linkage trigger'], ['ActionTimer', 'action timer']
                device_ctrl_logs = DeviceCtrlLog.objects.filter(executor_type__in=executor_types)
                action_logs = ActionLog.objects.filter(actor_name__in=executor_types).order_by('-created_at')[:5000]
                count = 0

                for action_log in action_logs:
                    fromtime = action_log.created_at - timedelta(minutes=2)

                    if action_log.actor_name in webuser:
                        type = webuser
                    elif action_log.actor_name in linkage:
                        type = linkage
                    elif action_log.actor_name in action:
                        type = action
                    dc_logs = device_ctrl_logs.filter(data=action_log.values,
                                                      executor_type__in=type,
                                                      created_at__range=[fromtime, action_log.created_at])

                    count = count + len(dc_logs)
                    if dc_logs.count() > 0:
                        for dc_log in dc_logs:
                            dc_log.action_log_id = action_log.id
                            dc_log.save()

                self.stdout.write(f'\t更新设备执行记录{count}条。', ending='\n')
            except CommandError:
                self.stderr.write(f"运行'更新设备执行记录'任务失败，项目ID：{project_id}，命令参数不合法！", ending='\n')
                # continue
            except Exception as e:
                self.stderr.write(f"运行'更新设备执行记录'任务失败，项目ID：{project_id}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
            # continue
