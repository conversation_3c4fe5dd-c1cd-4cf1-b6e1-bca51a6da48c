import datetime
import re

from saian_api.devdefine.models import AttributePrototype
from saian_api.device.models import DeviceAttribute, Device
from saian_api.issue.models import DeviceIssue
from .base import BaseDevice
from ...terminal.models import Terminal
from ...utils.tools import is_number

"""
  分体空调
"""
class SplitAirCon(BaseDevice):
    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)

        data = event.get('data', {})
        if 'Status' in data:
            # sw_da = DeviceAttribute.objects.get_by_idf(device, 'Status')
            sw_da = DeviceAttribute.objects.query_object_by_idf(device, 'Status')
            if sw_da is not None:
                if data['Status']:
                    device.sw_on = True
                else:
                    device.sw_on = False

                device.save()
                Terminal.objects.bulk_update_by_device(device, 'sw_on', device.sw_on)

    @classmethod
    def fault(cls, device, event):
        data = event.get('data', {})
        if 'LocalTemp' in data:
            if float(data['LocalTemp']) < 0:
                glossary = '传感器不可用'

                if float(data['LocalTemp']) == -186:
                    glossary = '温度CRC校验错误'
                if float(data['LocalTemp']) == -187:
                    glossary = '发送失败（温度）'
                if float(data['LocalTemp']) == -188:
                    glossary = '没连接（温度）'

                DeviceIssue.objects.add_fault(device, glossary)
            else:
                DeviceIssue.objects.recover_fault(device, '温度CRC校验错误')
                DeviceIssue.objects.recover_fault(device, '发送失败（温度）')
                DeviceIssue.objects.recover_fault(device, '没连接（温度）')

        if 'LocalRH' in data:
            if float(data['LocalRH']) < 0:
                glossary = '传感器不可用'

                if float(data['LocalRH']) == -186:
                    glossary = '湿度CRC校验错误'
                if float(data['LocalRH']) == -187:
                    glossary = '发送失败（湿度）'
                if float(data['LocalRH']) == -188:
                    glossary = '没连接（湿度）'

                DeviceIssue.objects.add_fault(device, glossary)
            else:
                DeviceIssue.objects.recover_fault(device, '湿度CRC校验错误')
                DeviceIssue.objects.recover_fault(device, '发送失败（湿度）')
                DeviceIssue.objects.recover_fault(device, '没连接（湿度）')

    @classmethod
    def exec_cmd(cls, device, data, executor):
        return super().exec_cmd(device, data, executor)

class PihSplitAirCon(BaseDevice):
    """派诺分体空调"""
    @classmethod
    def update_attres(cls, device, event):
        data = event.get('data', {})
        if 'temperature_real' in data:
            # 排除异常数据
            temp = float(data['temperature_real'])
            if temp < -35 or temp > 50:
                del data['temperature_real']

        super().update_attres(device, event)
        data = event.get('data', {})

        # 开关
        if 'switch_' in data:
            sw_da = DeviceAttribute.objects.query_object_by_idf(device, 'switch_')
            if sw_da is not None:
                device.sw_on = bool(sw_da.value)
            else:
                device.sw_on = bool(data['switch_'])
            device.save()
            Terminal.objects.bulk_update_by_device(device, 'sw_on', device.sw_on)

        # 根据上报数据中的"update_time"来判断在线状态
        if 'update_time' in data and data['update_time']:
            update_time = datetime.datetime.strptime(data['update_time'], '%Y-%m-%d %H:%M:%S')

            timeout = device.device_prototype.timeout_set
            if not timeout:
                timeout = 3600

            span_seconds = (datetime.datetime.now() - update_time).total_seconds()
            if device.online and span_seconds > timeout:
                device.online = False
                device.status = 30
                device.save(update_fields=['online', 'status'])
                Terminal.objects.bulk_update_by_device(device, 'online', device.online)
            if (not device.online) and span_seconds < timeout:
                device.online = True
                if device.status != 40:
                    device.status = 20
                device.save(update_fields=['online', 'status'])
                # 更新终端在线状态
                for terminal in Terminal.objects.filter(device=device, online=False):
                    terminal.online = True
                    terminal.save()

            # 将设备的更新时间设置为上报数据的更新时间
            Device.objects.filter(pk=device.id).update(updated_at=update_time)
            Terminal.objects.filter(device_id=device.id).update(updated_at=update_time)

    @classmethod
    def alarm(cls, device, event):
        super().alarm(device, event)

    @classmethod
    def fault(cls, device, event):
        data = event.get('data', {})
        if 'fault_name' in data:
            # -1: 故障中 0: 已维修 1: 同一故障仍存在 2: 原故障修好新故障又产生
            status = data['confirmation_maintenance']
            fault_name = data['fault_name']
            fault_desc = data.get('fault_desc', '')
            created_time = data.get('created_time')
            updated_time = data.get('updated_time')

            glossary = fault_name
            if fault_desc:
                glossary = fault_desc

            if status != 0:
                DeviceIssue.objects.add_fault(device, glossary)
            else:
                DeviceIssue.objects.recover_fault(device, fault_desc)


"""
  恒温恒湿空调
"""
class RdhAhuAirCon(BaseDevice):
    @classmethod
    def update_attres(self, device, event):
        super(RdhAhuAirCon, self).update_attres(device, event)
        data = event.get('data', {})

        if 'RDHAHUSW' in data:
            if data['RDHAHUSW'] == '0':
                device.sw_on = False
            else:
                device.sw_on = True

            device.save()
            Terminal.objects.bulk_update_by_device(device, 'sw_on', device.sw_on)

    @classmethod
    def fault(self, device, event):
        data = event.get('data', {})
        if 'RDHAHUFaultType' in data:
            faults = ['再生风机过载', '转轮电机过载', '送风风机变频故障', '除湿风机过载']
            if 1 <= int(data['RDHAHUFaultType']) <= 4:
                DeviceIssue.objects.add_fault(device, faults[data['RDHAHUFaultType'] - 1])
            else:
                for issue in faults:
                    DeviceIssue.objects.recover_fault(device, issue)

    @classmethod
    def alarm(self, device, event):
        data = event.get('data', {})
        if 'RDHAHUAlarmType' in data:
            if int(data['RDHAHUAlarmType']) == 1:
                DeviceIssue.objects.add_alarm(device, '再生风超温报警')
            elif int(data['RDHAHUAlarmType']) > 1:
                DeviceIssue.objects.recover_alarm(device, '未知报警')
            else:
                DeviceIssue.objects.recover_alarm(device, '再生风超温报警')
                DeviceIssue.objects.recover_alarm(device, '未知报警')

"""
  精密空调
"""
class PrecisionAirCon(BaseDevice):
    @classmethod
    def update_attres(self, device, event):
        super().update_attres(device, event)

        data = event.get('data', {})

        # 申菱精密空调 - RunningStatus
        # 精密空调 - UnitStatus
        status = ['UnitStatus', 'RunningStatus']
        for idf in status:
            if idf in data:
                attr = AttributePrototype.objects.get_by_idf(device, idf)
                if attr is not None and attr.options is not None:
                    if str(data[idf]).isdigit():
                        value = attr.options.split(',')[int(data[idf])]
                    else:
                        value = data[idf]

                    if value == '停止' or value == '机组关闭':
                        device.sw_on = False
                    else:
                        device.sw_on = True
                    device.save()
                    Terminal.objects.bulk_update_by_device(device, 'sw_on', device.sw_on)


    @classmethod
    def fault(self, device, event):
        fault_idfs = ['EquipmentFaultType1', 'EquipmentFaultType2', 'EquipmentFaultType3', 'WaterLeakageAlarmType', 'FLCCUFaultType',
                      'Compressor_1_FaultType', 'Compressor_2_FaultType', 'Compressor_3_FaultType']

        self.__alarm_fault(device, event, fault_idfs, 'Fault')

    @classmethod
    def alarm(self, device, event):
        alarm_idfs = ['DataComAlarmType', 'RemoteSWAlarmType', 'TempRHAlarmType', 'OverTimeAlarmType']
        self.__alarm_fault(device, event, alarm_idfs, 'Alarm')

    @classmethod
    def __alarm_fault(self, device, event, idfs, issue_type):
        data = event.get('data', {})

        for idf in idfs:
            if idf in data:
                if data[idf] != '无故障' and data[idf] != '无报警':
                    method = ''.join(['add_', str.lower(issue_type)])
                    getattr(DeviceIssue.objects, method)(device, data[idf])

                if data[idf] == '无故障' or data[idf] == '无报警':
                    issue_type_val = 10
                    if str.lower(issue_type) == 'alarm':
                        issue_type_val = 20
                    issues = DeviceIssue.objects.filter(device_id=device.id, is_solved=False, issue_type=issue_type_val)
                    attr = AttributePrototype.objects.get_by_idf(device, idf)

                    # 筛选idf对应的故障
                    if issues is not None and attr is not None:
                        for issue in issues:
                            options = attr.options.split(',')
                            if issue.display_name in options:
                                method = ''.join(['recover_', str.lower(issue_type)])
                                getattr(DeviceIssue.objects, method)(device, issue.display_name)

"""
  VRV空调网关
"""
class VrvAirCon(BaseDevice):
    @classmethod
    def update_attres(self, device, event):
        super(VrvAirCon, self).update_attres(device, event)
        data = event.get('data', {})
        sw_keys = list(filter(lambda x: x.startswith('IndoorUnit') and x.endswith('SW'), data.keys()))
        for key in sw_keys:
            idx = re.findall(r"\d+", key)[0]
            try:
                terminal = Terminal.objects.get(device=device, idx=int(idx))
                if data[key] == 0 or data[key] == '0':
                    terminal.sw_on = False
                else:
                    terminal.sw_on = True
                terminal.save()
            except Terminal.DoesNotExist:
                continue

    @classmethod
    def alarm(self, device, event):
        data = event.get('data', {})
        if 'VRVGatewayAlarm' in data:
            if data['VRVGatewayAlarm'] != 0:
                glossary = "未定义"
                if data['VRVGatewayAlarm'] == 1:
                    glossary = "通讯超时"
                if data['VRVGatewayAlarm'] == 2:
                    glossary = '通讯错误'

                DeviceIssue.objects.add_alarm(device, glossary)
            else:
                DeviceIssue.objects.recover_alarm(device, '')

"""
  DTU-VRV空调网关
"""
class DtuVrvAirCon(BaseDevice):
    @classmethod
    def update_attres(self, device, event):
        super().update_attres(device, event)
        data = event.get('data', {})
        # unit_qty = DeviceAttribute.objects.get_by_idf(device, 'IndoorUnitQty')
        unit_qty = DeviceAttribute.objects.query_object_by_idf(device, 'IndoorUnitQty')

        # 故障码处理
        if unit_qty is not None and int(unit_qty.value) > 0:
            if 'FaultCode' in data:
                for idx in range(1, unit_qty + 1):
                    fault_code = ''.join(['IndoorUnit_', str(idx), '_FaultCode'])
                    if data[fault_code] is not None and int(data[fault_code]) == 0:
                        # fault_da = DeviceAttribute.objects.get_by_idf(device, fault_code)
                        fault_da = DeviceAttribute.objects.query_object_by_idf(device, fault_code)
                        fault_da.value = '无'
                        fault_da.save()
                        DeviceAttribute.objects.save_to_redis(device, fault_da)

        # 子设备终端开关机处理
        sw_keys = list(filter(lambda x: (x.startswith('IndoorUnit') and x.endswith('SW')), data.keys()))
        for key in sw_keys:
            idx = re.findall(r"\d+", key)[0]
            try:
                terminal = Terminal.objects.get(device=device, idx=int(idx))
                sw_value = data[key]
                # 兼容开关值的各种类型，布尔值
                if isinstance(sw_value, bool):
                    terminal.sw_on = sw_value
                # 整数，浮点数，数字字符串
                elif is_number(data[key]):
                    sw = int(float(data[key]))
                    terminal.sw_on = bool(sw)
                terminal.save()
            except Terminal.DoesNotExist:
                continue

    @classmethod
    def exec_cmd(self, device, data, executor):
        pass


class FYMultiSplitAC(BaseDevice):
    """FY多联机空调"""

    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)
        data = event.get('data', {})

        # 处理空调的开关机状态
        if 'onOff' in data:
            try:
                sw = bool(float(data['onOff']))
                device.sw_on = sw
                device.save()
                Terminal.objects.bulk_update_by_device(device, 'sw_on', device.sw_on)
            except ValueError:
                pass
