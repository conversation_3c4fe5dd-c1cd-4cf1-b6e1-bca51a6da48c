import datetime
import os.path
import uuid

from django.db import models

def upload_to(instance, filename):
    now = datetime.datetime.now()
    base, extension = os.path.splitext(filename.lower())
    milliseconds = now.microsecond // 1000
    return f'assets/images/{uuid.uuid4().hex}-{now:%Y%m%d%H%M%S}{milliseconds}{extension}'

# Create your models here.
class Image(models.Model):
    # url
    image = models.ImageField(upload_to=upload_to)
    # 上传日期
    created_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'images'

    