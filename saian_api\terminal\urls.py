from django.urls import path

from saian_api.terminal.views import (TerminalStatsView,
                                      TerminalView,
                                      CategoryView,
                                      CategoryTerminalView,
                                      TaUseFor,
                                      TerminalAttributeView,
                                      RoomConditionView,
                                      NameplateView, ScanRecognition, TerminalLabelView)

terminal_list = TerminalView.as_view({
    'get': 'list',
    'post': 'create'
})

terminal_detail = TerminalView.as_view({
    'get': 'retrieve',
    'put': 'partial_update'
})

terminals_stats = TerminalStatsView.as_view({
    'get': 'list'
})

terminal_searches = TerminalView.as_view({
    'get': 'list'
})

terminal_labels = TerminalLabelView.as_view({
    'get': 'list'
})

category_list = CategoryView.as_view({
    'get': 'list',
    'post': 'create',
    'put': 'update'
})
category_detail = CategoryView.as_view({
    'get': 'retrieve',
    'delete': 'destroy',
    'put': 'partial_update'
})

category_terminal_list = CategoryTerminalView.as_view({
    'get': 'list',
    'post': 'create'
})

ta_use_for = TaUseFor.as_view({
    'put': 'update'
})

terminal_attributes = TerminalAttributeView.as_view({
    'get': 'list'
})

terminal_attribute_detail = TerminalAttributeView.as_view({
    'put': 'partial_update'
})

room_conditions = RoomConditionView.as_view({
    'get': 'list'
})

nameplates = NameplateView.as_view({
    'get': 'list',
    'post': 'create',
    'put': 'partial_update',
    'delete': 'destroy'
})

scan_recognition = ScanRecognition.as_view({
    'get': 'list'
})


urlpatterns = [
    path('saianapi/v5/terminals', terminal_list, name="terminal_list"),
    path('saianapi/v5/terminals/<int:pk>', terminal_detail, name='terminal_detail'),
    path('saianapi/v5/terminal_init', terminal_list, name="terminal_init"),
    path('saianapi/v5/terminals_stats', terminals_stats, name="terminals_stats"),
    path('saianapi/v5/terminal_searches', terminal_searches, name="terminal_searches"),
    # path('saianapi/v5/terminal_init', terminal_init, name="terminal_init"),
    path('saianapi/v5/terminal_labels', terminal_labels, name="terminal_labels"),
    path('saianapi/v5/categories', category_list, name='category_list'),
    path('saianapi/v5/categories/<int:pk>', category_detail, name="category_detail"),
    path('saianapi/v5/category_terminals', category_terminal_list, name="category_terminal_list"),
    # 根据 ap 更新 terminal_attribute 的 use_for 的值
    path('saianapi/v5/ta_use_for', ta_use_for, name="ta_use_for"),
    path('saianapi/v5/terminal_attributes', terminal_attributes, name="terminal_attributes"),
    path('saianapi/v5/terminal_attributes/<int:pk>', terminal_attribute_detail, name="terminal_attributes_detail"),
    # 客房空调管理
    path('saianapi/v5/room_conditions', room_conditions, name="room_conditions"),

    # 电子铭牌
    path('saianapi/v5/nameplates', nameplates, name='nameplates'),

    # 通用印刷体识别
    path('saianapi/v5/scan_recognition', scan_recognition, name='scan_recognition')
]
