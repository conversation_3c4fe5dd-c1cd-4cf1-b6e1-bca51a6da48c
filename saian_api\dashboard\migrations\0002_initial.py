# Generated by Django 3.2.8 on 2021-12-30 15:16

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('dashboard', '0001_initial'),
        ('project', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='projectweather',
            name='project',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='project.project'),
        ),
        migrations.AddField(
            model_name='eccriteria',
            name='project',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='project.project'),
        ),
        migrations.AddConstraint(
            model_name='eccriteria',
            constraint=models.UniqueConstraint(fields=('project_id', 'year_month', 'ec_type'), name='Unique Month'),
        ),
    ]
