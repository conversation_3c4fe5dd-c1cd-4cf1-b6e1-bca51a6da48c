import datetime
import json

from django.contrib.contenttypes.models import ContentType
from rest_framework.decorators import action
from django.core.exceptions import ObjectDoesNotExist
from rest_framework import viewsets, exceptions, status
from rest_framework.response import Response

from saian_api.devdefine.models import DeviceType
from saian_api.device.models import Device, RoomDevice
from saian_api.issue.serializers import CheckingRecordSerializer, DeviceIssueSerializer, IssueWhitelistSerializer
from saian_api.utils.intapi_auth import IntapiAuth
from saian_api.utils.legacy_auth import LegacyAuth
from saian_api.utils.sy_jsonrenderer import SyJ<PERSON><PERSON><PERSON>
from .models import DeviceIssue, IssueWhitelist, CheckingRecord
from ..terminal.models import Terminal

# Create your views here.
class DeviceIssueViewSet(viewsets.ModelViewSet):
    serializer_class = DeviceIssueSerializer
    renderer_classes = (SyJSONRender,)
    filterset_fields = ('device_id', 'issue_type', 'is_solved')

    def get_queryset(self):
        project_id = self.request.user['project_id']
        device_id = self.request.query_params.get('device_id', None)
        device_type_id = self.request.query_params.get('typeid', None)
        sub_devicetype_id = self.request.query_params.get('subtypeid', None)
        from_at = self.request.query_params.get('from', None)
        till_at = self.request.query_params.get('till', None)
        is_solved = self.request.query_params.get('is_solved', None)
        search = self.request.query_params.get('search', None)
        issue_type = self.request.query_params.get('issue_type', None)
        mac = self.request.query_params.get('mac', None)

        queryset = DeviceIssue.objects.filter(project_id=project_id, device__project_id__isnull=False).order_by('is_solved', '-created_at')

        if device_id is not None:
            queryset = queryset.filter(device_id=device_id)

        if device_type_id is not None and sub_devicetype_id is None:
            subtype_ids = DeviceType.objects.filter(parent_id=device_type_id).values_list('id', flat=True)
            if len(subtype_ids) == 0:
                queryset = queryset.filter(device_type_id=device_type_id)
            else:
                queryset = queryset.filter(device_type_id__in=subtype_ids)

        if sub_devicetype_id is not None:
            queryset = queryset.filter(device_type_id=sub_devicetype_id)

        if from_at is not None and till_at is not None:
            from_at = datetime.datetime.strptime(from_at, '%Y%m%d%H%M%S')
            till_at = datetime.datetime.strptime(till_at, '%Y%m%d%H%M%S')
            queryset = queryset.filter(created_at__range=(from_at, till_at))

        if is_solved is not None:
            queryset = queryset.filter(is_solved=is_solved)

        if search is not None:
            queryset = queryset.filter(display_name__contains=search)

        if issue_type is not None:
            queryset = queryset.filter(issue_type=issue_type)

        if mac is not None:
            queryset = queryset.filter(device__mac__icontains=mac)

        return queryset

    def get_object(self):
        try:
            issue = DeviceIssue.objects.get(
                pk=self.kwargs['pk'],
                project_id=self.request.user['project_id']
            )
        except ObjectDoesNotExist:
            raise exceptions.NotFound('Device issue not found!')

        return issue

    def list(self, request, *args, **kwargs):
        results = super().list(request, *args, **kwargs).data.get('results')
        return Response({"status": status.HTTP_200_OK, 'issues': results, 'count': self.get_queryset().count()})

    def retrieve(self, request, *args, **kwargs):
        result = super().retrieve(request, *args, **kwargs).data
        return Response({"status": status.HTTP_200_OK, 'issue': result})


class IssueWhiteListViewSet(viewsets.ModelViewSet):
    serializer_class = IssueWhitelistSerializer
    renderer_classes = (SyJSONRender,)

    def get_queryset(self):
        device_ids = Device.objects.filter(project_id=self.request.user['project_id']).values_list('id', flat=True)
        queryset = IssueWhitelist.objects.filter(device_id__in=device_ids)
        issue_name = self.request.query_params.get('issue_name', None)

        if issue_name is not None:
            queryset = queryset.filter(issue_name__contains=issue_name)

        return queryset

class CheckingRecordViewSet(viewsets.ModelViewSet):
    queryset = CheckingRecord.objects.all()
    serializer_class = CheckingRecordSerializer
    renderer_classes = (SyJSONRender,)

    def perform_create(self, serializer):
        serializer.save(user_id=self.request.user['id'])

class IssueStatViewSet(viewsets.ModelViewSet):
    @action(detail=True, methods=['get'])
    def get_authenticators(self):
        if 'intapi' in self.request.path:
            return [IntapiAuth(), ]
        return [LegacyAuth(), ]
        
    def list(self, request):
        issue_type = request.query_params.get('type', None)
        day = request.query_params.get('day', None)

        if issue_type is None or day is None:
            raise exceptions.ValidationError(detail={'detail': 'Issue type and day is required!'})

        dt_ids = Device.objects.values_list('device_type_id', flat=True).distinct()
        now = datetime.datetime.now()
        from_at = now - datetime.timedelta(days=int(day))

        issues = []
        for device_type in DeviceType.objects.filter(id__in=dt_ids):
            total = DeviceIssue.objects.filter(
                created_at__range=[from_at, now],
                device_type_id=device_type.id,
                project_id=request.user['project_id'],
                issue_type=issue_type
            ).count()

            issue = {
                'device_type': device_type.name,
                'uni_name': device_type.uni_name,
                'total': total
            }
            if issue not in issues:
                issues.append(issue)

        res_data = {
            'status': status.HTTP_200_OK,
            'data': {
                'issue_stats': issues
            }
        }

        return Response(res_data)

class WebIssueViewSet(viewsets.GenericViewSet):
    def list(self, request):
        typeids = request.query_params.get('typeids', None)
        issue_type = request.query_params.get('type', None)
        from_at = request.query_params.get('from', None)
        till_at = request.query_params.get('till', None)
        issue_status = request.query_params.get('status', None)
        search = request.query_params.get('search', None)
        content = request.query_params.get('content', None)
        nick_name = request.query_params.get('nick_name', None)
        mac = request.query_params.get('mac', None)
        device_id = request.query_params.get('device_id', None)
        room_id = request.query_params.get('room_id', None)

        queryset = DeviceIssue.objects.filter(project_id=request.user['project_id'],
                                              device__project_id__isnull=False).order_by('is_solved', '-created_at')

        if typeids is not None:
            queryset = queryset.filter(device_type_id__in=json.loads(typeids))

        if issue_type is not None:
            queryset = queryset.filter(issue_type=issue_type)

        if from_at is not None and till_at is not None:
            from_at = datetime.datetime.strptime(from_at, '%Y%m%d%H%M%S')
            till_at = datetime.datetime.strptime(till_at, '%Y%m%d%H%M%S')

            queryset = queryset.filter(created_at__range=[from_at, till_at])

        if issue_status is not None:
            queryset = queryset.filter(is_solved=int(issue_status))

        if search is not None:
            queryset = queryset.filter(display_name__contains=search)

        if content is not None:
            queryset = queryset.filter(display_name__contains=content)

        if nick_name is not None:
            queryset = queryset.filter(device__nick_name__contains=nick_name)

        if mac is not None:
            queryset = queryset.filter(device__mac__icontains=mac)

        if device_id is not None:
            queryset = queryset.filter(device_id=device_id)

        if room_id is not None:
            terminal_type = ContentType.objects.get_for_model(Terminal)
            terminal_ids = list(RoomDevice.objects.filter(active_room_id=room_id, content_type=terminal_type).values_list('object_id', flat=True))
            t_device_ids = list(Terminal.objects.filter(id__in=terminal_ids).values_list('device_id', flat=True))

            device_type = ContentType.objects.get_for_model(Device)
            device_ids = list(RoomDevice.objects.filter(active_room_id=room_id, content_type=device_type).values_list('object_id', flat=True))

            all_devices = t_device_ids + device_ids
            queryset = queryset.filter(device_id__in=all_devices)

        total = queryset.count()

        queryset = self.paginate_queryset(queryset)

        issues_fileds = []

        device_cache = {}

        for issue in queryset:
            device = device_cache.get(issue.device_id, None)
            if device is None:
                device = Device.objects.get(pk=issue.device_id)
                device_cache[device.id] = device
            device_type = device.device_type
            white_list = IssueWhitelist.objects.filter(device_id=device.id, issue_name=issue.display_name).last()
            white_list_fields = None
            if white_list is not None:
                white_list_fields = {
                    'id': white_list.id,
                    'days': white_list.days,
                    'left_days': white_list.left_days
                }

            issue_fields = {
                'id': issue.id,
                'name': issue.name,
                'device_name': device.nick_name,
                'display_name': issue.name,
                'is_solved': issue.is_solved,
                'issue_type': issue.issue_type,
                'created_at': issue.created_at,
                'updated_at': issue.updated_at,
                'solved_at': issue.updated_at if issue.is_solved else '',
                'mac': device.mac,
                'device_type_name': device_type.name,
                'device': {
                    'id': device.id,
                    'nick_name': device.nick_name,
                    'type_name': device_type.name,
                    'address': device.address
                },
                'white_list': white_list_fields
            }

            issues_fileds.append(issue_fields)

        res_data = {
            'status': status.HTTP_200_OK,
            'total': total,
            'data': {
                'file_name': 'filename',
                'issues': issues_fileds
            }
        }

        return Response(res_data)
