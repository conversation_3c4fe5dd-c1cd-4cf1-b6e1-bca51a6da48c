"""
    冷源能效日统计
"""
import datetime
import traceback

from django.core.management import CommandError, BaseCommand
from django.db.models import Count

from saian_api.coldsource.models import EcSource, ColdSource, CsEerAnalyse
from saian_api.project.models import Project
from saian_api.report.models import DeviceDailyStat
from saian_api.scheduledtask.utils import get_projects, set_global_db

class Command(BaseCommand):
    help = "冷源能效日统计"

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目ID列表
        projects = get_projects(self, options)
        now = datetime.datetime.now()

        for project_id in projects:
            try:
                # 设置全局数据库
                set_global_db(project_id)
                project = Project.objects.get(pk=project_id)

                self.stdout.write(f"冷源能效日统计开始: {project.name}", ending='\n')

                ec_sources = EcSource.objects.all()
                cold_sources = ColdSource.objects.all()

                # 判断项目是否有配置冷源能耗统计
                if not ec_sources.exists():
                    self.stdout.write(f"项目: {project.name} 未配置，跳过统计", ending='\n')
                    continue
                else:
                    # 取出已配置的冷源id
                    annotates = ec_sources.values('cold_source_id').annotate(count=Count('id'))
                    for anno in annotates:
                        csid = anno['cold_source_id']
                        mac = cold_sources.get(pk=csid).mac
                        # 计算公式字符串
                        power_eval_str = ''
                        cold_eval_str = ''
                        # 公式涉及的设备ID
                        device_annotates = ec_sources.filter(cold_source_id=csid).values('device_id').annotate(count=Count(id))
                        device_ids = [device['device_id'] for device in list(filter(lambda x: x['device_id'], device_annotates))]

                        one_day_ago = now - datetime.timedelta(days=1)
                        last_day_begin = one_day_ago.strftime('%Y-%m-%d 00:00:00')
                        last_day_end = one_day_ago.strftime('%Y-%m-%d 23:59:59')

                        last_day_stats = DeviceDailyStat.objects.filter(device_id__in=device_ids,
                                                                        created_at__range=[last_day_begin, last_day_end])

                        # csid 下的冷量公式
                        cold_ec_sources_options = ec_sources.filter(cold_source_id=csid, ec_type=30).order_by('seq')
                        # csid 下的电量公式
                        power_ec_sources_options = ec_sources.filter(cold_source_id=csid, ec_type=10).order_by('seq')

                        if last_day_stats.count() > 0:
                            if power_ec_sources_options.count() > 0:
                                for option in power_ec_sources_options:
                                    if option.value_type == 10:
                                        # 操作值
                                        stat = last_day_stats.filter(identifier=option.value, device_id=option.device_id)
                                        power = stat.last().avg if stat.exists() else 0
                                        power_eval_str = power_eval_str + str(power)
                                    else:
                                        # 操作符 以及 倍数
                                        power_eval_str = power_eval_str + option.value

                            if cold_ec_sources_options.count() > 0:
                                for option in cold_ec_sources_options:
                                    if option.value_type == 10:
                                        # 操作值
                                        stat = last_day_stats.filter(identifier=option.value, device_id=option.device_id)
                                        cold = stat.last().avg if stat.exists() else 0
                                        cold_eval_str = cold_eval_str + str(cold)
                                    else:
                                        # 操作符 以及 倍数
                                        cold_eval_str = cold_eval_str + option.value
                            else:
                                # 如果没有冷量公式，则使用冷源的累计冷量（CumulatedColdConsum）作为冷量值
                                stat = DeviceDailyStat.objects.filter(mac=mac, identifier='CumulatedColdConsum',
                                                                      created_at__range=[last_day_begin, last_day_end])
                                cold = stat.order_by('created_at').last().avg if stat.exists() else 0
                                cold_eval_str = str(cold)

                            # 根据计算公式得出总量
                            power = eval(power_eval_str) if power_eval_str != '' else 0
                            cold = eval(cold_eval_str) if cold_eval_str != '' else 0
                            eer = round(cold / power, 2) if power != 0 else 0

                            CsEerAnalyse.objects.create(
                                cold_source_id=csid,
                                power_cons=power,
                                cold_cons=cold,
                                eer=eer,
                                created_at=one_day_ago.strftime('%Y-%m-%d 23:59:59')
                            )
                            self.stdout.write(f"冷源能效日统计任务完成: {project.name}, power: {power}, cold: {cold}, eer: {eer}", ending='\n')

                        else:
                            self.stdout.write(f"冷源能耗统计：时间段 {last_day_begin}-{last_day_end} 内，DeviceDailyStat 无数据！", ending='\n')

                self.stdout.write(f"冷源能效日统计任务完成: {project.name}", ending='\n')

            except CommandError:
                self.stderr.write(f"运行'冷源能效日统计'任务失败，项目ID：{project_id}，命令参数不合法！", ending='\n')
                continue
            except Exception as e:
                self.stderr.write(f"运行'冷源能效日统计'任务失败，项目ID：{project_id}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
                continue
