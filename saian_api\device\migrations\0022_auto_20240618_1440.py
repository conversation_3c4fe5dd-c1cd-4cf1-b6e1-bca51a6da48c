# Generated by Django 3.2.19 on 2024-06-18 14:40

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('device', '0021_auto_20240617_0953'),
    ]

    operations = [
        migrations.CreateModel(
            name='DaSnapshot',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('attribute_prototype_id', models.BigIntegerField()),
                ('value', models.Char<PERSON>ield(max_length=255, null=True)),
                ('snapped_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'da_snapshots',
                'ordering': ['-snapped_at'],
            },
        ),
        migrations.AddIndex(
            model_name='deviceattribute',
            index=models.Index(fields=['device_id'], name='device_attr_device__f53572_idx'),
        ),
        migrations.RemoveIndex(
            model_name='deviceattribute',
            name='device_attr_device__8cfd69_idx',
        ),
        migrations.AddIndex(
            model_name='deviceattribute',
            index=models.Index(fields=['device_id', 'attribute_prototype_id'], name='device_attr_device__f53571_idx'),
        ),
        migrations.RemoveIndex(
            model_name='deviceattribute',
            name='device_attr_device__f53572_idx',
        ),
        migrations.AddField(
            model_name='dasnapshot',
            name='device',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='device.device'),
        ),
        migrations.AddIndex(
            model_name='dasnapshot',
            index=models.Index(fields=['device_id', 'attribute_prototype_id', 'snapped_at'], name='da_snapshot_device__a1182d_idx'),
        ),
    ]
