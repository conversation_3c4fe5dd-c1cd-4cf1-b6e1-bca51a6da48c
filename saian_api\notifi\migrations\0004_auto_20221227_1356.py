# Generated by Django 3.2.8 on 2022-12-27 13:56

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('user', '0007_userlog'),
        ('notifi', '0003_verifiedcode_used'),
    ]

    operations = [
        migrations.AddField(
            model_name='notification',
            name='concern_id',
            field=models.PositiveIntegerField(null=True),
        ),
        migrations.AddField(
            model_name='notification',
            name='concern_type',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype'),
        ),
        migrations.AddField(
            model_name='notification',
            name='content',
            field=models.CharField(max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='notification',
            name='msg_type',
            field=models.IntegerField(default=8),
        ),
        migrations.AddField(
            model_name='notification',
            name='noti_way',
            field=models.IntegerField(default=10),
        ),
        migrations.CreateModel(
            name='NotifyLimit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('concern_id', models.PositiveIntegerField(null=True)),
                ('identifier', models.CharField(max_length=255, null=True)),
                ('condition', models.TextField()),
                ('concern_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
            ],
            options={
                'db_table': 'notify_limits',
            },
        ),
        migrations.CreateModel(
            name='NotifyConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('concern_id', models.PositiveIntegerField(null=True)),
                ('msg_type', models.IntegerField()),
                ('issue_name', models.CharField(max_length=255, null=True)),
                ('noti_way', models.IntegerField()),
                ('concern_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('web_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='user.webuser')),
            ],
            options={
                'db_table': 'notify_configs',
            },
        ),
    ]
