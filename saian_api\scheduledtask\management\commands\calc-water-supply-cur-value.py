import datetime
import json
import traceback

from django.core.management import BaseCommand, CommandError
from django.db.models import Q, Sum

from saian_api.devdefine.models import AttributePrototype
from saian_api.device.models import Device, ParamRecordHistory, ParamRecord, DeviceAttribute, DeviceEvent
from saian_api.dimension.models import DimensionAttribute, DimensionHourlyStat, DimensionDailyStat, DimensionMonthlyStat, Dimension
from saian_api.scheduledtask.utils import get_projects, set_global_db
from saian_api.utils.db.Redis import RedisHelper
from saian_api.utils.tools import get_idx

class Command(BaseCommand):
    help = "人医生活供水 Cur 类参数计算"

    def handle(self, *args, **options):
        project_id = 102
        root_dimension_id = 7
        dp_id = 587
        now = datetime.datetime.now()
        calc_threshold = now - datetime.timedelta(minutes=1)
        self.stdout.write(f'[{now}] calc_threshold: {calc_threshold}')
        # 当年开始时间
        year_begin = datetime.datetime(now.year, 1, 1, 0, 0)
        # 当月开始时间
        month_begin = datetime.datetime(now.year, now.month, 1, 0, 0)
        # 当日开始时间
        day_begin = datetime.datetime(now.year, now.month, now.day, 0, 0)

        try:
            set_global_db(project_id)
            device = Device.objects.get(pk=25070)

            cac_das = DimensionAttribute.objects.filter(dimension__parent_id=root_dimension_id, updated_at__gte=calc_threshold).order_by('id')
            cur_aps = AttributePrototype.objects.filter(device_prototype_id=dp_id).filter(
                Q(identifier__contains='Cur') | Q(identifier__contains='Today')
            )
            cur_event_data = {}

            for da in cac_das:
                prefix = 'Power' if da.ec_type == 10 else 'Water'
                idx = get_idx(da.identifier)
                # 当前分钟
                cur_min_ap = next((ap for ap in cur_aps if
                                   prefix in ap.identifier and
                                   'CurMin' in ap.identifier and
                                   f'_{idx}' in ap.identifier), None)
                cur_min_da = DeviceAttribute.objects.query_object_by_ap(device, cur_min_ap)

                tail_index = 2
                tail_value = None
                tail_dt = None
                while True:
                    last_da_value = RedisHelper.get_list_tail_item(project_id, f'dimension_attribute:{da.id}', tail_index, True)
                    if last_da_value:
                        prev_da_value = json.loads(last_da_value)
                        prev_dt = datetime.datetime.fromisoformat(prev_da_value['dt'])
                        prev_value = prev_da_value['value']
                        if (now - prev_dt).total_seconds() >= 60:
                            tail_value = prev_value
                            tail_dt = prev_dt
                            break
                        else:
                            tail_index += 1
                    else:
                        break

                if tail_dt is not None and tail_value is not None:
                    prev_dt = tail_dt
                    prev_value = tail_value

                    diff_value = float(da.value) - float(prev_value)
                    diff_minute = (now - prev_dt).seconds / 60

                    cur_min_cons = round(diff_value / diff_minute, 4)
                    # print(f'{da.name}, {cur_min_ap.name}, diff_value: {diff_value}, diff_minute: {diff_minute},'
                    #       f' avg: {diff_value / diff_minute}, round value: {cur_min_cons}')
                    # if str(cur_min_cons) != cur_min_da.value:
                    cur_min_da.value = cur_min_cons
                    cur_min_da.updated_at = now
                    print(f'更新{da.name}-{cur_min_ap.name} value 为: {cur_min_cons}, '
                          f'redis key: recently_updated_{now.minute:02}:{device.device_prototype_id}')

                    cur_event_data[cur_min_ap.identifier] = cur_min_da.value

                    DeviceAttribute.objects.save_to_redis(device, cur_min_da)
                    # 保存设备参的属性类型id到缓存，快照任务需要使用
                    RedisHelper.add_set(device.project_id, f'recently_updated_{now.minute:02}:{device.device_prototype_id}',
                                        cur_min_ap.id, True, 120)
                    RedisHelper.push_list(device.project_id,
                                          f'device_event_{now.strftime("%Y%m%d%H")}:{device.id}_{cur_min_ap.identifier}',
                                          cur_min_cons, True, 7200)

                # 当前小时
                cur_hour_ap = next((ap for ap in cur_aps if
                                    prefix in ap.identifier and
                                    'CurHour' in ap.identifier and
                                    f'_{idx}' in ap.identifier), None)
                cur_hour_da = DeviceAttribute.objects.query_object_by_ap(device, cur_hour_ap)
                cur_hour_cons = da.calc_current_hour_cons()
                cur_hour_da.value = cur_hour_cons
                cur_hour_da.updated_at = now
                print(f'更新{da.name}-{cur_hour_ap.name} value 为: {cur_hour_cons}, '
                      f'redis key: recently_updated_{now.minute:02}:{device.device_prototype_id}')

                cur_event_data[cur_hour_ap.identifier] = cur_hour_da.value

                DeviceAttribute.objects.save_to_redis(device, cur_hour_da)
                RedisHelper.add_set(device.project_id, f'recently_updated_{now.minute:02}:{device.device_prototype_id}',
                                    cur_hour_ap.id, True, 120)
                RedisHelper.push_list(device.project_id,
                                      f'device_event_{now.strftime("%Y%m%d%H")}:{device.id}_{cur_hour_ap.identifier}',
                                      cur_hour_cons, True, 7200)

                # 今日
                today_ap = next((ap for ap in cur_aps if
                                 prefix in ap.identifier and
                                 'Today' in ap.identifier and
                                 f'_{idx}' in ap.identifier), None)
                today_da = DeviceAttribute.objects.query_object_by_ap(device, today_ap)
                today_cons = cur_hour_cons
                hourly_value = DimensionHourlyStat.objects.filter(dimension_id=da.dimension_id, dimension_attribute_id=da.id,
                                                                  created_at__gte=day_begin).aggregate(sum=Sum('avg'))['sum']
                if hourly_value:
                    today_cons = cur_hour_cons + hourly_value
                # if str(today_cons) != today_da.value and now.minute > 5:
                if now.minute > 5:
                    today_da.value = round(today_cons, 2)
                    today_da.updated_at = now
                    print(f'更新{da.name}-{today_ap.name} value 为: {today_cons}, '
                          f'redis key: recently_updated_{now.minute:02}:{device.device_prototype_id}')

                    cur_event_data[today_ap.identifier] = today_da.value

                    DeviceAttribute.objects.save_to_redis(device, today_da)
                    RedisHelper.add_set(device.project_id, f'recently_updated_{now.minute:02}:{device.device_prototype_id}',
                                        today_ap.id, True, 120)
                    RedisHelper.push_list(device.project_id,
                                          f'device_event_{now.strftime("%Y%m%d%H")}:{device.id}_{today_ap.identifier}',
                                          today_cons, True, 7200)

                # 当月
                cur_month_ap = next((ap for ap in cur_aps if
                                     prefix in ap.identifier and
                                     'CurMonth' in ap.identifier and
                                     f'_{idx}' in ap.identifier), None)
                cur_month_da = DeviceAttribute.objects.query_object_by_ap(device, cur_month_ap)
                cur_month_cons = today_cons
                daily_value = DimensionDailyStat.objects.filter(dimension_id=da.dimension_id, dimension_attribute_id=da.id,
                                                                created_at__gte=month_begin).aggregate(sum=Sum('avg'))['sum']
                if daily_value:
                    cur_month_cons = today_cons + daily_value
                # if str(cur_month_cons) != cur_month_da.value and now.minute > 5:
                if now.minute > 5:
                    cur_month_da.value = round(cur_month_cons, 2)
                    cur_month_da.updated_at = now

                    cur_event_data[cur_month_ap.identifier] = cur_month_da.value

                    DeviceAttribute.objects.save_to_redis(device, cur_month_da)
                    RedisHelper.add_set(device.project_id, f'recently_updated_{now.minute:02}:{device.device_prototype_id}',
                                        cur_month_ap.id, True, 120)
                    RedisHelper.push_list(device.project_id,
                                          f'device_event_{now.strftime("%Y%m%d%H")}:{device.id}_{cur_month_ap.identifier}',
                                          cur_month_cons, True, 7200)

                # 当年
                cur_year_ap = next((ap for ap in cur_aps if
                                    prefix in ap.identifier and
                                    'CurYear' in ap.identifier and
                                    f'_{idx}' in ap.identifier), None)
                cur_year_da = DeviceAttribute.objects.query_object_by_ap(device, cur_year_ap)
                cur_year_cons = cur_month_cons
                monthly_value = DimensionMonthlyStat.objects.filter(dimension_id=da.dimension_id, dimension_attribute_id=da.id,
                                                                    created_at__gte=year_begin).aggregate(sum=Sum('avg'))['sum']
                if monthly_value:
                    cur_year_cons = cur_month_cons + monthly_value
                # if str(cur_year_cons) != cur_year_da.value and now.minute > 5:
                if now.minute > 5:
                    cur_year_da.value = round(cur_year_cons, 2)
                    cur_year_da.updated_at = now

                    cur_event_data[cur_year_ap.identifier] = cur_year_da.value

                    DeviceAttribute.objects.save_to_redis(device, cur_year_da)
                    RedisHelper.add_set(device.project_id, f'recently_updated_{now.minute:02}:{device.device_prototype_id}',
                                        cur_year_ap.id, True, 120)
                    RedisHelper.push_list(device.project_id,
                                          f'device_event_{now.strftime("%Y%m%d%H")}:{device.id}_{cur_year_ap.identifier}',
                                          cur_year_cons, True, 7200)
            if cur_event_data:
                DeviceEvent.objects.create(
                    device_prototype_id=device.device_prototype_id,
                    cmd=None,
                    event_type='cur_data_calc',
                    event_id=None,
                    delivery_id=None,
                    product_key=None,
                    timestamp=None,
                    ip=None,
                    latitude=None,
                    longitude=None,
                    country=None,
                    region=None,
                    data=json.dumps(cur_event_data, ensure_ascii=False),
                    did=None,
                    mac=device.mac,
                    nick_name=device.nick_name,
                    device_id=device.id,
                    project_id=device.project_id
                )
            device.save()

            self.stdout.write(self.style.SUCCESS(f"[{now}] '人医生活供水 Cur 类参数计算' 任务完成."))
        except CommandError:
            self.stderr.write(f"运行'人医生活供水 Cur 类参数计算'任务失败, 命令参数不合法！", ending='\n')
        except Exception as e:
            self.stderr.write(f"运行'人医生活供水 Cur 类参数计算'任务失败, error: {e.__str__()}", ending='\n')
            self.stderr.write(traceback.format_exc(), ending='')
