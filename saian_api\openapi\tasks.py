import json
import logging

import requests

from saian_api.celery import celery_app
from saian_api.utils.inthttpapi import DOMAIN
from saian_api.utils.inthttpapi.base import BaseAPI


@celery_app.task(name='saian_api.openapi.tasks.tp_data_push', max_retries=3)
def tp_data_push(data):
    res = f'entapi/v1/push_data'
    headers = BaseAPI.open_entapi_header(res)
    url = f'{DOMAIN}/{res}'
    r = requests.post(url, headers=headers, json=data)
    return r
