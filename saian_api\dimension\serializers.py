import logging

from rest_framework import serializers, exceptions

from saian_api.devdefine.models import AttributePrototype
from saian_api.devdefine.serializers import SimpleAttributePrototypeSerializer
from saian_api.dimension.models import Dimension, DimensionTerminal, DimensionAttribute, PpvConfig, DimensionUser
from saian_api.terminal.models import Terminal, TerminalAttribute
from saian_api.utils.httpapi.image import ImageAPI
from saian_api.utils.tools import fill_element

class SimpleDimensionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Dimension
        fields = ['id', 'name', 'type_name', 'thumbs', 'images', 'unit_area', 'created_at', 'updated_at']

    def to_representation(self, instance):
        ret = super().to_representation(instance)

        if ret['images'] is not None:
            images = ImageAPI.get_urls(request=self.context['request'], image_id=ret['images'], size='originals')
            if images is not None and len(images) > 0:
                ret['images'] = [image['image'] for image in images]

        if ret['thumbs'] is not None:
            thumbs = ImageAPI.get_urls(request=self.context['request'], image_id=ret['thumbs'], size='originals')
            if thumbs is not None and len(thumbs) > 0:
                ret['thumbs'] = [thumb['image'] for thumb in thumbs]

        # 单位维度用户
        ret['user_count'] = DimensionUser.objects.filter(dimension_id=ret['id']).count()
        # 维度绑定的终端
        ret['terminal_count'] = DimensionTerminal.objects.filter(dimension_id=ret['id']).count()

        return ret

class DimensionSerializer(serializers.ModelSerializer):

    class Meta:
        model = Dimension
        fields = ['id', 'name', 'type_name', 'parent', 'thumbs', 'images', 'coords', 'unit_area', 'ec_persons', 'project', 'created_at', 'updated_at']

    def to_representation(self, instance):
        ret = super().to_representation(instance)

        if ret['images'] is not None:
            images = ImageAPI.get_urls(request=self.context['request'], image_id=ret['images'], size='originals')
            if images is not None and len(images) > 0:
                ret['images'] = [image['image'] for image in images]

        if ret['thumbs'] is not None:
            thumbs = ImageAPI.get_urls(request=self.context['request'], image_id=ret['thumbs'], size='originals')
            if thumbs is not None and len(thumbs) > 0:
                ret['thumbs'] = [thumb['image'] for thumb in thumbs]

        if self.context.get('request').query_params.get('no_coords', None) is not None:
            ret['coords'] = None
        else:
            ret['coords'] = fill_element(ret['coords']) if ret['coords'] else None

        ret.pop('project')

        # 单位维度用户
        ret['user_count'] = DimensionUser.objects.filter(dimension_id=ret['id']).count()
        # 维度绑定的终端
        ret['terminal_count'] = DimensionTerminal.objects.filter(dimension_id=ret['id']).count()

        return ret


class DimensionTerminalSerializer(serializers.ModelSerializer):
    class Meta:
        model = DimensionTerminal
        fields = ['id', 'dimension', 'terminal']

    def to_representation(self, instance):
        ret = super().to_representation(instance)

        dimension = Dimension.objects.get(pk=ret['dimension'])
        terminal = Terminal.objects.get(pk=ret['terminal'])

        ret['dimension'] = {
            'id': dimension.id,
            'name': dimension.name
        }

        ret['terminal'] = {
            'id': terminal.id,
            'nick_name': terminal.nick_name,
        }

        return ret


class SimpleDimensionAttributeSerializer(serializers.ModelSerializer):
    class Meta:
        model = DimensionAttribute
        fields = ['id', 'name', 'identifier', 'dimension', 'ec_type']

class DimensionAttributeSerializer(serializers.ModelSerializer):
    class Meta:
        model = DimensionAttribute
        fields = ['id', 'ta_ids', 'dimension', 'name', 'identifier', 'is_cum', 'read_only', 'value', 'ec_type', 'formula']

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        if ret['ta_ids']:
            terminal_attributes = TerminalAttribute.objects.filter(id__in=ret['ta_ids'].split(','))
            tas = TerminalAttributeSerializer(terminal_attributes, many=True).data
            ret['terminal_attributes'] = tas
        else:
            ret['terminal_attributes'] = None
        ret.pop('ta_ids')

        return ret


class TerminalAttributeSerializer(serializers.ModelSerializer):
    class Meta:
        model = TerminalAttribute
        fields = ['id', 'attribute_prototype_id', 'terminal_id', 'value']

    def to_representation(self, instance):
        ret = super().to_representation(instance)

        try:
            ap = AttributePrototype.objects.get(pk=ret['attribute_prototype_id'])
            ret['attribute_prototype'] = SimpleAttributePrototypeSerializer(ap).data
        except AttributePrototype.DoesNotExist:
            logging.error(f"终端属性对应的参数点不存在: attribute_prototype_id-{ret['attribute_prototype_id']}")
            ret['attribute_prototype'] = None

        terminal = Terminal.objects.get(pk=ret['terminal_id'])
        ret['terminal'] = {
            'id': terminal.id,
            'nick_name': terminal.nick_name
        }

        ret.pop('attribute_prototype_id')
        ret.pop('terminal_id')

        return ret

class PpvConfigSerializer(serializers.ModelSerializer):
    class Meta:
        model = PpvConfig
        fields = ['id', 'project', 'begin_at', 'price', 'unit', 'ec_category', 'ec_type', 'ppv_type']
