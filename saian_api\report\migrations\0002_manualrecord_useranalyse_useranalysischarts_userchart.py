# Generated by Django 3.2.19 on 2023-08-09 13:54

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0007_userlog'),
        ('contenttypes', '0002_remove_content_type_name'),
        ('report', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserAnalyse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('thumb', models.CharField(max_length=255)),
                ('web_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='user.webuser')),
            ],
            options={
                'db_table': 'user_analyses',
            },
        ),
        migrations.CreateModel(
            name='UserChart',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('uni_name', models.CharField(max_length=255)),
                ('chart_type', models.IntegerField()),
                ('data_src', models.IntegerField()),
                ('gran', models.IntegerField(null=True)),
                ('query_time', models.CharField(default='近24小时', max_length=255)),
                ('data_params', models.TextField(null=True)),
                ('style_params', models.TextField(null=True)),
                ('thumb', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(auto_now=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('web_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='user.webuser')),
            ],
            options={
                'db_table': 'user_charts',
            },
        ),
        migrations.CreateModel(
            name='UserAnalysisCharts',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content_id', models.PositiveBigIntegerField()),
                ('content_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('user_analyse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='report.useranalyse')),
            ],
            options={
                'db_table': 'user_analysis_charts',
            },
        ),
        migrations.CreateModel(
            name='ManualRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content_id', models.PositiveBigIntegerField()),
                ('name', models.CharField(max_length=255)),
                ('data_time', models.DateTimeField()),
                ('data', models.TextField()),
                ('unit', models.CharField(max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('content_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
            ],
            options={
                'db_table': 'manual_records',
            },
        ),
    ]
