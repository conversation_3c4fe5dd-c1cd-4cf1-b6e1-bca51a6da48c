from ...terminal.models import Terminal
from .base import BaseDevice
from saian_api.issue.models import DeviceIssue
from saian_api.devdefine.models import AttributePrototype
import json
import logging
import re

from saian_api.devdefine.models import DeviceProtocol
from saian_api.device.models import DeviceAttribute
from saian_api.utils import tools

logger = logging.getLogger('django')

"""
  水阀控制器
"""


class ValveCtrl(BaseDevice):
    @classmethod
    def alarm(cls, device, event):
        data = event.get('data', {})
        if 'VCAlarm' in data:
            if data['VCAlarm'] == 1 and data['VCAlarmType'] != 0:
                glossary = "未定义"
                if int(data['VCAlarmType']) == 1:
                    glossary = "阀门开到位异常"
                if int(data['VCAlarmType']) == 2:
                    glossary = '阀门关到位异常'

                DeviceIssue.objects.add_alarm(device, glossary)
            else:
                DeviceIssue.objects.recover_alarm(device, '')


class ValveCtrlDTS(BaseDevice):
    """
    阀门控制器DTS
    """
    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)
        data = event.get('data', {})
        status_idf = 'KnobStatus'
        if status_idf in data:
            attr = AttributePrototype.objects.query_by_idf(device, status_idf)
            dts_status = data[status_idf]
            if isinstance(dts_status, int) or dts_status.isdigit():
                value = attr.options.split(',')[int(dts_status)]
            else:
                value = dts_status

            device.sw_on = value != '停止'
            device.save()
            Terminal.objects.bulk_update_by_device(device, 'sw_on', device.sw_on)

    @classmethod
    def alarm(cls, device, event):
        super().alarm(device, event)

    @classmethod
    def fault(cls, device, event):
        super().fault(device, event)
            

class MultiLoop(BaseDevice):
    """
      多回路控制器
    """
    @classmethod
    def alarm(cls, device, event):
        data = event.get('data', {})
        alarms = [
            '回路1开启失败', '回路1关闭失败',
            '回路2开启失败', '回路2关闭失败',
            '回路3开启失败', '回路3关闭失败',
            '回路4开启失败', '回路4关闭失败',
            '回路5开启失败', '回路5关闭失败',
            '回路6开启失败', '回路6关闭失败',
            '回路7开启失败', '回路7关闭失败',
            '回路8开启失败', '回路8关闭失败',
        ]

        if 'MCCAlarm' in data:
            if data['MCCAlarm'] == 1 and data['MCCAlarmType'] != 0:
                idx = data['MCCAlarmType'] - 1
                try:
                    DeviceIssue.objects.add_alarm(device, alarms[idx])
                except IndexError:
                    # logger.error(f"多回路控制器报警越界错误: alarms length: {len(alarms)}, idx: {idx}")
                    pass
            else:
                for issue in alarms:
                    DeviceIssue.objects.recover_alarm(device, issue)


class ValveCtrl4g(BaseDevice):
    """
      水阀控制器4g
    """
    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)

        data = event.get('data', {})

        if 'VCStatus' in data:
            attr = AttributePrototype.objects.get(
                device_prototype_id=device.device_prototype_id, identifier='VCStatus')
            value = attr.options.split(',')[int(data['VCStatus'])]
            if value == '旋钮停止':
                device.sw_on = False
            else:
                device.sw_on = True
            device.save()
            Terminal.objects.bulk_update_by_device(device, 'sw_on', device.sw_on)

    @classmethod
    def alarm(cls, device, event):
        data = event.get('data', {})
        if 'VCAlarm' in data:
            if data['VCAlarm'] == 1 and data['VCAlarmType'] != 0:
                glossary = "未定义"
                if int(data['VCAlarmType']) == 1:
                    glossary = "阀门开到位异常"
                if int(data['VCAlarmType']) == 2:
                    glossary = '阀门关到位异常'

                DeviceIssue.objects.add_alarm(device, glossary)
            else:
                DeviceIssue.objects.recover_alarm(device, '')


class CSP2000GW(BaseDevice):
    """
      CSP2000网关
    """
    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)

        data = event.get('data', {})

        for key, value in data.items():
            try:
                if 'CSP_LA_' in key or 'CSP_LC_' in key:
                    da = DeviceAttribute.objects.get_by_idf(device, key)

                    if da is not None:
                        search_obj = re.search('CSP_L(A|C)_(\d+)', key)
                        if search_obj is not None:
                            idf = ''.join(['CSP_CT_', search_obj.group(2)])
                            idf_value = device.get_value_by_idf(idf)
                            if idf_value is not None:
                                csp_ct = float(idf_value)
                                da.value = float(value) * csp_ct
                                da.save()
                                DeviceAttribute.objects.save_to_redis(device, da)

                if 'CSP_Uab_' in key or 'CSP_Uac_' in key or 'CSP_Ubc_' in key:
                    da = DeviceAttribute.objects.get_by_idf(device, key)

                    if da is not None:
                        search_obj = re.search('CSP_(Uab|Uac|Ubc)_(\d+)', key)
                        if search_obj is not None:
                            idf = ''.join(['CSP_PT_', search_obj.group(2)])
                            idf_value = device.get_value_by_idf(idf)
                            if idf_value is not None:
                                csp_ct = float(idf_value)
                                da.value = float(value) * csp_ct / 10
                                da.save()
                                DeviceAttribute.objects.save_to_redis(device, da)

                if 'CSP_Datapoints_' in key:
                    search_obj = re.search('CSP_Datapoints_(\d+)', key)
                    if search_obj is not None:
                        idx = search_obj.group(1)
                        dpc = DeviceProtocol.objects.get(identifier='CSP_Datapoints', device_prototype_id=device.device_prototype_id)
                        if dpc is not None:
                            if dpc.sp_defs is not None:
                                sp_defs = json.loads(dpc.sp_defs)
                                new_values = tools.int_to_16bins(value)
                                for sp in sp_defs:
                                    new_value = new_values[int(sp['idx'])]
                                    new_key = str(sp['idf']) + '_' + str(idx)
                                    da = DeviceAttribute.objects.get_by_idf(device, new_key)
                                    if da is not None:
                                        attr = AttributePrototype.objects.filter(device_prototype_id=device.device_prototype_id, identifier=new_key)
                                        if attr.exists():
                                            attr = attr.last()
                                            if attr.data_type == 30 and str(new_value).isdigit():
                                                da.value = attr.options.split(',')[int(new_value)]
                                                da.save()
                                                DeviceAttribute.objects.save_to_redis(device, da)

            # 避免影响其他参数处理
            except ValueError as e:
                logger.error(f'CSP2000GW.update_attres处理异常，key={key}, value={value}, err: {e.__str__()}')
                pass
            except Exception as e:
                logging.error(f'CSP2000GW.update_attres处理异常，key={key}, value={value}, err: {e.__str__()}')
                logging.error(e.with_traceback)
                pass

    @classmethod
    def alarm(cls, device, event):
        pass


class ILValve(BaseDevice):
    """
      层间阀
    """
    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)

        data = event.get('data', {})

        if 'ILValve_SW' in data:
            attr = AttributePrototype.objects.get(device_prototype_id=device.device_prototype_id, identifier='ILValve_SW')
            value = attr.options.split(',')[int(data['ILValve_SW'])]
            if value == '关闭':
                device.sw_on = False
            else:
                device.sw_on = True
            device.save()
            Terminal.objects.bulk_update_by_device(device, 'sw_on', device.sw_on)

    @classmethod
    def fault(cls, device, event):
        data = event.get('data', {})
        if 'ILValve_FaultType' in data:
            attr = AttributePrototype.objects.get(device_prototype_id=device.device_prototype_id, identifier='ILValve_FaultType')
            options = attr.options.split(",")
            faultType = ''
            for i, v in enumerate(data['ILValve_FaultType']):
                try:
                    if int(v) == 1:
                        faultType = options[i] if faultType == '' else faultType + ',' + options[i]
                except IndexError:
                    pass
            if faultType != '':
                DeviceIssue.objects.add_fault(device, faultType)
            else:
                DeviceIssue.objects.recover_fault(device, '')
