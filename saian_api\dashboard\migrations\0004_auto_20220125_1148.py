# Generated by Django 3.2.8 on 2022-01-25 11:48

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('dashboard', '0003_alter_projectweather_created_at'),
    ]

    operations = [
        migrations.CreateModel(
            name='EcTranscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ec_type', models.IntegerField()),
                ('year_month', models.CharField(max_length=6)),
                ('value', models.CharField(max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'ec_transcription',
                'ordering': ['-year_month'],
            },
        ),
        migrations.AddConstraint(
            model_name='ectranscription',
            constraint=models.UniqueConstraint(fields=('year_month', 'ec_type'), name='Unique Ec Transcription'),
        ),
    ]
