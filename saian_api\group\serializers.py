from datetime import datetime
import json
import logging
from itertools import chain

from django.contrib.contenttypes.models import ContentType
from django.core.exceptions import ObjectDoesNotExist
from django.forms import CharField
from rest_framework import serializers, exceptions
from rest_framework.serializers import PrimaryKeyRelatedField

from saian_api.devdefine.models import AttributePrototype, DevicePrototype
from saian_api.devdefine.serializers import DevicePrototypeSerializer, SimpleDevicePrototypeSerializer
from saian_api.device.models import Device, DeviceCtrlLog, DeviceLimit, DeviceTimer
from saian_api.device.serializers import DeviceSerializer
from saian_api.linkage.models import CrossAttribute, CrossAttributePrototype, LinkageAttribute, LinkageRule, LinkageTarget, LinkageTrigger
from saian_api.project.models import Project
from saian_api.terminal.models import Terminal
from saian_api.user.models import WebUser
from saian_api.utils.tools import validate_week, is_number
from .models import (Action<PERSON>ttribute, ActionTimer, ActionTimerAttribute, Group, GroupAction,
                     GroupDevice, Shortcut, ActionLog, AcStrategies, ShortcutAttribute)
from ..utils.httpapi.image import ImageAPI


class GroupActionSerializer(serializers.Serializer):
    # 分组操作名字
    name = CharField(min_length=1, max_length=200)
    # 分组操作id
    action_id = serializers.IntegerField(required=False)
    # 分组操作对应的参数id
    attr_ids = serializers.ListField(
        child=serializers.IntegerField(),
        required=False
    )


class GroupActionModelSerializer(serializers.ModelSerializer):
    group = PrimaryKeyRelatedField(queryset=Group.objects.all())
    attr_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True
    )

    class Meta:
        model = GroupAction
        fields = ['id', 'name', 'group', 'attr_ids', 'created_at']
        read_only_fields = ['id', 'created_at']

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        view = self.context['view']

        group = Group.objects.get(pk=instance.group_id)

        ret['group'] = {
            'id': group.id,
            'name': group.name,
            'shared': group.shared
        }

        shortcut = Shortcut.objects.filter(op_type=10, op_id=ret['id']).last()
        ret['is_shortcut'] = shortcut is not None
        ret['shortcut_id'] = shortcut.id if shortcut else None

        ret['device_num'] = group.device_num()
        attrs = []
        for aa in instance.actionattribute_set.all().order_by('id'):
            if not aa.content_object:
                continue
            # 分组操作的快捷操作参数
            shortcut_attribute = ShortcutAttribute.objects.filter(shortcut_id=shortcut.id, object_id=aa.content_object.id,
                                                                  content_type=aa.content_type,
                                                                  value__isnull=False).last() if shortcut is not None else None

            action_attribute = {
                'id': aa.id,
                'name': aa.content_object.name,
                'identifier': aa.content_object.identifier,
                'data_type': aa.content_object.data_type,
                'exe_id': aa.object_id,
                'remark': aa.content_object.remark,
            }

            if aa.content_type.model == 'attributeprototype':
                action_attribute['options'] = aa.content_object.options
                action_attribute['min_value'] = aa.content_object.min_value
                action_attribute['max_value'] = aa.content_object.max_value
                action_attribute['default_value'] = aa.content_object.default_value
                action_attribute['label'] = aa.content_object.label
                action_attribute['unit'] = aa.content_object.unit
                if aa.content_object.data_type == 20:
                    action_attribute['in_crement'] = aa.content_object.in_crement
                    action_attribute['pre_cision'] = aa.content_object.pre_cision

            if aa.content_type.model == 'crossattribute':
                aaf = aa.content_object.attribute_prototypes.first()
                action_attribute['options'] = aaf.options
                action_attribute['min_value'] = aaf.min_value
                action_attribute['max_value'] = aaf.max_value
                action_attribute['default_value'] = aaf.default_value

            # 快捷操作参数值
            action_attribute['shortcut_value'] = shortcut_attribute.value if shortcut_attribute is not None else None

            attrs.append(action_attribute)

        ret['attributes'] = attrs

        if view.action == 'retrieve':
            targets = []
            for target in group.devices():
                device_id, prefix, idx = '', '', ''
                dp = target.device_prototype
                if isinstance(target, Device):
                    device_id = target.id
                elif isinstance(target, Terminal):
                    device_id = target.device_id
                    prefix = target.prefix
                    idx = target.idx
                targets.append({
                    'device_id': device_id,
                    "name": target.nick_name,
                    "custz_detail": dp.content is not None,
                    "prefix": prefix,
                    "idx": idx,
                    "device_prototype": dp.uni_name
                })
            ret['targets'] = targets

        # ret.pop('attr_ids')

        return ret


class GroupSerializer(serializers.Serializer):
    # 名称
    name = serializers.CharField(min_length=1, max_length=200)
    # 是否共享分组
    shared = serializers.BooleanField(required=False)
    # 设备类型id
    dp_ids = serializers.ListField(
        child=serializers.IntegerField(),
        required=False
    )
    # 设备id
    device_ids = serializers.ListField(
        child=serializers.IntegerField(),
        required=False
    )
    # 分组操作定义
    actions = GroupActionSerializer(many=True, required=False)


class GroupModelSerializer(serializers.ModelSerializer):
    project = PrimaryKeyRelatedField(queryset=Project.objects.all())
    web_user = PrimaryKeyRelatedField(queryset=WebUser.objects.all())

    class Meta:
        model = Group
        fields = ['id', 'project', 'web_user', 'name', 'shared', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']

    def create(self, validated_data):
        return Group.objects.create(**validated_data)

    def to_representation(self, group_ins):
        ret = super().to_representation(group_ins)

        ret.pop('project')
        ret.pop('updated_at')

        # 是否是跨设备类型分组
        ret['is_cross'] = group_ins.is_cross()

        is_cross = self.context.get('is_cross', None)
        if is_cross is not None:
            if (int(is_cross) and not ret['is_cross']) or (not int(is_cross) and ret['is_cross']):
                return None

        # 分组设备的数量
        ret['num'] = group_ins.device_num()
        # 分组的快捷操作名称，多个时以逗号隔开
        ret['actions'] = group_ins.actions()

        # 获取分组内设备的设备类型
        device_model_id = ContentType.objects.get(model='device').id
        terminal_model_id = ContentType.objects.get(model='terminal').id
        device_prototype_model_id = ContentType.objects.get_for_model(DevicePrototype).id

        ret['group_type'] = None
        ret['device_prototypes'] = []
        if ret['num']:
            first_item = GroupDevice.objects.filter(group_id=ret['id']).first()
            if first_item.content_type_id == device_model_id:
                device_ids = GroupDevice.objects.filter(group_id=ret['id']).values_list('object_id', flat=True)
                device_dp_ids = Device.objects.filter(id__in=device_ids).values_list('device_prototype_id', flat=True)
                device_dps = DevicePrototype.objects.filter(id__in=device_dp_ids)
                ret['group_type'] = 10
                ret['device_prototypes'] = SimpleDevicePrototypeSerializer(device_dps, many=True).data

            elif first_item.content_type_id == terminal_model_id:
                terminal_ids = GroupDevice.objects.filter(content_type_id=terminal_model_id, group_id=ret['id']).values_list('object_id', flat=True)
                terminal_dp_ids = Terminal.objects.filter(id__in=terminal_ids).values_list('device_prototype_id', flat=True)
                terminal_dp = DevicePrototype.objects.filter(id__in=terminal_dp_ids)
                terminal_pdps = DevicePrototype.objects.filter(id__in=terminal_dp.exclude(parent__isnull=True).values_list('parent_id', flat=True))
                ret['group_type'] = 20
                ret['device_prototypes'] = SimpleDevicePrototypeSerializer(chain(terminal_dp, terminal_pdps), many=True).data

            elif first_item.content_type_id == device_prototype_model_id:
                dps = DevicePrototype.objects.filter(id__in=GroupDevice.objects.filter(group_id=ret['id']).values_list('object_id', flat=True))
                ret['group_type'] = 30
                ret['device_prototypes'] = SimpleDevicePrototypeSerializer(dps, many=True).data
        # dps = set(DevicePrototype.objects.filter(id__in=list(terminal_dp_ids) + list(device_dp_ids)))

        return ret


class GroupMemberRelatedField(serializers.RelatedField):
    def to_representation(self, value):
        if isinstance(value, Device):
            serializer = DeviceSerializer(value)
        elif isinstance(value, DevicePrototype):
            serializer = DevicePrototypeSerializer(value)
        else:
            raise Exception('Unexpected type of group member object')

        return serializer.data

    def get_queryset(self):
        if isinstance(self.request.data, Device):
            return Device.objects.all()
        elif isinstance(self.request.data, DevicePrototype):
            return DevicePrototype.objects.all()
        else:
            raise Exception('Unexpected type of group member object')

    def to_internal_value(self, data):
        return data


class GroupDeviceSerializer(serializers.ModelSerializer):
    group = serializers.PrimaryKeyRelatedField(queryset=Group.objects.all())
    content_object = GroupMemberRelatedField()

    class Meta:
        model = GroupDevice
        fields = ['id', 'group', 'content_object']
        read_only_fields = ['id']


class AttributeSerializer(serializers.Serializer):
    attr_id = serializers.IntegerField()
    value = serializers.CharField(min_length=1, max_length=200)


class ActionTimerModelSerializer(serializers.ModelSerializer):
    group_action = serializers.PrimaryKeyRelatedField(queryset=GroupAction.objects.all())

    class Meta:
        model = ActionTimer
        fields = ['id', 'repeat', 'run_date', 'run_time', 'week', 'enabled', 'group_action', 'is_finished', 'created_at', 'updated_at', 'time_ranges']
        read_only_fields = ['id', 'is_finished', 'created_at', 'updated_at']

    def to_representation(self, instance):
        ret = super().to_representation(instance)

        attributers = []
        for attr in instance.actiontimerattribute_set.all().order_by("action_attribute_id"):
            content_object = attr.action_attribute.content_object
            if content_object:
                tmp_attr = {
                    'attr_id': attr.action_attribute_id,
                    'value': attr.value,
                    'name': content_object.name,
                    'data_type': content_object.data_type,
                }
                if isinstance(content_object, AttributePrototype):
                    tmp_attr['options'] = content_object.options
                    tmp_attr['unit'] = content_object.unit
                    tmp_attr['max_value'] = content_object.max_value
                    tmp_attr['min_value'] = content_object.min_value
                    tmp_attr['identifier'] = content_object.identifier

                attributers.append(tmp_attr)

        ret['attributers'] = attributers

        ret['group_action'] = {
            'id': instance.group_action.id,
            'name': instance.group_action.name
        }

        if ret['week'] is None:
            ret['week'] = ''

        return ret


class ActionTimerSerializer(serializers.Serializer):
    group_action_id = serializers.IntegerField()
    attributes = AttributeSerializer(many=True)
    repeat = serializers.BooleanField(required=False)
    run_date = serializers.CharField(min_length=10, max_length=10, required=False, allow_blank=True, allow_null=True)
    run_time = serializers.CharField(min_length=8, max_length=8, required=False)
    week = serializers.CharField(min_length=2, max_length=20, required=False, allow_blank=True, allow_null=True)
    enabled = serializers.BooleanField(required=False)
    time_ranges = serializers.JSONField(required=False, allow_null=True)

    def validate(self, attrs):
        # 校验attributes
        if 'attributes' in attrs:
            for attr in attrs['attributes']:
                try:
                    ActionAttribute.objects.get(pk=attr['attr_id'])
                except ObjectDoesNotExist:
                    raise exceptions.ValidationError('Aaction attribute %s not exist!' % attr['id'])

        # 校验run date为YYYMMDD
        if 'run_date' in attrs and attrs['run_date'] is not None and attrs['run_date'] != '':
            try:
                datetime.strptime(attrs['run_date'], '%Y-%m-%d')
            except ValueError:
                raise exceptions.ValidationError('Run_date is not valid!')

        # 校验run time为HHMMSS
        if 'run_time' in attrs and attrs['run_time'] is not None:
            try:
                datetime.strptime(attrs['run_time'], '%H:%M:%S')
            except ValueError:
                raise exceptions.ValidationError('Run_time is not valid!')

        # 校验week为"周一,周二等"
        if 'week' in attrs and attrs['week'] is not None:
            if not validate_week(attrs['week']):
                raise exceptions.ValidationError('Week is not valid!')

        return attrs


class ShortcutSerializer(serializers.ModelSerializer):
    class Meta:
        model = Shortcut
        fields = '__all__'

    def to_representation(self, instance):
        # TODO 前端未完成，只限制测试二项目的快捷操作列表
        project_id = self.context['request'].user.get('project_id')
        view = self.context['view']
        ret = super(ShortcutSerializer, self).to_representation(instance)
        params = [] if view.action == 'retrieve' and ret['op_type'] == 10 else ''
        attrs = []
        targets_list, targets_details, targets_type, targets_num = [], [], [], 0
        # 类型为分组操作
        if ret['op_type'] == 10:
            if GroupAction.objects.filter(pk=ret['op_id']).exists():
                group_action = GroupAction.objects.get(pk=ret['op_id'])
                group = Group.objects.get(pk=group_action.group_id)
                # 目标设备
                ret['targets'] = group.name

                # 快捷操作列表没有分组操作参数时不显示，直接返回
                if project_id == '66' and view.action == 'list' and not group_action.actionattribute_set.exists():
                    return

                for aa in group_action.actionattribute_set.all().order_by('id'):
                    if aa.content_object is None:
                        continue

                    # 分组操作的快捷操作参数
                    shortcut_attribute = ShortcutAttribute.objects.filter(shortcut_id=ret['id'], object_id=aa.content_object.id,
                                                                          content_type=aa.content_type).last()
                    if view.action == 'retrieve':
                        action_attribute = {
                            'id': aa.id,
                            'name': aa.content_object.name,
                            'identifier': aa.content_object.identifier,
                            'data_type': aa.content_object.data_type,
                            'exe_id': aa.object_id,
                            'remark': aa.content_object.remark
                        }

                        if aa.content_type.model == 'attributeprototype':
                            action_attribute['options'] = aa.content_object.options
                            if aa.content_object.data_type == 20:
                                action_attribute['unit'] = aa.content_object.unit
                                action_attribute['min_value'] = aa.content_object.min_value
                                action_attribute['max_value'] = aa.content_object.max_value
                                action_attribute['in_crement'] = aa.content_object.in_crement
                                action_attribute['pre_cision'] = aa.content_object.pre_cision
                            action_attribute['default_value'] = aa.content_object.default_value

                        if aa.content_type.model == 'crossattribute':
                            aaf = aa.content_object.attribute_prototypes.first()
                            action_attribute['options'] = aaf.options
                            if aa.content_object.data_type == 20:
                                action_attribute['min_value'] = aaf.min_value
                                action_attribute['max_value'] = aaf.max_value
                            action_attribute['default_value'] = aaf.default_value
                        # 快捷操作参数值
                        if shortcut_attribute is not None:
                            shortcut_value = shortcut_attribute.value
                            if shortcut_value and aa.content_object.data_type == 10 and shortcut_value.isdigit():
                                shortcut_value = int(shortcut_value)
                        else:
                            shortcut_value = None
                        action_attribute['shortcut_value'] = shortcut_value

                        params.append(action_attribute)
                        targets_list = []
                        for device in group.devices():
                            device_id, prefix, idx = '', '', ''
                            if isinstance(device, Device):
                                target = Device.objects.get(pk=device.id)
                                device_id = target.id
                            else:
                                target = Terminal.objects.get(pk=device.id)
                                device_id = target.device_id
                                prefix = target.prefix if target.prefix else ''
                                idx = target.idx if target.idx else ''

                            targets_list.append(target.nick_name)
                            dp = target.device_prototype
                            detail = {
                                'device_id': device_id,
                                'name': target.nick_name,
                                'custz_detail': dp.content is not None,
                                'prefix': prefix,
                                'idx': idx,
                                'device_prototype': dp.name
                            }
                            if detail not in targets_details:
                                targets_details.append(detail)
                            if dp.name not in targets_type:
                                targets_type.append(dp.name)
                    targets_num = len(group.devices())
            else:
                Shortcut.objects.filter(op_type=10, op_id=ret['op_id']).delete()
                return

        # 类型为联动操作
        elif ret['op_type'] == 20:
            targets = []
            linkage_rule = LinkageRule.objects.filter(id=ret['op_id']).last()
            if linkage_rule is not None:
                linkage_targets = LinkageTarget.objects.filter(linkage_rule_id=linkage_rule.id)
                targets_list = [linkage_target.target_to_json()['name'] for linkage_target in linkage_targets]
                for linkage_target in linkage_targets:
                    params = params + ('，' if params else '') + '，'.join([attr['name'] for attr in linkage_target.target_to_json()['attributes']])
                    ret['targets'] = params + ('，' if params else '') + linkage_target.target_to_json()['name']
                    # 目标设备为分组
                    if linkage_target.target_to_json()['target_type'] == 10:
                        group = Group.objects.get(pk=linkage_target.target_id)
                        for device in group.devices():
                            device_id, prefix, idx = '', '', ''
                            if isinstance(device, Device):
                                target = Device.objects.get(pk=device.id)
                                device_id = target.id
                            else:
                                target = Terminal.objects.get(pk=device.id)
                                device_id = target.device_id
                                prefix = target.prefix if target.prefix else ''
                                idx = target.idx if target.idx else ''
                            dp = target.device_prototype
                            detail = {
                                'device_id': device_id,
                                'name': target.nick_name,
                                'custz_detail': dp.content is not None,
                                'prefix': prefix,
                                'idx': idx,
                                'device_prototype': dp.name
                            }
                            if detail not in targets_details:
                                targets_details.append(detail)
                            if dp.name not in targets_type:
                                targets_type.append(dp.name)
                            
                    # 目标设备为单个设备
                    elif linkage_target.target_to_json()['target_type'] == 20:
                        target = Device.objects.get(pk=linkage_target.target_to_json()['target_id'])
                        dp = target.device_prototype
                        detail = {
                            'device_id': target.id,
                            'name': target.nick_name,
                            'custz_detail': dp.content is not None,
                            'prefix': '',
                            'idx': '',
                            'device_prototype': dp.name
                        }
                        if detail not in targets_details:
                            targets_details.append(detail)
                        if dp.name not in targets_type:
                            targets_type.append(dp.name)
                    targets_num = len(targets_details)

                ret['hasCode'] = linkage_rule.code is not None
        if view.action == 'retrieve':
            # 目标设备类型
            ret['targets_type'] = '，'.join(targets_type)
            # 目标设备详情列表
            ret['targets_details'] = targets_details
            # 目标设备名称列表
            ret['targets_list'] = targets_list
            ret['attrs'] = attrs

        # 目标设备数量
        ret['targets_num'] = targets_num
        # 参数名称，多个时以逗号隔开
        ret['params'] = params
        return ret


class ActionLogsSerializer(serializers.ModelSerializer):
    class Meta:
        model = ActionLog
        fields = ['id', 'executor_id', 'executor_id', 'actor_name', 'action_name', 'values', 'created_at', 'op_type', 'op_id']
        # fields = '__all__'

    def to_representation(self, instance):
        view = self.context['view']
        ret = super().to_representation(instance)

        ret['executor'] = {
            'id': ret['executor_id'],
            'executor_type': ret['actor_name'],
            'name': '',
        }
        if view.action == 'list':
            ret.pop('values')
        elif view.action == 'retrieve':
            ret['values'] = json.loads(instance.values.replace('\'', '\"'))

        ret.pop('actor_name')
        ret.pop('executor_id')

        if ret['op_type'] == 10:
            ret['operation'] = {
                'id': ret['op_id'],
                'type': '分组操作',
                'name': ret['action_name']
            }
            if GroupAction.objects.filter(pk=ret['op_id']).exists():
                group_action = GroupAction.objects.get(pk=ret['op_id'])
                group = Group.objects.get(pk=group_action.group_id)
                # 目标设备
                ret['target'] = group.name
            else:
                ret['target'] = ''

        elif ret['op_type'] == 20:
            ret['operation'] = {
                'id': ret['op_id'],
                'type': '联动操作',
                'name': ret['action_name']
            }
            linkage_rule = LinkageRule.objects.filter(id=ret['op_id']).last()
            if linkage_rule is not None:
                linkage_targets = LinkageTarget.objects.filter(linkage_rule_id=linkage_rule.id)
                params = ''
                for linkage_target in linkage_targets:
                    params = params + ('，' if params else '') + '，'.join([attr['name'] for attr in linkage_target.target_to_json()['attributes']])
                    ret['target'] = params + ('，' if params else '') + linkage_target.target_to_json()['name']
            else:
                ret['target'] = ''

        elif ret['op_type'] == 30 and ret['op_id']:
            ret['operation'] = {
                'id': ret['op_id'],
                'type': '设备软定时',
                'name': ret['action_name']
            }
            device = Device.objects.filter(pk=ret['op_id']).last()

            if device is not None:
                ret['target'] = device.nick_name
            else:
                ret['target'] = ''

        else:
            ret['operation'] = {
                'id': ret['op_id'],
                'type': '',
                'name': ret['action_name']
            }
            ret['target'] = ret['action_name']

        device_logs = DeviceCtrlLog.objects.filter(action_log_id=instance.id)
        ret['target_qty'] = device_logs.count()
        ret['target_finished_qty'] = device_logs.filter(errcode=0).count()
        return ret


class AcStrategiesSerializer(serializers.ModelSerializer):
    class Meta:
        model = AcStrategies
        fields = '__all__'

    def to_representation(self, instance):
        view = self.context['view']
        ret = super().to_representation(instance)
        identifiers = None

        if ret['st_type'] == 10:
            if ActionTimer.objects.filter(pk=ret['st_id']).exists():
                action_timer = ActionTimer.objects.get(pk=ret['st_id'])
                group_action = GroupAction.objects.get(pk=action_timer.group_action_id)
                group = Group.objects.get(pk=group_action.group_id)
                ret['name'] = group_action.name
                # 目标设备
                ret['target_name'] = group.name
                ret['enabled'] = action_timer.enabled
                ret['repeat'] = action_timer.repeat
                ret['run_week'] = action_timer.week
                ret['run_date'] = action_timer.run_date
                ret['run_time'] = action_timer.run_time
                ret['is_finished'] = action_timer.is_finished
                if view.action == 'retrieve':
                    identifiers = action_timer.actiontimerattribute_set.all()
                ret['time_ranges'] = action_timer.time_ranges

        elif ret['st_type'] == 20:
            if LinkageRule.objects.filter(pk=ret['st_id']).exists():
                linkage_rule = LinkageRule.objects.get(id=ret['st_id'])
                linkage_targets = LinkageTarget.objects.filter(linkage_rule_id=linkage_rule.id)
                params = ''
                identifiers = ActionTimerAttribute.objects.none()
                ret['name'] = linkage_rule.name
                for linkage_target in linkage_targets:
                    params = params + ('，' if params else '') + linkage_target.target_to_json()['name']
                    if view.action == 'retrieve':
                        identifiers = identifiers | linkage_target.linkageattribute_set.all()

                ret['target_name'] = params if linkage_rule.group_id is None else linkage_rule.group.name
                ret['enabled'] = linkage_rule.enabled
                ret['remark'] = linkage_rule.remark if linkage_rule.remark else ''
                ret['trigger_type'] = linkage_rule.trigger_type

                linkage_trigger = LinkageTrigger.objects.filter(linkage_rule_id=ret['st_id']).first()
                if linkage_trigger:
                    ret['repeat'] = linkage_trigger.repeat
                    ret['run_week'] = str(linkage_trigger.wdays) if linkage_trigger.wdays else ''
                    ret['run_date'] = str(linkage_trigger.run_date) if linkage_trigger.run_date else ''
                    ret['run_time'] = str(linkage_trigger.run_time) if linkage_trigger.run_time else ''
                    ret['interval'] = linkage_trigger.interval
                    ret['is_finished'] = linkage_trigger.is_finished
                    ret['time_ranges'] = linkage_trigger.time_ranges

        elif ret['st_type'] == 30:
            if LinkageRule.objects.filter(pk=ret['st_id']).exists():
                linkage_rule = LinkageRule.objects.get(id=ret['st_id'])
                linkage_targets = LinkageTarget.objects.filter(linkage_rule_id=linkage_rule.id)
                params = ''
                ret['name'] = linkage_rule.name
                for linkage_target in linkage_targets:
                    params = params + ('，' if params else '') + linkage_target.target_to_json()['name']
                ret['target_name'] = params if linkage_rule.group_id is None else linkage_rule.group.name
                ret['enabled'] = linkage_rule.enabled
                ret['repeat'] = False
                ret['remark'] = linkage_rule.remark if linkage_rule.remark else ''
                ret['trigger_type'] = linkage_rule.trigger_type

        elif ret['st_type'] == 40:
            if DeviceTimer.objects.filter(pk=ret['st_id']).exists():
                device_timer = DeviceTimer.objects.get(pk=ret['st_id'])
                ret['name'] = device_timer.name
                ret['target_name'] = Device.objects.get(pk=device_timer.device_id).nick_name
                ret['enabled'] = device_timer.enabled
                ret['repeat'] = device_timer.repeat
                ret['run_week'] = device_timer.wdays
                ret['run_date'] = device_timer.run_date
                ret['run_time'] = device_timer.run_time
                ret['is_finished'] = device_timer.is_finished
                if view.action == 'retrieve':
                    identifiers = device_timer.identifiers
                ret['time_ranges'] = device_timer.time_ranges

        elif ret['st_type'] == 50:
            if DeviceLimit.objects.filter(pk=ret['st_id']).exists():
                device_limit = DeviceLimit.objects.get(pk=ret['st_id'])
                ret['name'] = device_limit.name
                ret['target_name'] = DevicePrototype.objects.get(pk=device_limit.device_prototype_id).name
                ret['enabled'] = device_limit.enabled
                ret['repeat'] = True
                if view.action == 'retrieve':
                    ret['remark'] = device_limit.remark
                    ret['up_value'] = device_limit.up_value
                    ret['low_value'] = device_limit.low_value

        if view.action == 'retrieve' and (ret['st_type'] == 10 or ret['st_type'] == 40):
            idfs = []
            if identifiers:
                if ret['st_type'] == 10:
                    for ata in identifiers:
                        target_object = ata.action_attribute.content_object
                        if target_object is None:
                            continue
                        idf = {
                            'id': ata.id,
                            'name': target_object.name,
                            'identifier': target_object.identifier,
                            'data_type': target_object.data_type,
                            'default_value': target_object.default_value,
                            'remark': target_object.remark,
                            'value': ata.value
                        }

                        # 设备类型与跨设备类型的差异处理
                        if isinstance(target_object, CrossAttribute):
                            ap = target_object.attribute_prototypes.first()
                            idf['in_crement'] = ap.in_crement
                            idf['min_value'] = target_object.min
                            idf['max_value'] = target_object.max
                            idf['options'] = ap.options
                            idf['unit'] = ap.unit
                            idf['pre_cision'] = ap.pre_cision
                        elif isinstance(target_object, AttributePrototype):
                            idf['in_crement'] = target_object.in_crement
                            idf['min_value'] = target_object.min_value
                            idf['max_value'] = target_object.max_value
                            idf['options'] = target_object.options
                            idf['unit'] = target_object.unit
                            idf['pre_cision'] = target_object.pre_cision

                        idfs.append(idf)

                elif ret['st_type'] == 40:
                    device_timer = DeviceTimer.objects.get(pk=ret['st_id'])
                    existing_aps = json.loads(identifiers)
                    for existing_ap in existing_aps:
                        ap = AttributePrototype.objects.filter(
                            device_prototype_id=device_timer.device.device_prototype_id,
                            identifier=existing_ap['identifier']
                        ).last()
                        if ap is not None:
                            idfs.append({
                                'id': ap.id,
                                'name': ap.name,
                                'identifier': ap.identifier,
                                'data_type': ap.data_type,
                                'in_crement': ap.in_crement,
                                'min_value': ap.min_value,
                                'max_value': ap.max_value,
                                'options': ap.options,
                                'default_value': ap.default_value,
                                'unit': ap.unit,
                                'pre_cision': ap.pre_cision,
                                'remark': ap.remark,
                                'value': existing_ap.get('value', ap.default_value)
                            })

                ret['identifiers'] = idfs
            else:
                ret['identifiers'] = []

        return ret
