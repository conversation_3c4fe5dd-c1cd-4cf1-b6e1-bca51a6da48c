import datetime
import re
import logging
from collections import defaultdict

from django.contrib.contenttypes.models import ContentType
from django.db import transaction, IntegrityError
from django.db.models import Q, Avg, FloatField, Value, Prefetch, Count
from django.shortcuts import get_object_or_404
from rest_framework import status, exceptions
from rest_framework import viewsets
from rest_framework.decorators import action
# Create your views here.
from rest_framework.response import Response

from django.db.models.functions import Cast

from saian_api.utils.sy_jsonrenderer import SyJSONRender
from .models import Terminal, TerminalAttribute, Category, CategoryTerminal, TerminalCoolingStats, TerminalNameplate
from .serializers import (TerminalSerializer, CategorySerializer, CategoryTerminalSerializer, SimpleTerminalSerializer,
                          TerminalAttributeSerializer, MiniTerminalSerializer, TerminalNameplateSerializer, CategoryWithTerminalCountSerializer)
from ..building.models import ActiveRoom, Floor
from ..devdefine.models import DevicePrototype, AttributePrototype, DeviceType
from ..devdefine.serializers import SimpleDeviceTypeSerializer, SimpleDevicePrototypeSerializer, SimpleAttributePrototypeSerializer
from ..device.models import Device, DeviceAttribute, DeviceLimit, DyAttribute, RoomDevice
from ..device.views import DeviceV3ViewSet
from ..dimension.models import Dimension, DimensionTerminal, DimensionUser
from ..group.models import GroupDevice
from ..report.models import DeviceHourlyStat
from ..coldsource.models import ColdSource
from ..report.serializers import SimpleDeviceSerializer
from ..user.models import WebUser, UserSearches
from ..utils.db.Convert import Convert
from ..utils.intapi_auth import IntapiAuth
from ..utils.legacy_auth import LegacyAuth
from ..utils.tools import is_number
from ..utils.utils import AuthUtils
from saian_api.utils.httpapi.image import ImageAPI
from saian_api.utils.httpapi.wechat import WechatApi


class TerminalView(viewsets.ModelViewSet):
    serializer_class = TerminalSerializer
    renderer_classes = (SyJSONRender,)

    @action(detail=True, methods=['get', 'post'])
    def get_authenticators(self):
        if 'intapi' in self.request.path:
            return [IntapiAuth(), ]
        return [LegacyAuth(), ]

    def get_serializer_class(self):
        if self.request.query_params.get('simple', None) is not None:
            return SimpleTerminalSerializer
        return self.serializer_class

    def get_queryset(self):
        # 是否返回隐藏的终端
        all_terminal = self.request.query_params.get("all", None)
        if all_terminal:
            queryset = Terminal.objects.all()
        else:
            queryset = Terminal.objects.filter(show_en=True)

        dt_id = self.request.query_params.get("dt_id", None)
        dp_id = self.request.query_params.get("dp_id", None)
        device_id = self.request.query_params.get("device_id", None)
        in_alarm = self.request.query_params.get('in_alarm', None)
        in_fault = self.request.query_params.get('in_fault', None)
        is_online = self.request.query_params.get('is_online', None)
        sw_on = self.request.query_params.get('sw_on', None)
        online = self.request.query_params.get('online', None)
        terminal_type = self.request.query_params.get('terminal_type', None)
        terminal_label = self.request.query_params.get("terminal_label", None)
        search = self.request.query_params.get('search', None)
        min_temp = self.request.query_params.get('min_temp', None)
        max_temp = self.request.query_params.get('max_temp', None)
        local_min_temp = self.request.query_params.get('local_min_temp', None)
        local_max_temp = self.request.query_params.get('local_max_temp', None)

        # 现场湿度
        local_min_humi = self.request.query_params.get('local_min_humi', None)
        local_max_humi = self.request.query_params.get('local_max_humi', None)

        # 末端类型
        label_id = self.request.query_params.get('label_id', None)

        if dt_id is not None and dt_id != '':
            dt_ids = dt_id.split(',')
            queryset = queryset.filter(device__device_type_id__in=dt_ids)

        if dp_id is not None and dp_id != '':
            dp_ids = dp_id.split(',')
            c_dp = DevicePrototype.objects.filter(parent_id__in=dp_ids)

            queryset = queryset.filter(device_prototype_id__in=dp_ids + [dp.id for dp in c_dp])

        if device_id is not None:
            queryset = queryset.filter(device_id=device_id)

        if in_alarm is not None:
            queryset = queryset.filter(in_alarm=in_alarm)

        if in_fault is not None:
            queryset = queryset.filter(in_fault=in_fault)

        if is_online is not None:
            queryset = queryset.filter(online=(False if is_online == '0' else True))

        if online is not None:
            queryset = queryset.filter(online=online)

        if sw_on is not None:
            queryset = queryset.filter(sw_on=sw_on)

        if terminal_type is not None:
            queryset = queryset.filter(terminal_type__in=terminal_type.split(','))

        if terminal_label is not None:
            queryset = queryset.filter(terminal_label=terminal_label)

        if search is not None:
            queryset = queryset.filter(Q(nick_name__contains=search) | Q(device__mac__icontains=search) | Q(device__address__icontains=search))

        if min_temp is not None and is_number(min_temp):
            queryset = queryset.filter(terminalattribute__value__gte=Cast(Value(min_temp), FloatField()), terminalattribute__use_for=10)

        if max_temp is not None and is_number(max_temp):
            queryset = queryset.filter(terminalattribute__value__lte=Cast(Value(max_temp), FloatField()), terminalattribute__use_for=10)

        if local_min_temp is not None and is_number(local_min_temp):
            queryset = queryset.filter(terminalattribute__value__gte=Cast(Value(local_min_temp), FloatField()), terminalattribute__use_for=50)

        if local_max_temp is not None and is_number(local_max_temp):
            queryset = queryset.filter(terminalattribute__value__lte=Cast(Value(local_max_temp), FloatField()), terminalattribute__use_for=50)

        # 现场湿度
        if local_min_humi is not None and is_number(local_min_humi):
            queryset = queryset.filter(terminalattribute__value__gte=Cast(
                Value(local_min_humi), FloatField()), terminalattribute__identifier='LocalRH')

        if local_max_humi is not None and is_number(local_max_humi):
            queryset = queryset.filter(terminalattribute__value__lte=Cast(
                Value(local_max_humi), FloatField()), terminalattribute__identifier='LocalRH')

        # 运行状态（正常-10，故障-20，报警-30）
        run_status = self.request.query_params.get('run_status', None)
        if run_status is not None:
            if run_status == '10':
                queryset = queryset.filter(online=True, in_fault=False, in_alarm=False)
            if run_status == '20':
                queryset = queryset.filter(online=True, in_fault=True)
            if run_status == '30':
                queryset = queryset.filter(online=True, in_alarm=True)

        # 末端类型
        if label_id is not None:
            device_prototype_ids = list(DevicePrototype.objects.filter(device_type_id=label_id).values_list('id', flat=True))
            queryset = queryset.filter(device_prototype_id__in=device_prototype_ids)

        # 终端列表按照昵称排序。因为 devices 表和 terminals 表都有 'nick_name', 所以要带上表名 'terminals.nick_name'
        return queryset.order_by(Convert('terminals.nick_name', 'gbk'))

    def list(self, request, *args, **kwargs):
        project_id = request.user['project_id']
        user_id = request.user.get('id', None)
        queryset = self.get_queryset()

        key_word = request.query_params.get('key_word', None)
        run_mode = request.query_params.get('run_mode', None)
        per_page = request.query_params.get('per_page', None)
        page = request.query_params.get('page', '1')
        group_id = request.query_params.get('group_id', None)
        group_only = self.request.query_params.get("group_only", None)
        building_id = request.query_params.get('building_id', None)
        floor_id = request.query_params.get('floor_id', None)
        room_id = request.query_params.get('room_id', None)

        if run_mode is not None:

            cs_macs = ColdSource.objects.all().values_list('mac', flat=True).distinct()
            cs_tids = list(Terminal.objects.filter(device__mac__in=cs_macs, show_en=True,
                                                   idx__isnull=True).values_list('id', flat=True).distinct())
            # 冷源的运行状态 identifier 有两种：等于 RunMode 或以 SysRunMode 结尾
            cs_tas = TerminalAttribute.objects.filter(Q(identifier='RunMode') |
                                                      Q(identifier__endswith='SysRunMode'),
                                                      terminal_id__in=cs_tids)

            t_tids = list(Terminal.objects.filter(terminal_type=10, show_en=True).values_list('id', flat=True))
            # use_for=20 是设置运行模式的参数，一些设备类型的设置和实质运行模式参数是分开的，所以不能在这里使用use_for=20
            t_tas = TerminalAttribute.objects.filter(
                Q(identifier='RunMode') | Q(identifier='SysRunMode') | Q(identifier='FCURunningMode') |
                Q(identifier__endswith='_RunMode') | Q(identifier='CoolOrWarmSelect') | (Q(identifier='model')),
                terminal_id__in=t_tids, terminal__show_en=True)

            union_tas = list(cs_tas.union(t_tas))

            # 去除不相关的参数
            union_ap_ids = [ta.attribute_prototype_id for ta in union_tas]
            aps = AttributePrototype.objects.filter(pk__in=union_ap_ids)
            union_tas = [ta for ta in union_tas if (('供冷' in aps.get(
                pk=ta.attribute_prototype_id).options) or ('制冷' in aps.get(pk=ta.attribute_prototype_id).options))]

            cooling_tas = [ta for ta in union_tas if ta.value in ['制冷', '供冷']]
            heating_tas = [ta for ta in union_tas if ta.value in ['制暖', '供暖']]
            others_tas = set(union_tas).difference(set(cooling_tas).union(set(heating_tas)))

            target_ids = []
            if run_mode == '制冷':
                target_ids = [ta.terminal_id for ta in cooling_tas]
            elif run_mode == '制暖':
                target_ids = [ta.terminal_id for ta in heating_tas]
            elif run_mode == '其他':
                target_ids = [ta.terminal_id for ta in others_tas]

            queryset = queryset.filter(pk__in=target_ids)

        if key_word is not None and user_id is not None:
            device_ids = Device.objects.filter(Q(address__icontains=key_word) | Q(mac__icontains=key_word)).values_list('id', flat=True)
            queryset = queryset.filter(Q(nick_name__icontains=key_word) | Q(device_id__in=device_ids))
            # 如果已经搜过该关键词，则删除
            exists_searches = UserSearches.objects.filter(key_words=key_word, web_user_id=user_id)
            exists_searches.delete()
            user_search = UserSearches(key_words=key_word, web_user_id=user_id)
            user_search.save(using=f'prj{project_id}db')

        room_ids = None
        if room_id is not None:
            room_ids = [room_id]
        elif floor_id is not None:
            room_ids = ActiveRoom.objects.filter(floor_id=floor_id).values_list('id', flat=True)
        elif building_id is not None:
            floor_ids = Floor.objects.filter(building_id=building_id).values_list('id', flat=True)
            room_ids = ActiveRoom.objects.filter(floor_id__in=floor_ids).values_list('id', flat=True)

        if room_ids is not None:
            terminal_model = ContentType.objects.get_for_model(Terminal)
            device_ids = RoomDevice.objects.filter(content_type=terminal_model, active_room_id__in=room_ids).values_list('object_id', flat=True)
            queryset = queryset.filter(id__in=device_ids)

        # 分组ID，分组成员排在前面
        total_group = None
        if group_id is not None:
            group_objects = GroupDevice.objects.filter(group_id=group_id, content_type=ContentType.objects.get_for_model(Terminal))
            group_terminals = group_objects.values_list('object_id', flat=True)
            total_group = group_terminals.count()

            if group_only:
                if total_group:
                    selected = queryset.filter(id__in=group_terminals)
                else:
                    group_objects = GroupDevice.objects.filter(group_id=group_id, content_type=ContentType.objects.get_for_model(Device))
                    group_devices = list(group_objects.values_list('object_id', flat=True))
                    group_terminals = Terminal.objects.filter(device_id__in=group_devices).values_list('id', flat=True)
                    total_group = group_terminals.count()
                    selected = queryset.filter(id__in=group_terminals)
                unselect = queryset.none()
            else:
                selected = queryset.filter(id__in=group_terminals)
                unselect = queryset.exclude(id__in=group_terminals)

            total_offline = selected.filter(online=False).count() + unselect.filter(online=False).count()
            total_run = selected.filter(online=True, sw_on=True).count() + unselect.filter(online=True, sw_on=True).count()
            total_stop = selected.filter(online=True, sw_on=False).count() + unselect.filter(online=True, sw_on=False).count()

            queryset = selected.union(unselect)
        else:
            total_offline = queryset.filter(online=False).count()
            total_run = queryset.filter(online=True, sw_on=True).count()
            total_stop = queryset.filter(online=True, sw_on=False).count()

        total = queryset.count()

        if per_page is not None and per_page.isdigit() and page.isdigit():
            per_page = int(per_page)
            page = int(page)
            begin = (page - 1) * per_page
            end = page * per_page
            queryset = queryset[begin: end]
            # queryset = self.paginate_queryset(queryset)

        simple = request.query_params.get('simple', None)
        is_web = request.query_params.get('web', None)
        mini = request.query_params.get('mini', None)
        if mini is not None:
            terminals = MiniTerminalSerializer(queryset, many=True).data
        elif simple is None:
            terminals = TerminalSerializer(queryset, many=True, context={'request': request}).data
        else:
            terminals = SimpleTerminalSerializer(queryset, many=True, context={'request': request}).data
            dp_cache = {}
            device_cache = {}
            for terminal in terminals:
                dp_id = terminal.get('device_prototype_id')
                device_id = terminal.get('device_id')

                dp = dp_cache.get(dp_id, None)
                device_data = device_cache.get(device_id, None)

                if dp is None:
                    dp = DevicePrototype.objects.get(pk=dp_id)
                    dp_cache[dp_id] = dp

                if device_data is None:
                    device = Device.objects.get(pk=device_id)
                    device_data = SimpleDeviceSerializer(device).data
                    device_cache[device_id] = device_data
                terminal['device'] = device_data

                terminal['custz_detail'] = True if (dp.web_content if is_web else dp.content) else False
                terminal['device_prototype'] = dp.name

        # 末端类型
        labels = DeviceType.objects.filter(parent_id=2).values('id', 'name')

        return Response({
            'labels': labels,
            'terminals': terminals,
            'total': total,
            'total_offline': total_offline,
            'total_run': total_run,
            'total_stop': total_stop,
            'total_selected': total_group
        })

    def create(self, request, *args, **kwargs):
        web_user = WebUser.objects.get(pk=request.user['id'])
        if web_user.is_super:
            device_id = request.data.get('device_id', None)
            for device in Device.objects.all() if device_id is None else Device.objects.filter(pk=device_id):
                logging.info(f'create terminal for device: {device.id}-{device.nick_name}')
                Terminal.objects.create_by_device(device)
                TerminalAttribute.objects.create_by_device(device)

            return Response({
                'status': status.HTTP_200_OK,
                'data': None
            })
        else:
            return Response({
                'status': status.HTTP_403_FORBIDDEN,
                'data': '权限不足'
            })

    def partial_update(self, request, *args, **kwargs):
        terminal = Terminal.objects.get(pk=kwargs.get('pk'))
        nick_name = request.data.get('nick_name', None)
        address = request.data.get('address', None)

        # 非昵称和地址，默认更新
        if nick_name is None and address is None:
            return Response({
                'status': status.HTTP_200_OK,
                'terminal': super(TerminalView, self).partial_update(request, *args, **kwargs).data
            })

        if nick_name is not None:
            terminal.nick_name = nick_name
            terminal.save(update_fields=['nick_name'])

            # 如果更新了终端的昵称，要同步更新对应设备的昵称或对应 da 的值
            device = terminal.device
            if not terminal.idx:
                device.nick_name = terminal.nick_name
                device.save(update_fields=['nick_name'])
            else:
                nick_name_ap = AttributePrototype.objects.filter(device_prototype_id=terminal.device.device_prototype_id)
                nick_name_ap = nick_name_ap.filter(Q(identifier__contains='NickName') & (
                        Q(identifier__contains=f'_{terminal.idx}_') | Q(identifier__endswith=f'_{terminal.idx}')))
                if terminal.prefix:
                    nick_name_ap = nick_name_ap.filter(identifier__contains=terminal.prefix)
                if nick_name_ap.exists():
                    nick_name_ap = nick_name_ap.last()
                    try:
                        # da = DeviceAttribute.objects.get(device=device, attribute_prototype_id=nick_name_ap.id)
                        da = DeviceAttribute.objects.get_by_ap(device, nick_name_ap)
                        da.value = terminal.nick_name
                        da.save()
                        DeviceAttribute.objects.save_to_redis(device, da)
                    except DeviceAttribute.DoesNotExist:
                        logging.error(
                            f'Can not find the da. device: {device.id}-{device.nick_name}, ap: {nick_name_ap.id}-{nick_name_ap.identifier}')
                    except DeviceAttribute.MultipleObjectsReturned:
                        logging.error(
                            f'Multiple da return. device: {device.id}-{device.nick_name}, ap: {nick_name_ap.id}-{nick_name_ap.identifier}')
        if address is not None:
            device = terminal.device
            device.address = address
            device.save(update_fields=['address'])

        res_data = {
            'status': status.HTTP_200_OK,
            'terminal': self.serializer_class(terminal, context={'request': request}).data
        }

        return Response(res_data)

    def retrieve(self, request, pk=None, *args, **kwargs):
        # 是否web请求
        is_web = request.query_params.get('web', None)
        # 是否只需要简单的终端数据
        is_simple = request.query_params.get('simple', None)

        terminal = get_object_or_404(Terminal, pk=pk)

        if is_simple:
            serializer = SimpleTerminalSerializer(terminal)
            return Response({
                'status': status.HTTP_200_OK,
                'data': {
                    'terminal': serializer.data
                }
            })

        device = terminal.device
        device_prototype = terminal.device_prototype
        device_type = device_prototype.device_type

        issue_name = device.last_issue() if (terminal.in_alarm or terminal.in_fault) else ''

        detail_attres = []
        if terminal.prefix is not None and terminal.idx is not None:
            for et_type in device.et_types(prefix=terminal.prefix, index=terminal.idx):
                if et_type['prefix'] == terminal.prefix:
                    for exten in et_type['extensions']:
                        if exten['idx'] == terminal.idx:
                            detail_attres = exten['detail_attres']
                            if is_web:
                                attres = list(exten['detail_attres'])
                                dash_attres = terminal.dashboard_attres()
                                if dash_attres:
                                    attres += dash_attres

                                idfs = []
                                detail_attres = []
                                for attr in attres:
                                    if attr['idf'] not in idfs:
                                        idfs.append(attr['idf'])
                                        detail_attres.append(attr)
                            break
                    break

            dp_content = DevicePrototype.objects.content(device, terminal.prefix, terminal.idx)

            # 末端温度设定限制
            has_permission = AuthUtils.has_permissions(request.user.get('id'), 8)
            if not has_permission:
                device_limits = DeviceLimit.objects.filter(enabled=True, device_prototype_id=device.device_prototype_id)
                for content in dp_content:
                    if content.get('components', None) is not None:
                        DeviceV3ViewSet.content_attr_limit(device, content['components'], device_limits)

        elif is_web:
            attres = []
            dash_attres = device.dashboard_attres()
            deta_attres = device.detail_attres()
            if dash_attres:
                attres += dash_attres
            if deta_attres:
                attres += deta_attres

            idfs = []
            for attr in attres:
                if attr['idf'] not in idfs:
                    idfs.append(attr['idf'])
                    detail_attres.append(attr)

        else:
            detail_attres = device.detail_attres()
        # sub_status = TerminalAttribute.objects.filter(Q(identifier__endswith='RunStatus') | Q(identifier__endswith='_Status'),
        #                                               terminal=terminal).last()
        # ap = AttributePrototype.objects.filter(Q(identifier__endswith='RunStatus') | Q(identifier__endswith='_Status'),
        #                                        device_prototype_id=device.device_prototype_id)
        sw_on = terminal.sw_on
        # 处理子设备的开关
        if terminal.prefix:
            ap = AttributePrototype.objects.filter(Q(identifier__endswith='RunStatus') | Q(identifier__endswith='_Status'),
                                                   identifier__contains=terminal.idx,
                                                   device_prototype_id=device.device_prototype_id
                                                   ).filter(identifier__startswith=terminal.prefix).first()
            sub_status = TerminalAttribute.objects.query_object_by_ap(terminal, ap, device.project_id) if ap else None

            if sub_status:
                sub_status = TerminalAttribute.objects.query_object_by_ta(sub_status, device.project_id)
                sw_on = '停止' not in sub_status.value and sub_status.value != '关到位'

        dp_content = getattr(DevicePrototype.objects, 'web_content' if is_web else 'content')(device, terminal.prefix, terminal.idx)

        device_limits = []
        has_permission = AuthUtils.has_permissions(request.user.get('id'), 7)
        if not has_permission:
            device_limits = DeviceLimit.objects.filter(enabled=True, device_prototype_id=device.device_prototype_id)
            # 处理界面配置出来的组件，最大值和最小值
            for content in dp_content:
                if content.get('components', None) is not None:
                    DeviceV3ViewSet.content_attr_limit(device, content['components'], device_limits)
        terminal_attributes = []

        ap_ids = list(TerminalAttribute.objects.filter(terminal_id=terminal.id).values_list('attribute_prototype_id', flat=True))
        aps = list(AttributePrototype.objects.filter(id__in=ap_ids))

        for ta in TerminalAttribute.objects.query_mult_object_list([terminal], ap_ids, request.user['project_id']):
            # try:
            #     ap_id = ta.attribute_prototype_id
            #     ap = AttributePrototype.objects.get(pk=ap_id)
            # except AttributePrototype.DoesNotExist:
            #     continue
            ap = next(filter(lambda x: x.id == ta.attribute_prototype_id, aps), None)
            if ap is None:
                continue

            value = ta.value

            # 温控器v2酒店版，特殊功能为湿度控制时，运行模式和盘管状态值默认显示为除湿
            if device.device_prototype_id == 223 and TerminalAttribute.objects.query_object_by_idf(terminal, 'SpecialFunc').value == '湿度控制':
                if ap.identifier == 'FCURunningMode' or ap.identifier == 'FCUStatus':
                    value = '除湿'

            ta_fields = {
                'id': ta.id,
                'value': value,
                'identifier': ap.identifier,
                'name': ap.name,
                'icon': ap.icon,
                'unit': ap.unit,
                'seq': ap.seq,
                'data_type': ap.data_type,
                'remark': ap.remark,
                'options': ap.options,
                'read_only': ap.read_only,
                'min_value': ap.min_value,
                'max_value': ap.max_value,
            }
            # 无权限时受到末端温度限制的影响(device_limit)
            if not has_permission:
                # 处理 terminal_attributes 中的最大值和最小值
                AttributePrototype.objects.affect_by_device_limit(device_limits, device, ap, ta_fields)

            terminal_attributes.append(ta_fields)

        terminal_data = {
            'id': terminal.id,
            'address': device.address,
            'idx': terminal.idx,
            'prefix': terminal.prefix,
            'terminal_type': terminal.terminal_type,
            'terminal_label': terminal.terminal_label,
            'nick_name': terminal.nick_name,
            'status': terminal.status,
            'sw_on': sw_on,
            'sw': sw_on,
            'in_fault': terminal.in_fault,
            'in_alarm': terminal.in_alarm,
            'online': terminal.online,
            'mac': device.mac,
            'wudid': device.wudid,
            'created_at': terminal.created_at,
            'updated_at': terminal.updated_at,
            'terminal_attributes': terminal_attributes,
            'device_type': SimpleDeviceTypeSerializer(device_type).data,
            'device_prototype': SimpleDevicePrototypeSerializer(device_prototype).data,
            'icon': device_type.icon,
            'issue_name': issue_name,
            'custz_detail': True if (device_prototype.web_content if is_web else device_prototype.content) is not None else False,
            'detail_attres': detail_attres,
            'device': SimpleDeviceSerializer(device).data,
            'attach_devices': DyAttribute.objects.get_attach_devices(device.id),
        }
        terminal_data['device_prototype']['content'] = dp_content
        terminal_data['device']['custz_detail'] = True if (device.device_prototype.web_content
                                                           if is_web else device.device_prototype.content) is not None else False

        # 单位设备控制权限
        if Dimension.objects.filter(type_name='单位').exists():
            unit_ids = list(Dimension.objects.filter(type_name='单位').values_list('id', flat=True))
            if terminal is not None and DimensionTerminal.objects.filter(terminal_id=terminal.id, dimension_id__in=unit_ids).exists():
                user_id = request.user['id']
                user_units = list(DimensionUser.objects.filter(id__in=unit_ids, web_user_id=user_id).values_list('dimension_id', flat=True))
                unit_permission = DimensionTerminal.objects.filter(terminal_id=terminal.id, dimension_id__in=user_units).exists()
                terminal_data['unit_permission'] = unit_permission

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'terminal': terminal_data
            }

        })


class CategoryView(viewsets.ModelViewSet):
    serializer_class = CategoryWithTerminalCountSerializer
    renderer_classes = (SyJSONRender,)
    pagination_class = None

    @classmethod
    def get_children_category(cls, pid, categories, category_terminals):
        children = list(filter(lambda x: x['parent'] == pid, categories))
        if len(children) == 0:
            return []
        for child in children:
            child['children'] = cls.get_children_category(child['id'], categories, category_terminals)
            child['terminals'] = category_terminals.get(child['id'], set())

        return children

    def get_queryset(self):
        queryset = Category.objects.all().order_by('parent_id', 'seq', 'name')

        acc_end = self.request.query_params.get('acc_end', None)
        if acc_end is None:
            # 默认返回小程序类型
            queryset = queryset.filter(Q(acc_end__isnull=True) | Q(acc_end=10))
        else:
            queryset = queryset.filter(acc_end=acc_end)

        search = self.request.query_params.get('search', None)
        if search is not None:
            queryset = queryset.filter(name__icontains=search)

        queryset = queryset.annotate(terminal_count=Count('categoryterminal', filter=Q(categoryterminal__terminal__show_en=True)))
        return queryset

    def get_object(self):
        try:
            return Category.objects.get(pk=self.kwargs['pk'])
        except Category.DoesNotExist:
            raise exceptions.NotFound(f'Category not found! id={self.kwargs["pk"]}')

    def compute_terminal_count(self, category):
        """ 计算当前分类及其所有子分类的 terminal 数量，确保 terminal 只计数一次 """
        terminals = set(category.get('terminals', set()))  # 当前分类的终端
        for child in category.get('children', []):
            terminals |= self.compute_terminal_count(child)  # 合并子分类的终端

        category['terminal_count'] = len(terminals)  # 计算去重后的终端数
        return terminals

    def list(self, request, *args, **kwargs):
        data = super(CategoryView, self).list(request, *args, **kwargs).data

        categories = data
        category_ids = [category['id'] for category in categories]
        # 获取 category 对应的 terminal_id
        category_terminals = defaultdict(set)
        for ct in CategoryTerminal.objects.filter(category_id__in=category_ids).values('category_id', 'terminal_id'):
            category_terminals[ct['category_id']].add(ct['terminal_id'])

        hierarchy = []
        for category in list(filter(lambda x: x['parent'] is None, categories)):
            category['children'] = self.get_children_category(category['id'], categories, category_terminals)
            category['terminals'] = category_terminals.get(category['id'], set())
            hierarchy.append(category)

        # 处理每个根分类
        for root_category in hierarchy:
            self.compute_terminal_count(root_category)

        return Response({
            'categories': hierarchy,
            'count': len(categories)
        })

    def update(self, request, *args, **kwargs):
        if isinstance(request.data, list):
            for item in request.data:
                category = Category.objects.get(pk=item.get('id'))
                category.seq = item.get('seq', 0)
                category.save()
        else:
            super(CategoryView, self).update(request, *args, **kwargs)

        return Response({
            'status': status.HTTP_200_OK,
            'results': None
        })

    def destroy(self, request, *args, **kwargs):
        super().destroy(request, *args, **kwargs)
        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })


class CategoryTerminalView(viewsets.ModelViewSet):
    serializer_class = CategoryTerminalSerializer
    renderer_classes = (SyJSONRender,)
    queryset = Category.objects.all()

    def create(self, request, *args, **kwargs):
        terminal_ids = request.data.get('terminal_ids', None)
        category_id = request.data.get('category_id', None)
        if terminal_ids is None:
            return Response(status=status.HTTP_400_BAD_REQUEST, data={"error": 'terminal_ids field is required.'})
        if category_id is None:
            return Response(status=status.HTTP_400_BAD_REQUEST, data={"error": 'category_id field is required.'})
        category_terminals = []
        ct_query_set = CategoryTerminal.objects.filter(category_id=category_id)
        project_id = request.user.get('project_id')
        # category = get_object_or_404(Category, pk=category_id)

        t_ids = [t.terminal_id for t in ct_query_set]

        create_ids = list(set(terminal_ids).difference(t_ids))
        delete_ids = list(set(t_ids).difference(set(terminal_ids)))

        with transaction.atomic(using=f"prj{project_id}db"):
            # 创建关联
            for terminal_id in create_ids:
                # terminal = get_object_or_404(Terminal, pk=terminal_id)
                if not ct_query_set.filter(terminal_id=terminal_id).exists():
                    category_terminals.append(CategoryTerminal(terminal_id=terminal_id, category_id=category_id))
            try:
                CategoryTerminal.objects.bulk_create(category_terminals)
            except IntegrityError:
                return Response(status=status.HTTP_400_BAD_REQUEST, data={"error": "foreign key error"})

            # 删除关联
            ct_query_set.filter(terminal_id__in=delete_ids).delete()

        return Response({"status": status.HTTP_200_OK, "data": None})

    def list(self, request, *args, **kwargs):
        category_id = request.query_params.get('category_id', None)
        simple = request.query_params.get('simple', None)
        is_web = request.query_params.get('web', None)
        if category_id is None or category_id == '0':
            # return Response(status=status.HTTP_400_BAD_REQUEST, data={"error": 'category_id query field is required.'})
            terminals = Terminal.objects.filter(device__project_id__isnull=False, show_en=True).order_by(Convert('terminals.nick_name', 'gbk'))
            if category_id == '0':  # 未配置终端
                # 根据访问端查询未配置终端
                if is_web is not None and (is_web == 'true' or is_web == '1'):
                    acc_end = 20
                else:
                    acc_end = request.query_params.get('acc_end', 10)
                terminals = terminals.exclude(
                    id__in=CategoryTerminal.objects.filter(
                        category__acc_end=acc_end
                    ).values_list('terminal_id', flat=True)
                )
            total = terminals.count()
            total_offline = terminals.filter(online=False).count()
            total_run = terminals.filter(online=True, sw_on=True).count()
            total_stop = terminals.filter(online=True, sw_on=False).count()
            total_fault = terminals.filter(online=True, in_fault=True).count()
            total_alarm = terminals.filter(online=True, in_alarm=True).count()
            terminals = self.paginate_queryset(terminals)

            mini = request.query_params.get('mini', None)
            if mini is not None:
                serializer = MiniTerminalSerializer(terminals, many=True)
            elif simple is not None and simple != '0':
                serializer = SimpleTerminalSerializer(terminals, many=True, context={'request': request})
            else:
                serializer = TerminalSerializer(terminals, many=True, context={'request': request})
            return Response({
                'total': total,
                'total_run': total_run,
                'total_stop': total_stop,
                'total_offline': total_offline,
                'total_alarm': total_alarm,
                'total_fault': total_fault,
                'count': total,
                'terminals': serializer.data
            })
        # cts = CategoryTerminal.objects.filter(category_id=category_id)

        try:
            category = Category.objects.get(pk=category_id)
            if category.acc_end == 20:
                # web 返回所有终端
                queryset = Terminal.objects.all()
            else:
                # 小程序只返回 show_en=True
                queryset = Terminal.objects.filter(show_en=True)

            children_id = Category.objects.get_children_id(category_id)
            terminal_ids = list(CategoryTerminal.objects.filter(category_id__in=[category_id] + children_id).values_list('terminal_id', flat=True))
            terminals = queryset.filter(id__in=terminal_ids)
            terminals = terminals.order_by(Convert('terminals.nick_name', 'gbk'))

        # try:
        #     category = Category.objects.get(pk=category_id)
        #     terminals = category.terminals.get_queryset().filter(device__project_id__isnull=False, show_en=True).order_by(
        #         Convert('terminals.nick_name', 'gbk'))
        except Category.DoesNotExist:
            if is_number(category_id) and int(category_id) == 0:
                bind_terminals = CategoryTerminal.objects.values_list('terminal_id', flat=True)
                terminals = Terminal.objects.exclude(id__in=bind_terminals)
            else:
                return Response(status=status.HTTP_404_NOT_FOUND, data={'error': '终端类型不存在'})

        # total = CategoryTerminal.objects.filter(category_id=category_id).count()
        # terminals = category.terminals.get_queryset().filter(device__project_id__isnull=False, show_en=True).order_by(
        #     Convert('terminals.nick_name', 'gbk'))

        in_alarm = request.query_params.get('in_alarm', None)
        in_fault = request.query_params.get('in_fault', None)
        is_online = request.query_params.get('is_online', None)
        sw_on = request.query_params.get('sw_on', None)
        online = request.query_params.get('online', None)
        terminal_type = request.query_params.get('terminal_type', None)
        terminal_label = request.query_params.get("terminal_label", None)
        search = request.query_params.get('search', None)
        min_temp = request.query_params.get('min_temp', None)
        max_temp = request.query_params.get('max_temp', None)
        local_min_temp = request.query_params.get('local_min_temp', None)
        local_max_temp = request.query_params.get('local_max_temp', None)
        local_min_humi = request.query_params.get('local_min_humi', None)
        local_max_humi = request.query_params.get('local_max_humi', None)
        group_id = request.query_params.get('group_id', None)
        run_mode = request.query_params.get('run_mode', None)

        if run_mode is not None:

            cs_macs = ColdSource.objects.all().values_list('mac', flat=True).distinct()
            cs_tids = list(Terminal.objects.filter(device__mac__in=cs_macs, show_en=True,
                                                   idx__isnull=True).values_list('id', flat=True).distinct())
            # 冷源的运行状态 identifier 有两种：等于 RunMode 或以 SysRunMode 结尾
            cs_tas = TerminalAttribute.objects.filter(Q(identifier='RunMode') |
                                                      Q(identifier__endswith='SysRunMode'),
                                                      terminal_id__in=cs_tids)

            t_tids = list(Terminal.objects.filter(terminal_type=10, show_en=True).values_list('id', flat=True))
            # use_for=20 是设置运行模式的参数，一些设备类型的设置和实质运行模式参数是分开的，所以不能在这里使用use_for=20
            t_tas = TerminalAttribute.objects.filter(
                Q(identifier='RunMode') | Q(identifier='SysRunMode') | Q(identifier='FCURunningMode') |
                Q(identifier__endswith='_RunMode') | Q(identifier='CoolOrWarmSelect') | (Q(identifier='model')),
                terminal_id__in=t_tids, terminal__show_en=True)

            union_tas = list(cs_tas.union(t_tas))

            # 去除不相关的参数
            union_ap_ids = [ta.attribute_prototype_id for ta in union_tas]
            aps = AttributePrototype.objects.filter(pk__in=union_ap_ids)
            union_tas = [ta for ta in union_tas if (('供冷' in aps.get(
                pk=ta.attribute_prototype_id).options) or ('制冷' in aps.get(pk=ta.attribute_prototype_id).options))]

            cooling_tas = [ta for ta in union_tas if ta.value in ['制冷', '供冷']]
            heating_tas = [ta for ta in union_tas if ta.value in ['制暖', '供暖']]
            others_tas = set(union_tas).difference(set(cooling_tas).union(set(heating_tas)))

            target_ids = []
            if run_mode == '制冷':
                target_ids = [ta.terminal_id for ta in cooling_tas]
            elif run_mode == '制暖':
                target_ids = [ta.terminal_id for ta in heating_tas]
            elif run_mode == '其他':
                target_ids = [ta.terminal_id for ta in others_tas]

            terminals = terminals.filter(pk__in=target_ids)

        if in_alarm is not None:
            terminals = terminals.filter(in_alarm=in_alarm)

        if in_fault is not None:
            terminals = terminals.filter(in_fault=in_fault)

        if is_online is not None:
            terminals = terminals.filter(online=(False if is_online == '0' else True))

        if online is not None:
            terminals = terminals.filter(online=online)

        if sw_on is not None:
            terminals = terminals.filter(sw_on=sw_on)

        if terminal_type is not None:
            terminals = terminals.filter(terminal_type__in=terminal_type.split(','))

        if terminal_label is not None:
            terminals = terminals.filter(terminal_label=terminal_label)

        if search is not None:
            terminals = terminals.filter(Q(nick_name__contains=search) | Q(device__mac__icontains=search) | Q(device__address__icontains=search))

        if min_temp is not None and is_number(min_temp):
            terminals = terminals.filter(terminalattribute__value__gte=Cast(Value(min_temp), FloatField()), terminalattribute__use_for=10)

        if max_temp is not None and is_number(max_temp):
            terminals = terminals.filter(terminalattribute__value__lte=Cast(Value(max_temp), FloatField()), terminalattribute__use_for=10)

        if local_min_temp is not None and is_number(local_min_temp):
            terminals = terminals.filter(terminalattribute__value__gte=Cast(Value(local_min_temp), FloatField()), terminalattribute__use_for=50)

        if local_max_temp is not None and is_number(local_max_temp):
            terminals = terminals.filter(terminalattribute__value__lte=Cast(Value(local_max_temp), FloatField()), terminalattribute__use_for=50)

        if local_min_humi is not None and is_number(local_min_humi):
            terminals = terminals.filter(terminalattribute__value__gte=Cast(
                Value(local_min_humi), FloatField()), terminalattribute__identifier='LocalRH')

        if local_max_humi is not None and is_number(local_max_humi):
            terminals = terminals.filter(terminalattribute__value__lte=Cast(
                Value(local_max_humi), FloatField()), terminalattribute__identifier='LocalRH')

        if group_id is not None:
            terminal_model = ContentType.objects.get_for_model(Terminal)
            group_terminal_ids = list(GroupDevice.objects.filter(group_id=group_id, content_type=terminal_model).values_list('object_id', flat=True))
            terminals = terminals.filter(id__in=group_terminal_ids)

        total = terminals.count()
        total_offline = terminals.filter(online=False).count()
        total_run = terminals.filter(online=True, sw_on=True).count()
        total_stop = terminals.filter(online=True, sw_on=False).count()
        total_fault = terminals.filter(online=True, in_fault=True).count()
        total_alarm = terminals.filter(online=True, in_alarm=True).count()

        dp_ids = set(terminals.values_list('device_prototype_id', flat=True).distinct())
        sub_dp_ids = set([terminal.device_prototype_id for terminal in terminals.filter(idx__isnull=False)])
        dp_ids = dp_ids - sub_dp_ids
        parent_dp_ids = set([sub_dp.parent_id for sub_dp in DevicePrototype.objects.filter(id__in=sub_dp_ids) if sub_dp.parent_id])
        terminal_types = set(terminals.values_list('terminal_type', flat=True).distinct())

        mini = request.query_params.get('mini', None)
        if mini is not None:
            terminals = self.paginate_queryset(terminals)
            serializer = MiniTerminalSerializer(terminals, many=True)
            data = serializer.data

        elif simple is not None and simple != '0':
            terminals = self.paginate_queryset(terminals)
            serializer = SimpleTerminalSerializer(terminals, many=True, context={'request': request, 'key_attr': True})
            data = serializer.data
        else:
            # 小程序处理，返回终端所绑定的category_id
            category_terminal_qs = CategoryTerminal.objects.only('category_id')
            category_terminal_prefetch = Prefetch(
                'categoryterminal_set',  # 默认的反向关系名称
                queryset=category_terminal_qs,
                to_attr='category_list'  # 将结果存储到 category_list 中
            )
            terminals = terminals.prefetch_related(category_terminal_prefetch)
            terminals = self.paginate_queryset(terminals)

            serializer = TerminalSerializer(terminals, many=True, context={'request': request})
            data = serializer.data
            for terminal_data, terminal in zip(data, terminals):
                # 获取与 Terminal 相关联的 category_ids
                category_ids = [ct.category_id for ct in terminal.category_list]
                terminal_data['category_ids'] = category_ids

        return Response({
            'count': total,
            'total': total,
            'total_run': total_run,
            'total_stop': total_stop,
            'total_offline': total_offline,
            'total_alarm': total_alarm,
            'total_fault': total_fault,
            'terminals': data,
            'dp_ids': set(dp_ids | parent_dp_ids),
            'terminal_types': terminal_types
        })


class TerminalStatsView(viewsets.ModelViewSet):
    renderer_classes = (SyJSONRender,)
    serializer_class = TerminalSerializer

    @action(detail=True, methods=['get'])
    def get_authenticators(self):
        if 'intapi' in self.request.path:
            return [IntapiAuth(), ]
        return [LegacyAuth(), ]

    def list(self, request, *args, **kwargs):
        terminal_type = request.query_params.get('terminal_type', 10)
        temp_t = request.query_params.get('temp_t', None)
        from_at = request.query_params.get('from', None)
        till_at = request.query_params.get('till', None)

        if from_at is not None and till_at is not None:
            from_dt = datetime.datetime.strptime(from_at, '%Y%m%d%H%M%S')
            till_dt = datetime.datetime.strptime(till_at, '%Y%m%d%H%M%S')
            terminal_cooling_stats = TerminalCoolingStats.objects.filter(created_at__range=[from_dt, till_dt])
            histories = [{
                'cic': stat.cic,
                'cuc': stat.cuc,
                'created_at': datetime.datetime.strftime(stat.created_at, '%Y-%m-%d %H:00')
            } for stat in terminal_cooling_stats]
            return Response({"histories": histories})

        else:
            if temp_t is None:
                return Response(status=status.HTTP_400_BAD_REQUEST, data={"error": 'temp_t query field is required.'})

            temp_t = temp_t.split(',')

            terminals = list(Terminal.objects.filter(terminal_type=terminal_type, show_en=True))
            terminal_ids = [t.id for t in terminals]
            tas = list(TerminalAttribute.objects.filter(
                Q(identifier__contains='LocalTemp') | Q(identifier__contains='temperature') | Q(identifier='ReturnAirTemp') |
                Q(identifier__endswith='RATemp') | Q(identifier__endswith='AHURATemp') | Q(identifier__endswith='_SATemp'),
                terminal_id__in=terminal_ids))
            # tas = list(TerminalAttribute.objects.filter(use_for=50, terminal_id__in=terminal_ids))

            device_ids = [t.device_id for t in terminals]
            created_at = datetime.datetime.now() - datetime.timedelta(hours=24)
            times = []
            temps = []
            for i in range(24):
                times.append(created_at)
                result = DeviceHourlyStat.objects.filter(device_id__in=device_ids, identifier='LocalTemp',
                                                         created_at=created_at.strftime('%Y-%m-%d %H:59:59')
                                                         ).exclude(avg__isnull=True).aggregate(avg=Avg('avg'))
                avg = result.get('avg')
                temps.append(round(avg, 2) if is_number(avg) else 0)
                created_at = created_at + datetime.timedelta(hours=1)

            hourly_stats = {'temp': temps, 'time': times}

            sw_on = len(list(filter(lambda x: x.online and x.sw_on, terminals)))

            temp_sum, temp_count = 0, 0
            stats = [0, 0, 0, 0]

            label_terminal_stats = []
            terminal_labels = set([t.terminal_label for t in terminals])

            cic, cuc = 0, 0
            for terminal_label in terminal_labels:
                label_stats = [0, 0, 0, 0]
                label_terminals = list(filter(lambda x: x.terminal_label == terminal_label, terminals))
                valid_label_terminals = list(filter(lambda x: x.online and x.sw_on, label_terminals))

                label_tas = []
                for terminal in valid_label_terminals:
                    terminal_attrs = list(filter(lambda x: x.terminal_id == terminal.id, tas))
                    temp_tas = list(filter(lambda x: 'LocalTemp' in x.identifier or x.identifier == 'temperature', terminal_attrs))
                    if len(temp_tas) == 0:
                        temp_tas = list(filter(lambda x: x.identifier == 'ReturnAirTemp' or x.identifier.endswith('_RATemp')
                                                         or x.identifier.endswith('_EAFRATemp') or x.identifier == 'RATemp', terminal_attrs))
                    if len(temp_tas) == 0:
                        temp_tas = list(filter(lambda x: x.identifier.endswith('_SATemp'), terminal_attrs))

                    if len(temp_tas):
                        label_tas.append(temp_tas[0])

                    if float(temp_tas[0].value) < 50:
                        temp_ta = TerminalAttribute.objects.filter(use_for=10, terminal_id=terminal.id).last()
                        if temp_ta is not None:
                            diff = float(temp_tas[0].value) - float(temp_ta.value)
                            cic += diff
                            cuc += abs(diff)

                for ta in label_tas:
                    if ta.value and is_number(ta.value):
                        value = float(ta.value)
                        if value > 0:
                            temp_sum += value
                            temp_count += 1

                            # temp_t = [22, 24, 27]
                            # value < 22
                            if value < float(temp_t[0]):
                                label_stats[0] += 1
                                stats[0] += 1
                            # value >= 22 and value < 24
                            elif value < float(temp_t[1]):
                                label_stats[1] += 1
                                stats[1] += 1

                            # value >= 24 and value < 27
                            elif value < float(temp_t[2]):
                                label_stats[2] += 1
                                stats[2] += 1

                            # value >= 27
                            elif value >= float(temp_t[2]):
                                label_stats[3] += 1
                                stats[3] += 1

                label_terminal_stats.append({
                    "name": terminal_label,
                    "sw_on": len(valid_label_terminals),
                    "total": len(label_terminals),
                    "stats": label_stats
                })

            if 'intapi' in self.request.path:
                return Response({
                    'stats': stats,
                    'sw_on': sw_on,
                    'total': len(terminals),
                    'sub_stats': label_terminal_stats,
                    'avg': round(temp_sum / temp_count, 2) if temp_count else '',
                    'cic': round(cic, 2),
                    'cuc': round(cuc, 2)
                })
            else:
                return Response({
                    "hourly_stats": hourly_stats,
                    'stats': stats,
                    'sw_on': sw_on,
                    'total': len(terminals),
                    'term_list': label_terminal_stats,
                    'avg': round(temp_sum / temp_count, 2) if temp_count else '',
                    'cic': round(cic, 2),
                    'cuc': round(cuc, 2)
                })


class TaUseFor(viewsets.ModelViewSet):
    def update(self, request, *args, **kwargs):
        terminals = Terminal.objects.all()
        for terminal in terminals:
            tas = TerminalAttribute.objects.filter(terminal=terminal)
            for ta in tas:
                ap = AttributePrototype.objects.filter(pk=ta.attribute_prototype_id).last()
                if ap:
                    use_for = TerminalAttribute.objects.get_use_for(ap)
                    ta.use_for = use_for
                    ta.save()

        return Response({})


class TerminalAttributeView(viewsets.ModelViewSet):
    serializer_class = TerminalAttributeSerializer
    # renderer_classes = (SyJSONRender,)

    @action(detail=True, methods=['get'])
    def get_authenticators(self):
        if 'intapi' in self.request.path:
            return [IntapiAuth(), ]
        return [LegacyAuth(), ]

    def get_object(self):
        try:
            return TerminalAttribute.objects.get(pk=self.kwargs['pk'])
        except TerminalAttribute.DoesNotExist:
            raise exceptions.NotFound('TerminalAttribute not found!')

    def get_queryset(self):
        terminal_id = self.request.query_params.get('terminal_id')
        search = self.request.query_params.get('search', None)
        use_for = self.request.query_params.get('use_for', None)
        at_id = self.request.query_params.get('at_id', None)

        queryset = TerminalAttribute.objects.filter(terminal_id=terminal_id)

        if search is not None:
            queryset = queryset.filter(identifier__icontains=search)
        if use_for is not None:
            queryset = queryset.filter(use_for=use_for)
        if at_id is not None:
            ap_ids = list(AttributePrototype.objects.filter(attribute_type_id=at_id).values_list('id', flat=True))
            queryset = queryset.filter(attribute_prototype_id__in=ap_ids)

        return queryset

    def list(self, request, *args, **kwargs):
        terminal_id = self.request.query_params.get('terminal_id', None)
        if terminal_id is None:
            return Response({
                'status': status.HTTP_400_BAD_REQUEST,
                'error': 'terminal_id is required.'
            })

        # data = super(TerminalAttributeView, self).list(request, *args, **kwargs).data
        per_page = request.query_params.get('per_page', None)
        if per_page is not None:
            queryset = self.paginate_queryset(self.get_queryset())
            ap_ids = [ta.attribute_prototype_id for ta in queryset]
        else:
            ap_ids = list(self.get_queryset().values_list('attribute_prototype_id', flat=True))
        terminals = list(Terminal.objects.filter(pk=terminal_id))
        tas = TerminalAttribute.objects.query_mult_object_list(terminals, ap_ids, request.user['project_id'])

        ap_cache = {}
        results = []
        for ta in tas:
            ap = ap_cache.get(ta.attribute_prototype_id, None)
            if ap is None:
                ap = AttributePrototype.objects.filter(pk=ta.attribute_prototype_id).last()
                if ap is not None:
                    ap_cache[ta.attribute_prototype_id] = ap
            if ap is not None:
                serializer = self.serializer_class(ta).data
                serializer['attribute_prototype'] = SimpleAttributePrototypeSerializer(ap).data
                results.append(serializer)
        # serializer = TerminalAttributeSerializer(tas, many=True)

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'terminal_attributes': results,
            },
            'count': self.get_queryset().count()
        })

    def partial_update(self, request, *args, **kwargs):
        terminal_attribute = get_object_or_404(TerminalAttribute, pk=kwargs.get('pk'))

        serializer = self.get_serializer(terminal_attribute, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        return Response({
            'status': status.HTTP_200_OK,
            'terminal_attribute': serializer.data
        })

    def create(self, request, *args, **kwargs):
        terminal_id = request.data.get('terminal_id', None)
        if terminal_id is None:
            raise exceptions.ValidationError(detail='目标终端不能为空！')

        da_ids = request.data.get('da_ids', None)
        if da_ids is None:
            raise exceptions.ValidationError(detail='设备属性不能为空！')

        da_ids = da_ids.split(',')

        for da in da_ids:
            da = DeviceAttribute.objects.get(pk=da)
            ap = AttributePrototype.objects.get(pk=da.attribute_prototype_id)

            if not TerminalAttribute.objects.filter(terminal_id=terminal_id, attribute_prototype_id=ap.id).exists():
                TerminalAttribute.objects.create(
                    terminal_id=terminal_id,
                    attribute_prototype_id=ap.id,
                    identifier=ap.identifier,
                    value=da.value,
                    use_for=TerminalAttribute.objects.get_use_for(ap)
                )

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def destroy(self, request, *args, **kwargs):
        super().destroy(request, *args, **kwargs)
        return Response({
            'status': status.HTTP_200_OK,
            "data": None
        })


class RoomConditionView(viewsets.ModelViewSet):

    @classmethod
    def get_attr_var(cls, identifier):
        if 'LocalTemp' in identifier:
            return 'local_temp'
        if 'LocalRH' in identifier:
            return 'local_rh'
        if 'ExtInput' in identifier or 'RoomCard' in identifier:
            return 'room_card'
        if 'RunningMode' in identifier or 'RunMode' in identifier:
            return 'run_mode'
        if 'SW' in identifier:
            return 'sw'
        if 'FanSpeed' in identifier:
            return 'fan_speed'
        if 'TargetTemp' in identifier:
            return 'target_temp'
        if identifier == 'SpecialFunc':
            return 'special_func'

        return None

    def list(self, request, *args, **kwargs):
        project_id = request.user.get('project_id')
        floor_categories = Category.objects.filter(Q(name__contains='F') |
                                                   Q(name__contains='楼') |
                                                   Q(name__contains='层')).filter(acc_end=20).order_by('seq')

        floors = []
        # 缓存 AttributePrototype
        ap_cache = {}
        # 缓存 DevicePrototype
        dp_cache = {}

        room_names = []
        floor_names = []
        for category in floor_categories:
            # 尝试从终端类型名解析出楼层号码
            floor = re.search(r'^-?\d+', category.name).group()
            rooms = []
            if floor:
                terminals = category.terminals

                query = (Q(identifier='LocalTemp') | Q(identifier__endswith='_LocalTemp') |
                         Q(identifier='LocalRH') | Q(identifier__endswith='_LocalRH') |
                         Q(identifier='ExtInput') | Q(identifier__endswith='_RoomCard') |
                         Q(identifier__endswith='RunningMode') | Q(identifier__endswith='_RunMode') |
                         Q(identifier='FCUSW') | Q(identifier__endswith='_SW') |
                         Q(identifier='FCUFanSpeed') | Q(identifier__endswith='_FanSpeed') |
                         Q(identifier='FCUTargetTemp') | Q(identifier__endswith='_TargetTemp') |
                         Q(identifier='SpecialFunc'))
                ta_queryset = list(TerminalAttribute.objects.filter(Q(query), terminal_id__in=terminals.values_list('id')))

                ap_ids = list(set([ta.attribute_prototype_id for ta in ta_queryset]))
                floor_tas = TerminalAttribute.objects.query_mult_object_list(terminals.all(), ap_ids, project_id)
                for terminal in terminals.all():
                    tas = list(filter(lambda x: terminal.id == x.terminal_id, floor_tas))
                    if len(tas) == 0:
                        tas = list(filter(lambda x: terminal.id == x.terminal_id, ta_queryset))
                    # terminal_queryset = list(filter(lambda x: x['terminal_id'] == terminal.id, ta_queryset))
                    attrs = []

                    # 处理终端参数
                    for attr in tas:
                        # ap = ap_cache.get(attr['attribute_prototype_id'], None)
                        ap = ap_cache.get(attr.attribute_prototype_id, None)
                        if ap is None:
                            ap = AttributePrototype.objects.get(pk=attr.attribute_prototype_id)
                            ap_cache[ap.id] = ap

                        # attr = TerminalAttribute.objects.query_object_by_ap(terminal, ap)
                        # 温控器v2酒店版，特殊功能为湿度控制时，运行模式的值默认显示为除湿
                        if (terminal.device_prototype_id == 223 and ap.identifier == 'FCURunningMode' and
                                TerminalAttribute.objects.query_object_by_idf(terminal, 'SpecialFunc').value == '湿度控制'):
                            value = '除湿'
                        elif ap.data_type != 20:
                            value = attr.value
                        else:
                            value = round(float(attr.value), 2)

                        attrs.append({
                            # **AttributePrototypeSerializer(ap).data
                            'id': ap.id,
                            'identifier': ap.identifier,
                            'name': ap.name,
                            'data_type': ap.data_type,
                            'options': ap.options,
                            'var': self.get_attr_var(ap.identifier),
                            'value': value
                        })

                    # 尝试从终端昵称解析出房间名
                    if terminal.nick_name.endswith('（后勤）'):
                        room_name = '后勤'
                    elif terminal.nick_name.startswith(floor):
                        try:
                            num = re.search(r'^\d+', terminal.nick_name).group().replace(floor, '', 1)
                            if num:
                                room_name = '%02d' % int(num)
                            else:
                                room_name = terminal.nick_name.replace(floor, '').replace('F', '').replace('楼', '').replace('层', '')
                        except Exception as e:
                            print(f'fail to extract room name from terminal nick_name: {terminal.id}-{terminal.nick_name}, error: {e.__str__()}')
                            room_name = terminal.nick_name
                    else:
                        room_name = terminal.nick_name

                    # 将 “工作间1” “工作间2” 之类的房间名归类为 “工作间”
                    if not room_name[0].isdigit():
                        while room_name[-1].isdigit():
                            room_name = room_name[:-1]

                    room_names.append(room_name)

                    # 终端的设备类型，用于判断 custz_detail
                    dp = dp_cache.get(terminal.device_prototype_id, None)
                    if dp is None:
                        dp = terminal.device_prototype
                        dp_cache[dp.id] = dp

                    rooms.append({
                        'id': terminal.id,
                        'device_id': terminal.device_id,
                        'custz_detail': True if dp.web_content else False,
                        'idx': terminal.idx,
                        'prefix': terminal.prefix,
                        'nick_name': terminal.nick_name,
                        'in_alarm': terminal.in_alarm,
                        'in_fault': terminal.in_fault,
                        'online': terminal.online,
                        'sw_on': terminal.sw_on,
                        'room_name': room_name,
                        'attrs': attrs,
                        'device_prototype': {
                            'id': dp.id,
                            'name': dp.name,
                            'uni_name': dp.uni_name
                        }
                    })

                rooms.sort(key=lambda x: x.get('room_name', ''))

                floor = int(floor)
                floor_names.append(floor)

                floors.append({
                    'floor': floor,
                    'rooms': rooms
                })

        # 排序房间名
        room_names = list(set(room_names))
        room_names.sort()
        if '后勤' in room_names:
            room_names.remove('后勤')
            room_names.append('后勤')

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'floors': floors,
                'layout': {
                    'floors': floor_names,
                    'rooms': room_names
                }
            }
        })

class NameplateView(viewsets.ModelViewSet):
    serializer_class = TerminalNameplateSerializer

    def get_queryset(self):
        queryset = TerminalNameplate.objects.all()

        terminal_id = self.request.query_params.get('terminal_id', None)
        if terminal_id is not None:
            terminal_np = queryset.filter(terminal_id=terminal_id).order_by('created_at').last()
            if terminal_np:
                queryset = queryset.filter(np_id=terminal_np.np_id)
            else:
                queryset = queryset.filter(np_id__isnull=True)

        np_id = self.request.query_params.get('np_id', None)
        if np_id is not None:
            queryset = queryset.filter(np_id=np_id)

        queryset = queryset.filter(terminal_id__isnull=True).order_by('-created_at')

        return queryset

    def list(self, request, *args, **kwargs):
        data = super().list(request, *args, **kwargs).data

        nameplate_list = []
        for nameplate in data['results']:
            terminal_ids = TerminalNameplate.objects.filter(np_id=nameplate['np_id']).values_list('terminal_id', flat=True)
            terminals = []
            if terminal_ids:
                terminals = [
                    {'id': terminal['id'], 'nick_name': terminal['nick_name']}
                    for terminal in Terminal.objects.filter(id__in=terminal_ids).values('id', 'nick_name')
                ]
            np_img = []
            if nameplate['np_img']:
                np_img = [
                    {'id': img, "url": ImageAPI.get_url(request, image_id=img, size='originals')}
                    for img in nameplate['np_img'].split(',')
                ]
            nameplate_list.append({
                # 铭牌ID
                'np_id': nameplate['np_id'],
                # 铭牌名称
                'np_name': nameplate['np_name'],
                # 铭牌内容
                'np_content': nameplate['np_content'],
                # 铭牌相片
                'np_img': np_img,
                # 关联终端
                'terminals': terminals
            })

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'nameplates': nameplate_list
            },
            'total': data['count']
        })

    def create(self, request, *args, **kwargs):
        np_id = AuthUtils.generate_random_comm_char(9)
        np_name = request.data.get('np_name', None)
        np_content = request.data.get('np_content', None)
        np_img = request.data.get('np_img', None)
        terminal_ids = request.data.get('terminal_ids', None)
        terminal_ids = terminal_ids.split(',') if terminal_ids is not None else []
        terminal_ids = terminal_ids + [0]

        TerminalNameplate.objects.filter(terminal_id__in=terminal_ids).delete()
        objects_to_create = [
            TerminalNameplate(terminal_id=(terminal_id if terminal_id else None), np_id=np_id, np_name=np_name, np_content=np_content, np_img=np_img)
            for terminal_id in terminal_ids
        ]
        TerminalNameplate.objects.bulk_create(objects_to_create)

        nameplate_list = TerminalNameplate.objects.filter(np_id=np_id)
        if nameplate_list:
            terminal_ids = nameplate_list.values_list('terminal_id', flat=True)
            terminals = []
            if terminal_ids:
                terminals = [
                    {'id': terminal['id'], 'nick_name': terminal['nick_name']}
                    for terminal in Terminal.objects.filter(id__in=terminal_ids).values('id', 'nick_name')
                ]

            np_img = []
            if nameplate_list[0].np_img:
                np_img = [
                    {'id': img, "url": ImageAPI.get_url(request, image_id=img, size='originals')}
                    for img in nameplate_list[0].np_img.split(',')
                ]

            nameplate = {
                # 铭牌ID
                'np_id': nameplate_list[0].np_id,
                # 铭牌名称
                'np_name': nameplate_list[0].np_name,
                # 铭牌内容
                'np_content': nameplate_list[0].np_content,
                # 铭牌相片
                'np_img': np_img,
                # 关联终端
                'terminals': terminals
            }

            return Response({
                'status': status.HTTP_200_OK,
                'data': {
                    'nameplate': nameplate
                }
            })

        return Response({
            'status': status.HTTP_500_INTERNAL_SERVER_ERROR,
            'error': "创建数据失败"
        })

    def partial_update(self, request, *args, **kwargs):
        np_id = request.data.get('np_id', None)
        np_name = request.data.get('np_name', None)
        np_content = request.data.get('np_content', None)
        np_img = request.data.get('np_img', None)
        terminal_ids = request.data.get('terminal_ids', None)
        terminal_ids = terminal_ids.split(',') if terminal_ids is not None else []
        terminal_ids = terminal_ids + [0]

        TerminalNameplate.objects.filter(Q(terminal_id__in=terminal_ids) | Q(np_id=np_id)).delete()
        objects_to_create = [
            TerminalNameplate(terminal_id=(terminal_id if terminal_id else None), np_id=np_id, np_name=np_name, np_content=np_content, np_img=np_img)
            for terminal_id in terminal_ids
        ]
        TerminalNameplate.objects.bulk_create(objects_to_create)

        nameplate_list = TerminalNameplate.objects.filter(np_id=np_id)
        if nameplate_list:
            terminal_ids = nameplate_list.values_list('terminal_id', flat=True)
            terminals = []
            if terminal_ids:
                terminals = [
                    {'id': terminal['id'], 'nick_name': terminal['nick_name']}
                    for terminal in Terminal.objects.filter(id__in=terminal_ids).values('id', 'nick_name')
                ]

            np_img = []
            if nameplate_list[0].np_img:
                np_img = [
                    {'id': img, "url": ImageAPI.get_url(request, image_id=img, size='originals')}
                    for img in nameplate_list[0].np_img.split(',')
                ]

            nameplate = {
                # 铭牌ID
                'np_id': nameplate_list[0].np_id,
                # 铭牌名称
                'np_name': nameplate_list[0].np_name,
                # 铭牌内容
                'np_content': nameplate_list[0].np_content,
                # 铭牌相片
                'np_img': np_img,
                # 关联终端
                'terminals': terminals
            }

            return Response({
                'status': status.HTTP_200_OK,
                'data': {
                    'nameplate': nameplate
                }
            })

        return Response({
            'status': status.HTTP_500_INTERNAL_SERVER_ERROR,
            'error': "修改数据失败"
        })

    def destroy(self, request, *args, **kwargs):
        np_id = request.data.get('np_id', None)

        if np_id is not None:
            TerminalNameplate.objects.filter(np_id=np_id).delete()
            return Response({
                'status': status.HTTP_200_OK,
                'data': None
            })

        return Response({
            'status': status.HTTP_500_INTERNAL_SERVER_ERROR,
            'error': "删除失败"
        })


class ScanRecognition(viewsets.ModelViewSet):

    def list(self, request, *args, **kwargs):
        img_url = request.query_params.get('img_url', None)
        data = WechatApi.ocr(img_url)

        return Response({
            'status': status.HTTP_200_OK,
            'data': data
        })

class TerminalLabelView(viewsets.ModelViewSet):

    def list(self, request, *args, **kwargs):
        labels = list(Terminal.objects.filter(show_en=True, idx__isnull=True
                                              ).order_by('terminal_label').values_list('terminal_label', flat=True).distinct())
        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'terminal_labels': labels
            }
        })
