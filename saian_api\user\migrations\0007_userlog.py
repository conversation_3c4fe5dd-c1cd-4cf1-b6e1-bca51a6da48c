# Generated by Django 3.2.8 on 2022-11-10 12:01

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0006_webuser_msg_types'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_name', models.CharField(blank=True, max_length=255, null=True)),
                ('user_mobile', models.CharField(blank=True, max_length=255, null=True)),
                ('api_url', models.CharField(blank=True, max_length=255, null=True)),
                ('http_method', models.CharField(blank=True, max_length=255, null=True)),
                ('data', models.TextField(blank=True, null=True)),
                ('browser', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('ip_address', models.CharField(blank=True, max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('web_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='user.webuser')),
            ],
            options={
                'db_table': 'user_logs',
                'ordering': ['-created_at'],
            },
        ),
    ]
