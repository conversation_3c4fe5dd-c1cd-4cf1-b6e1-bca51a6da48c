"""
    冷源能效COP小时统计
"""
import datetime
import traceback

from django.core.management import BaseCommand, CommandError

from saian_api.coldsource.models import CsCop, CsEerAnalyse
from saian_api.dashboard.models import ProjectWeather
from saian_api.project.models import Project
from saian_api.regions.models import Region
from saian_api.scheduledtask.utils import get_projects, set_global_db
from saian_api.utils.httpapi.weather import WeatherApi

class Command(BaseCommand):
    help = "冷源能效COP小时统计"

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目ID列表
        projects = get_projects(self, options)

        now = datetime.datetime.now()

        for project_id in projects:
            try:
                # 设置全局数据库
                set_global_db(project_id)

                CsEerAnalyse.objects.clac_cop(now, 'hr')

            except CommandError:
                self.stderr.write(f"运行'冷源能效COP小时统计'任务失败，项目ID：{project_id}, 命令参数不合法！", ending='\n')
                continue
            except Exception as e:
                self.stderr.write(f"运行'冷源能效COP小时统计'任务失败，项目ID：{project_id}, error: {e.__str__()}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
                continue
