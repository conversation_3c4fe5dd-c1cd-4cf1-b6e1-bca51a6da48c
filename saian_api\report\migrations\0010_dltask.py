# Generated by Django 3.2.19 on 2024-05-08 10:04

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('report', '0009_reportconfigurer_do_minstat'),
    ]

    operations = [
        migrations.CreateModel(
            name='DlTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('web_user_id', models.BigIntegerField(null=True)),
                ('project_id', models.BigIntegerField(null=True)),
                ('device_id', models.BigIntegerField(null=True)),
                ('data_at', models.DateTimeField(null=True)),
                ('status', models.IntegerField()),
                ('name', models.CharField(max_length=255, null=True)),
                ('file_path', models.CharField(max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now=True)),
                ('updated_at', models.DateTime<PERSON>ield(auto_now=True)),
            ],
            options={
                'db_table': 'dl_tasks',
                'ordering': ['-created_at'],
            },
        ),
    ]
