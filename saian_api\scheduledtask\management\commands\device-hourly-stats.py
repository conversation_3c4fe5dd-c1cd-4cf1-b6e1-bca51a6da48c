"""
    设备小时统计
"""
import datetime
import json
import logging
import traceback

from django.core.management import BaseCommand, CommandError
from kombu.exceptions import EncodeError

from saian_api.devdefine.models import AttributePrototype
from saian_api.device.models import Device, ParamRecord, DeviceEvent, ParamRecordHistory
from saian_api.project.models import Project
from saian_api.report.models import ReportConfigurer, DeviceHourlyStat
from saian_api.scheduledtask.utils import get_projects, set_global_db
from saian_api.utils.db.Redis import RedisHelper
from saian_api.utils.tools import is_number

class Command(BaseCommand):
    help = '每小时统计设备的数据'

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目ID列表
        projects = get_projects(self, options)
        now = datetime.datetime.now()
        # now = datetime.datetime.strptime('20240401010500', '%Y%m%d%H%M%S')
        for project_id in projects:
            try:
                # 设置全局数据库
                begin_at = datetime.datetime.now()
                set_global_db(project_id)
                project = Project.objects.get(pk=project_id)

                self.stdout.write(f"设备小时统计开始: {project.name}", ending='\n')

                # 项目存在的设备类型
                prj_dp_ids = Device.objects.values_list('device_prototype_id', flat=True).distinct()

                # 报表中已配置的设备类型
                report_cfgs = list(ReportConfigurer.objects.filter(target_type__model='DevicePrototype', target_id__in=prj_dp_ids))
                dp_ids = [rc.target_id for rc in report_cfgs]

                # 所有相关设备
                devices = Device.objects.filter(device_prototype_id__in=dp_ids)

                # 查找所有相关属性类型
                ap_names = [f'attribute_prototype:{rc.target_id}_{rc.identifier}' for rc in report_cfgs]
                aps = AttributePrototype.objects.query_by_names(project_id, ap_names)

                one_hour_ago = now - datetime.timedelta(hours=1)
                # 上一小时设备相关事件
                device_events = {}

                for rc in report_cfgs:
                    rc_devices = list(filter(lambda x: x.device_prototype_id == rc.target_id, devices))
                    for device in rc_devices:
                        ap = next(filter(lambda x: x.device_prototype_id == device.device_prototype_id and x.identifier == rc.identifier, aps), None)
                        if ap is None:
                            ap = AttributePrototype.objects.filter(device_prototype_id=device.device_prototype_id, identifier=rc.identifier).last()
                        if ap is None:
                            msg = f'设备小时统计任务出错，找不到属性类型：target-id-{rc.target_id}, identifier-{rc.identifier}'
                            logging.error(msg)
                            # Notification.objects.send_debug_mail(device, msg)
                            continue
                        else:
                            if ap is not None and ap.data_type == 20:
                                # 计算值
                                if ap.formula:
                                    if ap.is_cum:
                                        # 计算值 + 累计值
                                        # 通过公式计算的参数，值是累计值（如：表读数）
                                        DeviceHourlyStatUtils.calc_cum_val_stat(device, ap, now)
                                    else:
                                        # 计算值 + 非累计值
                                        # 通过公式计算的值，值是非累计
                                        DeviceHourlyStatUtils.calc_val_stat(device, ap, now)
                                else:
                                    if ap.is_cum:
                                        # 非计算值 + 累计值
                                        DeviceHourlyStatUtils.calc_cum_val_stat(device, ap, now)
                                    else:
                                        # 非计算值 + 非累计值
                                        name = f'device_event_{(now - datetime.timedelta(hours=1)).strftime("%Y%m%d%H")}:{device.id}_{ap.identifier}'
                                        # 查询缓存的数据
                                        cache_values = RedisHelper.get_list(project_id, name, True)
                                        events = None

                                        if len(cache_values) == 0:
                                            # 如果没有缓存数据，就从事件表中查询
                                            events = device_events.get(device.id, None)
                                            if events is None:
                                                begin = one_hour_ago.strftime('%Y-%m-%d %H:00:00')
                                                end = one_hour_ago.strftime('%Y-%m-%d %H:59:59')

                                                events = list(DeviceEvent.objects.filter(device_id=device.id, created_at__range=(begin, end)
                                                                                         ).order_by('-created_at'))
                                                device_events[device.id] = events
                                        DeviceHourlyStatUtils.val_stat(device, ap, cache_values, events, now)

                self.stdout.write(f"设备小时统计任务完成: {project.name}, 耗时: {(datetime.datetime.now() - begin_at).seconds}秒", ending='\n')

            except CommandError:
                self.stderr.write(f"运行'设备小时统计'任务失败，项目ID：{project_id}，命令参数不合法！", ending='\n')
                continue
            except Exception as e:
                self.stderr.write(f"运行'设备小时统计'任务失败，项目ID：{project_id}, error: {e.__str__()}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
                continue


class DeviceHourlyStatUtils:
    @classmethod
    def calc_cum_val_stat(cls, device, ap, now):
        """
          计算值 + 累计值
        """
        result = 0.0
        one_hour_ago = now - datetime.timedelta(hours=1)

        # 从缓存查询
        current_name = f'param_record_{now.strftime("%Y%m%d%H")}:{device.id}_{ap.identifier}'
        current_reading = RedisHelper.get_value(device.project_id, current_name, True)
        prev_name = f'param_record_{one_hour_ago.strftime("%Y%m%d%H")}:{device.id}_{ap.identifier}'
        prev_reading = RedisHelper.get_value(device.project_id, prev_name, True)

        # 没有缓存，从 param_records 表查询
        if current_reading is None:
            current_hour = now.strftime('%Y-%m-%d %H:05:00')
            current_reading = ParamRecord.objects.get_by_hour(current_hour, device, ap.identifier)
            if current_reading is not None:
                current_reading = current_reading.value
        if prev_reading is None:
            pre_hour = one_hour_ago.strftime('%Y-%m-%d %H:05:00')
            prev_reading = ParamRecord.objects.get_by_hour(pre_hour, device, ap.identifier)
            if prev_reading is not None:
                prev_reading = prev_reading.value

        if prev_reading is not None and current_reading is not None:
            current_value = round(float(current_reading if current_reading != '' else 0), 2)
            prev_value = round(float(prev_reading if prev_reading != '' else 0), 2)
            result = current_value - prev_value

            # XXXX小于-1时，可能是换表，需要重新计算xx
            # XXXX算法：上个小时最大的读数减去上个小时的读数 + 当前小时的读数xx
            # result < 0时，视为仪表读数错误，result 为 -0.0。换表需要在项目视图重设读数，不在此处理。
            if result < 0:
                result = -0.0

        # 创建记录
        cls.cus_create(device, ap, result, now=now)

    @classmethod
    def calc_val_stat(cls, device, ap, now):
        """
          计算值 + 非累计值统计
          算法：查询上个小时的所有计算值集合，然后计算平均值
        """
        one_hour_ago = now - datetime.timedelta(hours=1)
        begin = one_hour_ago.strftime('%Y-%m-%d %H:00:00')
        end = one_hour_ago.strftime('%Y-%m-%d %H:59:59')
        records = ParamRecord.objects.filter(device_id=device.id, identifier=ap.identifier, created_at__range=(begin, end))

        if records.exists():
            values = [float(record.value) for record in records]
            avg_value = 0 if len(values) == 0 else sum(values) / len(values)
            cls.cus_create(device, ap, avg_value, max_value=str(max(values)), min_value=str(min(values)), now=now)

    @classmethod
    def val_stat(cls, device, ap, cache_values, device_events, now):
        """
          统计数值（非计算值、非累计值）类数据
          优先使用缓存的数据cache_values
          如果没有缓存数据，就从事件数据中解析
        """
        values = []

        if len(cache_values) or (device_events is not None and len(device_events)):
            # 当天COP处理，取最后一刻数值
            if 'Today' in ap.identifier and '当天' in ap.name:
                if len(cache_values):
                    value = float(cache_values[-1])
                    cls.cus_create(device, ap, value, min_value=str(value), max_value=str(value), now=now)
                else:
                    for record in device_events:
                        try:
                            data = json.loads(record.data)

                            if ap.identifier in data:
                                value = float(data[ap.identifier])
                                cls.cus_create(device, ap, value, min_value=str(value), max_value=str(value), now=now)
                                break
                        except Exception as e:
                            logging.warning(f'解析事件数据失败：{e.__str__()}')

            else:
                if len(cache_values):
                    values = [float(v) for v in cache_values if is_number(v)]
                else:
                    for record in device_events:
                        try:
                            data = json.loads(record.data)
                            if ap.identifier in data:
                                values.append(float(data[ap.identifier]))
                        except EncodeError:
                            logging.error(f'EncodeError: {record.data}')
                            continue
                        except ValueError:
                            logging.error(f'ValueError: {record.data}')
                            continue

                if len(values):
                    avg_value = sum(values) / len(values)
                    cls.cus_create(device, ap, avg_value, min_value=str(min(values)), max_value=str(max(values)), now=now)

    @staticmethod
    def cus_create(device, ap, value, min_value='--', max_value='--', now=None):
        value = round(value, 3)
        if now is None:
            now = datetime.datetime.now()
        created_at = (now - datetime.timedelta(hours=1)).strftime('%Y-%m-%d %H:59:59')
        dhs = DeviceHourlyStat.objects.filter(device_id=device.id, identifier=ap.identifier, created_at=created_at).last()
        if dhs is None:
            DeviceHourlyStat.objects.create(
                device_id=device.id,
                mac=device.mac,
                identifier=ap.identifier,
                avg=value,
                min=min_value,
                max=max_value,
                created_at=created_at
            )
        else:
            if dhs.avg != str(value):
                logging.info(f'\n{device.nick_name}, {ap.identifier}, {dhs.id}, {created_at}, old: {dhs.avg}, new: {value}')
                dhs.avg = value
                # dhs.save()
                pass
