# Generated by Django 3.2.8 on 2022-10-10 11:42

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('devdefine', '0007_attributeprototype_do_send'),
    ]

    operations = [
        migrations.CreateModel(
            name='DevicePrototypeRatio',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('params', models.CharField(blank=True, max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('device_prototype', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='devdefine.deviceprototype')),
            ],
            options={
                'db_table': 'dp_ratios',
            },
        ),
    ]
