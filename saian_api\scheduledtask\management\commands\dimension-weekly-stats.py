"""
    维度周统计
"""
import datetime
import re
import traceback

from django.core.management import BaseCommand, CommandError

from saian_api.dimension.models import DimensionDailyStat, DimensionWeeklyStat, DimensionAttribute, DimensionMonthlyStat
from saian_api.project.models import Project
from saian_api.scheduledtask.utils import get_projects, set_global_db
from saian_api.utils.report import do_dimension_stat_cce
from saian_api.utils.tools import is_number

def cus_create(dimension_id, dimension_attribute_id, last_week_end, value, min_value='--', max_value='--'):
    dws = DimensionWeeklyStat.objects.filter(dimension_id=dimension_id,
                                             dimension_attribute_id=dimension_attribute_id,
                                             created_at=last_week_end).last()
    if dws is None:
        DimensionWeeklyStat.objects.create(
            dimension_id=dimension_id,
            dimension_attribute_id=dimension_attribute_id,
            avg=value,
            min=min_value,
            max=max_value,
            created_at=last_week_end
        )
    else:
        if dws.avg != str(value):
            dws.avg = value
            dws.min = min_value
            dws.max = max_value
            # dws.save()

class Command(BaseCommand):
    help = '每周统计维度的数据'

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目ID列表
        projects = get_projects(self, options)
        now = datetime.datetime.now()

        # 每周一运行
        if now.isoweekday() != 1:
            return

        # 上一周的开始时间和结束时间
        begin = (now - datetime.timedelta(days=7)).replace(hour=0, minute=0, second=0, microsecond=0)
        end = (begin + datetime.timedelta(days=7)) - datetime.timedelta(seconds=1)

        for project_id in projects:
            try:
                # 设置全局数据库
                set_global_db(project_id)
                project = Project.objects.get(pk=project_id)

                self.stdout.write(f"维度周统计开始: {project.name}", ending='\n')

                do_dimension_stat_cce(cus_create, DimensionDailyStat.objects, begin, end)

                # 最后计算有公式的维度属性值
                for da in DimensionAttribute.objects.filter(formula__isnull=False):
                    da_ids = re.findall(r'{{(.*?)}}', da.formula)
                    da_values = []
                    for da_id in da_ids:
                        dms = DimensionWeeklyStat.objects.filter(created_at=end, dimension_attribute_id=da_id).last()
                        # da = DimensionAttribute.objects.get(pk=da_id)
                        da_values.append(float(dms.avg) if dms is not None and is_number(dms.avg) else 0)
                    # 构建计算表达式
                    eval_str = da.formula
                    for v in da_values:
                        eval_str = re.sub(r'{{(.*?)}}', f'{v}', eval_str, 1)

                    try:
                        value = eval(eval_str)
                        cus_create(da.dimension_id, da.id, end, value)

                    except Exception as e:
                        self.stdout.write(f'计算维度属性 {da.id}-{da.name} 公式时出错，表达式为: {eval_str}; ERROR: {e.__str__()}')

                self.stdout.write(f"维度周统计任务完成: {project.name}", ending='\n')

            except CommandError:
                self.stderr.write(f"运行'维度周统计'任务失败，项目ID：{project_id}，命令参数不合法！", ending='\n')
                continue
            except Exception as e:
                self.stderr.write(f"运行'维度周统计'任务失败，项目ID：{project_id}, error: {e.__str__()}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
                continue
