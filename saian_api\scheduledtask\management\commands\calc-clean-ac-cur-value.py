import datetime
import json
import traceback

from django.core.management import BaseCommand, CommandError
from django.db.models import Q, Sum

from saian_api.devdefine.models import AttributePrototype
from saian_api.device.models import Device, ParamRecordHistory, ParamRecord, DeviceAttribute
from saian_api.dimension.models import DimensionAttribute, DimensionHourlyStat, DimensionDailyStat, DimensionMonthlyStat
from saian_api.scheduledtask.utils import get_projects, set_global_db
from saian_api.utils.db.Redis import RedisHelper

class Command(BaseCommand):
    help = "人医洁净空调 Cur 类参数计算"

    def handle(self, *args, **options):
        project_id = 102
        now = datetime.datetime.now()
        calc_threshold = now - datetime.timedelta(minutes=1)
        # 当年开始时间
        year_begin = datetime.datetime(now.year, 1, 1, 0, 0)
        # 当月开始时间
        month_begin = datetime.datetime(now.year, now.month, 1, 0, 0)
        # 当日开始时间
        day_begin = datetime.datetime(now.year, now.month, now.day, 0, 0)
        # 当前小时整点
        hour_begin = datetime.datetime(now.year, now.month, now.day, now.hour, 0)
        # 当前分钟整点
        minute_begin = datetime.datetime(now.year, now.month, now.day, now.hour, now.minute)
        # 昨日开始
        yesterday_begin = day_begin - datetime.timedelta(days=1)

        ap_map = {
            '总用电量': {
                'minute': 'CurMinElectricity',
                'hour': 'CurHourElectricity',
                'today': 'TodayElectricity',
                'month': 'CurMonthElectricity',
                'year': 'CurYearElectricity'
            },
            '机组用电量': {
                'minute': 'CurMinUnitElectricity',
                'hour': 'CurHourUnitElectricity',
                'today': 'TodayUnitElectricity',
                'month': 'CurMonthUnitElectricity',
                'year': 'CurYearUnitElectricity'
            },
            '水泵用电量': {
                'minute': 'CurMinPumpElectricity',
                'hour': 'CurHourPumpElectricity',
                'today': 'TodayPumpElectricity',
                'month': 'CurMonthPumpElectricity',
                'year': 'CurYearPumpElectricity'
            },
            '用冷量': {
                'minute': 'CurMinCool',
                'hour': 'CurHourCool',
                'today': 'TodayCool',
                'month': 'CurMonthCool',
                'year': 'CurYearCool'
            }
        }

        try:
            # 设置全局数据库
            set_global_db(102)

            cac_devices = list(Device.objects.filter(device_prototype_id=544))
            cac_das: list[DimensionAttribute] = list(DimensionAttribute.objects.filter(name__icontains='洁净空调', updated_at__gte=calc_threshold))
            cur_aps = list(AttributePrototype.objects.filter(device_prototype_id=544).filter(
                Q(identifier__startswith='Cur') | Q(identifier__startswith='Today')
            ))
            cop_aps = list(AttributePrototype.objects.filter(device_prototype_id=544, identifier__endswith='COP'))
            eer_aps = list(AttributePrototype.objects.filter(device_prototype_id=544, identifier__endswith='EER'))

            for da in cac_das:
                name_idx = da.name.find('空调')
                device_name = da.name[:name_idx + 2]

                device = next((device for device in cac_devices if device.nick_name.startswith(device_name)), None)
                if device is None:
                    continue

                for key, ap_dict in ap_map.items():
                    if key in da.name:
                        # 当前分钟
                        cur_min_ele_ap = next((ap for ap in cur_aps if ap.identifier == ap_dict.get('minute')), None)
                        cur_min_ele_da = DeviceAttribute.objects.query_object_by_ap(device, cur_min_ele_ap)
                        last_da_value = RedisHelper.get_list_tail_item(project_id, f'dimension_attribute:{da.id}', 2, True)
                        if last_da_value:
                            prev_da_value = json.loads(last_da_value)
                            prev_dt = datetime.datetime.fromisoformat(prev_da_value['dt'])
                            prev_value = prev_da_value['value']

                            diff_value = float(da.value) - float(prev_value)
                            diff_minute = (now - prev_dt).seconds / 60

                            cur_min_cons = round(diff_value / diff_minute, 2)
                            if str(cur_min_cons) != cur_min_ele_da.value:
                                cur_min_ele_da.value = cur_min_cons
                                cur_min_ele_da.updated_at = now
                                DeviceAttribute.objects.save_to_redis(device, cur_min_ele_da)
                                # 保存设备参的属性类型id到缓存，快照任务需要使用
                                RedisHelper.add_set(device.project_id, f'recently_updated_{now.minute:02}:{device.device_prototype_id}',
                                                    cur_min_ele_ap.id, True, 120)
                                RedisHelper.push_list(device.project_id,
                                                      f'device_event_{now.strftime("%Y%m%d%H")}:{device.id}_{cur_min_ele_ap.identifier}',
                                                      cur_min_cons, True, 7200)

                        # 当前小时
                        cur_hour_ele_ap = next((ap for ap in cur_aps if ap.identifier == ap_dict.get('hour')), None)
                        cur_hour_ele_da = DeviceAttribute.objects.query_object_by_ap(device, cur_hour_ele_ap)
                        cur_hour_cons = da.calc_current_hour_cons()
                        if str(cur_hour_cons) != cur_hour_ele_da.value and now.minute >= 5:
                            cur_hour_ele_da.value = cur_hour_cons
                            cur_hour_ele_da.updated_at = now
                            DeviceAttribute.objects.save_to_redis(device, cur_hour_ele_da)
                            RedisHelper.add_set(device.project_id, f'recently_updated_{now.minute:02}:{device.device_prototype_id}',
                                                cur_hour_ele_ap.id, True, 120)
                            RedisHelper.push_list(device.project_id,
                                                  f'device_event_{now.strftime("%Y%m%d%H")}:{device.id}_{cur_hour_ele_ap.identifier}',
                                                  cur_hour_cons, True, 7200)

                        # 今日
                        today_ele_ap = next((ap for ap in cur_aps if ap.identifier == ap_dict.get('today')), None)
                        today_ele_da = DeviceAttribute.objects.query_object_by_ap(device, today_ele_ap)
                        today_cons = cur_hour_cons
                        hourly_value = DimensionHourlyStat.objects.filter(dimension_id=da.dimension_id, dimension_attribute_id=da.id,
                                                                          created_at__gte=day_begin).aggregate(sum=Sum('avg'))['sum']
                        if hourly_value:
                            today_cons = cur_hour_cons + hourly_value
                        if str(today_cons) != today_ele_da.value and now.minute >= 5:
                            today_ele_da.value = round(today_cons, 2)
                            today_ele_da.updated_at = now
                            DeviceAttribute.objects.save_to_redis(device, today_ele_da)
                            RedisHelper.add_set(device.project_id, f'recently_updated_{now.minute:02}:{device.device_prototype_id}',
                                                today_ele_ap.id, True, 120)
                            RedisHelper.push_list(device.project_id,
                                                  f'device_event_{now.strftime("%Y%m%d%H")}:{device.id}_{today_ele_ap.identifier}',
                                                  today_cons, True, 7200)

                        # 当月
                        cur_month_ele_ap = next((ap for ap in cur_aps if ap.identifier == ap_dict.get('month')), None)
                        cur_month_ele_da = DeviceAttribute.objects.query_object_by_ap(device, cur_month_ele_ap)
                        cur_month_cons = today_cons
                        daily_value = DimensionDailyStat.objects.filter(dimension_id=da.dimension_id, dimension_attribute_id=da.id,
                                                                        created_at__gte=month_begin).aggregate(sum=Sum('avg'))['sum']
                        if daily_value:
                            cur_month_cons = today_cons + daily_value
                        if str(cur_month_cons) != cur_month_ele_da.value and now.minute >= 5:
                            cur_month_ele_da.value = round(cur_month_cons, 2)
                            cur_month_ele_da.updated_at = now
                            DeviceAttribute.objects.save_to_redis(device, cur_month_ele_da)
                            RedisHelper.add_set(device.project_id, f'recently_updated_{now.minute:02}:{device.device_prototype_id}',
                                                cur_month_ele_ap.id, True, 120)
                            RedisHelper.push_list(device.project_id,
                                                  f'device_event_{now.strftime("%Y%m%d%H")}:{device.id}_{cur_month_ele_ap.identifier}',
                                                  cur_month_cons, True, 7200)

                        # 当年
                        cur_year_ele_ap = next((ap for ap in cur_aps if ap.identifier == ap_dict.get('year')), None)
                        cur_year_ele_da = DeviceAttribute.objects.query_object_by_ap(device, cur_year_ele_ap)
                        cur_year_cons = cur_month_cons
                        monthly_value = DimensionMonthlyStat.objects.filter(dimension_id=da.dimension_id, dimension_attribute_id=da.id,
                                                                            created_at__gte=year_begin).aggregate(sum=Sum('avg'))['sum']
                        if monthly_value:
                            cur_year_cons = cur_month_cons + monthly_value
                        if str(cur_year_cons) != cur_year_ele_da.value and now.minute >= 5:
                            cur_year_ele_da.value = round(cur_year_cons, 2)
                            cur_year_ele_da.updated_at = now
                            DeviceAttribute.objects.save_to_redis(device, cur_year_ele_da)
                            RedisHelper.add_set(device.project_id, f'recently_updated_{now.minute:02}:{device.device_prototype_id}',
                                                cur_year_ele_ap.id, True, 120)
                            RedisHelper.push_list(device.project_id,
                                                  f'device_event_{now.strftime("%Y%m%d%H")}:{device.id}_{cur_year_ele_ap.identifier}',
                                                  cur_year_cons, True, 7200)

            calc_devices = set()
            for da in cac_das:
                name_idx = da.name.find('空调')
                device_name = da.name[:name_idx + 2]

                device = next((device for device in cac_devices if device.nick_name.startswith(device_name)), None)
                if device is not None:
                    calc_devices.add(device)

            for device in calc_devices:
                # min
                min_cool_ap = next((ap for ap in cur_aps if ap.identifier == 'CurMinCool'), None)
                min_unit_ap = next((ap for ap in cur_aps if ap.identifier == 'CurMinUnitElectricity'))
                min_ele_ap = next((ap for ap in cur_aps if ap.identifier == 'CurMinElectricity'))

                if min_cool_ap and min_unit_ap and min_ele_ap:
                    min_cool = DeviceAttribute.objects.query_object_by_ap(device, min_cool_ap)
                    min_unit_cons = DeviceAttribute.objects.query_object_by_ap(device, min_unit_ap)
                    min_ele_cons = DeviceAttribute.objects.query_object_by_ap(device, min_ele_ap)
                    if min_cool and min_unit_cons and min_ele_cons:
                        if float(min_unit_cons.value):
                            min_cop = round(float(min_cool.value) / float(min_unit_cons.value), 2)
                        else:
                            min_cop = 0
                        if float(min_ele_cons.value):
                            min_eer = round(float(min_cool.value) / float(min_ele_cons.value), 2)
                        else:
                            min_eer = 0

                        min_cop_ap = next((ap for ap in cop_aps if ap.identifier == 'CurMinCOP'), None)
                        min_eer_ap = next((ap for ap in eer_aps if ap.identifier == 'CurMinEER'), None)

                        if min_cop_ap:
                            min_cop_da = DeviceAttribute.objects.query_object_by_ap(device, min_cop_ap)
                            if min_cop_da:
                                min_cop_da.value = min_cop
                                min_cop_da.updated_at = now
                                DeviceAttribute.objects.save_to_redis(device, min_cop_da)
                                RedisHelper.add_set(device.project_id, f'recently_updated_{now.minute:02}:{device.device_prototype_id}',
                                                    min_cop_ap.id, True, 120)
                                RedisHelper.push_list(device.project_id,
                                                      f'device_event_{now.strftime("%Y%m%d%H")}:{device.id}_{min_cop_ap.identifier}',
                                                      min_cop, True, 7200)
                        if min_eer_ap:
                            min_eer_da = DeviceAttribute.objects.query_object_by_ap(device, min_eer_ap)
                            if min_eer_da:
                                min_eer_da.value = min_eer
                                min_eer_da.updated_at = now
                                DeviceAttribute.objects.save_to_redis(device, min_eer_da)
                                RedisHelper.add_set(device.project_id, f'recently_updated_{now.minute:02}:{device.device_prototype_id}',
                                                    min_eer_ap.id, True, 120)
                                RedisHelper.push_list(device.project_id,
                                                      f'device_event_{now.strftime("%Y%m%d%H")}:{device.id}_{min_eer_ap.identifier}',
                                                      min_eer, True, 7200)

                        print(f'device: {device.id}-{device.nick_name}, min_cop: {min_cop}, min_eer: {min_eer}')

                # hour
                hour_cool_ap = next((ap for ap in cur_aps if ap.identifier == 'CurHourCool'), None)
                hour_unit_ap = next((ap for ap in cur_aps if ap.identifier == 'CurHourUnitElectricity'))
                hour_ele_ap = next((ap for ap in cur_aps if ap.identifier == 'CurHourElectricity'))

                if hour_cool_ap and hour_unit_ap and hour_ele_ap:
                    hour_cool = DeviceAttribute.objects.query_object_by_ap(device, hour_cool_ap)
                    hour_unit_cons = DeviceAttribute.objects.query_object_by_ap(device, hour_unit_ap)
                    hour_ele_cons = DeviceAttribute.objects.query_object_by_ap(device, hour_ele_ap)
                    if hour_cool and hour_unit_cons and hour_ele_cons:
                        if float(hour_unit_cons.value):
                            hour_cop = round(float(hour_cool.value) / float(hour_unit_cons.value), 2)
                        else:
                            hour_cop = 0
                        if float(hour_ele_cons.value):
                            hour_eer = round(float(hour_cool.value) / float(hour_ele_cons.value), 2)
                        else:
                            hour_eer = 0

                        hour_cop_ap = next((ap for ap in cop_aps if ap.identifier == 'CurHrCOP'), None)
                        hour_eer_ap = next((ap for ap in eer_aps if ap.identifier == 'CurHrEER'), None)

                        if hour_cop_ap:
                            hour_cop_da = DeviceAttribute.objects.query_object_by_ap(device, hour_cop_ap)
                            if hour_cop_da:
                                hour_cop_da.value = hour_cop
                                hour_cop_da.updated_at = now
                                DeviceAttribute.objects.save_to_redis(device, hour_cop_da)
                                RedisHelper.add_set(device.project_id, f'recently_updated_{now.minute:02}:{device.device_prototype_id}',
                                                    hour_cop_ap.id, True, 120)
                                RedisHelper.push_list(device.project_id,
                                                      f'device_event_{now.strftime("%Y%m%d%H")}:{device.id}_{hour_cop_ap.identifier}',
                                                      hour_cop, True, 7200)
                        if hour_eer_ap:
                            hour_eer_da = DeviceAttribute.objects.query_object_by_ap(device, hour_eer_ap)
                            if hour_eer_da:
                                hour_eer_da.value = hour_eer
                                hour_eer_da.updated_at = now
                                DeviceAttribute.objects.save_to_redis(device, hour_eer_da)
                                RedisHelper.add_set(device.project_id, f'recently_updated_{now.minute:02}:{device.device_prototype_id}',
                                                    hour_eer_ap.id, True, 120)
                                RedisHelper.push_list(device.project_id,
                                                      f'device_event_{now.strftime("%Y%m%d%H")}:{device.id}_{hour_eer_ap.identifier}',
                                                      hour_eer, True, 7200)

                        print(f'device: {device.id}-{device.nick_name}, hour_cop: {hour_cop}, hour_eer: {hour_eer}')

                # today
                today_cool_ap = next((ap for ap in cur_aps if ap.identifier == 'TodayCool'), None)
                today_unit_ap = next((ap for ap in cur_aps if ap.identifier == 'TodayUnitElectricity'))
                today_ele_ap = next((ap for ap in cur_aps if ap.identifier == 'TodayElectricity'))

                if today_cool_ap and today_unit_ap and today_ele_ap:
                    today_cool = DeviceAttribute.objects.query_object_by_ap(device, today_cool_ap)
                    today_unit_cons = DeviceAttribute.objects.query_object_by_ap(device, today_unit_ap)
                    today_ele_cons = DeviceAttribute.objects.query_object_by_ap(device, today_ele_ap)
                    if today_cool and today_unit_cons and today_ele_cons:
                        if float(today_unit_cons.value):
                            today_cop = round(float(today_cool.value) / float(today_unit_cons.value), 2)
                        else:
                            today_cop = 0
                        if float(today_ele_cons.value):
                            today_eer = round(float(today_cool.value) / float(today_ele_cons.value), 2)
                        else:
                            today_eer = 0

                        today_cop_ap = next((ap for ap in cop_aps if ap.identifier == 'TodayCOP'), None)
                        today_eer_ap = next((ap for ap in eer_aps if ap.identifier == 'TodayEER'), None)

                        if today_cop_ap:
                            today_cop_da = DeviceAttribute.objects.query_object_by_ap(device, today_cop_ap)
                            if today_cop_da:
                                today_cop_da.value = today_cop
                                today_cop_da.updated_at = now
                                DeviceAttribute.objects.save_to_redis(device, today_cop_da)
                                RedisHelper.add_set(device.project_id, f'recently_updated_{now.minute:02}:{device.device_prototype_id}',
                                                    today_cop_ap.id, True, 120)
                                RedisHelper.push_list(device.project_id,
                                                      f'device_event_{now.strftime("%Y%m%d%H")}:{device.id}_{today_cop_ap.identifier}',
                                                      today_cop, True, 7200)
                        if today_eer_ap:
                            today_eer_da = DeviceAttribute.objects.query_object_by_ap(device, today_eer_ap)
                            if today_eer_da:
                                today_eer_da.value = today_eer
                                today_eer_da.updated_at = now
                                DeviceAttribute.objects.save_to_redis(device, today_eer_da)
                                RedisHelper.add_set(device.project_id, f'recently_updated_{now.minute:02}:{device.device_prototype_id}',
                                                    today_eer_ap.id, True, 120)
                                RedisHelper.push_list(device.project_id,
                                                      f'device_event_{now.strftime("%Y%m%d%H")}:{device.id}_{today_eer_ap.identifier}',
                                                      today_eer, True, 7200)

                        print(f'device: {device.id}-{device.nick_name}, today_cop: {today_cop}, today_eer: {today_eer}')

                # month
                cur_month_cool_ap = next((ap for ap in cur_aps if ap.identifier == 'CurMonthCool'), None)
                cur_month_unit_ap = next((ap for ap in cur_aps if ap.identifier == 'CurMonthUnitElectricity'))
                cur_month_ele_ap = next((ap for ap in cur_aps if ap.identifier == 'CurMonthElectricity'))

                if cur_month_cool_ap and cur_month_unit_ap and cur_month_ele_ap:
                    cur_month_cool = DeviceAttribute.objects.query_object_by_ap(device, cur_month_cool_ap)
                    cur_month_unit_cons = DeviceAttribute.objects.query_object_by_ap(device, cur_month_unit_ap)
                    cur_month_ele_cons = DeviceAttribute.objects.query_object_by_ap(device, cur_month_ele_ap)
                    if cur_month_cool and cur_month_unit_cons and cur_month_ele_cons:
                        if float(cur_month_unit_cons.value):
                            cur_month_cop = round(float(cur_month_cool.value) / float(cur_month_unit_cons.value), 2)
                        else:
                            cur_month_cop = 0
                        if float(cur_month_ele_cons.value):
                            cur_month_eer = round(float(cur_month_cool.value) / float(cur_month_ele_cons.value), 2)
                        else:
                            cur_month_eer = 0

                        cur_month_cop_ap = next((ap for ap in cop_aps if ap.identifier == 'CurMonthCOP'), None)
                        cur_month_eer_ap = next((ap for ap in eer_aps if ap.identifier == 'CurMonthEER'), None)

                        if cur_month_cop_ap:
                            cur_month_cop_da = DeviceAttribute.objects.query_object_by_ap(device, cur_month_cop_ap)
                            if cur_month_cop_da:
                                cur_month_cop_da.value = cur_month_cop
                                cur_month_cop_da.updated_at = now
                                DeviceAttribute.objects.save_to_redis(device, cur_month_cop_da)
                                RedisHelper.add_set(device.project_id, f'recently_updated_{now.minute:02}:{device.device_prototype_id}',
                                                    cur_month_cop_ap.id, True, 120)
                                RedisHelper.push_list(device.project_id,
                                                      f'device_event_{now.strftime("%Y%m%d%H")}:{device.id}_{cur_month_cop_ap.identifier}',
                                                      cur_month_cop, True, 7200)
                        if cur_month_eer_ap:
                            cur_month_eer_da = DeviceAttribute.objects.query_object_by_ap(device, cur_month_eer_ap)
                            if cur_month_eer_da:
                                cur_month_eer_da.value = cur_month_eer
                                cur_month_eer_da.updated_at = now
                                DeviceAttribute.objects.save_to_redis(device, cur_month_eer_da)
                                RedisHelper.add_set(device.project_id, f'recently_updated_{now.minute:02}:{device.device_prototype_id}',
                                                    cur_month_eer_ap.id, True, 120)
                                RedisHelper.push_list(device.project_id,
                                                      f'device_event_{now.strftime("%Y%m%d%H")}:{device.id}_{cur_month_eer_ap.identifier}',
                                                      cur_month_eer, True, 7200)

                        print(f'device: {device.id}-{device.nick_name}, cur_month_cop: {cur_month_cop}, cur_month_eer: {cur_month_eer}')

                    # year
                    cur_year_cool_ap = next((ap for ap in cur_aps if ap.identifier == 'CurYearCool'), None)
                    cur_year_unit_ap = next((ap for ap in cur_aps if ap.identifier == 'CurYearUnitElectricity'))
                    cur_year_ele_ap = next((ap for ap in cur_aps if ap.identifier == 'CurYearElectricity'))

                    if cur_year_cool_ap and cur_year_unit_ap and cur_year_ele_ap:
                        cur_year_cool = DeviceAttribute.objects.query_object_by_ap(device, cur_year_cool_ap)
                        cur_year_unit_cons = DeviceAttribute.objects.query_object_by_ap(device, cur_year_unit_ap)
                        cur_year_ele_cons = DeviceAttribute.objects.query_object_by_ap(device, cur_year_ele_ap)
                        if cur_year_cool and cur_year_unit_cons and cur_year_ele_cons:
                            if float(cur_year_unit_cons.value):
                                cur_year_cop = round(float(cur_year_cool.value) / float(cur_year_unit_cons.value), 2)
                            else:
                                cur_year_cop = 0
                            if float(cur_year_ele_cons.value):
                                cur_year_eer = round(float(cur_year_cool.value) / float(cur_year_ele_cons.value), 2)
                            else:
                                cur_year_eer = 0

                            cur_year_cop_ap = next((ap for ap in cop_aps if ap.identifier == 'CurYearCOP'), None)
                            cur_year_eer_ap = next((ap for ap in eer_aps if ap.identifier == 'CurYearEER'), None)

                            if cur_year_cop_ap:
                                cur_year_cop_da = DeviceAttribute.objects.query_object_by_ap(device, cur_year_cop_ap)
                                if cur_year_cop_da:
                                    cur_year_cop_da.value = cur_year_cop
                                    cur_year_cop_da.updated_at = now
                                    DeviceAttribute.objects.save_to_redis(device, cur_year_cop_da)
                                    RedisHelper.add_set(device.project_id, f'recently_updated_{now.minute:02}:{device.device_prototype_id}',
                                                        cur_year_cop_ap.id, True, 120)
                                    RedisHelper.push_list(device.project_id,
                                                          f'device_event_{now.strftime("%Y%m%d%H")}:{device.id}_{cur_year_cop_ap.identifier}',
                                                          cur_year_cop, True, 7200)
                            if cur_year_eer_ap:
                                cur_year_eer_da = DeviceAttribute.objects.query_object_by_ap(device, cur_year_eer_ap)
                                if cur_year_eer_da:
                                    cur_year_eer_da.value = cur_year_eer
                                    cur_year_eer_da.updated_at = now
                                    DeviceAttribute.objects.save_to_redis(device, cur_year_eer_da)
                                    RedisHelper.add_set(device.project_id, f'recently_updated_{now.minute:02}:{device.device_prototype_id}',
                                                        cur_year_eer_ap.id, True, 120)
                                    RedisHelper.push_list(device.project_id,
                                                          f'device_event_{now.strftime("%Y%m%d%H")}:{device.id}_{cur_year_eer_ap.identifier}',
                                                          cur_year_eer, True, 7200)

                            print(f'device: {device.id}-{device.nick_name}, cur_year_cop: {cur_year_cop}, cur_year_eer: {cur_year_eer}')

            Device.objects.filter(id__in=[d.id for d in calc_devices]).update(updated_at=now)

        except CommandError:
            self.stderr.write(f"运行'人医洁净空调 Cur 类参数计算'任务失败, 命令参数不合法！", ending='\n')
        except Exception as e:
            self.stderr.write(f"运行'人医洁净空调 Cur 类参数计算'任务失败, error: {e.__str__()}", ending='\n')
            self.stderr.write(traceback.format_exc(), ending='')
