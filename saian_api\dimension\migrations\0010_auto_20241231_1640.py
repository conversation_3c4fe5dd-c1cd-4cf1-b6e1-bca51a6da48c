# Generated by Django 3.2.19 on 2024-12-31 16:40

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('building', '0005_auto_20241025_1715'),
        ('dimension', '0009_auto_20241113_1510'),
    ]

    operations = [
        migrations.AddField(
            model_name='dimension',
            name='ec_persons',
            field=models.IntegerField(default=None, null=True),
        ),
        migrations.CreateModel(
            name='DimensionRoom',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('dimension', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dimension.dimension')),
                ('room', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='building.activeroom')),
            ],
            options={
                'db_table': 'dimension_rooms',
            },
        ),
    ]
