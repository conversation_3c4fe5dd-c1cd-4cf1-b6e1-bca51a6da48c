import os

from celery import Celery
from celery.schedules import crontab

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'saian_api.settings')

from django.conf import settings  # noqa


celery_app = Celery(
    'saian_api',
    broker=settings.REDIS_URL
)

celery_app.config_from_object('django.conf:settings', namespace='CELERY')

celery_app.autodiscover_tasks()

# task_routes = {
#     'campaigns.tasks.stats.push_sent': {'queue': 'stats:pushes'},
#     'tracker.tasks.stats.*': {'queue': 'stats'},
# }

# 启用运行时自动重连
celery_app.conf.broker_connection_retry_on_startup = True
celery_app.conf.broker_transport_options = {
    'max_retries': 0,  # 无限重试
    'interval_start': 0,  # 初始等待秒数
    'interval_step': 2,  # 每次递增等待秒数
    'interval_max': 60,  # 最大等待秒数
}

# 让 gevent 模式下断线后能快速重建连接
celery_app.conf.broker_pool_limit = None
celery_app.conf.broker_heartbeat = 60
celery_app.conf.broker_heartbeat_checkrate = 10

celery_app.conf.update(
    task_serializer='json',
    accept_content=['json'],  # Ignore other content
    timezone='Asia/Shanghai',
    # task_routes=task_routes,
)

# 设置定时任务，也可以在代码中设定定时任务
# celery_app.conf.beat_schedule = {
#     'periodic': {
#         'task': 'tutorial.quickstart.tasks.periodic.periodic',
#         'schedule': 30
#     }
# }

celery_app.conf.beat_schedule = {
    # 设备定时统计
    # 'device_hourly_stat': {
    #     'task': 'saian_api.report.tasks.device.device_hourly_stat',
    #     'schedule': crontab(minute=5, hour='*')
    # },
    # 'device_daily_stat': {
    #     'task': 'saian_api.report.tasks.device.device_daily_stat',
    #     'schedule': crontab(minute=10, hour=0, day_of_month='*')
    # },
    # 'device_monthly_stat': {
    #     'task': 'saian_api.report.tasks.device.device_monthly_stat',
    #     'schedule': crontab(minute=13, hour=0, day_of_month=1)
    # },
    # 'device_yearly_stat': {
    #     'task': 'saian_api.report.tasks.device.device_yearly_stat',
    #     'schedule': crontab(minute=15, hour=0, day_of_month=1, month_of_year=1)
    # },

    # # 设备累计值参数统计
    # 'device_calc_cum': {
    #     'task': 'saian_api.device.tasks.task_calc_cum',
    #     'schedule': crontab(minute=0, hour='*')
    # },
    # # 设备维护检查
    # 'device_check_maint': {
    #     'task': 'saian_api.device.tasks.task_check_device_maint',
    #     'schedule': crontab(minute=1, hour='*')
    # },
    # # 设备在线检查
    # 'device_check_online': {
    #     'task': 'saian_api.device.tasks.task_check_device_online',
    #     'schedule': crontab(minute=8, hour='*')
    # },
    #
    # # 项目天气
    # 'project_hourly_weather': {
    #     'task': 'saian_api.dashboard.tasks.project_hourly_weather',
    #     'schedule': crontab(minute=0, hour='*')  # 每小时的0分执行任务
    # },
    # 'project_daily_weather': {
    #     'task': 'saian_api.dashboard.tasks.project_daily_weather',
    #     'schedule': crontab(minute=10, hour=0, day_of_month='*')  # 每天0时10分执行任务
    # },
    # 'project_monthly_weather': {
    #     'task': 'saian_api.dashboard.tasks.project_monthly_weather',
    #     'schedule': crontab(minute=13, hour=0, day_of_month=1)  # 每月一号0时13分执行任务
    # },
    # 'project_yearly_weather': {
    #     'task': 'saian_api.dashboard.tasks.project_yearly_weather',
    #     'schedule': crontab(minute=15, hour=0, day_of_month=1, month_of_year=1)  # 每年一月一号0时15分执行任务
    # },
    #
    # # 冷源能耗分析
    # 'cold_source_analyse': {
    #     'task': 'saian_api.coldsource.tasks.cold_source_analyse',
    #     'schedule': crontab(minute=35, hour='*')  # 每小时的20分执行任务
    # },

    # 重检
    # "recheck_project_hourly_weather": {
    #     'task': 'saian_api.report.tasks.recheck.recheck_project_hourly_weather',
    #     'schedule': crontab(minute=30, hour='*')
    # },
    # "recheck_device_hourly_stat": {
    #     'task': 'saian_api.report.tasks.recheck.recheck_device_hourly_stat',
    #     'schedule': crontab(minute=17, hour='*')
    # }
}

# celery_app.config_from_object("saian_api.report.tasks.celery_config")
