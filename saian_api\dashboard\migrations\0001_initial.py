# Generated by Django 3.2.8 on 2021-12-30 15:16

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='EcCriteria',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ec_type', models.IntegerField()),
                ('year_month', models.CharField(max_length=6)),
                ('value', models.CharField(max_length=20)),
                ('is_refer', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'ec_criteria',
            },
        ),
        migrations.CreateModel(
            name='ProjectWeather',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('temp', models.IntegerField()),
                ('humidity', models.IntegerField()),
                ('min_temp', models.IntegerField()),
                ('max_temp', models.IntegerField()),
                ('min_humidity', models.IntegerField()),
                ('max_humidity', models.IntegerField()),
                ('type', models.CharField(choices=[('hr', '小时'), ('di', '天'), ('mth', '月'), ('yr', '年')], max_length=5)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'web_project_weather',
            },
        ),
    ]
