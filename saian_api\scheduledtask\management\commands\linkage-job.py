"""
  设备联动定时任务
"""
from fcntl import F_SETFL
from django.core.management.base import BaseCommand, CommandError

from saian_api.utils.tools import replenishment_time
from saian_api.linkage.models import LinkageTrigger, LinkageRule
from saian_api.group.models import AcStrategies
from saian_api.project.models import Project
from saian_api.scheduledtask.utils import get_projects, hit_time, set_global_db

import datetime
import traceback
import json


class Command(BaseCommand):
    help = '设备联动定时任务'

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目列表
        projects = get_projects(self, options)

        now = datetime.datetime.now()

        for project_id in projects:
            try:
                # 存储满足条件的定时任务
                hit_timers = []
                # 设置全局数据库

                set_global_db(project_id)
                project = Project.objects.get(pk=project_id)

                self.stdout.write(f"运行联动定时任务: {project.name}", ending='\n')

                # 当前项目的，启用的，触发方式是"定时"的联动规则
                rules = LinkageRule.objects.filter(project=project, enabled=True, trigger_type=10)
                timers = LinkageTrigger.objects.filter(linkage_rule__in=rules, is_finished=False)
                for timer in timers:
                    try:

                        is_effective = False
                        if timer.time_ranges:
                            time_period_list = replenishment_time(now, json.loads(timer.time_ranges))
                            for item in time_period_list:
                                if item['from_at'] <= now < item['till_at']:
                                    is_effective = True
                                    break
                        else:
                            is_effective = True

                        # 首次运行用updated_at
                        last_run_at = int(timer.updated_at.timestamp())
                        if timer.last_run_at is not None:
                            last_run_at = int(timer.last_run_at.timestamp())
                        if hit_time(self, timer.run_date, timer.run_time, last_run_at, timer.wdays, timer.interval, timer.repeat, now) and is_effective:
                            # 更新时间和状态，避免重复执行
                            timer.last_run_at = datetime.datetime.now()
                            if not timer.repeat:
                                timer.is_finished = True
                                ac_strategy = AcStrategies.objects.filter(st_type=20, st_id=timer.linkage_rule_id).last()
                                if ac_strategy is not None:
                                    ac_strategy.status = 20
                                    ac_strategy.save()
                            timer.save()

                            hit_timers.append(timer)

                    except Exception:
                        self.stderr.write(f"处理联动定时任务出错，linkage_trigger_id = {timer.id}", ending='\n')
                        self.stderr.write(traceback.format_exc(), ending='')
                        continue

                for timer in hit_timers:
                    try:
                        self.stdout.write(f"execute linkage trigger job, id = {timer.id}")
                        timer.linkage_rule.execute(timer, False)

                        # 重复任务不会结束
                        # if timer.repeat:
                        #     timer.is_finished = False
                        #     timer.save()
                        #
                        #     ac_strategy = AcStrategies.objects.get(st_type=20, st_id=timer.linkage_rule_id)
                        #     ac_strategy.status = 10
                        #     ac_strategy.save()

                    except Exception as e:
                        self.stderr.write(f"运行联动定时任务出错，linkage_trigger_id = {timer.id}, err = {e.__str__()}", ending='\n')
                        self.stderr.write(traceback.format_exc(), ending='')

                        # 重复任务异常也要恢复原样
                        # if timer.repeat:
                        #     timer.is_finished = False
                        #     timer.save()
                        #
                        #     ac_strategy = AcStrategies.objects.get(st_type=20, st_id=timer.linkage_rule_id)
                        #     ac_strategy.status = 10
                        #     ac_strategy.save()
                        # continue

            except Exception:
                self.stderr.write(f"运行联动定时任务出错，project_id = {project_id}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
                continue
