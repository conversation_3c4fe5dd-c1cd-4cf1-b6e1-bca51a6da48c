# Generated by Django 3.2.8 on 2022-12-30 16:18

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('group', '0007_auto_20221208_1721'),
    ]

    operations = [
        migrations.CreateModel(
            name='AcStrategies',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('st_type', models.IntegerField(null=True)),
                ('st_id', models.PositiveBigIntegerField(null=True)),
                ('status', models.IntegerField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'ac_strategies',
                'ordering': ['-created_at'],
            },
        ),
    ]
