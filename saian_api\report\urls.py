from django.urls import path

from .views import (DeviceStatViewSet, ReportViewSet,
                    WebReportViewSet, EcApportionView,
                    ChartDataViewSet, UserChartViewSet,
                    UserAnalyseViewSet, UserAnalyseChartViewSet,
                    ManualRecordViewSet, DlTasksView, StatReportView, DerivedDataViewSet,
                    SettlePartyViewSet, SettleReferViewSet, SettleRecordViewSet, DevstlRecordViewSet, GuananMonthlyReportView)

report_list = ReportViewSet.as_view({
    'get': 'list'
})

web_report_list = WebReportViewSet.as_view({
    'get': 'list'
})

device_stats = DeviceStatViewSet.as_view({
    'get': 'list',
    'post': 'create'
})

ec_apportion = EcApportionView.as_view({
    'get': 'list'
})

chart_data = ChartDataViewSet.as_view({
    'get': 'list'
})

user_charts = UserChartViewSet.as_view({
    'get': 'list',
    'post': 'create'
})

user_chart = UserChartViewSet.as_view({
    'get': 'retrieve',
    'put': 'partial_update',
    'delete': 'destroy',
})

user_analysis = UserAnalyseViewSet.as_view({
    'get': 'list',
    'post': 'create'
})

user_analyse = UserAnalyseViewSet.as_view({
    'get': 'retrieve',
    'put': 'partial_update',
    'delete': 'destroy',
})

analyse_charts = UserAnalyseChartViewSet.as_view({
    'post': 'create'
})

analyse_chart = UserAnalyseChartViewSet.as_view({
    'put': 'partial_update',
    'delete': 'destroy'
})

manual_records = ManualRecordViewSet.as_view({
    'get': 'list',
    'post': 'create'
})

manual_record = ManualRecordViewSet.as_view({
    'put': 'partial_update',
    'delete': 'destroy'
})

# 节能报告
dl_tasks = DlTasksView.as_view({
    'get': 'list'
})
dl_task_detail = DlTasksView.as_view({
    'delete': 'destroy'
})

# 分析报告
stat_reports = StatReportView.as_view({
    'get': 'list'
})
stat_report_detail = StatReportView.as_view({
    'get': 'retrieve'
})

# 通用数据导出
derive_data = DerivedDataViewSet.as_view({
    'post': 'create'
})

settle_parties = SettlePartyViewSet.as_view({
    'get': 'list',
    'post': 'create'

})

settle_party = SettlePartyViewSet.as_view({
    'get': 'retrieve',
    'put': 'partial_update',
    'delete': 'destroy'
})

settle_refers = SettleReferViewSet.as_view({
    'get': 'list',
    'post': 'create'
})

settle_refer = SettleReferViewSet.as_view({
    'get': 'retrieve',
    'put': 'partial_update',
    'delete': 'destroy'
})

settle_records = SettleRecordViewSet.as_view({
    'get': 'list'
})

devstl_records = DevstlRecordViewSet.as_view({
    'get': 'list'
})

guanan_monthly_report = GuananMonthlyReportView.as_view(({
    'post': 'create'
}))

urlpatterns = [
    path('saianapi/v1/reports', report_list, name='report-list'),
    path('saianapi/v1/web_reports', web_report_list, name='web-report-list'),
    path('saianapi/v2/device_stats', device_stats, name='device-stats'),
    path('saianapi/v5/device_stats', device_stats, name='device-stats'),
    path('saianapi/v5/ec_apportion', ec_apportion, name='ec-apportion'),
    path('saianapi/v5/chart_data', chart_data, name='chart_data'),
    path('saianapi/v1/user_charts', user_charts, name='user_charts'),
    path('saianapi/v1/user_charts/<int:pk>', user_chart, name='user_chart'),
    path('saianapi/v1/user_analysis', user_analysis, name='user_analysis'),
    path('saianapi/v1/user_analysis/<int:pk>', user_analyse, name='user_analyse'),
    path('saianapi/v1/analyse_charts', analyse_charts, name='analyse_charts'),
    path('saianapi/v1/analyse_charts/<int:pk>', analyse_chart, name='analyse_chart'),
    path('saianapi/v1/manual_records', manual_records, name='manual_records'),
    path('saianapi/v1/manual_records/<int:pk>', manual_record, name='manual_record'),
    path('saianapi/v5/dl_tasks', dl_tasks, name='dl_tasks'),
    path('saianapi/v5/dl_tasks/<int:pk>', dl_task_detail, name='dl_task_detail'),
    path('saianapi/v5/stat_reports', stat_reports, name='stat_reports'),
    path('saianapi/v5/stat_reports/<int:pk>', stat_report_detail, name='stat_report_detail'),
    path('saianapi/v5/derive_data', derive_data, name='derive_data'),
    path('saianapi/v5/settle_parties', settle_parties, name='settle_parties'),
    path('saianapi/v5/settle_parties/<int:pk>', settle_party, name='settle_party'),
    path('saianapi/v5/settle_refers', settle_refers, name='settle_refers'),
    path('saianapi/v5/settle_refers/<int:pk>', settle_refer, name='settle_refer'),
    path('saianapi/v5/settle_records', settle_records, name='settle_records'),
    path('saianapi/v5/devstl_records', devstl_records, name='devstl_records'),
    path('saianapi/v5/guanan_monthly_report', guanan_monthly_report, name="guanan_monthly_report"),
]
