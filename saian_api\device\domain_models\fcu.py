from saian_api.devdefine.models import AttributePrototype
from saian_api.issue.models import DeviceIssue
from .base import BaseDevice
from ...terminal.models import Terminal

"""
  温控器（电信）
"""
class Fcu(BaseDevice):
    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)

        data = event.get('data', {})

        if data is not None and 'FCUStatus' in data:
            attr = AttributePrototype.objects.get(device_prototype_id=device.device_prototype_id, identifier='FCUStatus')
            fcu_status = data['FCUStatus']
            # 判断内容是否为数字
            if type(fcu_status) == int or fcu_status.isdigit():
                value = attr.options.split(',')[int(data['FCUStatus'])]
            else:
                value = fcu_status

            if value == '关机':
                device.sw_on = False
            else:
                device.sw_on = True
            device.save()
            Terminal.objects.bulk_update_by_device(device, 'sw_on', device.sw_on)

    @classmethod
    def fault(cls, device, event):
        data = event.get('data', {})

        if data is not None and 'FCUfault' in data:
            if data['FCUfault'] == 1:
                DeviceIssue.objects.add_fault(device, data['FCUfaultType'])
            else:
                DeviceIssue.objects.recover_fault(device, '')

    @classmethod
    def alarm(cls, device, event):
        super().alarm(device, event)
        data = event.get('data', {})

        if data is not None and 'FCUAlarm' in data:
            if data['FCUAlarm'] == 1:
                DeviceIssue.objects.add_alarm(device, data['FCUAlarmType'])
            else:
                DeviceIssue.objects.recover_alarm(device, '')

"""
  温控器FcuM5311
"""
class FcuM5311(Fcu):
    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)

    @classmethod
    def fault(cls, device, event):
        pass

    @classmethod
    def alarm(cls, device, event):
        data = event.get('data', {})

        if data is not None and 'FCUAlarmType' in data:
            alarms = ['温度测量失效', '湿度测量失效']

            # data['FCUAlarmType'] 是字符串
            if 1 <= int(data['FCUAlarmType']) <= 2:
                DeviceIssue.objects.add_alarm(device, alarms[int(data['FCUAlarmType']) - 1])
            else:
                DeviceIssue.objects.recover_alarm(device, '')

    @classmethod
    def exec_cmd(cls, device, data, executor):
        pass

"""
  温控器Fcu 4G
"""
class Fcu4g(Fcu):
    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)

        data = event.get('data', {})

        if 'FCUStatus' in data:
            attr = AttributePrototype.objects.get_by_idf(device, 'FCUStatus')
            if attr is not None:
                value = attr.options.split(',')[int(data['FCUStatus'])]
                if value == '关机':
                    device.sw_on = False
                else:
                    device.sw_on = True
                device.save()
                Terminal.objects.bulk_update_by_device(device, 'sw_on', device.sw_on)

        if 'FCUSW' in data:
            device.sw_on = data['FCUSW']
            device.save()
            Terminal.objects.bulk_update_by_device(device, 'sw_on', device.sw_on)


    @classmethod
    def fault(cls, device, event):
        pass

    @classmethod
    def alarm(cls, device, event):
        super().alarm(device, event)
        data = event.get('data', {})

        if 'FCUAlarmType' in data:
            if int(data['FCUAlarmType']) == 1:
                DeviceIssue.objects.add_alarm(device, '温度测量失效')
            else:
                DeviceIssue.objects.recover_alarm(device, '温度测量失效')

            if int(data['FCUAlarmType']) == 2:
                DeviceIssue.objects.add_alarm(device, '湿度测量失效')
            else:
                DeviceIssue.objects.recover_alarm(device, '湿度测量失效')
