"""
  删除解除项目绑定的设备
"""
import traceback

from django.contrib.contenttypes.models import ContentType
from django.core.management.base import BaseCommand, CommandError

from saian_api.device.models import Device, DaSnapshotHistory
from saian_api.group.models import GroupDevice
from saian_api.linkage.models import LinkageTarget
from saian_api.project.models import Project
from saian_api.scheduledtask.utils import get_projects, set_global_db
from saian_api.terminal.models import Terminal

class Command(BaseCommand):
    help = '删除解除项目绑定的设备'

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目ID列表
        projects = get_projects(self, options)

        for project_id in projects:
            try:
                # 设置全局数据库
                set_global_db(project_id)
                project = Project.objects.get(pk=project_id)

                orphan_devices = Device.objects.filter(project__isnull=True)
                orphan_size = len(orphan_devices)

                self.stdout.write(f"项目{project_id}-{project.name}存在解除绑定的设备共 {len(orphan_devices)} 个。", ending='\n')
                if orphan_devices.exists():
                    device_type_id = ContentType.objects.get(model='device').id
                    terminal_type_id = ContentType.objects.get(model='terminal').id
                    for device in orphan_devices:
                        gd = GroupDevice.objects.filter(content_type_id=device_type_id, object_id=device.id)
                        gt = GroupDevice.objects.filter(content_type_id=terminal_type_id, object_id__in=[t.id for t in device.terminal_set.all()])
                        ld = LinkageTarget.objects.filter(target_type=device_type_id, target_id=device.id)
                        snap_histories = DaSnapshotHistory.objects.filter(device_id=device.id)
                        if gd.exists():
                            self.stdout.write(f"从设备分组中删除该设备")
                            gd.delete()
                        if gt.exists():
                            self.stdout.write(f"从终端分组中删除该设备的终端")
                            gt.delete()
                        if ld.exists():
                            self.stdout.write(f"从联动目标中删除该设备")
                            ld.delete()
                        if snap_histories.exists():
                            self.stdout.write(f"从历史快照中删除该设备")
                            snap_histories.delete()
                        Terminal.objects.filter(device=device).delete()
                        device.delete()
                    self.stdout.write(f"已删除 {orphan_size} 个设备。")
            except CommandError:
                self.stderr.write(f"运行'设备在线检查'任务失败，项目ID：{project_id}，命令参数不合法！", ending='\n')
                continue
            except Exception as e:
                self.stderr.write(f"运行'设备在线检查'任务失败，项目ID：{project_id}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
                continue
