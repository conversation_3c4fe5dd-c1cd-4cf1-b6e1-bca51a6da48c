# Generated by Django 3.2.8 on 2021-12-30 15:16

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
    ]

    operations = [
        migrations.CreateModel(
            name='DeviceDailyStat',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('device_id', models.BigIntegerField(db_index=True)),
                ('mac', models.CharField(db_index=True, max_length=255)),
                ('identifier', models.CharField(max_length=255)),
                ('avg', models.Char<PERSON>ield(max_length=64)),
                ('min', models.CharField(max_length=64)),
                ('max', models.CharField(max_length=64)),
                ('created_at', models.DateTimeField()),
                ('updated_at', models.DateTime<PERSON>ield(auto_now=True)),
            ],
            options={
                'db_table': 'device_daily_stats',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DeviceHourlyStat',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('device_id', models.BigIntegerField(db_index=True)),
                ('mac', models.CharField(db_index=True, max_length=255)),
                ('identifier', models.CharField(max_length=255)),
                ('avg', models.CharField(max_length=64)),
                ('min', models.CharField(max_length=64)),
                ('max', models.CharField(max_length=64)),
                ('created_at', models.DateTimeField()),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'device_hourly_stats',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DeviceMonthlyStat',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('device_id', models.BigIntegerField(db_index=True)),
                ('mac', models.CharField(db_index=True, max_length=255)),
                ('identifier', models.CharField(max_length=255)),
                ('avg', models.CharField(max_length=64)),
                ('min', models.CharField(max_length=64)),
                ('max', models.CharField(max_length=64)),
                ('created_at', models.DateTimeField()),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'device_monthly_stats',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DeviceYearlyStat',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('device_id', models.BigIntegerField(db_index=True)),
                ('mac', models.CharField(db_index=True, max_length=255)),
                ('identifier', models.CharField(max_length=255)),
                ('avg', models.CharField(max_length=64)),
                ('min', models.CharField(max_length=64)),
                ('max', models.CharField(max_length=64)),
                ('created_at', models.DateTimeField()),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'device_yearly_stats',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ReportConfigurer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('target_id', models.PositiveBigIntegerField()),
                ('name', models.CharField(max_length=64, null=True)),
                ('identifier', models.CharField(max_length=255)),
                ('unit', models.CharField(max_length=20)),
                ('refer_point', models.CharField(max_length=20)),
                ('chart_type', models.IntegerField()),
                ('device_idx', models.IntegerField()),
                ('seq', models.IntegerField()),
            ],
            options={
                'db_table': 'report_configurers',
            },
        ),
        migrations.CreateModel(
            name='RoomDailyStat',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('active_room_id', models.BigIntegerField(db_index=True)),
                ('room_no', models.CharField(db_index=True, max_length=255)),
                ('identifier', models.CharField(max_length=255)),
                ('avg', models.CharField(max_length=64)),
                ('min', models.CharField(max_length=64)),
                ('max', models.CharField(max_length=64)),
                ('created_at', models.DateTimeField()),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'room_daily_stats',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='RoomHourlyStat',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('active_room_id', models.BigIntegerField(db_index=True)),
                ('room_no', models.CharField(db_index=True, max_length=255)),
                ('identifier', models.CharField(max_length=255)),
                ('avg', models.CharField(max_length=64)),
                ('min', models.CharField(max_length=64)),
                ('max', models.CharField(max_length=64)),
                ('created_at', models.DateTimeField()),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'room_hourly_stats',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='RoomMonthlyStat',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('active_room_id', models.BigIntegerField(db_index=True)),
                ('room_no', models.CharField(db_index=True, max_length=255)),
                ('identifier', models.CharField(max_length=255)),
                ('avg', models.CharField(max_length=64)),
                ('min', models.CharField(max_length=64)),
                ('max', models.CharField(max_length=64)),
                ('created_at', models.DateTimeField()),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'room_monthly_stats',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='RoomYearlyStat',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('active_room_id', models.BigIntegerField(db_index=True)),
                ('room_no', models.CharField(db_index=True, max_length=255)),
                ('identifier', models.CharField(max_length=255)),
                ('avg', models.CharField(max_length=64)),
                ('min', models.CharField(max_length=64)),
                ('max', models.CharField(max_length=64)),
                ('created_at', models.DateTimeField()),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'room_yearly_stats',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='roomyearlystat',
            index=models.Index(fields=['active_room_id', 'identifier'], name='room_yearly_active__f40fec_idx'),
        ),
        migrations.AddIndex(
            model_name='roomyearlystat',
            index=models.Index(fields=['room_no', 'identifier'], name='room_yearly_room_no_48e484_idx'),
        ),
        migrations.AddIndex(
            model_name='roomyearlystat',
            index=models.Index(fields=['active_room_id', 'created_at'], name='room_yearly_active__6b7104_idx'),
        ),
        migrations.AddIndex(
            model_name='roomyearlystat',
            index=models.Index(fields=['room_no', 'created_at'], name='room_yearly_room_no_70dc0d_idx'),
        ),
        migrations.AddIndex(
            model_name='roomyearlystat',
            index=models.Index(fields=['active_room_id', 'identifier', 'created_at'], name='room_yearly_active__8a50dc_idx'),
        ),
        migrations.AddIndex(
            model_name='roomyearlystat',
            index=models.Index(fields=['room_no', 'identifier', 'created_at'], name='room_yearly_room_no_d35d76_idx'),
        ),
        migrations.AddIndex(
            model_name='roommonthlystat',
            index=models.Index(fields=['active_room_id', 'identifier'], name='room_monthl_active__d19081_idx'),
        ),
        migrations.AddIndex(
            model_name='roommonthlystat',
            index=models.Index(fields=['room_no', 'identifier'], name='room_monthl_room_no_10b939_idx'),
        ),
        migrations.AddIndex(
            model_name='roommonthlystat',
            index=models.Index(fields=['active_room_id', 'created_at'], name='room_monthl_active__b31735_idx'),
        ),
        migrations.AddIndex(
            model_name='roommonthlystat',
            index=models.Index(fields=['room_no', 'created_at'], name='room_monthl_room_no_57ad7c_idx'),
        ),
        migrations.AddIndex(
            model_name='roommonthlystat',
            index=models.Index(fields=['active_room_id', 'identifier', 'created_at'], name='room_monthl_active__abe849_idx'),
        ),
        migrations.AddIndex(
            model_name='roommonthlystat',
            index=models.Index(fields=['room_no', 'identifier', 'created_at'], name='room_monthl_room_no_a0b4f5_idx'),
        ),
        migrations.AddIndex(
            model_name='roomhourlystat',
            index=models.Index(fields=['active_room_id', 'identifier'], name='room_hourly_active__8dbc71_idx'),
        ),
        migrations.AddIndex(
            model_name='roomhourlystat',
            index=models.Index(fields=['room_no', 'identifier'], name='room_hourly_room_no_fef9d8_idx'),
        ),
        migrations.AddIndex(
            model_name='roomhourlystat',
            index=models.Index(fields=['active_room_id', 'created_at'], name='room_hourly_active__adc259_idx'),
        ),
        migrations.AddIndex(
            model_name='roomhourlystat',
            index=models.Index(fields=['room_no', 'created_at'], name='room_hourly_room_no_18394a_idx'),
        ),
        migrations.AddIndex(
            model_name='roomhourlystat',
            index=models.Index(fields=['active_room_id', 'identifier', 'created_at'], name='room_hourly_active__7a5d3e_idx'),
        ),
        migrations.AddIndex(
            model_name='roomhourlystat',
            index=models.Index(fields=['room_no', 'identifier', 'created_at'], name='room_hourly_room_no_a7eb4e_idx'),
        ),
        migrations.AddIndex(
            model_name='roomdailystat',
            index=models.Index(fields=['active_room_id', 'identifier'], name='room_daily__active__6f77a4_idx'),
        ),
        migrations.AddIndex(
            model_name='roomdailystat',
            index=models.Index(fields=['room_no', 'identifier'], name='room_daily__room_no_d3bf9c_idx'),
        ),
        migrations.AddIndex(
            model_name='roomdailystat',
            index=models.Index(fields=['active_room_id', 'created_at'], name='room_daily__active__8c1ed6_idx'),
        ),
        migrations.AddIndex(
            model_name='roomdailystat',
            index=models.Index(fields=['room_no', 'created_at'], name='room_daily__room_no_e2c7dc_idx'),
        ),
        migrations.AddIndex(
            model_name='roomdailystat',
            index=models.Index(fields=['active_room_id', 'identifier', 'created_at'], name='room_daily__active__89a87d_idx'),
        ),
        migrations.AddIndex(
            model_name='roomdailystat',
            index=models.Index(fields=['room_no', 'identifier', 'created_at'], name='room_daily__room_no_f2f197_idx'),
        ),
        migrations.AddField(
            model_name='reportconfigurer',
            name='target_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype'),
        ),
        migrations.AddIndex(
            model_name='deviceyearlystat',
            index=models.Index(fields=['device_id', 'identifier'], name='device_year_device__aa711c_idx'),
        ),
        migrations.AddIndex(
            model_name='deviceyearlystat',
            index=models.Index(fields=['mac', 'identifier'], name='device_year_mac_90ad39_idx'),
        ),
        migrations.AddIndex(
            model_name='deviceyearlystat',
            index=models.Index(fields=['device_id', 'created_at'], name='device_year_device__e0ac5f_idx'),
        ),
        migrations.AddIndex(
            model_name='deviceyearlystat',
            index=models.Index(fields=['mac', 'created_at'], name='device_year_mac_78c578_idx'),
        ),
        migrations.AddIndex(
            model_name='deviceyearlystat',
            index=models.Index(fields=['device_id', 'identifier', 'created_at'], name='device_year_device__b1fb82_idx'),
        ),
        migrations.AddIndex(
            model_name='deviceyearlystat',
            index=models.Index(fields=['mac', 'identifier', 'created_at'], name='device_year_mac_4acd53_idx'),
        ),
        migrations.AddIndex(
            model_name='devicemonthlystat',
            index=models.Index(fields=['device_id', 'identifier'], name='device_mont_device__696487_idx'),
        ),
        migrations.AddIndex(
            model_name='devicemonthlystat',
            index=models.Index(fields=['mac', 'identifier'], name='device_mont_mac_3b72d3_idx'),
        ),
        migrations.AddIndex(
            model_name='devicemonthlystat',
            index=models.Index(fields=['device_id', 'created_at'], name='device_mont_device__05f50b_idx'),
        ),
        migrations.AddIndex(
            model_name='devicemonthlystat',
            index=models.Index(fields=['mac', 'created_at'], name='device_mont_mac_e2c2b6_idx'),
        ),
        migrations.AddIndex(
            model_name='devicemonthlystat',
            index=models.Index(fields=['device_id', 'identifier', 'created_at'], name='device_mont_device__836813_idx'),
        ),
        migrations.AddIndex(
            model_name='devicemonthlystat',
            index=models.Index(fields=['mac', 'identifier', 'created_at'], name='device_mont_mac_13c5e7_idx'),
        ),
        migrations.AddIndex(
            model_name='devicehourlystat',
            index=models.Index(fields=['device_id', 'identifier'], name='device_hour_device__322ba9_idx'),
        ),
        migrations.AddIndex(
            model_name='devicehourlystat',
            index=models.Index(fields=['mac', 'identifier'], name='device_hour_mac_09d287_idx'),
        ),
        migrations.AddIndex(
            model_name='devicehourlystat',
            index=models.Index(fields=['device_id', 'created_at'], name='device_hour_device__f3f2c9_idx'),
        ),
        migrations.AddIndex(
            model_name='devicehourlystat',
            index=models.Index(fields=['mac', 'created_at'], name='device_hour_mac_768949_idx'),
        ),
        migrations.AddIndex(
            model_name='devicehourlystat',
            index=models.Index(fields=['device_id', 'identifier', 'created_at'], name='device_hour_device__1288e1_idx'),
        ),
        migrations.AddIndex(
            model_name='devicehourlystat',
            index=models.Index(fields=['mac', 'identifier', 'created_at'], name='device_hour_mac_563cf4_idx'),
        ),
        migrations.AddIndex(
            model_name='devicedailystat',
            index=models.Index(fields=['device_id', 'identifier'], name='device_dail_device__3c809a_idx'),
        ),
        migrations.AddIndex(
            model_name='devicedailystat',
            index=models.Index(fields=['mac', 'identifier'], name='device_dail_mac_1beacb_idx'),
        ),
        migrations.AddIndex(
            model_name='devicedailystat',
            index=models.Index(fields=['device_id', 'created_at'], name='device_dail_device__b7d9d1_idx'),
        ),
        migrations.AddIndex(
            model_name='devicedailystat',
            index=models.Index(fields=['mac', 'created_at'], name='device_dail_mac_fa3481_idx'),
        ),
        migrations.AddIndex(
            model_name='devicedailystat',
            index=models.Index(fields=['device_id', 'identifier', 'created_at'], name='device_dail_device__7c0aa8_idx'),
        ),
        migrations.AddIndex(
            model_name='devicedailystat',
            index=models.Index(fields=['mac', 'identifier', 'created_at'], name='device_dail_mac_da409f_idx'),
        ),
    ]
