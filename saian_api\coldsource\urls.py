from django.urls import path

from .views import (ColdSourceViewSet,
                    ColdSourceDetailViewSet,
                    ColdSourceEerAnalyses,
                    EcSourceViewSet,
                    EcMeterListView,
                    ColdSourceStatsViewSet,
                    EcMeterViewSet,
                    MeterReading,
                    ColdSourceOverview, CopViewSet, CsEerAnalyseView)

cold_source_viewset = ColdSourceViewSet.as_view({
    'get': 'list',
    'post': 'create'
})

cold_source_detail = ColdSourceDetailViewSet.as_view({
    'get': 'retrieve',
    'put': 'partial_update',
    'delete': 'destroy'
})

cold_source_stats = ColdSourceStatsViewSet.as_view({
    'get': 'list'
})

ec_sources_viewset = EcSourceViewSet.as_view({
    'get': 'list',
    'post': 'create'
})

ec_source_detail = EcSourceViewSet.as_view({
    'delete': 'destroy',
    'put': 'partial_update'
})

cs_cop_list = CopViewSet.as_view({
    'get': 'list',
    'post': 'create'
})
cs_cop_detail = CopViewSet.as_view({
    'delete': 'destroy',
    'put': 'partial_update'
})

cs_eer_analyses = CsEerAnalyseView.as_view({
    'get': 'list'
})

ec_meter_list = EcMeterViewSet.as_view({
    'get': 'list',
    'post': 'create'
})
ec_meter_detail = EcMeterViewSet.as_view({
    'get': 'retrieve',
    'put': 'partial_update',
    'delete': 'destroy'
})

meter_reading_list = MeterReading.as_view({
    'get': 'list',
    'post': 'create'
})
meter_reading_detail = MeterReading.as_view({
    'get': 'retrieve',
    'put': 'partial_update',
    'delete': 'destroy'
})

# 冷源概况
cold_source_overview = ColdSourceOverview.as_view({
    'get': 'retrieve'
})

urlpatterns = [
    # 冷源
    path('saianapi/v1/cold_sources', cold_source_viewset, name='cold-source-list'),
    path('saianapi/v1/cold_sources/<int:pk>', cold_source_detail, name='cold-source-detail'),
    # 冷源能耗分析
    path('saianapi/v1/cs_eer_analyses', cs_eer_analyses, name='cold-source-eer'),
    # 能耗源 EcSource
    path('saianapi/v1/ec_sources', ec_sources_viewset, name='ec-sources-list'),
    path('saianapi/v1/ec_sources/<int:pk>', ec_source_detail, name='ec-source-detail'),
    # 能耗比COP
    path('saianapi/v5/cs_cops', cs_cop_list, name='cs-cop-list'),
    path('saianapi/v5/cs_cops/<int:pk>', cs_cop_detail, name='cs-cop-detail'),
    # 冷源状态
    path('saianapi/v5/cold_source_stats', cold_source_stats, name="cold-source-stats"),
    # 能耗仪表
    path('saianapi/v1/ec_meters', EcMeterListView.as_view()),
    path('saianapi/v5/ec_meters', ec_meter_list, name="ec-meter-list"),
    path('saianapi/v5/ec_meters/<int:pk>', ec_meter_detail, name="ec-meter-detail"),
    # 仪表读数
    path('saianapi/v5/meter_readings', meter_reading_list, name='meter-reading-list'),
    path('saianapi/v5/meter_readings/<int:pk>', meter_reading_detail, name='meter-reading-list'),
    # 冷源概况
    path('saianapi/v5/cs_overview', cold_source_overview, name='cold-source-overview'),
]
