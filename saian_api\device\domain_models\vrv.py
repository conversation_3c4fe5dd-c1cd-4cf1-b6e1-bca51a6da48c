import logging

from saian_api.devdefine.models import AttributePrototype
from saian_api.device.domain_models.base import BaseDevice
from saian_api.device.models import Device, DeviceEvent
from saian_api.terminal.models import Terminal
from saian_api.utils.inthttpapi.device import AdminDeviceApi

class EdgeVrv(BaseDevice):
    """ VRV空调网关(EDGE_V2) """
    @classmethod
    def update_attres(cls, device, event):
        data = event.get('data', {})
        event_id = event.get('event_id', None)

        if 'addr' in data and 'iu' in data:
            # 内机参数处理
            addr = data['addr']
            iu = data['iu']
            mac = f"{device.mac}-{addr}-{iu}"
            iu_device = Device.objects.get_or_create_iu(device, mac, addr, iu)
            if iu_device is not None:
                if 'SW' in data:
                    iu_device.sw_on = bool(data['SW'])
                if event_id is not None:
                    device_event = DeviceEvent.objects.get(pk=event_id)
                    device_event.mac = mac
                    device_event.device_id = iu_device.id
                    device_event.device_prototype_id = iu_device.device_prototype_id
                    device_event.save(update_fields=['mac', 'device_id', 'device_prototype_id'])
                super().update_attres(iu_device, event)
                iu_device.save(update_fields=['updated_at', 'sw_on'])
                # iu_device.save(using='syadmindb')
                # AdminDeviceApi.update_device(iu_device.id, {})
                Terminal.objects.bulk_update_by_device(iu_device, 'sw_on', iu_device.sw_on)
                Terminal.objects.bulk_update_by_device(iu_device, 'online', iu_device.online)
                return
        else:
            # VRV 网关参数处理
            for key, value in data.items():
                # 判断内机在线离线状态
                if key.endswith('OnlineStatus'):
                    # key like 'GW_2_OnlineStatus'
                    addr = key.split('_')[1]  # addr = '2'
                    addr = addr.zfill(3)  # convert to str '002'
                    if int(value):
                        iu_devices = Device.objects.filter(mac__startswith=f'{device.mac}-{addr}', online=False)
                        for iu in iu_devices:
                            iu.status = 20
                            iu.online = True
                    else:
                        iu_devices = Device.objects.filter(mac__startswith=f'{device.mac}-{addr}', online=True)
                        for iu in iu_devices:
                            iu.status = 30
                            iu.online = False
                    # 更新内机设备的 online 状态
                    Device.objects.bulk_update(iu_devices, fields=['status', 'online'])
                    # Device.objects.using('syadmindb').bulk_update(iu_devices, fields=['status', 'online'])
                    # for iu_device in iu_devices:
                    #     AdminDeviceApi.update_device(iu_device.id, {'status': iu_device.status, 'online': iu_device.iu})
                    # 更新终端的 online 状态
                    for device in iu_devices:
                        Terminal.objects.bulk_update_by_device(device, 'online', device.online)

                attr = AttributePrototype.objects.get_by_idf(device, key)
                if attr is not None:
                    super().update_da(device, event, attr, value)
                else:
                    # logging.error(f'VRV空调网关(EDGE_V2) 参数点不存在: dp_id-{device.device_prototype_id}, idf-{key}')
                    pass

            # 处理网关的在离线状态
            if 'status' in data:
                if data['status'] == 'online':
                    Device.objects.filter(mac__startswith=device.mac, online=False).update(online=True, status=20)
                elif data['status'] == 'offline':
                    Device.objects.filter(mac__startswith=device.mac, online=True).update(online=False, status=30)

        # super().update_attres(device, event)

    @classmethod
    def fault(cls, device, event):
        super().fault(device, event)

    @classmethod
    def alarm(cls, device, event):
        super().alarm(device, event)

