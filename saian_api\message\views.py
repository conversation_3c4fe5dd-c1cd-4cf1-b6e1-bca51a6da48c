# Create your views here.
import json

from django.shortcuts import get_object_or_404
from rest_framework import viewsets, status
from rest_framework.response import Response

from saian_api.message.models import Message, UserMessage
from saian_api.message.serializers import MessageSerializer
from saian_api.user.models import WebUser
from saian_api.utils.sy_jsonrenderer import SyJSONRender

class MessageViewSet(viewsets.ModelViewSet):
    renderer_classes = (SyJSONRender,)
    serializer_class = MessageSerializer

    def get_queryset(self):
        user_id = self.request.user['id']
        return Message.objects.filter(web_user=user_id).order_by('-created_at')

    def list(self, request, *args, **kwargs):
        user_id = request.user['id']
        msg_type = request.query_params.get('types', None)
        is_read = request.query_params.get('read', None)
        page = request.query_params.get('page', None)
        per_page = request.query_params.get('per_page', None)

        web_user = WebUser.objects.get(id=user_id)
        if msg_type is None:
            if web_user.msg_types:
                msg_types = json.loads(web_user.msg_types)
                types = [msg.get('msg_type') for msg in filter(lambda x: x.get('blocked') is False, msg_types)]
                types = list(set(types + [1, 8, 9]))
            else:
                types = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 99]
        else:
            types = msg_type.split(',')

        query_set = self.get_queryset()
        messages = query_set.filter(msg_type__in=types)
        # unread_messages = messages.filter(usermessage__read=False)
        unread_messages = UserMessage.objects.filter(read=False, web_user=web_user)
        unread_count = {}
        msg_count = {}
        for msg_type in types:
            unread_count[msg_type] = unread_messages.filter(message__msg_type=int(msg_type)).count()
            msg_count[msg_type] = messages.filter(msg_type=int(msg_type)).count()

        if is_read is not None:
            messages = messages.filter(usermessage__read=True if is_read == 'true' else False, web_user=user_id)

        count = messages.count()
        messages = messages.order_by('usermessage__read', '-created_at')
        if page is None and per_page is None:
            messages = messages[:30]
        else:
            messages = self.paginate_queryset(messages)

        messages = MessageSerializer(messages, many=True).data
        msg_ids = [msg.get('id') for msg in messages]
        ums = list(UserMessage.objects.filter(web_user=web_user, message_id__in=msg_ids))
        for msg in messages:
            um = next(filter(lambda x: x.message_id == msg.get('id'), ums), None)
            msg['useful'] = um.useful if um else 9
            msg['read'] = um.read if um else False

        return Response({
            'messages': messages,
            'count': count,
            'unread_count': unread_count,
            'msg_count': msg_count
        })

class UserMessageViewSet(viewsets.ModelViewSet):
    renderer_classes = (SyJSONRender,)
    serializer_class = MessageSerializer

    def create(self, request, *args, **kwargs):
        """标记消息已读"""
        msg_ids = request.data.get('msg_ids', None)
        user_id = request.user['id']
        msg_type = request.data.get('msg_type', None)

        if msg_ids is None:
            user_messages = UserMessage.objects.filter(read=False, web_user_id=user_id)
        else:
            msg_ids = msg_ids.split(',')
            user_messages = UserMessage.objects.filter(web_user_id=user_id, message_id__in=msg_ids, read=False)

        if msg_type is not None:
            user_messages = user_messages.filter(message__msg_type=msg_type)

        for user_msg in user_messages:
            user_msg.read = True

        UserMessage.objects.bulk_update(user_messages, ['read'])

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def list(self, request, *args, **kwargs):
        user_id = request.user['id']
        try:
            web_user = WebUser.objects.get(id=user_id)
        except WebUser.DoesNotExist:
            return Response({"message": {}})

        msg_type = request.query_params.get('types', None)
        page = request.query_params.get('page', None)
        per_page = request.query_params.get('per_page', None)

        if msg_type is None:
            if web_user.msg_types:
                msg_types = json.loads(web_user.msg_types)
                types = [msg.get('msg_type') for msg in filter(lambda x: x.get('blocked') is False, msg_types)]
            else:
                types = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 99]
        else:
            types = msg_type.split(',')

        types = list(set(types + [1, 8, 9]))

        query_set = Message.objects.filter(web_user=user_id, usermessage__read=False)
        result = {}
        for type in types:
            messages = query_set.filter(msg_type=type)
            count = messages.count()
            if page is None and per_page is None:
                messages = messages[:30]
            else:
                messages = self.paginate_queryset(messages)
            result[type] = {
                'messages': MessageSerializer(messages, many=True, context={'request': request}).data,
                'count': count
            }
            # messages = Message.objects.raw(
            #     f"""select * from messages msg inner join user_messages u_msg on msg.id = u_msg.message_id
            #                    where u_msg.`read`=false and u_msg.web_user_id={web_user.id} limit 30""")
            #
            # cursor = connections[f'prj{project_id}db'].cursor()
            # cursor.execute(f"""select COUNT(*) from messages msg inner join user_messages u_msg on msg.id = u_msg.message_id
            #                    where u_msg.`read`=false and u_msg.web_user_id={web_user.id} and msg.msg_type={type} """)
            # count = cursor.fetchall()
            #
            # result[type] = {
            #     'messages': [{'id': msg.id, 'msg_type': msg.msg_type, 'content': msg.content} for msg in messages],
            #     'count': count[0][0]
            # }
        return Response({'messages': result})

    def retrieve(self, request, *args, **kwargs):
        message = Message.objects.get(pk=kwargs.get('pk'))
        serializer = MessageSerializer(message, context={'request': request})
        return Response({
            'status': 200,
            'message': serializer.data
        })

    def update(self, request, *args, **kwargs):
        """标记消息有用或无用，或取消标记"""
        msg_id = kwargs.get('pk')
        user_id = request.user['id']
        user_message = get_object_or_404(UserMessage, message_id=msg_id, web_user_id=user_id)
        useful = request.data.get('useful', None)
        if useful is None:
            return Response(status=status.HTTP_400_BAD_REQUEST,
                            data={"status": status.HTTP_400_BAD_REQUEST, 'error': 'useful 不能为空'})
        user_message.useful = int(useful)
        user_message.save()
        message = Message.objects.get(pk=msg_id)
        return Response({'status': status.HTTP_200_OK, 'data': MessageSerializer(message, context={'request': request}).data})


class UserMessageConfigView(viewsets.ModelViewSet):
    def create(self, request, *args, **kwargs):
        msg_types = request.data.get('msg_types', None)
        if msg_types is None:
            return Response(status=status.HTTP_400_BAD_REQUEST,
                            data={"status": status.HTTP_400_BAD_REQUEST, 'error': 'msg_types 不能为空'})
        web_user = WebUser.objects.get(pk=request.user['id'])
        user_message_config = json.loads(web_user.msg_types) if web_user.msg_types else []
        msg_types = [int(msg_type) for msg_type in msg_types.split(',')] if msg_types != '' else []
        for config in user_message_config:
            if config['msg_type'] in msg_types:
                config['blocked'] = False
                msg_types.remove(config['msg_type'])
            else:
                config['blocked'] = True

        for msg_type in msg_types:
            user_message_config.append({'msg_type': msg_type, 'blocked': False, 'sql': 0})

        web_user.msg_types = json.dumps(user_message_config)
        web_user.save()
        return Response({'status': status.HTTP_200_OK, 'data': user_message_config})

    def retrieve(self, request, *args, **kwargs):
        web_user = get_object_or_404(WebUser, pk=request.user['id'])
        config = json.loads(web_user.msg_types) if web_user.msg_types else ''
        return Response({'status': status.HTTP_200_OK, 'data': config})
