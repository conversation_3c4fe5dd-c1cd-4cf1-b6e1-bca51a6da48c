import datetime
import json
import logging
import traceback

from django.core.management import BaseCommand, CommandError
from kombu.exceptions import EncodeError

from saian_api.building.models import ActiveRoom
from saian_api.devdefine.models import AttributePrototype
from saian_api.device.models import RoomDevice, Device, DeviceEvent
from saian_api.issue.models import DeviceIssue
from saian_api.project.models import Project
from saian_api.report.apps import ReportConfig
from saian_api.report.models import ReportConfigurer, RoomHourlyStat
from saian_api.scheduledtask.utils import get_projects, set_global_db
from saian_api.utils.db.Redis import RedisHelper
from saian_api.utils.tools import is_number

class Command(BaseCommand):
    help = '每小时统计房间的数据'

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目ID列表
        projects = get_projects(self, options)
        now = datetime.datetime.now()
        # now = datetime.datetime.strptime('20240515150500', '%Y%m%d%H%M%S')
        for project_id in projects:
            try:
                # 设置全局数据库
                set_global_db(project_id)
                project = Project.objects.get(pk=project_id)
                self.stdout.write(f"房间小时统计开始: {project.name}", ending='\n')

                prj_room_ids = list(ActiveRoom.objects.values_list('id', flat=True).distinct())
                # prj_room_ids = [246]
                report_cfgs = list(ReportConfigurer.objects.filter(target_type__model='ActiveRoom', target_id__in=prj_room_ids))

                room_ids = [report.target_id for report in report_cfgs]

                rooms = list(ActiveRoom.objects.filter(id__in=room_ids))
                device_ids = RoomDevice.objects.filter(content_type__model='Device', active_room_id__in=room_ids).values_list('object_id', flat=True)
                devices = Device.objects.filter(id__in=device_ids)

                # 查找所有相关属性类型
                ap_names = [f'attribute_prototype:{rc.target_id}_{rc.identifier}' for rc in report_cfgs]
                aps = AttributePrototype.objects.query_by_names(project_id, ap_names)

                # 上一小时设备相关事件
                one_hour_ago = now - datetime.timedelta(hours=1)
                begin = datetime.datetime.combine(one_hour_ago.date(), datetime.time(one_hour_ago.hour, 0, 0))
                end = begin + datetime.timedelta(hours=1)
                print(begin, end)
                device_events = {}

                for rc in report_cfgs:
                    # rc_devices = list(filter(lambda x: x.device_prototype_id == rc.target_id, devices))
                    rc_rooms = list(filter(lambda x: x.id == rc.target_id, rooms))
                    for room in rc_rooms:
                        values = []
                        room_did = list(RoomDevice.objects.filter(content_type__model='Device', active_room_id=room.id
                                                                  ).values_list('object_id', flat=True))
                        room_devices = list(filter(lambda x: x.id in room_did, devices))
                        for device in room_devices:
                            ap = next(filter(lambda x: x.device_prototype_id == device.device_prototype_id and x.identifier == rc.identifier, aps),
                                      None)
                            if ap is None:
                                ap = AttributePrototype.objects.filter(device_prototype_id=device.device_prototype_id,
                                                                       identifier=rc.identifier).last()
                            if ap is None:
                                # logging.error(f'\n房间小时统计任务出错，找不到属性类型：target-id-{rc.target_id}, identifier-{rc.identifier}')
                                continue
                            else:
                                if ap is not None and ap.data_type == 20:
                                    if not ap.is_cum:
                                        name = f'device_event_{(now - datetime.timedelta(hours=1)).strftime("%Y%m%d%H")}:{device.id}_{ap.identifier}'
                                        # 查询缓存的数据
                                        cache_values = RedisHelper.get_list(project_id, name, True)
                                        events = None

                                        if len(cache_values) == 0:
                                            # 如果没有缓存数据，就从事件表中查询
                                            events = device_events.get(device.id, None)
                                            if events is None:
                                                events = list(DeviceEvent.objects.filter(device_id=device.id, created_at__gte=begin,
                                                                                         created_at__lte=end).order_by('-created_at'))
                                                device_events[device.id] = events
                                        device_stat = self.val_stat(device, ap, cache_values, events)
                                        if device_stat:
                                            values.append(device_stat)
                        if len(values):
                            value = round(sum(values) / len(values), 2)
                            self.cus_create(room, rc.identifier, value, min_value=min(values), max_value=max(values), now=now)

                self.stdout.write(f"房间小时统计任务完成: {project.name}", ending='\n')

            except CommandError:
                self.stderr.write(f"运行'房间小时统计'任务失败，项目ID：{project_id}，命令参数不合法！", ending='\n')
                continue
            except Exception as e:
                self.stderr.write(f"运行'房间小时统计'任务失败，项目ID：{project_id}, error: {e.__str__()}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
                continue

    @classmethod
    def val_stat(cls, device, ap, cache_values, device_events):
        values = []

        if len(cache_values):
            values = [float(v) for v in cache_values if is_number(v)]
        else:
            for record in device_events:
                try:
                    data = json.loads(record.data)
                    if ap.identifier in data:
                        value = float(data[ap.identifier])
                        if device.in_fault:
                            last_fault = DeviceIssue.objects.last_not_solved_issue(10, device)
                            if last_fault is not None and '校验错误' in last_fault.display_name:
                                continue
                        if device.in_alarm:
                            last_alarm = DeviceIssue.objects.last_not_solved_issue(20, device)
                            if last_alarm is not None and '校验错误' in last_alarm.display_name:
                                continue
                        if float(ap.min_value) < value < float(ap.max_value):
                            values.append(value)
                except EncodeError:
                    logging.error(f'EncodeError: {record.data}')
                    continue
                except ValueError:
                    logging.error(f'ValueError: {record.data}')
                    continue

        if len(values):
            avg_value = sum(values) / len(values)
            return round(avg_value, 2)
        return 0

    @classmethod
    def cus_create(cls, room, identifier, value, min_value=None, max_value=None, now=None):
        if now is None:
            now = datetime.datetime.now()
        created_at = (now - datetime.timedelta(hours=1)).strftime('%Y-%m-%d %H:59:59')
        rhs = RoomHourlyStat.objects.filter(active_room_id=room.id, identifier=identifier, created_at=created_at).last()
        if rhs is None:
            RoomHourlyStat.objects.create(
                active_room_id=room.id,
                identifier=identifier,
                avg=value,
                min=min_value,
                max=max_value,
                created_at=created_at
            )
        else:
            if rhs.avg != str(value):
                logging.info(f'\n{room.name}, {identifier}, {rhs.id}, {created_at}, old: {rhs.avg}, new: {value}')
                rhs.avg = value
                rhs.min = min_value
                rhs.max = max_value
                rhs.save()
                pass
