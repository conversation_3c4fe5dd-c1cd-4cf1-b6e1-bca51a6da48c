"""
    设备分钟统计
"""
import datetime
import json
import logging
import traceback

from django.core.management import BaseCommand, CommandError

from saian_api.devdefine.models import AttributePrototype
from saian_api.device.models import Device, ParamRecord, DeviceEvent
from saian_api.project.models import Project
from saian_api.report.models import ReportConfigurer, DeviceMinuteStat
from saian_api.scheduledtask.utils import get_projects, set_global_db


class Command(BaseCommand):
    help = '补全分钟统计数据处理'

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目ID列表
        projects = get_projects(self, options)
        now = datetime.datetime.now()
        one_minute_ago = now - datetime.timedelta(minutes=1)
        begin = one_minute_ago.strftime('%Y-%m-%d %H:%M:00')
        end = one_minute_ago.strftime('%Y-%m-%d %H:%M:59')

        for project_id in projects:
            try:
                # 设置全局数据库
                set_global_db(project_id)
                project = Project.objects.get(pk=project_id)

                self.stdout.write(f"补全设备分钟统计数据任务开始: {project.name}", ending='\n')

                report_cfgs = ReportConfigurer.objects.filter(target_type__model='DevicePrototype', do_minstat=True)
                if report_cfgs.exists():
                    for cfg in report_cfgs:
                        devices = Device.objects.filter(device_prototype_id=cfg.target_id)
                        aps = AttributePrototype.objects.filter(device_prototype_id=cfg.target_id, identifier=cfg.identifier)
                        if devices.exists() and aps.exists():
                            ap = aps.last()
                            for device in devices:
                                dms = DeviceMinuteStat.objects.filter(
                                    device_id=device.id, identifier=ap.identifier, created_at__range=(begin, end))
                                # 有记录则跳过
                                if not dms.exists():
                                    if ap.data_type != 20:
                                        # 非数值
                                        DeviceMinuteStatUtils.str_stats(device, ap, begin)
                                    elif not ap.is_cum == 1:
                                        # 数值 + 非计算值
                                        DeviceMinuteStatUtils.val_stat(device, ap.identifier, ap.pre_cision, begin)

                self.stdout.write(f"补全分钟统计数据处理任务完成: {project.name}", ending='\n')

            except CommandError:
                self.stderr.write(f"运行'补全分钟统计数据处理'任务失败，项目ID：{project_id}，命令参数不合法！", ending='\n')
                continue
            except Exception as e:
                self.stderr.write(f"运行'补全分钟统计数据处理'任务失败，项目ID：{project_id}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
                continue


class DeviceMinuteStatUtils:
    @classmethod
    def val_stat(cls, device, identifier, pre_cision, begin):
        """
          数值 + 非计算值
          若分钟统计为空，则直接取最后一条记录的值
          反之，取当前统计时刻往前3个数据进行平均处理
        """
        dmss = DeviceMinuteStat.objects.filter(device_id=device.id, identifier=identifier).order_by('-created_at')
        count = dmss.count()
        if count:
            fixed = len(pre_cision.split('.')[1]) if pre_cision and '.' in pre_cision else 3
            avg = 0.0
            if count < 3:
                avg = sum(float(dms.avg) for dms in dmss) / count
            else:
                avg = sum(float(dms.avg) for dms in dmss[:3]) / 3
            cls.cus_create(device, identifier, round(float(avg), fixed), begin)
        else:
            records = DeviceEvent.objects.filter(device_id=device.id, data__icontains=identifier).order_by('-created_at')
            if records.exists():
                for record in records:
                    data = json.loads(record.data)
                    if identifier in data:
                        cls.cus_create(device, identifier, data[identifier], begin)
                        return

    @classmethod
    def str_stats(cls, device, ap, begin):
        """
          非数值，则用最后一条记录的值
        """
        records = DeviceEvent.objects.filter(device_id=device.id, data__icontains=ap.identifier).order_by('-created_at')
        if records.exists():
            for record in records:
                data = json.loads(record.data)
                if data[ap.identifier]:
                    if ap.options:
                        options = ap.options.split(',')
                        result = options[int(data[ap.identifier])]
                    else:
                        result = data[ap.identifier]
                    cls.cus_create(device, ap.identifier, result, begin)
                    return

    @staticmethod
    def cus_create(device, identifier, avg, begin):
        DeviceMinuteStat.objects.create(
            device_id=device.id,
            mac=device.mac,
            identifier=identifier,
            avg=avg,
            created_at=begin
        )
