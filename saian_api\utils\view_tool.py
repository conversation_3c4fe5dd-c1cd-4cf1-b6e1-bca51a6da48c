
"""
  根据设备构造设备属性和值
"""
from saian_api.device.models import DeviceAttribute

def device_attributes(device):
    from saian_api.devdefine.models import AttributePrototype
    results = []
    aps = list(AttributePrototype.objects.filter(device_prototype_id=device.device_prototype_id))
    ap_ids = [ap.id for ap in aps]
    for da in DeviceAttribute.objects.query_object_list(device, ap_ids):
        da_aps = list(filter(lambda x: x.id == da.attribute_prototype_id, aps))
        if len(da_aps):
            ap = da_aps[0]
            # ap = AttributePrototype.objects.get(pk=da.attribute_prototype_id)
            result = {
                'id': da.id,
                'data_type': ap.data_type,
                'identifier': ap.identifier,
                'icon': ap.icon,
                'name': ap.name,
                'options': ap.options,
                'read_only': ap.read_only,
                'remark': ap.remark,
                'seq': ap.seq,
                'unit': ap.unit,
                'value': da.value,
            }
            results.append(result)

    return results
