"""
    维度小时统计。只统计数值类型。
"""
import datetime
import json
import logging
import re
import traceback
from collections import defaultdict

from django.core.management import BaseCommand, CommandError
from kombu.exceptions import EncodeError

from saian_api.devdefine.models import AttributePrototype
from saian_api.device.models import ParamRecord, DeviceEvent, ParamRecordHistory
from saian_api.dimension.models import DimensionAttribute, Dimension, DimensionHourlyStat
from saian_api.project.models import Project
from saian_api.report.models import ReportConfigurer, DeviceHourlyStat
from saian_api.scheduledtask.utils import get_projects, set_global_db
from saian_api.terminal.models import TerminalAttribute
from saian_api.utils.db.Redis import RedisHelper
from saian_api.utils.tools import is_number

class Command(BaseCommand):
    help = '每小时统计维度的数据'

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    @staticmethod
    def cus_create(dimension_id, dimension_attribute, value, min_value: [float, str] = '--', max_value: [float, str] = '--', now=None):
        value = round(value, 3)
        if is_number(min_value):
            min_value = round(min_value, 3)
        if is_number(max_value):
            max_value = round(max_value, 3)

        if now is None:
            now = datetime.datetime.now()
        # created_at = (now - datetime.timedelta(hours=1)).strftime('%Y-%m-%d %H:59:59')
        created_at = (now - datetime.timedelta(hours=1)).replace(minute=59, second=59, microsecond=0)
        record = DimensionHourlyStat.objects.filter(dimension_id=dimension_id, dimension_attribute_id=dimension_attribute.id,
                                                    created_at=created_at).last()

        if record is None:
            return DimensionHourlyStat.objects.create(
                dimension_id=dimension_id,
                dimension_attribute_id=dimension_attribute.id,
                avg=value,
                min=min_value,
                max=max_value,
                created_at=created_at
            )
        else:
            if record.avg != str(value):
                logging.info(f'{dimension_attribute.name}, {created_at}, old-{record.avg}, new-{value}')
                record.avg = value
                record.save()

            return record


    @classmethod
    def val_stat(cls, device, identifier, now):
        """
          统计数值（非计算值、非累计值）类数据
        """
        one_hour_ago = now - datetime.timedelta(hours=1)
        begin = one_hour_ago.strftime('%Y-%m-%d %H:00:00')
        end = one_hour_ago.strftime('%Y-%m-%d %H:59:59')

        records = DeviceEvent.objects.filter(device_id=device.id, created_at__range=(begin, end))

        values = []

        if records.exists():
            for record in records:
                try:
                    data = json.loads(record.data)
                    if identifier in data:
                        values.append(float(data[identifier]))
                except EncodeError:
                    continue
                except ValueError:
                    continue

            if len(values):
                avg_value = sum(values) / len(values)
                return avg_value

    @staticmethod
    def cal_cum_stat(device, identifier, now):
        """ 累计值 """
        result = 0.0
        one_hour_ago = now - datetime.timedelta(hours=1)

        # 从缓存查询
        current_name = f'param_record_{now.strftime("%Y%m%d%H")}:{device.id}_{identifier}'
        current_reading = RedisHelper.get_value(device.project_id, current_name, True)
        prev_name = f'param_record_{one_hour_ago.strftime("%Y%m%d%H")}:{device.id}_{identifier}'
        prev_reading = RedisHelper.get_value(device.project_id, prev_name, True)

        # 没有缓存，从 param_records 表查询
        if current_reading is None:
            current_hour = now.strftime('%Y-%m-%d %H:05:00')
            current_pr = ParamRecord.objects.get_by_hour(current_hour, device, identifier)
            if current_pr is not None:
                current_reading = current_pr.value
        if prev_reading is None:
            pre_hour = one_hour_ago.strftime('%Y-%m-%d %H:05:00')
            prev_pr = ParamRecord.objects.get_by_hour(pre_hour, device, identifier)
            if prev_pr is not None:
                prev_reading = prev_pr.value

        if prev_reading is not None and current_reading is not None:
            # current_value = round(float(current_reading if current_reading != '' else 0), 2)
            # prev_value = round(float(prev_reading if prev_reading != '' else 0), 2)
            current_value = round(float(current_reading) if current_reading not in [None, ''] else 0, 2)
            prev_value = round(float(prev_reading) if prev_reading not in [None, ''] else 0, 2)

            result = current_value - prev_value
            # 小于-1时，可能是换表，需要重新计算
            # 算法：上个小时最大的读数减去上个小时的读数 + 当前小时的读数
            # -1 < result < 0 时，result = 0
            if result < 0:
                if result < -1:

                    beginning_of_hour = one_hour_ago.replace(minute=0, second=0, microsecond=0)
                    end_of_hour = one_hour_ago.replace(minute=59, second=59, microsecond=0)

                    records = ParamRecord.objects.filter(device_id=device.id, identifier=identifier,
                                                         created_at__range=(beginning_of_hour, end_of_hour)).values_list('value', flat=True)

                    values = [round(float(v), 2) for v in records if v not in [None, '']]
                    result = max(values) - prev_value + current_value if values else 0
                else:
                    result = 0
        return result

    def handle(self, *args, **options):
        # 从命令行解释的项目ID列表
        projects = get_projects(self, options)
        # projects = [66]
        # 统计任务开始时的时间。
        now = datetime.datetime.now()
        # now = datetime.datetime.fromisoformat('2025-08-21 01:05:00')

        for project_id in projects:
            try:
                # 设置全局数据库
                set_global_db(project_id)
                project = Project.objects.get(pk=project_id)

                self.stdout.write(f"维度小时统计开始: {project.name}", ending='\n')

                all_dimension_attrs = DimensionAttribute.objects.filter(dimension_id__isnull=False)

                regular_dimension_attrs = [attr for attr in all_dimension_attrs if attr.ta_ids]
                formula_dimension_attrs = [attr for attr in all_dimension_attrs if attr.formula]

                # 查询所有有关终端属性
                all_ta_ids = set()
                for attr in regular_dimension_attrs:
                    if attr.ta_ids:
                        all_ta_ids.update(map(int, attr.ta_ids.split(',')))

                tas = TerminalAttribute.objects.filter(id__in=all_ta_ids).select_related('terminal')

                # 构建 dimension_attr_id -> ta list 映射
                attr_tas_map = defaultdict(list)

                for attr in regular_dimension_attrs:
                    if attr.ta_ids:
                        for ta_id in map(int, attr.ta_ids.split(',')):
                            ta = next((t for t in tas if t.id == ta_id), None)
                            if ta:
                                attr_tas_map[attr.id].append(ta)

                new_records = []

                for attr in regular_dimension_attrs:
                    if attr.ta_ids:
                        # tas = TerminalAttribute.objects.filter(id__in=attr.ta_ids.split(','))
                        ta_list = attr_tas_map[attr.id]
                        values = []
                        for ta in ta_list:
                            # 累积值-param_records 非累积值-device_events
                            if attr.is_cum:
                                ta_value = self.cal_cum_stat(ta.terminal.device, ta.identifier, now)
                            else:
                                ta_value = self.val_stat(ta.terminal.device, ta.identifier, now)

                            if ta_value is not None:
                                values.append(ta_value)

                        if len(values):
                            if not attr.is_cum:
                                avg = sum(values) / len(values)
                                record = self.cus_create(attr.dimension_id, attr, avg, min(values), max(values), now=now)
                            else:
                                record = self.cus_create(attr.dimension_id, attr, sum(values), now=now)

                            new_records.append(record)

                # 最后计算有公式的维度属性值
                for da in formula_dimension_attrs:
                    # da_ids = re.findall(r'{{(.*?)}}', da.formula)
                    da_ids = [int(x) for x in re.findall(r'{{(.*?)}}', da.formula)]
                    da_values = []

                    dhs_records = [record for record in new_records if record.dimension_attribute_id in da_ids]

                    for dhs in dhs_records:
                        # dhs = DimensionHourlyStat.objects.filter(created_at=created_at, dimension_attribute_id=da_id).last()
                        # da = DimensionAttribute.objects.get(pk=da_id)
                        da_values.append(float(dhs.avg) if dhs is not None and is_number(dhs.avg) else 0)
                    # 构建计算表达式
                    eval_str = da.formula
                    for v in da_values:
                        eval_str = re.sub(r'{{(.*?)}}', f'{v}', eval_str, 1)

                    try:
                        value = eval(eval_str)
                        self.cus_create(da.dimension_id, da, value, now=now)

                    except Exception as e:
                        self.stdout.write(f'计算维度属性 {da.id}-{da.name} 公式时出错，表达式为: {eval_str}; ERROR: {e.__str__()}')

                self.stdout.write(f"维度小时统计任务完成: {project.name}", ending='\n')

            except CommandError:
                self.stderr.write(f"运行'维度小时统计'任务失败，项目ID：{project_id}，命令参数不合法！", ending='\n')
                continue
            except Exception as e:
                self.stderr.write(f"运行'维度小时统计'任务失败，项目ID：{project_id}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
                continue
