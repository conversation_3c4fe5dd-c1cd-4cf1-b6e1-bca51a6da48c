from django.contrib.contenttypes.models import ContentType
from django.core.exceptions import ObjectDoesNotExist
from rest_framework import serializers

from saian_api.device.models import Device
from saian_api.group.models import Group, GroupDevice
from saian_api.notifi.models import NotifyConfig, NotifyLimit, Notification
from saian_api.report.serializers import SimpleDeviceSerializer
from saian_api.terminal.models import Terminal
from saian_api.terminal.serializers import SimpleTerminalSerializer
from saian_api.user.models import WebUser
from saian_api.user.serializers import SimpleWebUserSerializer

class VerifiedCodeSerializer(serializers.Serializer):
    mobile = serializers.CharField(min_length=11, max_length=11)

    def validate_mobile(self, value):
        if not str(value).isdigit():
            raise serializers.ValidationError('Mobile must be digit!')

        if ' ' in value:
            raise serializers.ValidationError('Mobile cannot has space!')

        return value


class NotifyConfigSerializer(serializers.ModelSerializer):

    class Meta:
        model = NotifyConfig
        fields = ['id', 'msg_type', 'web_user', 'issue_name', 'noti_way', 'concern_type', 'concern_id']

    def to_representation(self, instance):
        ret = super().to_representation(instance)

        group_by = self.context.get('group_by', None)

        if group_by != 'user':
            web_user = WebUser.objects.get(pk=ret['web_user'])
            serializer = SimpleWebUserSerializer(web_user)
            ret['web_user'] = serializer.data

        if group_by != 'concern':
            ret['concern'] = {
                "id": ret['concern_id'],
                "concern_type": ret['concern_type'],
                "name": None
            }
            if ret['concern_type']:
                concern_type = ContentType.objects.get(pk=ret['concern_type'])
                ret['concern']["concern_type"] = concern_type.model_class().__name__
                try:
                    target = concern_type.model_class().objects.get(pk=ret['concern_id'])
                    if concern_type.model == 'device' or concern_type.model == 'terminal':
                        ret['concern']['name'] = target.nick_name
                    else:
                        ret['concern']['name'] = target.name
                except ObjectDoesNotExist:
                    ret['concern']['name'] = None

        ret.pop('concern_type')
        ret.pop('concern_id')
        return ret


class NotifyLimitSerializer(serializers.ModelSerializer):
    class Meta:
        model = NotifyLimit
        fields = '__all__'

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        ret['concern'] = {
            "id": ret['concern_id'],
        }
        concern_type = ContentType.objects.get(pk=ret['concern_type'])
        ret['concern']["concern_type"] = concern_type.model_class().__name__
        try:
            target = concern_type.model_class().objects.get(pk=ret['concern_id'])
            if concern_type.model == 'device' or concern_type.model == 'terminal':
                ret['concern']['name'] = target.nick_name
            else:
                ret['concern']['name'] = target.name
        except ObjectDoesNotExist:
            ret['concern']['name'] = None

        device_count = 1
        devices = None
        terminals = None

        if ret['concern']["concern_type"] == 'DevicePrototype':
            device_count = Device.objects.filter(device_prototype_id=ret['concern_id']).count()
            devices = Device.objects.filter(device_prototype_id=ret['concern_id'])

        if ret['group']:
            group = Group.objects.get(pk=ret['group'])
            ret['group'] = {
                'id': group.id,
                'name': group.name
            }

            if ret['concern']["concern_type"] == 'Device':
                device_ids = [ret['concern_id']] + list(GroupDevice.objects.filter(group_id=ret['group']['id'],
                                                                                   content_type=concern_type).values_list('object_id', flat=True))
                device_count = len(set(device_ids))
                devices = Device.objects.filter(id__in=device_ids)

            if ret['concern']["concern_type"] == 'Terminal':
                terminal_ids = [ret['concern_id']] + list(GroupDevice.objects.filter(group_id=ret['group']['id'],
                                                                                     content_type=concern_type).values_list('object_id', flat=True))
                device_count = len(set(terminal_ids))
                terminals = Terminal.objects.filter(id__in=terminal_ids)

        else:
            if ret['concern']["concern_type"] == 'Device':
                devices = Device.objects.filter(id=ret['concern_id'])
            elif ret['concern']["concern_type"] == 'Terminal':
                terminals = Terminal.objects.filter(id=ret['concern_id'])

        ret['device_count'] = device_count
        if devices is not None:
            devices = SimpleDeviceSerializer(devices, many=True).data
        ret['devices'] = devices
        if terminals is not None:
            terminals = SimpleTerminalSerializer(terminals, many=True).data
        ret['terminals'] = terminals

        return ret

class NotificationSerializer(serializers.ModelSerializer):
    web_user = SimpleWebUserSerializer()

    class Meta:
        model = Notification
        fields = '__all__'

    def to_representation(self, instance):
        ret = super().to_representation(instance)

        ret['concern'] = {
            "id": ret['concern_id'],
            "concern_type": ret['concern_type'],
            "name": None
        }
        if ret['concern_type']:
            concern_type = ContentType.objects.get(pk=ret['concern_type'])
            ret['concern']["concern_type"] = concern_type.model_class().__name__
            try:
                target = concern_type.model_class().objects.get(pk=ret['concern_id'])
                if concern_type.model == 'device' or concern_type.model == 'terminal':
                    ret['concern']['name'] = target.nick_name
                else:
                    ret['concern']['name'] = target.name
            except ObjectDoesNotExist:
                ret['concern']['name'] = None

        return ret
