import datetime
import json
import logging
from json import JSONDecodeError

from django.core import exceptions
from rest_framework import serializers
from rest_framework.serializers import PrimaryKeyRelatedField

from saian_api.devdefine.models import AttributePrototype, DevicePrototype
from saian_api.project.models import Project
from saian_api.user.models import WebUser
from saian_api.utils import view_tool
from saian_api.utils.tools import validate_value, validate_week
from .models import (
    <PERSON>ce,
    DeviceCtrlLog,
    DeviceEvent, DeviceLimit, DeviceRuntime, DataRoute, SySim, DaSnapshot
)
from ..devdefine.serializers import SimpleDevicePrototypeSerializer
from ..group.models import ActionTimer
from ..linkage.models import LinkageRule
from ..utils.httpapi.image import ImageAPI


class DeviceSerializer(serializers.ModelSerializer):
    # device_type = PrimaryKeyRelatedField(queryset=DeviceType.objects.all())
    # device_prototype = PrimaryKeyRelatedField(queryset=DevicePrototype.objects.all())
    project = PrimaryKeyRelatedField(queryset=Project.objects.all())

    class Meta:
        model = Device
        fields = ['id', 'device_type_id', 'device_prototype_id', 'nick_name', 'mac', 'did', 'address', 'status', 'remark', 'in_alarm', 'wudid',
                  'in_fault', 'online', 'sw_on', 'in_acc', 'needs_m', 'created_at', 'updated_at', 'project']
        # read_only_fields = ('id', 'created_at', 'updated_at')


class DeviceListV2Serializer(serializers.ModelSerializer):

    class Meta:
        model = Device
        fields = ['id', 'nick_name', 'mac', 'device_type_id', 'device_prototype_id']


class DeviceAttributeSerializer(serializers.ModelSerializer):
    class Meta:
        model = AttributePrototype
        fields = ['id', 'name', 'identifier', 'remark', 'read_only', 'data_type', 'pre_cision',
                  'in_crement', 'min_value', 'max_value', 'options', 'length', 'icon', 'unit', 'default_value',
                  'seq', 'send_immediate', 'is_key', 'can_debug', 'hidden', 'in_add', 'show_in_list', 'formula',
                  'is_cum', 'label', 'do_export', 'created_at', 'updated_at', 'device_prototype', 'attribute_type']
        read_only_fields = ['id', 'created_at', 'updated_at']


class DeviceDetailSerializer(serializers.ModelSerializer):
    class Meta:
        model = Device
        fields = ['id', 'nick_name', 'mac', 'did', 'address', 'status', 'remark', 'in_alarm', 'wudid', 'in_fault', 'online', 'sw_on', 'in_acc',
                  'needs_m', 'created_at', 'updated_at']

    def to_representation(self, device):
        ret = super().to_representation(device)

        request = self.context['request']

        # 来自扫设备二维码
        from_scan = request.query_params.get('scan', None)

        device_prototype = device.device_prototype
        device_type = device.device_type

        if from_scan is not None and from_scan == '1':
            ret = {
                'device_id': device.id,
                'project_id': device.project_id,
                'project': {
                    'id': device.project_id,
                    'name': device.project_name if device.project_id else None
                },
                'address': device.address,
                'added': True if device.project_id is not None else False,
                'device_type': {
                    'id': device_type.id,
                    'name': device_type.parent.name,
                    'uni_name': device_type.uni_name
                },
                'device_prototype': {
                    'id': device_prototype.id,
                    'name': device_prototype.name,
                    'uni_name': device_prototype.uni_name
                }
            }
        else:
            ret['device_attributes'] = view_tool.device_attributes(device)
            ret['last_update_at'] = device.last_update_at()
            # device_type = device.device_type
            ret['icon'] = device_type.icon
            ret['device_type'] = {
                'id': device_type.id,
                'name': device_type.name,
                'uni_name': device_type.uni_name
            }
            # device_prototype = device.device_prototype
            ret['device_prototype'] = {
                'id': device_prototype.id,
                'name': device_prototype.name,
                'uni_name': device_prototype.uni_name
            }

            ret['detail_attres'] = device.detail_attres()

        ret['nick_name'] = device.nick_name
        ret['custz_detail'] = True if device_prototype.content is not None else False
        ret['today_power_cons'] = device.today_power_cons()
        ret['today_cool_cons'] = device.today_cool_cons()

        return ret


class DeviceValueSerializer(serializers.Serializer):
    device_id = serializers.IntegerField()
    identifier = serializers.CharField(max_length=200)
    value = serializers.CharField(max_length=200)


class DeviceValuesSerializer(serializers.Serializer):
    values = DeviceValueSerializer(many=True)


class AttributePrototypeSerializer(serializers.Serializer):
    identifier = serializers.CharField(max_length=200)
    value = serializers.CharField(max_length=200)


class DeviceTimerSerializer(serializers.Serializer):
    device_id = serializers.IntegerField()
    name = serializers.CharField(max_length=200)
    repeat = serializers.BooleanField(required=False)
    run_date = serializers.CharField(
        min_length=10, max_length=10, required=False, allow_null=True, allow_blank=True)
    run_time = serializers.CharField(
        min_length=8, max_length=8, required=False)
    wdays = serializers.CharField(min_length=2, max_length=20, required=False, allow_null=True, allow_blank=True)
    enabled = serializers.BooleanField(required=False)
    aps = AttributePrototypeSerializer(many=True)
    time_ranges = serializers.JSONField(required=False, allow_null=True)

    def validate_device_id(self, value):
        try:
            Device.objects.get(pk=value)
        except exceptions.ObjectDoesNotExist:
            raise serializers.ValidationError('Device not found!')

        return value

    def validate_run_date(self, value):
        # 校验run date为YYY-MM-DD
        try:
            if 'wdays' not in self.initial_data or not self.initial_data['wdays']:
                datetime.datetime.strptime(value, '%Y-%m-%d')
        except Exception:
            raise serializers.ValidationError('Run_date is not valid!')

        return value

    def validate_run_time(self, value):
        try:
            datetime.datetime.strptime(value, '%H:%M:%S')
        except Exception:
            raise serializers.ValidationError('Run_time is not valid!')

        return value

    def validate_wdays(self, value):
        if 'run_date' not in self.initial_data or not self.initial_data['run_date']:
            try:
                if not validate_week(value):
                    raise serializers.ValidationError('Wdays is not valid!')
            except Exception:
                raise serializers.ValidationError('Wdays is not valid!')

        return value

    def validate_aps(self, values):
        if self.instance is not None:
            device = self.instance.device
        else:
            device = Device.objects.get(pk=self.initial_data.get('device_id'))

        for attr in values:
            try:
                attr_obj = AttributePrototype.objects.get(
                    identifier=attr['identifier'],
                    device_prototype_id=device.device_prototype_id
                )
                if not validate_value(attr_obj, attr['value']):
                    raise serializers.ValidationError(
                        'Value of attribute %s is invalid!' % attr['identifier'])

            except exceptions.ObjectDoesNotExist:
                raise serializers.ValidationError(
                    'Attribute %s not exist!' % attr['identifier'])
            except exceptions.MultipleObjectsReturned:
                raise serializers.ValidationError(
                    'Attribute %s not unique!' % attr['identifier'])

        return values


class DeviceCtrlLogSerializer(serializers.ModelSerializer):
    class Meta:
        model = DeviceCtrlLog
        fields = [
            'id',
            'executor_id',
            'executor_type',
            'project_id',
            'device_id',
            'mac',
            'data',
            'errcode',
            'result',
            'created_at',
            'updated_at',
            'retries'
        ]
        read_only_fields = ['id']

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        ret['executor'] = {
            'id': ret['executor_id'],
            'executor_type': ret['executor_type'],
            'name': '',
        }
        return ret


class DeviceEventSerializer(serializers.ModelSerializer):
    class Meta:
        model = DeviceEvent
        exclude = ['project']

    def to_representation(self, instance):
        ret = super().to_representation(instance)

        # if instance.device is not None:
        #     dp = DevicePrototype.objects.get(pk=instance.device.device_prototype_id)
        #
        #     ret['device'] = {
        #         'id': instance.device.id,
        #         'nick_name': instance.nick_name,
        #         'device_prototype': dp.name
        #     }
        #
        #     ret['device_info'] = '%s - %s' % (dp.device_type.name, instance.nick_name)
        # else:
        #     ret['device_info'] = ''

        is_summary = self.context.get("summary", None)
        if is_summary:
            data_dict = json.loads(ret['data'])
            summary_dict = {}
            for i, (k, v) in enumerate(data_dict.items()):
                summary_dict[k] = v
                if i == 3:
                    break

            ret['data_num'] = len(data_dict)
            ret['data'] = json.dumps(summary_dict)

        return ret


class WebDeviceSerializer(serializers.ModelSerializer):
    class Meta:
        model = Device
        fields = ['nick_name', 'address', 'remark']


class DeviceLimitSerializer(serializers.ModelSerializer):
    class Meta:
        model = DeviceLimit
        fields = '__all__'

    def to_representation(self, instance):
        from ..devdefine.serializers import AttributePrototypeSerializer

        ret = super(DeviceLimitSerializer, self).to_representation(instance)
        aps = AttributePrototype.objects.filter(id__in=ret['attribute_prototypes'].split(','))
        serializer = AttributePrototypeSerializer(aps, many=True)
        ret['attribute_prototypes'] = serializer.data
        device_prototype = DevicePrototype.objects.get(pk=ret['device_prototype'])
        ret['device_prototype'] = SimpleDevicePrototypeSerializer(device_prototype).data
        ret.pop('project')
        return ret


class DeviceRuntimeSerializer(serializers.ModelSerializer):
    class Meta:
        model = DeviceRuntime
        fields = '__all__'


class DataRouteSerializer(serializers.ModelSerializer):
    class Meta:
        model = DataRoute
        fields = '__all__'


class SySimSerializer(serializers.ModelSerializer):
    class Meta:
        model = SySim
        fields = '__all__'

class DaSnapshotSerializer(serializers.ModelSerializer):
    class Meta:
        model = DaSnapshot
        fields = '__all__'

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        ret['device_id'] = ret['device']
        ret.pop('device')

        return ret
