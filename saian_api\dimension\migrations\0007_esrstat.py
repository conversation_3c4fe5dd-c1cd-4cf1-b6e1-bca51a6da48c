# Generated by Django 3.2.19 on 2024-02-23 15:19

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('project', '0008_webchart_data_name'),
        ('dimension', '0006_auto_20231225_1726'),
    ]

    operations = [
        migrations.CreateModel(
            name='EsrStat',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.Char<PERSON>ield(max_length=5)),
                ('esr_value', models.Char<PERSON>ield(max_length=20)),
                ('cur_value', models.Char<PERSON>ield(max_length=20)),
                ('refer_value', models.CharField(max_length=20)),
                ('created_at', models.DateTimeField()),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='project.project')),
            ],
            options={
                'db_table': 'esr_stats',
            },
        ),
    ]
