# Generated by Django 3.2.8 on 2021-12-30 15:16

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('building', '0001_initial'),
        ('project', '0001_initial'),
        ('device', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='building',
            name='project',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='project.project'),
        ),
        migrations.AddField(
            model_name='activeroom',
            name='building',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='building.building'),
        ),
        migrations.AddField(
            model_name='activeroom',
            name='devices',
            field=models.ManyToManyField(through='device.RoomDevice', to='device.Device'),
        ),
        migrations.AddField(
            model_name='activeroom',
            name='floor',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='building.floor'),
        ),
        migrations.AddIndex(
            model_name='roomparam',
            index=models.Index(fields=['room_no', 'identifier'], name='room_params_room_no_d1afa4_idx'),
        ),
        migrations.AddIndex(
            model_name='roomparam',
            index=models.Index(fields=['active_room_id', 'identifier'], name='room_params_active__f94e51_idx'),
        ),
        migrations.AddIndex(
            model_name='roomparam',
            index=models.Index(fields=['room_no', 'created_at'], name='room_params_room_no_c5329d_idx'),
        ),
        migrations.AddIndex(
            model_name='roomparam',
            index=models.Index(fields=['active_room_id', 'created_at'], name='room_params_active__e56b0f_idx'),
        ),
        migrations.AddIndex(
            model_name='roomparam',
            index=models.Index(fields=['active_room_id', 'identifier', 'created_at'], name='room_params_active__72764e_idx'),
        ),
        migrations.AddIndex(
            model_name='roomparam',
            index=models.Index(fields=['room_no', 'identifier', 'created_at'], name='room_params_room_no_5b78b2_idx'),
        ),
        migrations.AddIndex(
            model_name='floor',
            index=models.Index(fields=['id', 'building_id'], name='floors_id_67721b_idx'),
        ),
        migrations.AddIndex(
            model_name='building',
            index=models.Index(fields=['id', 'project_id'], name='buildings_id_60d7a3_idx'),
        ),
        migrations.AddIndex(
            model_name='activeroom',
            index=models.Index(fields=['id', 'building_id'], name='active_room_id_cb3225_idx'),
        ),
        migrations.AddIndex(
            model_name='activeroom',
            index=models.Index(fields=['id', 'floor_id'], name='active_room_id_b83797_idx'),
        ),
    ]
