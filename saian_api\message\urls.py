from django.urls import path

from saian_api.message.views import MessageViewSet, UserMessageViewSet, UserMessageConfigView

message_list = MessageViewSet.as_view({
    'get': 'list'
})
user_message_list = UserMessageViewSet.as_view({
    'post': 'create',
    'get': 'list'
})
user_message_detail = UserMessageViewSet.as_view({
    'put': 'update',
    'get': 'retrieve'
})

user_message_config = UserMessageConfigView.as_view({
    'post': 'create',
    'get': 'retrieve'
})

urlpatterns = [
    path('saianapi/v5/messages', message_list, name="message_list"),
    path('saianapi/v5/unread_messages', user_message_list, name="unread_message_list"),
    path('saianapi/v5/read_messages', user_message_list, name="read_messages"),
    path('saianapi/v5/messages/<int:pk>', user_message_detail, name="useful_message"),
    path('saianapi/v5/accept_msg_types', user_message_config, name="user_message_config"),
]
