# Generated by Django 3.2.8 on 2022-10-26 16:01

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('group', '0005_actiontimer_action_attributes'),
    ]

    operations = [
        migrations.CreateModel(
            name='Shortcut',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, null=True)),
                ('uni_name', models.CharField(max_length=255, null=True)),
                ('op_type', models.IntegerField()),
                ('op_id', models.PositiveBigIntegerField(null=True)),
                ('remark', models.Char<PERSON><PERSON>(max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'shortcuts',
                'ordering': ['-created_at'],
            },
        ),
    ]
