import calendar
import datetime
import traceback

from django.core.management import BaseCommand, CommandError

from saian_api.notifi.models import Notification
from saian_api.project.models import Project
from saian_api.report.models import StatReport
from saian_api.scheduledtask.utils import get_projects, set_global_db

class Command(BaseCommand):
    help = "项目分析报告"

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目ID列表
        projects = get_projects(self, options)
        now = datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        last_day = now - datetime.timedelta(days=1)

        # 分析报告的 report_dt 时间都是0
        report_time = datetime.time(0, 0, 0, 0)

        for project_id in projects:
            try:
                # 设置全局数据库
                set_global_db(project_id)
                project = Project.objects.get(pk=project_id)

                # 日统计
                if not StatReport.objects.filter(report_dt=last_day, periods=10).exists():
                    title = f'分析报告（{last_day.strftime("%Y年%m月%d日")}）'
                    StatReport.objects.create(
                        title=title,
                        content_name=title,
                        report_dt=last_day,
                        periods=10
                    )
                    data = {
                        'content': f'尊敬的用户，{title}已完成，请前往项目平台或小程序查看。',
                        'title': title,
                        'project': project.name
                    }
                    Notification.objects.notify(12, None, data, project=project)

                # 周统计
                year, week, iso_weekday = last_day.isocalendar()
                if now.isoweekday() == 1:
                    # 星期一，新的一周
                    week_begin = now - datetime.timedelta(days=7)
                    if not StatReport.objects.filter(report_dt=week_begin, periods=20).exists():
                        title = f'分析报告（{year}年{week}周）'
                        StatReport.objects.create(
                            title=title,
                            content_name=title,
                            report_dt=week_begin,
                            periods=20
                        )
                        data = {
                            'content': f'尊敬的用户，{title}已完成，请前往项目平台或小程序查看。',
                            'title': title,
                            'project': project.name
                        }
                        Notification.objects.notify(13, None, data, project=project)

                # 月统计
                if now.day == 1:
                    monthrange = calendar.monthrange(last_day.year, last_day.month)[1]
                    month_begin = now - datetime.timedelta(days=monthrange)
                    if not StatReport.objects.filter(report_dt=month_begin, periods=30).exists():
                        title = f'分析报告（{year}年{month_begin.month}月）'
                        StatReport.objects.create(
                            title=title,
                            content_name=title,
                            report_dt=month_begin,
                            periods=30
                        )
                        data = {
                            'content': f'尊敬的用户，{title}已完成，请前往项目平台或小程序查看。',
                            'title': title,
                            'project': project.name
                        }
                        Notification.objects.notify(14, None, data, project=project)

                # 年统计
                if now.month == 1 and now.day == 1:
                    year_begin = last_day.replace(month=1, day=1)
                    if not StatReport.objects.filter(report_dt=year_begin, periods=40).exists():
                        title = f'分析报告（{year}年）'
                        StatReport.objects.create(
                            title=title,
                            content_name=title,
                            report_dt=year_begin,
                            periods=40
                        )
                        data = {
                            'content': f'尊敬的用户，{title}已完成，请前往项目平台或小程序查看。',
                            'title': title,
                            'project': project.name
                        }
                        Notification.objects.notify(15, None, data, project=project)

            except CommandError:
                self.stderr.write(f"运行'分析报告'任务失败，项目ID：{project_id}, 命令参数不合法！", ending='\n')
                continue
            except Exception as e:
                self.stderr.write(f"运行'分析报告'任务失败，项目ID：{project_id}, error: {e.__str__()}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
                continue
