import logging

import requests
from django.db import models
from django.db.models import Count
from django.db.models.deletion import CASCADE

from saian_api.device.models import Device
from saian_api.project.models import Project
from saian_api.project.models import WebMenu
from saian_api.utils.db.Redis import RedisHelper
from saian_api.utils.httpapi.image import ImageAPI
from saian_api.utils.inthttpapi import DOMAIN
from saian_api.utils.inthttpapi.base import BaseAPI

# Create your models here.


class WebRole(models.Model):
    """
      web用户角色
    """
    # 所属项目
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    # 名称
    name = models.CharField(max_length=255)
    # 英文名字
    en_name = models.CharField(max_length=255, null=True)
    # 权限字段
    permissions = models.CharField(max_length=255)

    web_menus = models.ManyToManyField(WebMenu)
    # 角色的总览配置
    dashboards = models.TextField(blank=True, null=True, default=None)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'web_roles'
        ordering = ['-created_at']


class WebUserManager(models.Manager):
    def valid_update(self, username, mobile, web_user_id):
        if username:
            is_exist = self.filter(username=username, status=10).exclude(pk=web_user_id).count() > 1
            if is_exist:
                return not is_exist
        if mobile:
            is_exist = self.filter(mobile=mobile, status=10).exclude(pk=web_user_id).count() > 1
            if is_exist:
                return not is_exist
        return True

    def valid_create(self, username, mobile):
        if username:
            is_exist = self.filter(username=username).exists()
            if is_exist:
                return False
        if mobile:
            is_exist = self.filter(mobile=mobile).exists()
            if is_exist:
                return False
        return True

    @classmethod
    def get_executor_info(cls, request, web_user_id):
        """
            为日志(action_log, device_ctrl_log, device_log)查询用户信息。
        """
        user = {
            'executor_type': 'WebUser',
            'mini_username': '',
            'remark': '',
            'id': web_user_id,
        }
        try:
            web_user = WebUser.objects.filter(pk=web_user_id).last()
            if web_user is None:
                return user
            # 用户名
            user['name'] = web_user.name
            user['mobile'] = web_user.mobile
            user['id'] = None
            # 已注销用户关联不到小程序账号，用web昵称作为默认值
            user['mini_username'] = web_user.name
            user['mini_userid'] = None

            # 用户头像
            if web_user.avatar and web_user.avatar.isdigit():
                avatar = ImageAPI.get_url(request, web_user.avatar, 'thumb')
                user['avatar'] = avatar
            else:
                user['avatar'] = web_user.avatar

            # 小程序用户
            project_id = request.user['project_id']
            unionid = web_user.unionid
            if unionid:
                mini_user = cls.get_mini_user(unionid, project_id)

                if mini_user:
                    if 'id' in mini_user:
                        user['id'] = mini_user['id']
                        user['mini_userid'] = mini_user['id']
                    if 'mobile' in mini_user:
                        user['mobile'] = mini_user['mobile']
                    if 'mini_username' in mini_user:
                        user['mini_username'] = mini_user['mini_username']
                    if 'remark' in mini_user:
                        user['remark'] = mini_user['remark']
                    if 'avatar' in mini_user:
                        user['avatar'] = mini_user['avatar']

        except Exception as e:
            logging.error(f'query device log executor error: {e.__str__()}')

        return user

    @classmethod
    def get_mini_user(cls, unionid, project_id=None):
        """
        查询 Web 用户对应的小程序用户信息，并缓存至 Redis
        """
        mini_user = {
            'id': '',
            'mobile': '',
            'mini_username': '',
            'remark': '',
            'avatar': '',
            'enable_sms': 0,
            'user_type': 0
        }

        if unionid is None:
            return mini_user

        mini_user = RedisHelper.get_hvalue(project_id, f'mini_user:{unionid}')
        if mini_user:
            return mini_user
        res = f'saianadmin/intapi/sy_users/{unionid}'
        url = f'{DOMAIN}/{res}?project_id={project_id}'
        headers = BaseAPI.admin_intapi_header(res)
        r = requests.get(url, headers=headers)
        if r.status_code == 200:
            sy_user = r.json()
            if sy_user.get('id', None) is None:
                logging.error(f'get mini use info error: unionid-{unionid}, sy_user: {sy_user}')
            else:
                mini_user['id'] = sy_user['id']
                if sy_user.get('mobile', None):
                    mini_user['mobile'] = sy_user['mobile']
                if sy_user.get('mini_username', None):
                    mini_user['mini_username'] = sy_user['mini_username']
                if sy_user.get('remark', None):
                    mini_user['remark'] = sy_user['remark']
                if sy_user.get('avatar', None):
                    mini_user['avatar'] = sy_user['avatar']
                if sy_user.get('enable_sms', None):
                    mini_user['enable_sms'] = 1 if sy_user['enable_sms'] else 0
                if sy_user.get('user_type', None):
                    mini_user['user_type'] = sy_user['user_type']
        RedisHelper.set_hvalue(project_id, f'mini_user:{unionid}', values=mini_user)
        return mini_user


class WebUser(models.Model):
    """
      web用户
    """
    # 用户名，用于登录
    username = models.CharField(max_length=255, null=True)
    # 登录密码
    password = models.CharField(max_length=255)
    # 用户名字
    name = models.CharField(max_length=255, null=True)
    # email
    email = models.CharField(max_length=255, null=True)
    # 手机号码
    mobile = models.CharField(max_length=255, null=True)
    # 用户状态 10-正常，20-冻结，30-注销
    status = models.IntegerField()
    # 头像
    avatar = models.CharField(max_length=255, null=True)
    # 微信的unionid
    unionid = models.CharField(max_length=255, null=True)
    # 微信openid
    openid = models.CharField(max_length=255, null=True)
    # 微信公众号openid
    wechat_openid = models.CharField(max_length=255, null=True)
    # 微信access_token
    access_token = models.CharField(max_length=255, null=True)
    # 微信refresh_token
    refresh_token = models.CharField(max_length=255, null=True)
    # 权限字段
    permissions = models.CharField(max_length=255, null=True)
    # 能耗平台登录的token
    ec_token = models.CharField(max_length=255, null=True)
    # 是否超级用户
    is_super = models.BooleanField(default=False)
    # 最后登录时间
    last_login = models.DateTimeField()
    # web token
    authentication_token = models.CharField(max_length=255, null=True)
    # 登陆验证码
    captcha = models.CharField(max_length=255, null=True)
    # 用户角色
    web_roles = models.ManyToManyField(WebRole)
    # 用户消息配置json
    msg_types = models.TextField(null=True, blank=True)
    # 在线时长
    online_time = models.FloatField(default=0.0)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'web_users'
        ordering = ['-created_at']

    objects = WebUserManager()


"""
  用户项目关系
"""


class UserProject(models.Model):
    # 用户
    web_user = models.ForeignKey(WebUser, on_delete=CASCADE)
    # 项目
    project = models.ForeignKey(Project, on_delete=CASCADE)

    class Meta:
        db_table = 'user_projects'


class UserDeviceManager(models.Manager):
    # 关注设备最多的用户前十
    def device_follower_top10(self):
        followed_devices = self.all()
        # 加入 'web_user__id' 是为了防止用户名相同时，两个用户会被视作同一个用户
        top10 = followed_devices.values('web_user__name', 'web_user__id', 'web_user__last_login').annotate(
            count=Count('id')).order_by('-count')[:10]

        result = []
        for user in top10:
            result.append({
                'web_user': {
                    'id': user['web_user__id'],
                    'name': user['web_user__name'],
                    'last_login': user['web_user__last_login']
                },
                'count': user['count']
            })

        return result

    # 最多用户关注的设备前十
    def device_following_top10(self):
        followed_devices = self.all()
        top10 = followed_devices.values('device__id', 'device__nick_name').annotate(count=Count('id')).order_by(
            '-count')[:10]

        result = []
        for device in top10:
            result.append({
                'device': {
                    'id': device['device__id'],
                    'nick_name': device['device__nick_name']
                },
                'count': device['count']
            })

        return result


"""
    用户设备关系
"""


class UserDevice(models.Model):
    # 用户
    web_user = models.ForeignKey(WebUser, on_delete=CASCADE)
    # 设备
    device = models.ForeignKey(Device, on_delete=CASCADE)
    # 子设备序号/索引
    unit_idx = models.IntegerField(null=True)
    # 子设备前缀
    unit_prefix = models.CharField(max_length=255, null=True)
    # 创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 更新时间
    updated_at = models.DateTimeField(auto_now=True)

    objects = UserDeviceManager()

    class Meta:
        db_table = 'user_devices'


class UserSearches(models.Model):
    """
        用户搜索历史
    """
    # 关键词
    key_words = models.CharField(max_length=255, null=True)
    # 用户
    web_user = models.ForeignKey(WebUser, on_delete=CASCADE)
    # 创建时间
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'user_searches'


class UserLogManager(models.Manager):
    def cus_create(self, request, data=None):
        if isinstance(request.user, dict):
            web_user = WebUser.objects.filter(pk=request.user.get('id')).last()
            if web_user is not None:
                return UserLog.objects.create(
                    web_user=web_user,
                    user_name=web_user.name if WebUser else None,
                    user_mobile=web_user.mobile if WebUser else None,
                    api_url=request.get_full_path()[:255],
                    http_method=request.method,
                    data=data,
                    browser=request.META.get('HTTP_USER_AGENT')[:255],
                    ip_address=self.get_ip(request)
                )

    @classmethod
    def get_ip(cls, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]  # 所以这里是真实的ip
        else:
            ip = request.META.get('REMOTE_ADDR')  # 这里获得代理ip
        return ip


class UserLog(models.Model):
    """用户操作日志"""
    # 操作用户
    web_user = models.ForeignKey(WebUser, on_delete=CASCADE)
    # 用户名称
    user_name = models.CharField(max_length=255, null=True, blank=True)
    # 用户号码
    user_mobile = models.CharField(max_length=255, null=True, blank=True)
    # 请求的 api 路径
    api_url = models.CharField(max_length=255, null=True, blank=True)
    # 请求的 http 方法
    http_method = models.CharField(max_length=255, null=True, blank=True)
    # http 的 body 数据
    data = models.TextField(null=True, blank=True)
    # 浏览器信息
    browser = models.CharField(max_length=255, null=True, blank=True)
    # 请求的客户端IP地址
    ip_address = models.CharField(max_length=255, null=True, blank=True)
    # 请求时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'user_logs'
        ordering = ['-created_at']

    objects = UserLogManager()

class UserStat(models.Model):
    """用户数据统计"""
    # 统计的周期，10-小时，20-日，30-周，40-月
    periods = models.IntegerField()
    # 用户id
    web_user_id = models.IntegerField()
    # 用户名称
    user_name = models.CharField(max_length=255, null=True)
    # 用户手机号码
    user_mobile = models.CharField(max_length=255, null=True, blank=True)
    # 排名
    ranking = models.IntegerField()
    # 打开平台次数
    acc_times = models.IntegerField()
    # 在线时长，单位：分钟
    online_time = models.IntegerField()
    # 访问页面的数量
    acc_pages = models.IntegerField()
    # 统计的时间，根据统计周期确定，例子：统计周期为小时时，时间为2024-04-02 01:00:00
    target_dt = models.DateTimeField()
    # 统计的时间
    created_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'user_stats'

class PageUrl(models.Model):
    """页面与api url对应关系"""
    # 页面
    page = models.CharField(max_length=255, null=True)
    # 请求的 api 路径
    api_url = models.CharField(max_length=255, null=True, blank=True)
    # 请求的 http 方法
    http_method = models.CharField(max_length=255, null=True, blank=True)

    class Meta:
        db_table = 'page_url'
