import calendar
import json
import re
from datetime import datetime, timedelta
from functools import reduce
from statistics import mean
from rest_framework.decorators import action

from django.db.models import Q, Sum
from rest_framework import status, viewsets
from rest_framework.response import Response
from rest_framework.views import APIView

from saian_api.coldsource.models import EcMeter, ColdSource
# Create your views here.
from saian_api.dashboard.models import ProjectWeather, EcTranscription
from saian_api.device.models import Device
from saian_api.issue.models import DeviceIssue
from saian_api.project.models import Project
from saian_api.regions.models import Region
from saian_api.report.models import DeviceHourlyStat, DeviceDailyStat, DeviceMonthlyStat, DeviceYearlyStat, ManualRecord
from saian_api.utils.httpapi.weather import WeatherApi
from saian_api.utils.intapi_auth import IntapiAuth
from saian_api.utils.legacy_auth import LegacyAuth
from saian_api.utils.standard_pagination import StandardResultsSetPagination
from saian_api.utils.utils import DatetimeUtils
from .models import EcCriteria
from .serializers import EcCriteriaSerializer, EcTranscriptionSerializer
from .tasks import projectWeatherYearTask, projectWeatherMonthTask, projectWeatherDayTask
from ..devdefine.models import DevicePrototype, AttributePrototype, DeviceType
from ..terminal.models import TerminalAttribute, Terminal
from ..user.models import UserDevice
from ..utils.httpapi.image import ImageAPI
from ..utils.sy_jsonrenderer import ResponseUtils, SyJSONRender
from ..utils.tools import is_number

"""
    用户看板中的温湿度数据
"""
class ProjectPanelWeather(APIView):

    def get(self, request):
        project_id = request.user.get('project_id')
        try:
            project = Project.objects.get(pk=project_id)
        except Project.DoesNotExist:
            return Response(status=status.HTTP_400_BAD_REQUEST)

        weather_type = request.query_params.get('type') or 'di'

        temp = []
        humidity = []
        times = []

        begin = datetime.min
        end = datetime.now()
        today = datetime.today()

        # 类型为"年"
        if weather_type == 'yr':
            # 找出所有的年数据
            weathers = ProjectWeather.objects.filter(
                created_at__range=[begin, end], project=project, type=weather_type
            ).order_by('-created_at')

            for weather in weathers:
                times.append(f'{weather.created_at.year}年')
                temp.append(weather.temp)
                humidity.append(weather.humidity)

        # 类型为"月" (取过去12月数据)
        elif weather_type == 'mth':
            # 初始化，温湿度初始化为零
            year_ago = datetime(year=today.year - 1, month=today.month, day=1)

            calc_month = year_ago

            while calc_month < today:
                weather = ProjectWeather.objects.filter(
                    created_at__month=calc_month.month,
                    created_at__year=calc_month.year,
                    type=weather_type,
                    project=project
                )
                times.append(f"{calc_month.month}月")
                if calc_month.month == today.month:
                    days = ProjectWeather.objects.filter(
                        created_at__year=calc_month.year,
                        created_at__month=calc_month.month,
                        type='di',
                        project=project
                    )
                    dts = [d.temp for d in days] if days.exists() else [0]
                    dhs = [d.humidity for d in days] if days.exists() else [0]

                    temp.append(round(mean(dts), 2))
                    humidity.append(round(mean(dhs), 2))

                else:
                    humidity.append(weather.last().humidity if weather.count() else 0)
                    temp.append(weather.last().temp if weather.count() else 0)

                month = calc_month.month + 1 if calc_month.month != 12 else 1
                year = calc_month.year if calc_month.month != 12 else calc_month.year + 1
                calc_month = datetime(year=year, month=month, day=1)

            # for month in range(1, 13):
            #     times.append(f"{month}月")
            #     humidity.append(0)
            #     temp.append(0)
            # # 年的开始
            # begin = datetime.combine(datetime(year=today.year, month=1, day=1), time.min)
            # # 今年的月数据
            # weathers = ProjectWeather.objects.filter(
            #     created_at__range=[begin, end], project=project, type=weather_type
            # ).order_by('-created_at')
            # # 更新相应月的温湿度
            # for weather in weathers:
            #     created_at = weather.created_at
            #     temp[times.index((str(created_at.month)) + '月')] = weather.temp
            #     humidity[times.index((str(created_at.month)) + '月')] = weather.humidity

        # 类型为"日" (取获取一个月数据)
        else:

            # 构建一个月前的今天
            # month_ago_day = today.day
            # month_ago_month = today.month - 1 if today.month != 1 else 12
            # month_ago_year = today.year if today.month != 1 else today.year - 1
            # month_ago = datetime(year=month_ago_year, month=month_ago_month, day=month_ago_day)
            month_ago = today - timedelta(days=30)

            calc_day = month_ago

            while calc_day < today:
                weather = ProjectWeather.objects.filter(
                    created_at__year=calc_day.year,
                    created_at__month=calc_day.month,
                    created_at__day=calc_day.day,
                    type='di',
                    project=project
                )
                times.append(f"{calc_day.day}日")
                if calc_day.day == today.day:
                    hours = ProjectWeather.objects.filter(
                        created_at__year=calc_day.year,
                        created_at__month=calc_day.month,
                        created_at__day=calc_day.day,
                        type='hr',
                        project=project
                    )

                    hts = [h.temp for h in hours] if hours.exists() else [0]
                    hhs = [h.humidity for h in hours] if hours.exists() else [0]

                    temp.append(round(mean(hts), 2))
                    humidity.append(round(mean(hhs), 2))
                else:
                    humidity.append(weather.last().humidity if weather.count() else 0)
                    temp.append(weather.last().temp if weather.count() else 0)

                calc_day = calc_day + timedelta(days=1)

            # # 当月共有多少天
            # monthrange = calendar.monthrange(today.year, today.month)[1]
            # for day in range(1, monthrange + 1):
            #     # 初始化，温湿度初始化为零
            #     times.append('%i日' % day)
            #     humidity.append(0)
            #     temp.append(0)
            # # 当月的开始
            # begin = datetime.combine(datetime(year=today.year, month=today.month, day=1), time.min)
            # # 查找这个月的日数据
            # weathers = ProjectWeather.objects.filter(
            #     created_at__range=[begin, end], project=project, type=weather_type
            # ).order_by('-created_at')
            # # 把温湿度存进相应的位置
            # for weather in weathers:
            #     created_at = weather.created_at
            #     temp[times.index(str(created_at.day) + '日')] = weather.temp
            #     humidity[times.index(str(created_at.day) + '日')] = weather.humidity

        res = {
            'data': {
                'stats': {
                    'humidity': {
                        'data': humidity,
                        'name': '湿度',
                        'unit': '%'
                    },
                    'temp': {
                        'data': temp,
                        'name': '温度',
                        'unit': '°C'
                    }

                },
                'time': times,
            },
            'total': len(times),
            'status': status.HTTP_200_OK,
        }

        return Response(data=res)


"""
    项目温湿度历史数据接口
"""
class ProjectWeatherAPIView(APIView, StandardResultsSetPagination):  # 继承 StandardResultsSetPagination， 使分页生效

    def get(self, request):
        project_id = request.user.get('project_id')
        try:
            project = Project.objects.get(pk=project_id)
        except Project.DoesNotExist:
            return Response(status=status.HTTP_400_BAD_REQUEST)
        # print('project: ', project_id, project)
        region = Region.objects.get(pk=project.admin_region_id)

        # 获取查询参数
        time_from = request.query_params.get('from')
        time_till = request.query_params.get('till')
        weather_type = request.query_params.get('type') or 'hr'
        if weather_type == 'mo':
            weather_type = 'mth'

        times = []
        temp = []
        humidity = []
        wetbulb_temp = []

        start = datetime.strptime(time_from, '%Y%m%d%H%M%S')
        start = start.replace(minute=0, second=0, microsecond=0)
        end = datetime.strptime(time_till, '%Y%m%d%H%M%S')
        if start > end:
            return Response(status=status.HTTP_400_BAD_REQUEST)

        if weather_type == 'di':
            end = end.replace(hour=23, minute=59, second=59, microsecond=0)
        elif weather_type == 'mth':
            month_range = calendar.monthrange(end.year, end.month)[1]
            end = end.replace(day=month_range, hour=23, minute=59, second=59, microsecond=0)

        now = datetime.now()

        weathers = list(ProjectWeather.objects.filter(
            created_at__range=[start, end], project=project, type=weather_type
        ).order_by('created_at'))

        if weather_type == 'hr':
            while start < end:
                weather = next(filter(lambda x: x.created_at.date() == start.date() and x.created_at.hour == start.hour, weathers), None)
                times.append(start)
                if now > start:
                    if not weather and now.date() == start.date() and now.hour == start.hour:
                        weather_result = WeatherApi.get_weather(region.weather_code)
                        tem = weather_result.get('tem')
                        hum = weather_result.get('humidity').replace('%', '')
                        temp.append(float(tem))
                        humidity.append(float(hum))
                    else:
                        temp.append(round(weather.temp, 2) if weather else 0)
                        humidity.append(round(weather.humidity, 2) if weather else 0)
                        wetbulb_temp.append(round(weather.wetbulb_temp, 2) if (weather and weather.wetbulb_temp) else 0)
                start = start + timedelta(hours=1)
        elif weather_type == 'di':
            while start <= end:
                weather = next(filter(lambda x: x.created_at.date() == start.date(), weathers), None)
                times.append(start)
                if now > start:
                    if start.date() != now.date():
                        temp.append(round(weather.temp, 2) if weather else 0)
                        humidity.append(round(weather.humidity, 2) if weather else 0)
                        wetbulb_temp.append(round(weather.wetbulb_temp, 2) if (weather and weather.wetbulb_temp) else 0)
                    else:
                        weather_result = WeatherApi.get_weather(region.weather_code)
                        tem = weather_result.get('tem')
                        hum = weather_result.get('humidity').replace('%', '')
                        wbt = WeatherApi.get_wetbulb_temp(float(tem), float(hum))
                        temp.append(float(tem))
                        humidity.append(float(hum))
                        wetbulb_temp.append(wbt)

                start = start + timedelta(days=1)
        else:
            while start <= end:
                weather = next(filter(lambda x: x.created_at.year == start.year and x.created_at.month == start.month, weathers), None)
                times.append(start)
                if now > start:
                    if now.year == start.year and now.month == start.month:
                        weather_result = WeatherApi.get_weather(region.weather_code)
                        tem = weather_result.get('tem')
                        hum = weather_result.get('humidity').replace('%', '')
                        wbt = WeatherApi.get_wetbulb_temp(float(tem), float(hum))
                        temp.append(float(tem))
                        humidity.append(float(hum))
                        wetbulb_temp.append(wbt)
                    else:
                        temp.append(round(weather.temp, 2) if weather else 0)
                        humidity.append(round(weather.humidity, 2) if weather else 0)
                        wetbulb_temp.append(round(weather.wetbulb_temp, 2) if (weather and weather.wetbulb_temp) else 0)
                if start.month == 12:
                    start = start.replace(year=start.year + 1, month=1)
                else:
                    start = start.replace(month=start.month + 1)

        res = {
            'data': {
                'stats': {
                    'temp': {
                        'data': temp,
                        'name': '温度' if weather_type == 'hr' else '平均温度',
                        'unit': '°C'
                    },
                    'humidity': {
                        'data': humidity,
                        'name': '湿度' if weather_type == 'hr' else '平均湿度',
                        'unit': '%'
                    },
                    'wetbulb_temp': {
                        'data': wetbulb_temp,
                        'name': '湿球温度' if weather_type == 'hr' else '平均湿球温度',
                        'unit': '°C'
                    }
                },
                'time': times,
            },
            'status': status.HTTP_200_OK,
            'total': len(weathers),
        }

        return Response(data=res)

    def post(self, request):
        """
        重做项目气温统计的接口，参数包括 类型，日期，指定值（可选），项目ID（可选）
        如果有指定值和项目ID，插入的数据是与参数相同的
        如果没有指定值，则根据数据库中的数据做统计，与定时任务的逻辑相同，日期应该是需要重新统计的日期的下一天（月，年）
        """
        # 'yr' 'mth' 'di'
        weather_type = request.data.get('type')
        # 需要重做统计的日期 '20220322'
        date = datetime.strptime(request.data.get('date'), '%Y%m%d')
        # 指定值：重做每日统计时有效。相当于手动录入项目某一日的温湿度数据
        values = request.data.get('values', None)
        # 指定项目ID：与 values 对应的项目
        project_id = request.data.get('project_id', None)

        if values is not None:
            if weather_type != 'di':
                return Response(status=status.HTTP_400_BAD_REQUEST)
            begin = date
            end = date + timedelta(days=1) - timedelta(seconds=1)
            record = ProjectWeather.objects.using(f'prj{project_id}db').filter(
                created_at__range=[begin, end],
                type='di'
            )

            weather = ProjectWeather(
                temp=values['temp'], humidity=values['humidity'], min_temp=values['min_temp'], max_temp=values['max_temp'],
                min_humidity=values['min_humidity'], max_humidity=values['max_humidity'], project_id=project_id, type='di',
                updated_at=date, created_at=date
            )
            if record.exists():
                weather.id = record.last().id
                weather.created_at = record.last().created_at
            weather.save(using=f'prj{project_id}db')
        else:

            if weather_type == 'yr':
                projectWeatherYearTask(date)
            elif weather_type == 'mth':
                projectWeatherMonthTask(date)
            elif weather_type == 'di':
                projectWeatherDayTask(date)

        return Response({
            'status': 200,
            'data': None
        })
"""
    项目所在地实时的天气
"""
class RealtimeWeather(APIView):
    # authentication_classes = ()
    def get(self, request, pk=None):
        project_id = request.user.get('project_id') if pk is None else pk

        try:
            project = Project.objects.get(pk=project_id)
        except Project.DoesNotExist:
            return Response(status=status.HTTP_400_BAD_REQUEST)
        try:
            region = Region.objects.get(pk=project.admin_region_id)
        except Region.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)

        result = WeatherApi.get_weather(region.weather_code)

        data = {
            'city': project.city.name,
            'district': project.district.name,
            'project_logo': ImageAPI.get_url(request, project.logo, size='originals'),
            'slogan': ImageAPI.get_url(request, project.slogan, size='originals'),
            'result': result
        }
        return Response(data={'data': data, 'status': status.HTTP_200_OK}, status=status.HTTP_200_OK)


"""
    空调电耗和冷耗
"""
class ProjectPanelEcApiView(APIView):

    def get(self, request):
        today = datetime.today()
        project_id = request.user.get('project_id')
        try:
            project = Project.objects.get(pk=project_id)
        except Project.DoesNotExist:
            return Response(status=status.HTTP_400_BAD_REQUEST)

        period_type = request.query_params.get('type') or 'di'
        # 'pc' 电耗，'cc' 冷耗
        ec_type = 'pc' if request.path.endswith('ec_power') else 'cc'

        formula = project.pc_formula if ec_type == 'pc' else project.cc_formula

        values = []
        times = []

        # 以项目有无能耗计算公式来判断项目是使用公式计算能耗还是手动录入能耗
        if formula:
            meter_id_list = re.findall(r'{{(.*?)}}', formula) if formula else []

            # 公式中包含的 meter
            meter_list = [EcMeter.objects.get(pk=meter_id) for meter_id in meter_id_list]

            # 仪表对应的数据列表
            device_stats = []

            # 日数据（取过去30天的数据）
            if period_type == 'di':
                # monthrange = calendar.monthrange(today.year, today.month)[1]

                # 构建一个月前的今天
                # month_ago_day = today.day
                # month_ago_month = today.month - 1 if today.month != 1 else 12
                # month_ago_year = today.year if today.month != 1 else today.year - 1
                # month_ago = datetime(year=month_ago_year, month=month_ago_month, day=month_ago_day)
                month_ago = today - timedelta(days=30)

                for meter in meter_list:
                    device_stats.append(DeviceDailyStat.objects.filter(
                        created_at__range=(month_ago, today),
                        device_id=meter.device_id,
                        identifier=meter.identifier
                    ))

                calc_day = month_ago
                while calc_day < today:
                    value = 0
                    # 公式中对应仪表的值
                    meter_value_list = []
                    if formula:
                        for index, _ in enumerate(meter_id_list):
                            day_device_stats = device_stats[index].filter(created_at__day=calc_day.day,
                                                                          created_at__month=calc_day.month,
                                                                          created_at__year=calc_day.year)
                            meter_value_list.append(day_device_stats.last().avg if day_device_stats.count() else 0)

                        # 构建计算公式字符串
                        eval_str = formula
                        for value in meter_value_list:
                            eval_str = re.sub(r'{{(.*?)}}', f'{value}', eval_str, 1)

                        try:
                            value = eval(eval_str)
                        except Exception as e:
                            print(f'构建{calc_day.day}日公式出错： ', e)

                    values.append(value)
                    times.append(f'{calc_day.day}日')
                    calc_day = calc_day + timedelta(days=1)

                # for day in range(1, monthrange + 1):
                #     value = 0
                #     # 公式中对应仪表的值
                #     meter_value_list = []
                #     if day <= today.day and formula:
                #         for index, _ in enumerate(meter_id_list):
                #             day_device_stats = device_stats[index].filter(created_at__day=day)
                #             meter_value_list.append(day_device_stats.last().avg if day_device_stats.count() else 0)
                #
                #         # 构建计算公式字符串
                #         eval_str = formula
                #         for value in meter_value_list:
                #             eval_str = re.sub(r'{{(.*?)}}', f'{value}', eval_str, 1)
                #
                #         try:
                #             value = eval(eval_str)
                #         except Exception as e:
                #             print(f'构建{day}日公式出错： ', e)
                #
                #     values.append(value)
                #     times.append(f'{day}日')

            # 月数据 (取过去12个月的数据)
            elif period_type == 'mth':

                # 构建一年前的时间
                year_ago = datetime(year=today.year - 1, month=today.month, day=1)

                for meter in meter_list:
                    device_stats.append(DeviceMonthlyStat.objects.filter(
                        created_at__range=(year_ago, today),
                        device_id=meter.device_id,
                        identifier=meter.identifier
                    ))

                calc_month = year_ago
                while calc_month < today:
                    value = 0
                    # 公式中对应仪表的值
                    meter_value_list = []
                    if formula:
                        for index, _ in enumerate(meter_id_list):
                            month_device_stats = device_stats[index].filter(created_at__month=calc_month.month,
                                                                            created_at__year=calc_month.year)
                            meter_value_list.append(month_device_stats.last().avg if month_device_stats.count() else 0)

                        # 构建计算公式字符串
                        eval_str = formula
                        for value in meter_value_list:
                            eval_str = re.sub(r'{{(.*?)}}', f'{value}', eval_str, 1)

                        try:
                            value = eval(eval_str)
                        except Exception as e:
                            print(f'构建{calc_month.month}月公式出错： ', e, eval_str)

                    values.append(value)
                    times.append(f'{calc_month.month}月')

                    month = calc_month.month + 1 if calc_month.month != 12 else 1
                    year = calc_month.year if calc_month.month != 12 else calc_month.year + 1
                    calc_month = datetime(year=year, month=month, day=1)
                # for month in range(1, 13):
                #     value = 0
                #     # 公式中对应仪表的值
                #     meter_value_list = []
                #     if month <= today.month and formula:
                #         for index, _ in enumerate(meter_id_list):
                #             month_device_stats = device_stats[index].filter(created_at__month=month)
                #             meter_value_list.append(month_device_stats.last().avg if month_device_stats.count() else 0)
                #
                #         # 构建计算公式字符串
                #         eval_str = formula
                #         for value in meter_value_list:
                #             eval_str = re.sub(r'{{(.*?)}}', f'{value}', eval_str, 1)
                #
                #         try:
                #             value = eval(eval_str)
                #         except Exception as e:
                #             print(f'构建{month}月公式出错： ', e)
                #
                #     values.append(value)
                #     times.append(f'{month}月')

            elif period_type == 'yr':
                for meter in meter_list:
                    device_stats.append(DeviceYearlyStat.objects.filter(
                        device_id=meter.device_id,
                        identifier=meter.identifier
                    ))
                first_record = DeviceYearlyStat.objects.all().order_by('created_at').first()
                if first_record:
                    min_year = first_record.created_at.year

                    for year in range(min_year, today.year + 1):
                        value = 0
                        # 公式中对应仪表的值
                        meter_value_list = []
                        if year <= today.year:
                            for index, _ in enumerate(meter_id_list):
                                year_device_stats = device_stats[index].filter(created_at__year=year)
                                meter_value_list.append(
                                    year_device_stats.last().avg if year_device_stats.count() else 0)

                            # 构建计算公式字符串
                            eval_str = formula
                            for value in meter_value_list:
                                eval_str = re.sub(r'{{(.*?)}}', f'{value}', eval_str, 1)

                            try:
                                value = eval(eval_str)
                            except Exception as e:
                                print(f'构建{year}年公式出错： ', e)

                        values.append(value)
                        times.append(f'{year}年')

            else:
                return Response({
                    'data': None,
                    'msg': 'unsupported type',
                    'status': 50010
                })

        else:
            # 接下来是手动录入能耗的计算
            ec_type_code = 10 if ec_type == 'pc' else 30

            # 月数据 (取过去12个月的数据, 有效的)
            if period_type == 'mth':

                last_efficient_ec_transcription = EcTranscription.objects.filter(ec_type=ec_type_code)
                if last_efficient_ec_transcription.exists():
                    last_efficient_ec_transcription = last_efficient_ec_transcription.order_by('year_month').last()
                    last_month = int(last_efficient_ec_transcription.year_month[4:])
                    last_year = int(last_efficient_ec_transcription.year_month[:-2])
                    year_ago = datetime(year=last_year - 1, month=last_month, day=1)
                    last_efficient_date = datetime(year=last_year, month=last_month, day=1)

                    calc_month = year_ago
                    while calc_month <= last_efficient_date:
                        record = EcTranscription.objects.filter(ec_type=ec_type_code,
                                                                year_month='%d%02d' % (
                                                                    calc_month.year, calc_month.month)).last()
                        values.append(float(record.value) if record else 0)
                        times.append(f'{calc_month.month}月')

                        month = calc_month.month + 1 if calc_month.month != 12 else 1
                        year = calc_month.year if calc_month.month != 12 else calc_month.year + 1
                        calc_month = datetime(year=year, month=month, day=1)

            elif period_type == 'yr':
                first_record = EcTranscription.objects.all().order_by('year_month').first()
                if first_record:
                    min_year = int(first_record.year_month[0:4])

                    for year in range(min_year, today.year + 1):
                        records = EcTranscription.objects.filter(ec_type=ec_type_code,
                                                                 year_month__startswith='%d' % year)
                        values.append(sum([float(record.value) for record in records]))
                        times.append(f'{year}年')
            else:
                return Response({
                    'data': None,
                    'msg': 'unsupported type',
                    'status': 50010
                })

        # 能耗单价
        # from saian_api.utils.constants import pc_price, cc_price
        settings = json.loads(project.user_panel_settings) if project.user_panel_settings else {}
        price = settings.get('price', {})
        pc_price = float(price.get('pc', 0.8))
        cc_price = float(price.get('cc', 0.8))
        price = pc_price if ec_type == 'pc' else cc_price

        data = {
            'data': {
                'stats': {
                    'ec': {
                        'data': [round(value, 2) for value in values],
                        'name': '空调电耗' if ec_type == 'pc' else '空调冷耗',
                        'unit': 'kWh'
                    },
                    'cost': {
                        'data': [round(value * price, 2) for value in values],
                        'name': '费用',
                        'unit': '元'
                    }
                },
                'time': times,
            },
            'total': len(times),
            'status': status.HTTP_200_OK
        }
        return Response(status=status.HTTP_200_OK, data=data)


"""
    节能率以及节冷率
"""
class ProjectPanelEsrApiView(APIView):

    def get(self, request):
        today = datetime.today()
        project_id = request.user.get('project_id')
        try:
            project = Project.objects.get(pk=project_id)
        except Project.DoesNotExist:
            return Response(status=status.HTTP_400_BAD_REQUEST)

        period_type = request.query_params.get('type') or 'mth'
        esr_type = 'pc' if request.path.endswith('esr_power') else 'cc'

        formula = project.pc_formula if esr_type == 'pc' else project.cc_formula

        # 节能率
        esr_ratio = []
        # 节约费用
        cost_saving = []
        # 能耗值
        ec_values = []
        # 同比
        yoy_values = []
        times = []
        # 能耗单价
        # from saian_api.utils.constants import pc_price, cc_price
        settings = json.loads(project.user_panel_settings) if project.user_panel_settings else {}
        price = settings.get('price', {})
        pc_price = float(price.get('pc', 0.8))
        cc_price = float(price.get('cc', 0.8))
        price = pc_price if esr_type == 'pc' else cc_price

        criteria = EcCriteria.objects.filter(
            is_refer=True,
            ec_type=10 if esr_type == 'pc' else 30
        )

        if criteria.count() != 12:
            # raise Exception(f'能耗基准年数据不完整{criteria.count()}')
            return Response(status=status.HTTP_200_OK, data={
                'status': 40030,
                'msg': '能耗基准年数据不完整'
            })

        if formula:
            meter_id_list = re.findall(r'{{(.*?)}}', formula) if formula else []

            # 公式中包含的 meter
            meter_list = [EcMeter.objects.get(pk=meter_id) for meter_id in meter_id_list]

            # 仪表对应的数据列表
            device_stats = []
            # 同比计算
            yoy_device_stats = []

            # 月数据 (取过去十二个月的数据)
            if period_type == 'mth':

                # 构建一年前的时间
                year_ago = datetime(year=today.year - 1, month=today.month, day=1)
                # 两年前，用于计算同比
                yyear_ago = datetime(year=year_ago.year - 1, month=year_ago.month, day=1)

                for meter in meter_list:
                    device_stats.append(DeviceMonthlyStat.objects.filter(
                        created_at__range=(year_ago, today),
                        device_id=meter.device_id,
                        identifier=meter.identifier
                    ))
                    yoy_device_stats.append(DeviceMonthlyStat.objects.filter(
                        created_at__range=(yyear_ago, year_ago),
                        device_id=meter.device_id,
                        identifier=meter.identifier
                    ))

                calc_month = year_ago
                yoy_month = yyear_ago
                while calc_month < today:
                    ec_value = 0
                    yoy_ec_value = 0
                    # 公式中对应仪表的值
                    meter_value_list = []
                    yoy_meter_value_list = []
                    if formula:
                        for index, _ in enumerate(meter_id_list):
                            month_device_stats = device_stats[index].filter(created_at__month=calc_month.month,
                                                                            created_at__year=calc_month.year)
                            meter_value_list.append(month_device_stats.last().avg if month_device_stats.count() else 0)
                            # 同比
                            yoy_month_device_stats = yoy_device_stats[index].filter(created_at__month=yoy_month.month,
                                                                                    created_at__year=yoy_month.year)
                            yoy_meter_value_list.append(
                                yoy_month_device_stats.last().avg if yoy_month_device_stats.count() else 0)

                        # 构建计算公式字符串
                        eval_str = formula
                        for value in meter_value_list:
                            eval_str = re.sub(r'{{(.*?)}}', f'{value}', eval_str, 1)

                        # 同比计算公式
                        yoy_eval_str = formula
                        for value in yoy_meter_value_list:
                            yoy_eval_str = re.sub(r'{{(.*?)}}', f'{value}', yoy_eval_str, 1)

                        try:
                            ec_value = eval(eval_str)
                            yoy_ec_value = eval(yoy_eval_str)
                        except Exception as e:
                            print(f'构建{calc_month.month}月公式出错： ', e)
                    criterion = criteria.filter(year_month__endswith='%02d' % calc_month.month).last()
                    if not criterion:
                        raise Exception(f'缺少{calc_month.month}月的基准年数据')

                    ec_values.append(ec_value)
                    yoy_values.append((ec_value - yoy_ec_value) / yoy_ec_value if yoy_ec_value else '--')
                    ratio = (float(criterion.value) - ec_value) / float(criterion.value)
                    esr_ratio.append(ratio if ec_value != 0 else 0)
                    saving = (float(criterion.value) - ec_value) * price
                    cost_saving.append(saving if ec_value != 0 else 0)
                    times.append(f'{calc_month.month}月')

                    month = calc_month.month + 1 if calc_month.month != 12 else 1
                    calc_year = calc_month.year if calc_month.month != 12 else calc_month.year + 1
                    yoy_year = calc_year - 1
                    calc_month = datetime(year=calc_year, month=month, day=1)
                    yoy_month = datetime(year=yoy_year, month=month, day=1)

                # for month in range(1, 13):
                #     ec_value = 0
                #     yoy_ec_value = 0
                #     # 公式中对应仪表的值
                #     meter_value_list = []
                #     yoy_meter_value_list = []
                #     if month <= today.month and formula:
                #         for index, _ in enumerate(meter_id_list):
                #             month_device_stats = device_stats[index].filter(created_at__month=month)
                #             meter_value_list.append(month_device_stats.last().avg if month_device_stats.count() else 0)
                #             # 同比
                #             yoy_month_device_stats = yoy_device_stats[index].filter(created_at__month=month)
                #             yoy_meter_value_list.append(
                #                 yoy_month_device_stats.last().avg if yoy_month_device_stats.count() else 0)
                #
                #         # 构建计算公式字符串
                #         eval_str = formula
                #         for value in meter_value_list:
                #             eval_str = re.sub(r'{{(.*?)}}', f'{value}', eval_str, 1)
                #
                #         # 同比计算公式
                #         yoy_eval_str = formula
                #         for value in yoy_meter_value_list:
                #             yoy_eval_str = re.sub(r'{{(.*?)}}', f'{value}', yoy_eval_str, 1)
                #
                #         try:
                #             ec_value = eval(eval_str)
                #             yoy_ec_value = eval(yoy_eval_str)
                #         except Exception as e:
                #             print(f'构建{month}月公式出错： ', e)
                #     criterion = criteria.filter(year_month__endswith='%02d' % month).last()
                #     if not criterion:
                #         raise Exception(f'缺少{month}月的基准年数据')
                #
                #     ec_values.append(ec_value)
                #     yoy_values.append((ec_value - yoy_ec_value) / yoy_ec_value if yoy_ec_value else '--')
                #     ratio = (float(criterion.value) - ec_value) / float(criterion.value)
                #     esr_ratio.append(ratio if ec_value != 0 else 0)
                #     saving = (float(criterion.value) - ec_value) * price
                #     cost_saving.append(saving if ec_value != 0 else 0)
                #     times.append(f'{month}月')
            # 年数据
            elif period_type == 'yr':
                # 年基准数
                year_criteria = reduce(lambda x, y: x + float(y.value), criteria, 0)
                for meter in meter_list:
                    device_stats.append(DeviceYearlyStat.objects.filter(
                        device_id=meter.device_id,
                        identifier=meter.identifier
                    ))
                first_record = DeviceYearlyStat.objects.all().order_by('created_at').first()
                if first_record:
                    min_year = first_record.created_at.year

                    for year in range(min_year, today.year + 1):
                        # 计算出来的能耗值，默认零
                        ec_value = 0
                        # 公式中对应仪表的值
                        meter_value_list = []
                        if year <= today.year:
                            for index, _ in enumerate(meter_id_list):
                                year_device_stats = device_stats[index].filter(created_at__year=year)
                                meter_value_list.append(
                                    year_device_stats.last().avg if year_device_stats.count() else 0)

                            # 构建计算公式字符串
                            eval_str = formula
                            for value in meter_value_list:
                                eval_str = re.sub(r'{{(.*?)}}', f'{value}', eval_str, 1)
                            try:
                                ec_value = eval(eval_str)
                            except Exception as e:
                                print(f'构建{year}年公式出错： ', e)
                        ec_values.append(ec_value)
                        ratio = (year_criteria - ec_value) / year_criteria
                        yoy_values.append('--')
                        esr_ratio.append(ratio if ec_value != 0 else 0)
                        saving = (year_criteria - ec_value) * price
                        cost_saving.append(saving if ec_value != 0 else 0)
                        times.append(f'{year}年')
            else:
                return Response({
                    'data': None,
                    'msg': 'unsupported type',
                    'status': 50010
                })

        else:
            ec_type_code = 10 if esr_type == 'pc' else 30
            if period_type == 'mth':
                last_efficient_ec_transcription = EcTranscription.objects.filter(ec_type=ec_type_code)

                if last_efficient_ec_transcription.exists():
                    last_efficient_ec_transcription = last_efficient_ec_transcription.order_by('year_month').last()
                    last_year = int(last_efficient_ec_transcription.year_month[:-2])
                    last_month = int(last_efficient_ec_transcription.year_month[4:])
                    last_efficient_date = datetime(year=last_year, month=last_month, day=1)
                    year_ago = datetime(year=last_year - 1, month=last_month, day=1)
                    # 两年前，用于计算同比
                    yyear_ago = datetime(year=year_ago.year - 1, month=year_ago.month, day=1)

                    # # 构建一年前时间
                    # year_ago = datetime(year=today.year - 1, month=today.month, day=today.day)
                    # # 两年前，用于计算同比
                    # yyear_ago = datetime(year=year_ago.year - 1, month=year_ago.month, day=year_ago.day)

                    calc_month = year_ago
                    yoy_month = yyear_ago

                    while calc_month <= last_efficient_date:
                        record = EcTranscription.objects.filter(ec_type=ec_type_code,
                                                                year_month='%d%02d' % (
                                                                    calc_month.year, calc_month.month)).last()
                        yoy_record = EcTranscription.objects.filter(ec_type=ec_type_code,
                                                                    year_month='%d%02d' % (
                                                                        yoy_month.year, yoy_month.month)).last()

                        criterion = criteria.filter(year_month__endswith='%02d' % calc_month.month).last()
                        print(calc_month.month, calc_month.year)
                        if not criterion:
                            raise Exception(f'缺少{calc_month.month}月的基准年数据')

                        ec_value = float(record.value) if record else 0
                        yoy_ec_value = float(yoy_record.value) if yoy_record else 0
                        criterion_value = float(criterion.value)
                        ec_values.append(ec_value)
                        yoy_values.append((ec_value - yoy_ec_value) / yoy_ec_value if yoy_ec_value and ec_value else '--')
                        ratio = (criterion_value - ec_value) / criterion_value
                        esr_ratio.append(ratio if ec_value != 0 else 0)
                        saving = (criterion_value - ec_value) * price
                        cost_saving.append(saving if ec_value != 0 else 0)
                        times.append(f'{calc_month.month}月')

                        month = calc_month.month + 1 if calc_month.month != 12 else 1
                        calc_year = calc_month.year if calc_month.month != 12 else calc_month.year + 1
                        yoy_year = calc_year - 1
                        calc_month = datetime(year=calc_year, month=month, day=1)
                        yoy_month = datetime(year=yoy_year, month=month, day=1)

                # for month in range(1, 13):
                #     record = EcTranscription.objects.filter(ec_type=ec_type_code,
                #                                             year_month='%d%02d' % (today.year, month)).last()
                #     yoy_record = EcTranscription.objects.filter(ec_type=ec_type_code,
                #                                                 year_month='%d%02d' % (today.year - 1, month)).last()
                #
                #     criterion = criteria.filter(year_month__endswith='%02d' % month).last()
                #     if not criterion:
                #         raise Exception(f'缺少{month}月的基准年数据')
                #
                #     ec_value = float(record.value) if record else 0
                #     yoy_ec_value = float(yoy_record.value) if yoy_record else 0
                #     criterion_value = float(criterion.value)
                #     ec_values.append(ec_value)
                #     yoy_values.append((ec_value - yoy_ec_value) / yoy_ec_value if yoy_ec_value and ec_value else '--')
                #     ratio = (criterion_value - ec_value) / criterion_value
                #     esr_ratio.append(ratio if ec_value != 0 else 0)
                #     saving = (criterion_value - ec_value) * price
                #     cost_saving.append(saving if ec_value != 0 else 0)
                #     times.append(f'{month}月')

            elif period_type == 'yr':
                # 年基准数
                year_criteria = reduce(lambda x, y: x + float(y.value), criteria, 0)
                first_record = EcTranscription.objects.all().order_by('year_month').first()
                if first_record:
                    min_year = int(first_record.year_month[0:4])

                    for year in range(min_year, today.year + 1):
                        records = EcTranscription.objects.filter(ec_type=ec_type_code,
                                                                 year_month__startswith=year)

                        ec_value = sum([float(record.value) for record in records])
                        ratio = (year_criteria - ec_value) / year_criteria
                        yoy_values.append('--')
                        esr_ratio.append(ratio if ec_value != 0 else 0)
                        saving = (year_criteria - ec_value) * price
                        cost_saving.append(saving)
                        times.append(f'{year}年')
            else:
                return Response({
                    'data': None,
                    'msg': 'unsupported type',
                    'status': 50010
                })

        data = {
            'data': {
                'stats': {
                    'ratio': {
                        'name': '节冷率' if esr_type == 'cc' else '节能率',
                        'data': [round((ratio * 100), 2) for ratio in esr_ratio],
                        'unit': '%'

                    },
                    'yoy': {
                        'name': '同比',
                        'data': [round((yoy * 100), 2) if yoy != '--' else '--' for yoy in yoy_values],
                        'unit': '%'
                    },
                    'cost_saving': {
                        'name': '节省开支',
                        'data': [round(saving, 2) for saving in cost_saving],
                        'unit': '元'
                    },
                },
                'time': times
            },
            'total': len(times),
            'status': status.HTTP_200_OK
        }
        return Response(status=status.HTTP_200_OK, data=data)


"""
    实时数据
"""
class EnergySummaryApiView(APIView):
    @staticmethod
    def get_ec_summary(formula, now, criteria, price, ec_type_code):
        result = {
            'year': {'ec': [], 'time': [], 'ec_saving': [], 'cost_saving': []},
            'month': {'ec': [], 'time': [], 'ec_saving': [], 'cost_saving': []},
            'day': {'ec': [], 'time': []},
            'hour': {'ec': [], 'time': []},
            'last_nature_month_ec': 0,
            'last_nature_month_esr': 0
        }

        year_criteria = reduce(lambda x, y: x + float(y.value), criteria, 0)

        if criteria.count() == 12:
            if formula:
                meter_id_list = re.findall(r'{{(.*?)}}', formula)
                meter_list = [EcMeter.objects.get(pk=meter_id) for meter_id in meter_id_list]

                year_device_stats = []
                month_device_stats = []
                day_device_stats = []
                hour_device_stats = []
                last_nature_month_stats = []

                for meter in meter_list:
                    year_device_stats.append(DeviceYearlyStat.objects.filter(
                        device_id=meter.device_id,
                        identifier=meter.identifier
                    ))
                    month_device_stats.append(DeviceMonthlyStat.objects.filter(
                        created_at__year=now.year,
                        device_id=meter.device_id,
                        identifier=meter.identifier
                    ))
                    day_device_stats.append(DeviceDailyStat.objects.filter(
                        created_at__year=now.year,
                        created_at__month=now.month,
                        device_id=meter.device_id,
                        identifier=meter.identifier
                    ))
                    hour_device_stats.append(DeviceHourlyStat.objects.filter(
                        created_at__year=now.year,
                        created_at__month=now.month,
                        created_at__day=now.day,
                        device_id=meter.device_id,
                        identifier=meter.identifier
                    ))

                    if now.month == 1:
                        last_nature_month_stats.append(DeviceMonthlyStat.objects.filter(
                            created_at__year=now.year - 1,
                            created_at__month=12,
                            device_id=meter.device_id,
                            identifier=meter.identifier
                        ))
                    else:
                        last_nature_month_stats.append(DeviceMonthlyStat.objects.filter(
                            created_at__year=now.year,
                            created_at__month=now.month - 1,
                            device_id=meter.device_id,
                            identifier=meter.identifier
                        ))

                # 年数据
                first_year_record = DeviceYearlyStat.objects.order_by('created_at').first()
                if first_year_record:
                    min_year = first_year_record.created_at.year
                    for year in range(min_year, now.year):
                        ec_year = []
                        meter_value_list = []
                        for index, _ in enumerate(meter_id_list):
                            meter_stats = year_device_stats[index].filter(created_at__year=year)
                            meter_value_list.append(meter_stats.last().avg if meter_stats.count() else 0)

                        eval_str = formula
                        for value in meter_value_list:
                            eval_str = re.sub(r'{{(.*?)}}', f'{value}', eval_str, 1)
                        try:
                            ec_year = eval(eval_str)
                        except Exception as e:
                            print(f'构建{year}年公式出错： ', e)
                        result['year']['ec'].append(ec_year)
                        result['year']['time'].append(f'{year}年')
                        result['year']['ec_saving'].append(
                            year_criteria - ec_year if (year_criteria > ec_year and ec_year) else 0)
                    # 计算节约的费用
                    result['year']['cost_saving'] = [ec * price for ec in result['year']['ec_saving']]

                # 月数据
                for month in range(1, now.month + 1):
                    ec_month = 0
                    month_criterion = float(criteria.filter(year_month__endswith='%02d' % month).last().value)
                    meter_value_list = []
                    for index, _ in enumerate(meter_id_list):
                        meter_stats = month_device_stats[index].filter(created_at__month=month)
                        meter_value_list.append(meter_stats.last().avg if meter_stats else 0)

                    eval_str = formula
                    for value in meter_value_list:
                        eval_str = re.sub(r'{{(.*?)}}', f'{value}', eval_str, 1)
                    try:
                        ec_month = eval(eval_str)
                    except Exception as e:
                        print(f'构建{month}月公式出错： ', e)
                    result['month']['ec'].append(ec_month)
                    result['month']['time'].append(f'{month}月')
                    result['month']['ec_saving'].append(
                        month_criterion - ec_month if (month_criterion > ec_month and ec_month) else 0)
                # 计算节约的费用
                result['month']['cost_saving'] = [ec * price for ec in result['month']['ec_saving']]

                # 日数据
                for day in range(1, now.day + 1):
                    ec_day = 0
                    meter_value_list = []
                    for index, _ in enumerate(meter_id_list):
                        meter_stats = day_device_stats[index].filter(created_at__day=day)
                        meter_value_list.append(meter_stats.last().avg if meter_stats.count() else 0)

                    eval_str = formula
                    for value in meter_value_list:
                        eval_str = re.sub(r'{{(.*?)}}', f'{value}', eval_str, 1)
                    try:
                        ec_day = eval(eval_str)
                    except Exception as e:
                        print(f'构建{day}日公式出错： ', e)
                    result['day']['ec'].append(ec_day)
                    result['day']['time'].append(f'{day}日')

                # 小时数据
                for hour in range(1, now.hour + 1):
                    ec_hour = 0
                    meter_value_list = []
                    for index, _ in enumerate(meter_id_list):
                        meter_stats = hour_device_stats[index].filter(created_at__hour=hour)
                        meter_value_list.append(meter_stats.last().avg if meter_stats.count() else 0)

                    eval_str = formula
                    for value in meter_value_list:
                        eval_str = re.sub(r'{{(.*?)}}', f'{value}', eval_str, 1)
                    try:
                        ec_hour = eval(eval_str)
                    except Exception as e:
                        print(f'构建{hour}时公式出错： ', e)
                    result['hour']['ec'].append(ec_hour)
                    result['hour']['time'].append(f'{hour}时')

                # 上一个自然月的能耗
                ec_last_nature_month = 0
                meter_value_list = []
                for index, _ in enumerate(meter_id_list):
                    meter_stats = last_nature_month_stats[index]
                    meter_value_list.append(meter_stats.last().avg if meter_stats.count() else 0)
                eval_str = formula

                for value in meter_value_list:
                    eval_str = re.sub(r'{{(.*?)}}', f'{value}', eval_str, 1)
                try:
                    ec_last_nature_month = eval(eval_str)
                except Exception as e:
                    print(f'构建上一个自然月公式出错： ', e)
                result['last_nature_month_ec'] = ec_last_nature_month

                # 上一个自然月的节能率
                end_width_str = '12' if now.month == 1 else '%02d' % (now.month - 1)
                last_nature_month_criterion = float(criteria.filter(year_month__endswith=end_width_str).last().value)
                esr = (last_nature_month_criterion - ec_last_nature_month) / last_nature_month_criterion

                result['last_nature_month_esr'] = f'{esr * 100}%'
            else:
                # 计算年数据
                first_record = EcTranscription.objects.all().order_by('year_month').first()
                if first_record:
                    min_year = int(first_record.year_month[0:4])
                    if min_year < now.year:
                        for year in range(min_year, now.year):
                            records = EcTranscription.objects.filter(ec_type=ec_type_code,
                                                                     year_month__startswith=year)
                            ec_value = sum([float(record.value) for record in records])
                            result['year']['ec'].append(ec_value)
                            result['year']['time'].append(f'{year}年')
                            result['year']['ec_saving'].append(
                                year_criteria - ec_value if (year_criteria > ec_value and ec_value) else 0)
                        # 计算节约的费用
                        result['year']['cost_saving'] = [ec * price for ec in result['year']['ec_saving']]

                # 计算月数据
                for month in range(1, now.month + 1):
                    # 12 月时，这里要查询12次
                    month_criterion = float(criteria.filter(year_month__endswith='%02d' % month).last().value)
                    record = EcTranscription.objects.filter(ec_type=ec_type_code,
                                                            year_month='%d%02d' % (now.year, month)).last()
                    ec_value = float(record.value) if record else 0
                    result['month']['ec'].append(ec_value)
                    result['month']['time'].append(f'{month}月')
                    result['month']['ec_saving'].append(
                        month_criterion - ec_value if (month_criterion > ec_value and ec_value) else 0)
                # 计算节约的费用
                result['month']['cost_saving'] = [ec * price for ec in result['month']['ec_saving']]

                # 计算上一个月的能耗以及节能率（有效的）
                last_month_record = None
                last_month_criterion = None
                last_efficient_ec_transcription = EcTranscription.objects.filter(ec_type=ec_type_code)
                if last_efficient_ec_transcription.exists():
                    last_efficient_ec_transcription = last_efficient_ec_transcription.order_by('year_month').last()
                    last_month = last_efficient_ec_transcription.year_month[4:]
                    # last_month = 12 if now.month == 1 else now.month - 1
                    # last_month_criterion = criteria.filter(year_month__endswith='%02d' % last_month).last()
                    last_month_criterion = criteria.filter(year_month__endswith=last_month).last()
                    last_month_record = last_efficient_ec_transcription
                    # last_month_record = EcTranscription.objects.filter(
                    #     ec_type=ec_type_code,
                    #     year_month='%d%02d' % (
                    #         now.year if now.month != 1 else now.year - 1, (now.month - 1) if now.month != 1 else 12)
                    # ).last()

                result['last_efficient_month_ec'] = float(last_month_record.value) if last_month_record else 0

                esr = (float(last_month_criterion.value) - float(last_month_record.value)) / float(
                    last_month_criterion.value) if (last_month_criterion and last_month_record) else 0

                result['last_efficient_month_esr'] = f'{round(esr * 100, 2)}%'

                last_nature_month = 12 if now.month == 1 else now.month - 1
                if last_month_record and int(last_month_record.year_month[4:]) == last_nature_month:
                    result['last_nature_month_ec'] = result['last_efficient_month_ec']
                    result['last_nature_month_esr'] = result['last_efficient_month_esr']
                else:
                    result['last_nature_month_ec'] = 0
                    result['last_nature_month_esr'] = '0%'

        return result

    def get(self, request):
        now = datetime.now()

        project_id = request.user.get('project_id')
        try:
            project = Project.objects.get(pk=project_id)
        except Project.DoesNotExist:
            return Response(status=status.HTTP_400_BAD_REQUEST)

        # 能耗单价
        # from saian_api.utils.constants import pc_price, wc_price, cc_price
        settings = json.loads(project.user_panel_settings) if project.user_panel_settings else {}
        price = settings.get('price', {})
        pc_price = float(price.get('pc', 0.8))
        wc_price = float(price.get('wc', 0.8))
        cc_price = float(price.get('cc', 0.8))

        # 项目面积
        cooling_area = project.cooling_area

        # 项目能耗基准
        pc_criteria = EcCriteria.objects.filter(is_refer=True, ec_type=10)
        wc_criteria = EcCriteria.objects.filter(is_refer=True, ec_type=20)
        cc_criteria = EcCriteria.objects.filter(is_refer=True, ec_type=30)
        # 年能耗基准
        year_pc_criteria = reduce(lambda x, y: x + float(y.value), pc_criteria, 0)
        year_wc_criteria = reduce(lambda x, y: x + float(y.value), wc_criteria, 0)
        year_cc_criteria = reduce(lambda x, y: x + float(y.value), cc_criteria, 0)

        # 电，水，冷 计算公式
        pc_formula = project.pc_formula
        wc_formula = project.wc_formula
        cc_formula = project.cc_formula

        # 电，水，冷历史能耗
        pc = self.get_ec_summary(pc_formula, now, pc_criteria, pc_price, 10)
        wc = self.get_ec_summary(wc_formula, now, wc_criteria, wc_price, 20)
        cc = self.get_ec_summary(cc_formula, now, cc_criteria, cc_price, 30)

        return Response(
            data={
                'data': {
                    'pc': pc,
                    'wc': wc,
                    'cc': cc,
                    'ec_per_unit': {
                        'pc': pc['last_nature_month_ec'] / cooling_area if cooling_area else 0,
                        'wc': wc['last_nature_month_ec'] / cooling_area if cooling_area else 0,
                        'cc': cc['last_nature_month_ec'] / cooling_area if cooling_area else 0,
                    }
                },
                'unit': 'kWh',
                'status': status.HTTP_200_OK
            },
            status=status.HTTP_200_OK)


"""
    设备类型离线前十
"""
class DeviceOfflineRate(APIView):
    def get(self, request):
        project_id = request.user.get('project_id')
        try:
            project = Project.objects.get(pk=project_id)
        except Project.DoesNotExist:
            return Response(status=status.HTTP_400_BAD_REQUEST)

        result = Device.objects.offline_rate(project)

        data = {
            'data': result,
            'total': len(result['top10']),
            'status': status.HTTP_200_OK
        }
        return Response(status=status.HTTP_200_OK, data=data)


"""
    设备分类故障率前十
"""
class DeviceFault30d(APIView):
    def get(self, request):
        project_id = request.user.get('project_id')
        try:
            project = Project.objects.get(pk=project_id)
        except Project.DoesNotExist:
            return Response(status=status.HTTP_400_BAD_REQUEST)

        result = DeviceIssue.objects.fault_rate_30(project)

        data = {
            'data': result,
            'total': len(result['top10']),
            'status': status.HTTP_200_OK
        }

        return Response(status=status.HTTP_200_OK, data=data)


"""
    故障原因统计前十
"""
class DeviceFaultReasonStats(APIView):
    def get(self, request):
        project_id = request.user.get('project_id')
        try:
            project = Project.objects.get(pk=project_id)
        except Project.DoesNotExist:
            return Response(status=status.HTTP_400_BAD_REQUEST)

        result = DeviceIssue.objects.fault_reason_rate(project)

        return Response(status=status.HTTP_200_OK,
                        data={'data': result, 'total': len(result['top10']), 'status': status.HTTP_200_OK})


"""
    报警简单统计
"""
class DeviceAlarmSummary(APIView):
    def get(self, request):
        project_id = request.user.get('project_id')
        try:
            project = Project.objects.get(pk=project_id)
        except Project.DoesNotExist:
            return Response(status=status.HTTP_400_BAD_REQUEST)

        result = DeviceIssue.objects.alarm_summary(project)

        return Response(status=status.HTTP_200_OK, data={'data': result, 'status': status.HTTP_200_OK})


"""
    用户关注度前十
"""
class DeviceFollowerTop10(APIView):
    def get(self, request):
        return Response(ResponseUtils.format(UserDevice.objects.device_follower_top10(), result_key='ranker'))


"""
    设备关注度前十
"""
class DeviceFollowingTop10(APIView):
    def get(self, request):
        return Response(ResponseUtils.format(UserDevice.objects.device_following_top10(), result_key='ranker'))


"""
    能耗基准年数据
"""
class EcCriteriaViewSet(viewsets.ModelViewSet):
    renderer_classes = (SyJSONRender,)
    serializer_class = EcCriteriaSerializer

    def check_refer_unique(self, name):
        return EcCriteria.objects.filter(is_refer=True, ec_type=self.request.data['ec_type'], name=name,
                                         year_month__endswith=self.request.data['year_month'][-2:])

    def perform_create(self, serializer):
        name = self.request.data.get('name', None)
        if self.request.data['is_refer']:
            referObj = self.check_refer_unique(name)
            if referObj.exists():
                referObj.update(is_refer=False)
        serializer.save()

    def perform_update(self, serializer):
        name = self.request.data.get('name', self.name)
        if self.request.data['is_refer']:
            referObj = self.check_refer_unique(name)
            if referObj.exists():
                referObj.update(is_refer=False)
        serializer.save()

    def get_queryset(self):
        queryset = EcCriteria.objects.order_by('-created_at')

        ec_type = self.request.query_params.get('ec_type', None)
        if ec_type is not None:
            queryset = queryset.filter(ec_type=ec_type)

        is_refer = self.request.query_params.get('is_refer', None)
        if is_refer is not None:
            queryset = queryset.filter(is_refer=is_refer)

        return queryset


"""
    手动录入能耗数据
"""
class EcTranscriptionViewSet(viewsets.ModelViewSet):
    renderer_classes = (SyJSONRender,)
    serializer_class = EcTranscriptionSerializer
    queryset = EcTranscription.objects.all()

    # def perform_create(self, serializer):
    #     # 手动将能耗对象设为总能耗
    #     serializer.save(ec_object=10)

class EcTranscriptionV5View(viewsets.ModelViewSet):
    def list(self, request, *args, **kwargs):
        records = {}
        queryset = EcTranscription.objects.all()

        ec_types = EcTranscription.objects.values_list('ec_type', flat=True).distinct().order_by('ec_type')

        for ec_type in ec_types:
            for item in queryset.filter(ec_type=ec_type):
                year = item.year_month[:4]
                month = item.year_month[-2:]

                key = f'{year}-{ec_type}'

                data = records.get(key, [0] * 12)
                data[int(month) - 1] = item.value
                records[key] = data

        result = []
        for k, record in records.items():
            year, ec_type = k.split('-')
            result.append({
                'year': year,
                'ec_type': int(ec_type),
                'data': record
            })

        return Response({
            'status': status.HTTP_200_OK,
            'data': result
        })

class PanelSettingsViewSet(APIView):
    """
        用户看板设置项
    """
    def get(self, request):
        project = Project.objects.get(pk=request.user['project_id'])
        panel_settings_json = project.user_panel_settings
        settings = json.loads(panel_settings_json) if panel_settings_json else {}
        return Response(ResponseUtils.custom_response_format(settings, 'settings', True))

    def put(self, request):
        project = Project.objects.get(pk=request.user['project_id'])
        settings = json.dumps(request.data)
        project.user_panel_settings = settings
        project.save()
        return Response(ResponseUtils.custom_response_format({}))


class WebDashboardChartView(viewsets.ModelViewSet):
    """
        Web 总览页四宫格的数据（设备总览，设备在线统计，故障报警统计，房间温湿度）
    """
    @action(detail=True, methods=['get'])
    def get_authenticators(self):
        if 'intapi' in self.request.path:
            return [IntapiAuth(), ]
        return [LegacyAuth(), ]
    
    def list(self, request, *args, **kwargs):
        dt_ids = Device.objects.values_list('device_prototype_id', flat=True)
        dps = DevicePrototype.objects.filter(id__in=dt_ids)
        dts = set([dp.device_type for dp in dps])
        devices = Device.objects.filter(project__isnull=False)

        run_stats = []
        device_stats = {'data': []}
        issue_stats = []
        temphum_stats = {}

        for dt in dts:
            run_stats.append(dt.run_stats())
            device_stats['data'].extend(dt.device_stats())
            issue_stats.append(dt.issue_stats())

        device_stats['online'] = devices.filter(online=True).count()
        device_stats['offline'] = devices.filter(online=False).count()
        device_stats['total'] = devices.count()
        device_stats['total_alarm'] = devices.filter(online=True, in_alarm=True).count()
        device_stats['total_fault'] = devices.filter(online=True, in_fault=True).count()
        device_stats['total_run'] = devices.filter(online=True, sw_on=True).count()
        device_stats['total_stop'] = devices.filter(online=True, sw_on=False).count()
        # 排序，把 'on_value' 放在前面
        device_stats['data'].sort(key=lambda x: x.get('on_value', 0), reverse=True)

        # devices = Device.objects.all()

        if 'intapi/dashboards' in self.request.path:
            return Response({
                'status': status.HTTP_200_OK,
                'device_stats': device_stats
            })
        elif 'intapi/run_stats' in self.request.path:
            return Response({
                'status': status.HTTP_200_OK,
                'device_stats': run_stats
            })
        else:
            return Response({
                'status': status.HTTP_200_OK,
                'data': {
                    'run_stats': run_stats,
                    'device_stats': device_stats,
                    'issue_stats': issue_stats,
                    'temphum_stats': temphum_stats
                    # },
                    # 'statistic': {
                    #     "total": devices.count(),
                    #     "total_normal": devices.filter(online=True, in_fault=False, in_alarm=False).count(),
                    #     "total_fault": devices.filter(online=True, in_fault=True).count(),
                    #     "total_offline": devices.filter(online=False).count(),
                    #     "total_run": devices.filter(online=True, sw_on=True).count(),
                    #     "total_stop": devices.filter(online=True, sw_on=False).count(),
                    #     "total_alarm": devices.filter(online=True, in_alarm=True).count()
                }
            })


class MiniStatsInfo(viewsets.ModelViewSet):
    @classmethod
    def get_cs_mac(cls, project_settings):
        dashboard_layout = json.loads(project_settings.get('dashboard_layout', 'null'))
        cs_mac = None
        if dashboard_layout:
            web_cs_overview = list(filter(lambda x: x.get('web_chart', {}).get('uni_name', None) == '冷源概况', dashboard_layout))
            cs_mac = web_cs_overview[0].get('mac', None) if len(web_cs_overview) else None

        return cs_mac

    @classmethod
    def is_display(cls, project_settings, cs_mac):

        dis_ecanalysis = project_settings.get('dis_ecanalysis', None)
        dis_ecanalysis_v5 = project_settings.get('dis_ecanalysis_v5', None)
        dis_termstats = project_settings.get('dis_termstats', None)
        dis_csinfo = project_settings.get('dis_csinfo', None)

        return bool((dis_ecanalysis or dis_ecanalysis_v5) and dis_termstats and (dis_csinfo and cs_mac))

    def list(self, request, *args, **kwargs):
        project_settings = json.loads(Project.objects.get(pk=request.user.get('project_id')).settings)
        temp_t = project_settings.get('temp_t', None)
        cs_mac = self.get_cs_mac(project_settings)

        # display_stats_info = self.is_display(project_settings, cs_mac)

        # if display_stats_info:
        # 节能
        ec_stats = None
        esr_record: ManualRecord = ManualRecord.objects.filter(use_for=10, name__endswith='节能率').order_by('updated_at').last()
        if esr_record is not None:
            esr = json.loads(esr_record.data).get('value')
            ec_stats = {
                "esr": esr,
            }

            ec_criteria = EcCriteria.objects.filter(is_refer=True, ec_type=10).aggregate(sum=Sum('value')).get('sum')
            ec_stats['ec_criteria'] = ec_criteria
            ec_stats['ec_saving'] = float(esr) / 100 * ec_criteria

        # 末端详情
        terminals = list(Terminal.objects.filter(terminal_type=10, online=True, show_en=True))
        terminal_ids = [t.id for t in terminals]
        terminal_temps = list(TerminalAttribute.objects.filter(
            Q(identifier__contains='LocalTemp') | Q(identifier__contains='temperature') | Q(identifier='ReturnAirTemp') | Q(
                identifier__endswith='RATemp') | Q(identifier__endswith='AHURATemp') | Q(identifier__endswith='_SATemp'),
            terminal_id__in=terminal_ids).values_list('value', flat=True))

        terminal_temps = list(filter(lambda x: x > 0, map(float, [temp for temp in terminal_temps if is_number(temp)])))

        terminal_stats = {
            "total": len(terminals),
            "avg": round(sum(terminal_temps) / len(terminal_temps), 2) if len(terminal_temps) else 0,
            "min": min(terminal_temps) if len(terminal_temps) else 0,
            "max": max(terminal_temps) if len(terminal_temps) else 0,
            "temp_t": temp_t
        }

        # 冷源详情
        if not cs_mac:
            cs_dt_ids = list(DeviceType.objects.filter(parent_id=1).values_list('id', flat=True))
            cs_device = Device.objects.filter(device_type_id__in=cs_dt_ids, project_id__isnull=False,
                                              nick_name__contains='冷源').order_by('updated_at').last()
        else:
            cs_device = Device.objects.filter(mac=cs_mac).order_by('created_at').last()

        if cs_device:
            terminals = Terminal.objects.filter(device_id=cs_device.id).order_by('id')

            cs_terminal = terminals.get(idx__isnull=True)
            # cs_terminal_attrs = TerminalAttribute.objects.filter(terminal=cs_terminal)
            # temp_set_attr = cs_terminal_attrs.filter(identifier='CWOTempSetVal').last()
            temp_set_attr = TerminalAttribute.objects.query_object_by_idf(cs_terminal, 'CWOTempSetVal', cs_device)
            # run_status = cs_terminal_attrs.filter(Q(identifier='CSRunStatus') | Q(identifier='RunStatus')).last()
            run_status = TerminalAttribute.objects.query_object_by_idf(cs_terminal, 'CSRunStatus', cs_device)
            if not run_status:
                run_status = TerminalAttribute.objects.query_object_by_idf(cs_terminal, 'RunStatus', cs_device)

            # cwop_temp = cs_terminal_attrs.filter(identifier='CWOPTemp').last()
            cwop_temp = TerminalAttribute.objects.query_object_by_idf(cs_terminal, 'CWOPTemp', cs_device)
            # cwrp_temp = cs_terminal_attrs.filter(identifier='CWRPTemp').last()
            cwrp_temp = TerminalAttribute.objects.query_object_by_idf(cs_terminal, 'CWRPTemp', cs_device)

            host_terminals = terminals.filter(prefix='Host')
            hosts = []
            ap_ids = AttributePrototype.objects.filter(Q(identifier__endswith='RTLoad') |
                                                       Q(identifier__endswith='RunStatus') |
                                                       Q(identifier__contains='Host'),
                                                       device_prototype_id=cs_device.device_prototype_id).values_list('id', flat=True)
            tas = TerminalAttribute.objects.query_object_list(cs_device, ap_ids, request.user['project_id'])
            for terminal in host_terminals:
                # host_tas = TerminalAttribute.objects.filter(terminal=terminal)
                # rt_load = host_tas.filter(identifier__endswith='RTLoad').last()
                # host_run_status = host_tas.filter(identifier__endswith='RunStatus').last()

                rt_load = next((ta for ta in tas if f'_{terminal.idx}_' in ta.identifier and ta.identifier.endswith('RTLoad')), None)
                host_run_status = next((ta for ta in tas if f'_{terminal.idx}_' in ta.identifier and ta.identifier.endswith('RunStatus')), None)
                hosts.append({
                    'nick_name': terminal.nick_name,
                    'rt_load': rt_load.value if rt_load is not None else None,
                    'run_status': host_run_status.value if host_run_status is not None else None
                })
        else:
            temp_set_attr = None
            run_status = None
            cwop_temp = None
            cwrp_temp = None
            hosts = []

        cs_stats = {
            "nick_name": cs_device.nick_name if cs_device else None,
            "mac": cs_device.mac if cs_device else None,
            "temp_set": temp_set_attr.value if temp_set_attr else None,
            "run_status": run_status.value if run_status else None,
            "cwop_temp": cwop_temp.value if cwop_temp else None,
            "cwrp_temp": cwrp_temp.value if cwrp_temp else None,
            "hosts": hosts
        }

        # else:
        #     ec_stats = None
        #     terminal_stats = None
        #     cs_stats = None

        return Response({
            "data": {
                # "display": display_stats_info,
                "ec_stats": ec_stats,
                "terminal_stats": terminal_stats,
                "cs_stats": cs_stats
            },
            "status": status.HTTP_200_OK
        })
