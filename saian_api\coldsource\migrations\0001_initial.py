# Generated by Django 3.2.8 on 2021-12-30 15:16

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ColdSource',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('image', models.CharField(max_length=255, null=True)),
                ('coords_arr', models.TextField(null=True)),
                ('mac', models.Char<PERSON>ield(max_length=255)),
                ('max_hr_power_cons', models.CharField(max_length=255, null=True)),
                ('eer_step', models.Char<PERSON>ield(max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTime<PERSON>ield(auto_now=True)),
            ],
            options={
                'db_table': 'cold_sources',
            },
        ),
        migrations.CreateModel(
            name='CsEerAnalyse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('power_cons', models.CharField(max_length=255)),
                ('cold_cons', models.CharField(max_length=255)),
                ('eer', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'cs_eer_analyses',
            },
        ),
        migrations.CreateModel(
            name='EcMeter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('idx', models.IntegerField()),
                ('identifier', models.CharField(max_length=255)),
                ('enabled', models.BooleanField(default=False)),
                ('ec_type', models.IntegerField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'ec_meters',
            },
        ),
        migrations.CreateModel(
            name='EcSource',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('value', models.CharField(max_length=255)),
                ('value_type', models.IntegerField()),
                ('seq', models.IntegerField()),
                ('ec_type', models.IntegerField()),
                ('cold_source', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='coldsource.coldsource')),
            ],
            options={
                'db_table': 'ec_sources',
            },
        ),
    ]
