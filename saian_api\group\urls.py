from django.urls import path

from .views import GroupModelViewSet, GroupActionViewSet, ActionTimerViewSet, ExecuteActionViewSet, ShortcutViewSet, ExecuteShortcutViewSet, \
    ActionLogsViewSet, AcStrategiesViewSet, GroupDeviceView, ActionAttributes

group_list = GroupModelViewSet.as_view({
    'get': 'list',
    'post': 'create'
})

group_detail = GroupModelViewSet.as_view({
    'get': 'retrieve',
    'put': 'partial_update',
    'delete': 'destroy'
})

ga_list = GroupActionViewSet.as_view({
    'get': 'list',
    'post': 'create'
})

ga_detail = GroupActionViewSet.as_view({
    'get': 'retrieve',
    'put': 'partial_update',
    'delete': 'destroy'
})

at_list = ActionTimerViewSet.as_view({
    'get': 'list',
    'post': 'create'
})

at_detail = ActionTimerViewSet.as_view({
    'get': 'retrieve',
    'put': 'partial_update',
    'delete': 'destroy'
})

execute_action = ExecuteActionViewSet.as_view({
    'post': 'create'
})

shortcuts_list = ShortcutViewSet.as_view({
    'get': 'list'
})

shortcuts_detail = ShortcutViewSet.as_view({
    'get': 'retrieve',
    'delete': 'destroy'
})

execute_shortcut = ExecuteShortcutViewSet.as_view({
    'post': 'create'
})

action_logs = ActionLogsViewSet.as_view({
    'get': 'list',
})
action_logs_detail = ActionLogsViewSet.as_view({
    'get': 'retrieve',
})

strategy_list = AcStrategiesViewSet.as_view({
    'get': 'list',
    'post': 'create'
})

strategy_detail = AcStrategiesViewSet.as_view({
    'get': 'retrieve',
    'put': 'partial_update',
    'delete': 'destroy'
})

group_devices = GroupDeviceView.as_view({
    'post': 'create',
    'delete': 'destroy'
})

action_attributes = ActionAttributes.as_view({
    'post': 'create',
    'delete': 'destroy'
})

urlpatterns = [
    path('saianapi/v1/groups', group_list, name='group-list'),
    path('saianapi/v1/groups/<int:pk>', group_detail, name='group-detail'),
    path('saianapi/v1/group_actions', ga_list, name='ga-list'),
    path('saianapi/v1/group_actions/<int:pk>', ga_detail, name='ga-detail'),

    path('saianapi/v1/action_timers', at_list, name='at-list'),
    path('saianapi/v1/group_actions/<int:ga_id>/action_timers', at_list, name='gat-list'),
    path('saianapi/v1/group_actions/<int:ga_id>/action_timers/<int:pk>', at_detail, name='gat-list'),
    path('saianapi/v1/action_timers/<int:pk>', at_detail, name='at-detail'),

    path('saianapi/v1/execute_actions', execute_action, name='execute-action'),

    path('saianapi/v5/shortcuts', shortcuts_list, name='shortcuts-list'),
    path('saianapi/v5/shortcuts/<int:pk>', shortcuts_detail, name='shortcuts-detail'),
    path('saianapi/v5/execute_shortcuts', execute_shortcut, name='execute-shortcut'),
    path('saianapi/v5/action_logs', action_logs, name='action-logs'),
    path('saianapi/v5/action_logs/<int:pk>', action_logs_detail, name='action-logs-detail'),

    path('saianapi/v5/strategies', strategy_list, name='strategy-list'),
    path('saianapi/v5/strategies/<int:pk>', strategy_detail, name='strategy-detail'),

    path('saianapi/v5/group_devices', group_devices, name='group-devices'),
    path('saianapi/v5/action_attributes', action_attributes, name='action-attributes'),
]
