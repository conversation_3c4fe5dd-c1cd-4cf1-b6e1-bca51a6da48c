import datetime
import json
import logging
import re
from itertools import chain
from json import JSONDecodeError

import requests
from django.contrib.contenttypes.models import ContentType
from django.db import transaction
from django.db.models import Q
from django.http import Http404
from django.shortcuts import get_object_or_404
from rest_framework import serializers
from rest_framework import status
from rest_framework import viewsets, exceptions
from rest_framework.decorators import action
from rest_framework.response import Response

from saian_api.devdefine.models import (
    AttributePrototype,
    AttributeType,
    DevicePrototype,
    DeviceType, DeviceProtocol
)
from saian_api.project.models import Project
from saian_api.user.models import UserDevice, UserSearches, WebUser
from saian_api.utils.sy_jsonrenderer import SyJSONRender
from saian_api.utils.tools import validate_value, to_int, getCommandMeaning, is_number
from .models import (
    Device,
    <PERSON><PERSON><PERSON><PERSON>ribute,
    <PERSON><PERSON><PERSON><PERSON>r,
    <PERSON><PERSON>CtrlLog,
    <PERSON>ceEvent,
    LivingDetection, RoomDevice, DeviceLimit, DeviceRuntime, DeviceEventHistory, DataRoute, DyAttribute, SySim, DaSnapshot, DaSnapshotHistory,
    ParamRecord
)
from .serializers import (
    DeviceSerializer,
    DeviceDetailSerializer,
    DeviceValuesSerializer,
    DeviceTimerSerializer,
    DeviceCtrlLogSerializer,
    DeviceEventSerializer,
    WebDeviceSerializer,
    DeviceAttributeSerializer,
    DeviceListV2Serializer, DeviceLimitSerializer, DeviceRuntimeSerializer, DataRouteSerializer, SySimSerializer
)
# Create your views here.
from saian_api.group.models import AcStrategies, GroupDevice, ActionTimer, Group, GroupAction, ActionTimerAttribute, ActionAttribute
from ..building.models import ActiveRoom, Floor
from ..coldsource.serializers import LivingDetectionSerializer
from ..dimension.models import (Dimension, DimensionTerminal, DimensionUser, DimensionAttribute, DimensionHourlyStat, DimensionDailyStat,
                                DimensionMonthlyStat)
from ..linkage.models import LinkageRule, LinkageTarget, LinkageTrigger
from ..report.models import ReportConfigurer, DeviceHourlyStat, DeviceDailyStat, DeviceMonthlyStat
from ..report.serializers import SimpleDeviceSerializer
from ..scheduledtask.utils import set_global_db
from ..terminal.models import Terminal, TerminalAttribute
from ..utils.db.Convert import Convert
from ..utils.db.Redis import RedisHelper
from ..utils.httpapi import DOMAIN
from ..utils.httpapi.image import ImageAPI
from ..utils.intapi_auth import IntapiAuth
from ..utils.inthttpapi.base import BaseAPI
from ..utils.inthttpapi.device import AdminDeviceApi
from ..utils.legacy_auth import LegacyAuth
from ..utils.standard_pagination import StandardResultsSetPagination
from ..utils.utils import AuthUtils, ExcelUtils


class DeviceViewSet(viewsets.ModelViewSet):
    renderer_classes = (SyJSONRender,)
    serializer_class = DeviceSerializer

    def get_queryset(self):
        queryset = Device.objects.filter(project_id=self.request.user['project_id'])

        type = self.request.query_params.get('type', None)
        subtype = self.request.query_params.get('subtype', None)
        status = self.request.query_params.get('status', None)
        search = self.request.query_params.get('search', None)

        types = None
        if type is not None:
            types = DeviceType.objects.filter(parent_id=type)

        if subtype is not None:
            types = DeviceType.objects.filter(id=subtype)

        if types is not None:
            queryset = queryset.filter(device_type_id__in=types)

        if status is not None:
            queryset = queryset.filter(status=status)
        else:
            queryset = queryset.filter(status__in=[10, 20, 30, 40])

        if search is not None:
            queryset = queryset.filter(nick_name__contains=search)

        queryset = queryset.order_by(Convert('nick_name', 'gbk'))

        return queryset

    def get_object(self) -> Device:
        obj = Device.objects.filter(wudid=self.kwargs['str'])
        if obj.count() == 0 and (self.kwargs['str']).isdigit():
            obj = Device.objects.filter(pk=self.kwargs['str'])
        if obj.count() == 0:
            obj = Device.objects.filter(mac=self.kwargs['str'])
        return obj.first()

    def get_admin_object(self, pk) -> Device | None:
        device = None
        # if pk.isdigit():
        #     device = Device.objects.using('syadmindb').filter(pk=pk).last()
        # if device is None:
        #     device = Device.objects.using('syadmindb').filter(mac=pk).last()
        # if device is None:
        #     device = Device.objects.using('syadmindb').filter(wudid=pk).last()
        r = AdminDeviceApi.get_device(pk)
        if r.status_code == status.HTTP_200_OK:
            data = r.json().get('row')
            # 从管理后台返回的数据中提取项目名称
            project_name = data.pop('project_name', None)
            # 过滤不在 Device 类声明的字段
            device_fields = {f.name if not f.is_relation else f.attname for f in Device._meta.fields}
            filtered_data = {k: v for k, v in data.items() if k in device_fields}
            device = Device(**filtered_data)
            # 把项目名称附加到device对象中
            setattr(device, 'project_name', project_name)
        else:
            logging.error(f'从Admin3查询设备失败，status_code: {r.status_code}, text: {r.text}')

        return device

    # 创建前添加项目id
    # def perform_create(self, serializer):
    #     serializer.save(project_id=self.request.user['project_id'])

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()

        display_list = request.query_params.get('list', None)
        type = self.request.query_params.get('type', None)
        subtype = self.request.query_params.get('subtype', None)

        device_types = None
        if type is not None:
            device_types = DeviceType.objects.filter(parent_id=type)

        if subtype is not None:
            device_types = DeviceType.objects.filter(id=subtype)

        data = []

        if device_types is not None:
            tmp_fields = None
            for device_type in device_types:
                total = Device.objects.filter(
                    device_type_id=device_type.id,
                    project_id=request.user['project_id']
                ).count()

                total_normal = Device.objects.filter(
                    device_type_id=device_type.id,
                    online=True,
                    in_fault=False,
                    in_alarm=False,
                    project_id=request.user['project_id']
                ).count()

                total_fault = Device.objects.filter(
                    device_type_id=device_type.id,
                    online=True,
                    in_fault=True,
                    project_id=request.user['project_id']
                ).count()

                total_offline = Device.objects.filter(
                    device_type_id=device_type.id,
                    online=False,
                    project_id=request.user['project_id']
                ).count()

                total_run = Device.objects.filter(
                    device_type_id=device_type.id,
                    online=True,
                    sw_on=True,
                    project_id=request.user['project_id']
                ).count()

                total_stop = Device.objects.filter(
                    device_type_id=device_type.id,
                    online=True,
                    sw_on=False,
                    project_id=request.user['project_id']
                ).count()

                total_alarm = Device.objects.filter(
                    device_type_id=device_type.id,
                    online=True,
                    in_alarm=True,
                    project_id=request.user['project_id']
                ).count()

                tmp_fields = {
                    'id': device_type.id,
                    'name': device_type.name,
                    'uni_name': device_type.uni_name,
                    'statistic': {
                        'total': total,
                        'total_normal': total_normal,
                        'total_fault': total_fault,
                        'total_offline': total_offline,
                        'total_run': total_run,
                        'total_stop': total_stop,
                        'total_alarm': total_alarm
                    }
                }

                devices = self.paginate_queryset(self.get_queryset().order_by('nick_name'))

                devices_fields = []
                if display_list is None or display_list == '1':
                    device_fields = None
                    for device in devices:
                        device_prototype = DevicePrototype.objects.get(pk=device.device_prototype_id)
                        device_fields = {
                            'id': device.id,
                            'nick_name': device.nick_name,
                            'status': device.status,
                            'sw_on': device.sw_on,
                            'in_fault': device.in_fault,
                            'online': device.online,
                            'mac': device.mac,
                            'in_alarm': device.in_alarm,
                            'created_at': device.created_at,
                            'updated_at': device.updated_at,
                            'device_prototype': {
                                'id': device_prototype.id,
                                'name': device_prototype.name,
                                'uni_name': device_prototype.uni_name
                            },
                            'icon': device_type.icon
                        }
                        terminal = Terminal.objects.filter(device=device, prefix=None, idx=None).last()
                        if device_prototype.content is None or '冷源' in device_type.uni_name:
                            if terminal is None:
                                device_fields['key_attributes'] = device.key_attributes()
                            else:
                                device_fields['key_attributes'] = terminal.key_attributes(device.project_id)

                        device_fields['today_power_cons'] = device.today_power_cons()
                        device_fields['today_cool_cons'] = device.today_cool_cons()
                        device_fields['custz_detail'] = True if device_prototype.content is not None else False
                        device_fields['dashboard_attres'] = device.dashboard_attres()

                        device_fields['et_types'] = device.et_types()
                        if device_fields is not None:
                            devices_fields.append(device_fields)
                    if devices_fields is not None:
                        tmp_fields['devices'] = devices_fields

                data.append(tmp_fields)

        else:
            raise exceptions.ValidationError('Type or subtype is required!')

        res_data = {
            "count": queryset.count(),
            'device_types': data,
            'per_page': 200
        }

        return Response(res_data)

    def create(self, request, *args, **kwargs):
        # device = super(DeviceViewSet, self).create(request, *args, **kwargs).data
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        device = Device.objects.create_single(request, serializer)
        res_data = {
            'device': device
        }

        return Response(res_data)

    def retrieve(self, request, *args, **kwargs):
        # self.serializer_class = DeviceDetailSerializer
        device = self.get_object()
        if device is None:
            pk = self.kwargs.get('str')
            device = self.get_admin_object(pk)
        else:
            project_name = Project.objects.only('name').get(pk=device.project_id).name
            setattr(device, 'project_name', project_name)

        if device is None:
            return Response(data={'status': status.HTTP_404_NOT_FOUND, 'data': None})

        serializer = DeviceDetailSerializer(device, context={'request': request})

        # device = super(DeviceViewSet, self).retrieve(request, *args, **kwargs).data
        res_data = {
            'device': serializer.data
        }

        return Response(res_data)

    def partial_update(self, request, *args, **kwargs):
        # device = super().partial_update(request, *args, **kwargs).data
        identifier = request.data.get('identifier', None)
        value = request.data.get('identifier_value', None)
        address = request.data.get('address', None)
        nick_name = request.data.get('nick_name', None)
        project_id = request.data.get('project_id', None)
        wudid = request.data.get('wudid', None)
        remark = request.data.get('remark', None)
        device = self.get_object()

        device_id = self.kwargs['str']
        # 找不到设备，则从 admin3 查找
        if device is None:
            device = self.get_admin_object(device_id)

        if identifier is None or value is None:
            if address is not None or nick_name is not None or project_id is not None or wudid is not None or remark is not None:
                if device.in_acc is None:
                    device.in_acc = 0
                if device.needs_m is None:
                    device.needs_m = 0
                if device.live_update is None:
                    device.live_update = 0
                if project_id is not None:
                    device.project_id = project_id
                    device_type = DeviceType.objects.get(pk=device.device_type_id)
                    # 设备分类是计量或环境，默认开关状态为True
                    if device_type.parent_id in [3, 4]:
                        device.sw_on = True
                    device.save()
                    Terminal.objects.create_by_device(device)
                    TerminalAttribute.objects.create_by_device(device)

                    # 同步设备的项目信息到管理后台
                    data = {
                        'project': device.project_id,
                        'nick_name': device.nick_name,
                        'address': device.address,
                        'remark': device.remark,
                    }
                    r = AdminDeviceApi.update_device(device.id, data)
                    if r.status_code != 200:
                        logging.error(f'设备绑定项目，同步到管理后台出错。device: {device.id}-{device.nick_name}, project: {device.project_id}')

                    # 更新终端的可见状态
                    for terminal in Terminal.objects.filter(device=device, show_en=False):
                        # 隐藏冷源设备的非电表子终端
                        if terminal.terminal_type == 40 and terminal.prefix and terminal.prefix != 'Meter':
                            terminal.show_en = False
                        else:
                            terminal.show_en = True
                        terminal.save()

                if address is not None:
                    device.address = address
                    device.save(update_fields=['address'])
                if remark is not None:
                    device.remark = remark
                    device.save(update_fields=['remark'])
                if nick_name is not None:
                    device.nick_name = nick_name
                    terminal = Terminal.objects.filter(device_id=device.id, idx__isnull=True, prefix__isnull=True).last()
                    if terminal is not None:
                        terminal.nick_name = nick_name
                        terminal.save(update_fields=['nick_name'])
                    device.save(update_fields=['nick_name'])
                if wudid is not None:
                    if not wudid or len(wudid) != 8:
                        return Response({
                            'status': status.HTTP_400_BAD_REQUEST,
                            'data': None,
                            'message': '无效 wudid！'
                        }, status.HTTP_400_BAD_REQUEST)
                    if not device.wudid == wudid:
                        r = AdminDeviceApi.get_device(wudid)
                        if r.status_code == status.HTTP_200_OK:
                            logging.error(f'查询到设备，wudid 已存在！wudid-{wudid}, data: {r.json()}')
                            return Response({
                                'status': status.HTTP_409_CONFLICT,
                                'data': None,
                                'message': 'wudid 已存在！'
                            }, status.HTTP_409_CONFLICT)
                        elif r.status_code == status.HTTP_404_NOT_FOUND:
                            device.wudid = wudid
                            device.save()
                        else:
                            logging.error(f'查询 wudid 失败!status-{r.status_code}, content-{r.content}')
                            return Response({
                                'status': status.HTTP_500_INTERNAL_SERVER_ERROR,
                                'data': None,
                                'message': '查询 wudid 失败！请稍后再试。'
                            })

                data = {
                    'address': device.address,
                    'ali_device_name': device.ali_device_name,
                    'ali_region': device.ali_region,
                    'ali_secret': device.ali_secret,
                    'did': device.did,
                    'in_acc': device.in_acc,
                    'nick_name': device.nick_name,
                    'platform': device.platform,
                    'prd_key': device.prd_key,
                    'project_id': device.project_id,
                    'remark': device.remark,
                    'wudid': device.wudid
                }
                r = AdminDeviceApi.update_device(device.id, data)
                if r.status_code != 200:
                    logging.warning(f'向 admin3 更新设备信息失败. msg: {r.text}')
                return Response(status=status.HTTP_200_OK, data=None)
            return Response(status=status.HTTP_400_BAD_REQUEST, data=None)

        try:
            ap = AttributePrototype.objects.get(device_prototype_id=device.device_prototype_id, identifier=identifier)
            # da = DeviceAttribute.objects.get(attribute_prototype_id=ap.id, device=device)
            da = DeviceAttribute.objects.get_by_ap(device, ap)
            # old_prefix = da.value if '_NickName' in identifier and da.value else ap.name.replace('昵称', "")
            da.value = value
            da.save()
            DeviceAttribute.objects.save_to_redis(device, da)
            # 如果是昵称，需要同时更新终端的昵称
            if '_NickName' in identifier:
                try:
                    # 修改昵称时同步修改对应ap的名字
                    # start = identifier.replace('_NickName', "")
                    # print(start, old_prefix, value)
                    # AttributePrototype.objects.update_name_by_nickname(start, old_prefix, value)

                    terminal = Terminal.objects.get(device=device, terminalattribute__identifier=identifier)
                    terminal.nick_name = value
                    terminal.save(update_fields=['nick_name'])
                    ta = TerminalAttribute.objects.get_by_idf(terminal, identifier)
                    if ta is not None:
                        ta.value = value
                        ta.save()
                        TerminalAttribute.objects.save_to_redis(device.project_id, ta)
                except Terminal.DoesNotExist:
                    pass
                except Terminal.MultipleObjectsReturned:
                    logging.error(f"更新子设备昵称时找到了多个对应的子设备终端。device:{device_id}-{device.nick_name}, identifier: {identifier}")

            # r = requests.put(url, headers=headers, data=request.data)
            # if r.status_code == 200:
            #     return Response({'status': 200, 'data': r.json().get('data')})
        except AttributePrototype.DoesNotExist:
            logging.error(f'ap不存在: dp_id={device.device_prototype_id}, dt_id={device.device_type_id}, identifier={identifier}')
            return Response(status=status.HTTP_404_NOT_FOUND, data=None)

        device_type = DeviceType.objects.get(pk=device.device_type_id)
        device_prototype = DevicePrototype.objects.get(pk=device.device_prototype_id)
        res_data = {
            'device': {
                'id': device.id,
                'nick_name': device.nick_name,
                'status': device.status,
                'in_alarm': device.in_alarm,
                'address': device.address,
                'remark': device.remark,
                'created_at': device.created_at,
                'updated_at': device.updated_at,
                'icon': device_type.icon,
                'custz_detail': True if device_prototype.content is not None else False,
                'device_type': {
                    'id': device_type.id,
                    'name': device_type.name,
                    'uni_name': device_type.uni_name
                },
                'device_prototype': {
                    'id': device_prototype.id,
                    'name': device_prototype.name,
                    'uni_name': device_prototype.uni_name
                }
            }
        }

        return Response(res_data)

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        if instance is not None:
            self.perform_destroy(instance)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

class DeviceValuesViewSet(viewsets.GenericViewSet):
    def get_queryset(self):
        mac = self.request.query_params.get('mac', None)
        ap_id = self.request.query_params.get('apid', None)

        if mac is None:
            raise exceptions.ValidationError(detail={'detail': 'mac is required!'})

        if ap_id is None:
            raise exceptions.ValidationError(detail={'detail': 'Apid is required!'})

        device = Device.objects.get(mac=mac)

        queryset = DeviceAttribute.objects.filter(device=device, attribute_prototype_id=ap_id)

        if queryset.count() == 0:
            raise exceptions.NotFound(detail={'detail': 'Device attribute not found!'})

        return queryset

    def list(self, request):
        self.renderer_classes = (SyJSONRender,)

        da = self.get_queryset().first()

        res_data = {
            'attribute': {
                'value': da.value
            }
        }

        return Response(res_data)

    def create(self, request):
        serializer = DeviceValuesSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        value_items = serializer.validated_data['values']
        project_id = request.user.get('project_id')

        with transaction.atomic(using=f'prj{project_id}db'):
            for item in value_items:
                device = Device.objects.get(pk=item['device_id'])
                attr = AttributePrototype.objects.get(device_prototype_id=device.device_prototype_id, identifier=item['identifier'])
                if not validate_value(attr, item['value']):
                    raise exceptions.ValidationError(detail={'detail': 'Attribute %s value invalid!' % item['identifier']})

                # da = DeviceAttribute.objects.get(device=device, attribute_prototype_id=attr.id)
                da = DeviceAttribute.objects.get_by_ap(device, attr)
                if da is not None:
                    da.value = item['value']
                    da.save()
                    DeviceAttribute.objects.save_to_redis(device, da)

        res_data = {
            'status': status.HTTP_200_OK,
            'data': None
        }

        return Response(res_data)


class DeviceAttributeViewV5(viewsets.ModelViewSet):
    @action(detail=True, methods=['get'])
    def get_authenticators(self):
        if 'intapi' in self.request.path:
            return [IntapiAuth(), ]
        return [LegacyAuth(), ]

    @classmethod
    def query_snaps(cls, view, da, from_at=None, till_at=None, page=None, per_page=None, interval=0, fill_gap=None):
        # device = da.device
        # attribute_prototype = da.attribute_prototype
        # DeviceEvent 保留1日内数据

        if isinstance(da, TerminalAttribute):
            attribute_prototype = AttributePrototype.objects.get(pk=da.attribute_prototype_id)
            device_id = da.terminal.device_id
            report_label = attribute_prototype.label
        else:
            attribute_prototype = da.attribute_prototype
            report_label = attribute_prototype.label
            device = da.device
            device_id = device.id
            rc = ReportConfigurer.objects.filter(target_id=device.device_prototype_id,
                                                 target_type=ContentType.objects.get_for_model(DevicePrototype),
                                                 identifier=attribute_prototype.identifier).last()
            if rc is not None:
                report_label = ReportConfigurer.objects.get_report_label(device, rc, report_label)

        recent_queryset = DaSnapshot.objects.filter(device_id=device_id, attribute_prototype_id=attribute_prototype.id).order_by('snapped_at')

        histories_queryset = None
        before_snap = None

        now = datetime.datetime.now()

        if from_at is not None:
            if not isinstance(from_at, datetime.datetime):
                from_at = datetime.datetime.strptime(from_at, '%Y%m%d%H%M%S')
            recent_queryset = recent_queryset.filter(snapped_at__gt=from_at)
            if (now.date() - from_at.date()).days > 1:
                histories_queryset = DaSnapshotHistory.objects.filter(device_id=device_id,
                                                                      attribute_prototype_id=attribute_prototype.id,
                                                                      snapped_at__gt=from_at).order_by('snapped_at')
                before_snap = DaSnapshotHistory.objects.filter(device_id=device_id, attribute_prototype_id=attribute_prototype.id,
                                                               snapped_at__lte=from_at).order_by('-snapped_at').first()
            else:
                before_snap = DaSnapshot.objects.filter(device_id=device_id, attribute_prototype_id=attribute_prototype.id,
                                                        snapped_at__lte=from_at).order_by('-snapped_at').first()

        if till_at is not None:
            if not isinstance(till_at, datetime.datetime):
                till_at = datetime.datetime.strptime(till_at, '%Y%m%d%H%M%S')
            recent_queryset = recent_queryset.filter(snapped_at__lte=till_at)
            if histories_queryset is not None:
                histories_queryset = histories_queryset.filter(snapped_at__lte=till_at)

        if histories_queryset is None:
            result_set = recent_queryset
            count = recent_queryset.count()
            if view is not None and (page is not None or per_page is not None):
                result_set = cls.paginate_queryset(view, result_set)
        else:
            count = recent_queryset.count() + histories_queryset.count()
            if view is not None and (page is not None and per_page is not None):
                page = int(page)
                per_page = int(per_page)
                result_set = cls.paginate_queryset(view, histories_queryset)
                histories_page = int(histories_queryset.count() / per_page)
                if len(result_set) < per_page and recent_queryset.count():
                    remain = page * per_page - histories_queryset.count()
                    offset = (per_page - histories_queryset.count() % per_page) + (page - histories_page - 2) * per_page
                    result_set.extend(recent_queryset[offset if offset > 0 else 0: remain])
            else:
                result_set = chain(histories_queryset, recent_queryset)

        values = []
        if before_snap is not None:
            values.append({'value': before_snap.value, 'time': from_at})

        # 导出时间隔 interval 分钟
        threshold = from_at
        for event in result_set:
            if interval and threshold and event.snapped_at < threshold:
                continue
            threshold = event.snapped_at + datetime.timedelta(minutes=int(interval))
            value = event.value

            values.append({'value': value, 'time': event.snapped_at})

        # 是否填充缺失的分钟数据
        fill_gap = fill_gap in ['1', 'y', 'yes']

        fill_values = []
        if fill_gap and len(values):
            prev_value = None
            prev_time = None
            for snap in values:
                current_time = snap['time']
                current_value = snap['value']
                if current_time == prev_time:
                    fill_values[-1]['value'] = current_value
                    continue
                if prev_time is None:
                    # 第一个点，直接加入
                    fill_values.append(snap)
                else:
                    next_minute = prev_time + datetime.timedelta(minutes=1)
                    # 只填补 prev_time 到 current_time 之间的缺口（不包含 current_time）
                    while next_minute < current_time:
                        fill_values.append({'value': prev_value, 'time': next_minute})
                        next_minute += datetime.timedelta(minutes=1)
                    fill_values.append(snap)  # 原始点
                prev_time = current_time
                prev_value = current_value

        return {
            'id': attribute_prototype.id,
            'name': attribute_prototype.name,
            'label': report_label,
            'identifier': attribute_prototype.identifier,
            'unit': attribute_prototype.unit,
            'data_type': attribute_prototype.data_type,
            'options': attribute_prototype.options,
            'values': fill_values if (fill_gap and len(fill_values)) else values,
        }, count


    @classmethod
    def query_stats(cls, view, da, from_at=None, till_at=None, page=None, per_page=None, interval=0):
        # device = da.device
        # attribute_prototype = da.attribute_prototype
        # DeviceEvent 保留1日内数据

        if isinstance(da, TerminalAttribute):
            attribute_prototype = AttributePrototype.objects.get(pk=da.attribute_prototype_id)
            device_id = da.terminal.device_id
            report_label = attribute_prototype.label
        else:
            attribute_prototype = da.attribute_prototype
            report_label = attribute_prototype.label
            device = da.device
            device_id = device.id
            rc = ReportConfigurer.objects.filter(target_id=device.device_prototype_id,
                                                 target_type=ContentType.objects.get_for_model(DevicePrototype),
                                                 identifier=attribute_prototype.identifier).last()
            if rc is not None:
                report_label = ReportConfigurer.objects.get_report_label(device, rc, report_label)

        # 在事件表中查找的目标 idf
        target_identifier = attribute_prototype.identifier

        device_protocol = DeviceProtocol.objects.filter(sp_defs__contains=f'"{attribute_prototype.identifier}"',
                                                        device_prototype_id=attribute_prototype.device_prototype_id).last()

        # 如果目标参数配置在 device_protocol 中，目标 idf 就是 device_protocol.identifier
        if device_protocol is not None:
            target_identifier = device_protocol.identifier

        recent_queryset = DeviceEvent.objects.filter(Q(data__icontains=f"'{target_identifier}'") |
                                                     Q(data__icontains=f'"{target_identifier}"'),
                                                     device_id=device_id).order_by('created_at')

        histories_queryset = None

        now = datetime.datetime.now()

        if from_at is not None:
            if not isinstance(from_at, datetime.datetime):
                from_at = datetime.datetime.strptime(from_at, '%Y%m%d%H%M%S')
            recent_queryset = recent_queryset.filter(created_at__gte=from_at)
            if (now.date() - from_at.date()).days > 1:
                histories_queryset = DeviceEventHistory.objects.filter(Q(data__icontains=f"'{target_identifier}'") |
                                                                       Q(data__icontains=f'"{target_identifier}"'),
                                                                       created_at__gte=from_at, device_id=device_id, ).order_by('created_at')
        if till_at is not None:
            if not isinstance(till_at, datetime.datetime):
                till_at = datetime.datetime.strptime(till_at, '%Y%m%d%H%M%S')
            recent_queryset = recent_queryset.filter(created_at__lte=till_at)
            if histories_queryset is not None:
                histories_queryset = histories_queryset.filter(created_at__lte=till_at)

        if histories_queryset is None:
            result_set = recent_queryset
            count = recent_queryset.count()
            if view is not None and (page is not None or per_page is not None):
                result_set = cls.paginate_queryset(view, result_set)
        else:
            count = recent_queryset.count() + histories_queryset.count()
            if view is not None and (page is not None and per_page is not None):
                page = int(page)
                per_page = int(per_page)
                result_set = cls.paginate_queryset(view, histories_queryset)
                histories_page = int(histories_queryset.count() / per_page)
                if len(result_set) < per_page and recent_queryset.count():
                    remain = page * per_page - histories_queryset.count()
                    offset = (per_page - histories_queryset.count() % per_page) + (page - histories_page - 2) * per_page
                    result_set.extend(recent_queryset[offset if offset > 0 else 0: remain])
            else:
                result_set = chain(histories_queryset, recent_queryset)

        values = []
        # 导出时间隔 interval 分钟
        threshold = from_at
        for event in result_set:
            if interval and threshold and event.created_at < threshold:
                continue
            threshold = event.created_at + datetime.timedelta(minutes=int(interval))
            event_data = event.data if (isinstance(event, DeviceEvent) or isinstance(event, DeviceEventHistory)) else event.get('data')
            try:
                data = json.loads(event_data.replace("'", '"').replace(" True", " true").replace(" False", " false"))
                value = data.get(target_identifier, None)

            except JSONDecodeError:
                data = json.loads(event_data.replace('"', '\\"').replace("'", '"').replace(" True", " true").replace(" False", " false"))
                value = data.get(target_identifier, None)

            # 处理 protocol 的值
            if value is not None and device_protocol is not None and device_protocol.sp_defs:
                idx = None
                defs = json.loads(device_protocol.sp_defs)
                for config in defs:
                    if config.get('idf', None) == attribute_prototype.identifier:
                        idx = config.get('idx', None)
                        break

                if idx is not None:
                    value = value[int(idx)]

            values.append({'value': value, 'time': event.created_at})

        return {
            'id': attribute_prototype.id,
            'name': attribute_prototype.name,
            'label': report_label,
            'identifier': attribute_prototype.identifier,
            'unit': attribute_prototype.unit,
            'data_type': attribute_prototype.data_type,
            'options': attribute_prototype.options,
            'values': values,
        }, count

    def retrieve(self, request, *args, **kwargs):
        da = DeviceAttribute.objects.get(pk=kwargs.get('pk'))
        device = da.device

        from_at = request.query_params.get('from', None)
        till_at = request.query_params.get('till', None)
        page = request.query_params.get('page', None)
        per_page = request.query_params.get('per_page', None)
        interval = request.query_params.get('interval', 0)
        fill_gap = request.query_params.get('fill_gap', None)
        snap = request.query_params.get('snap', None)
        if snap:
            series, count = self.query_snaps(self, da, from_at, till_at, page, per_page, interval, fill_gap)
        else:
            series, count = self.query_stats(self, da, from_at, till_at, page, per_page, interval)

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                "device_id": device.id,
                'nick_name': device.nick_name,
                'series': series
            },
            'total': count
        })

    def create(self, request, *args, **kwargs):

        da_ids = request.data.get('da_ids', None)
        ta_ids = request.data.get('ta_ids', None)

        if da_ids is None and ta_ids is None:
            raise exceptions.ValidationError(detail={'detail': 'ta_ids or da_ids is required!'})

        from_at = request.data.get('from', None)
        till_at = request.data.get('till', None)
        page = request.data.get('page', None)
        per_page = request.data.get('per_page', None)
        interval = request.data.get('interval', 0)
        snap = request.data.get('snap', None)

        if da_ids is not None:
            ids = da_ids.split(',')
            das = list(DeviceAttribute.objects.filter(id__in=ids))
        else:
            ids = ta_ids.split(',')
            das = list(TerminalAttribute.objects.filter(id__in=ids))

        result = []

        # 根据id的顺序排序
        try:
            sorted_das = sorted(das, key=lambda obj: ids.index(f'{obj.id}'))
        except ValueError as e:
            logging.error(f'设备参数原始值导出，排序出错。Error: {e.__str__()}')
            sorted_das = das

        for da in sorted_das:
            if snap:
                series, _ = self.query_snaps(self, da, from_at, till_at, page, per_page, interval)
            else:
                series, _ = self.query_stats(self, da, from_at, till_at, page, per_page, interval)
            result.append(series)

        path = ''
        filename = ''
        if len(result):
            path, filename = ExcelUtils.generate_work_book(result, filename)

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'file_url': path,
                'filename': filename
            }
        })

    def list(self, request, *args, **kwargs):
        device_id = request.query_params.get('device_id', None)
        if device_id is None:
            raise exceptions.ValidationError(detail={'detail': 'device_id is required!'})

        device = Device.objects.get(pk=device_id)
        dp_id = device.device_prototype_id
        aps_queryset = AttributePrototype.objects.filter(device_prototype_id=dp_id, do_export=True)

        at_id = request.query_params.get('attribute_type_id', None)
        if at_id is not None:
            aps_queryset = aps_queryset.filter(attribute_type=at_id)

        identifier = request.query_params.get('identifier', None)
        if identifier is not None:
            aps_queryset = aps_queryset.filter(identifier__icontains=identifier)

        search = request.query_params.get('search', None)
        if search is not None:
            aps_queryset = aps_queryset.filter(name__icontains=search)

        count = aps_queryset.count()

        per_page = request.query_params.get('per_page', None)
        if per_page is not None:
            aps_queryset = self.paginate_queryset(aps_queryset)

        aps_queryset = list(aps_queryset)
        aps = {}
        for ap in aps_queryset:
            aps[ap.id] = ap

        ap_ids = list(aps.keys())

        # das = DeviceAttribute.objects.filter(device_id=device_id, attribute_prototype_id__in=ap_ids)
        das = DeviceAttribute.objects.query_object_list(device, ap_ids)

        result = []

        dp_model_type = ContentType.objects.get_for_model(DevicePrototype)

        for da in das:
            ap = aps.get(da.attribute_prototype_id)

            rc = ReportConfigurer.objects.filter(target_id=device.device_prototype_id, target_type=dp_model_type, identifier=ap.identifier).last()
            report_label = ap.name
            if rc is not None:
                report_label = ReportConfigurer.objects.get_report_label(device, rc, ap.label)

            result.append({
                'id': da.id,
                'name': report_label,
                'value': da.value,
                'device_id': da.device_id,
                'attribute_prototype': {
                    'id': ap.id,
                    'identifier': ap.identifier,
                    'name': ap.name,
                    'unit': ap.unit,
                    'data_type': ap.data_type,
                    'options': ap.options
                }
            })

        device_attributes = sorted(result, key=lambda x: x['name'])

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'device_attributes': device_attributes
            },
            'total': count
        })

class DeviceAttributeViewSet(viewsets.ModelViewSet):
    # renderer_classes = (SyJSONRender,)
    serializer_class = DeviceAttributeSerializer

    @action(detail=True, methods=['get'])
    def get_authenticators(self):
        if 'intapi' in self.request.path:
            return [IntapiAuth(), ]
        return [LegacyAuth(), ]

    def get_queryset(self):
        device_id = self.request.query_params.get('device_id', None)
        at_id = self.request.query_params.get('attribute_type_id', None)

        if at_id is None:
            at_id = self.kwargs['pk']

        if device_id is None or at_id is None:
            raise exceptions.ValidationError(
                detail={'detail': 'Device id and at id is required!'})

        ap_ids = AttributePrototype.objects.filter(attribute_type_id=at_id)

        if ap_ids.count() == 0:
            raise exceptions.APIException(detail={'detail': 'Attribute prototypes not exist!'})

        queryset = DeviceAttribute.objects.filter(
            attribute_prototype_id__in=ap_ids.values_list('id', flat=True),
            device_id=device_id
        )

        return queryset

    def list(self, request, *args, **kwargs):
        das = self.get_queryset()

        device_id = request.query_params.get('device_id', None)
        at_id = request.query_params.get('attribute_type_id', None)

        if at_id is None:
            at_id = self.kwargs['pk']

        device_type = AttributeType.objects.get(pk=at_id)
        device = Device.objects.get(pk=device_id)

        aps = AttributePrototype.objects.filter(attribute_type_id=at_id, device_prototype_id=device.device_prototype_id).order_by('seq')

        results = []

        for ap in aps:
            try:
                # da = DeviceAttribute.objects.get(device_id=device_id, attribute_prototype_id=ap.id)
                # da = DeviceAttribute.objects.get_by_ap(device, ap)
                da = DeviceAttribute.objects.query_object_by_ap(device, ap)
            # 新增了属性类型，但是还没有同步 da 时，则创建缺失的 da
            except DeviceAttribute.DoesNotExist:
                da = DeviceAttribute.objects.create(
                    attribute_prototype_id=ap.id,
                    value=ap.default_value,
                    show_in_list=False if ap.show_in_list is None else ap.show_in_list,
                    device_id=device_id
                )
            results.append({
                'id': da.id,
                'value': da.value,
                'created_at': da.created_at,
                'updated_at': da.updated_at,
                'name': ap.name,
                'icon': ap.icon,
                'unit': ap.unit,
                'identifier': ap.identifier,
                'data_type': ap.data_type,
                'pre_cision': ap.pre_cision,
                'in_crement': ap.in_crement,
                'min_value': ap.min_value,
                'max_value': ap.max_value,
                'options': ap.options,
                'default_value': ap.default_value,
                'read_only': ap.read_only,
                'attribute_prototype': {
                    'id': ap.id,
                    'identifier': ap.identifier,
                    'data_type': ap.data_type,
                    'pre_cision': ap.pre_cision,
                    'in_crement': ap.in_crement,
                    'min_value': ap.min_value,
                    'max_value': ap.max_value,
                    'options': ap.options,
                    'default_value': ap.default_value,
                    'unit': ap.unit,
                    'read_only': ap.read_only
                }
            })

        res_data = {
            'status': status.HTTP_200_OK,
            'data': {
                'id': device_type.id,
                'name': device_type.name,
                'device': {
                    'id': device.id,
                    'nick_name': device.nick_name
                },
                'device_prototype': {
                    'id': device.device_prototype.id,
                    'uni_name': device.device_prototype.uni_name
                },
                'device_attributes': results
            }
        }

        return Response(res_data)


class DeviceTimerViewSet(viewsets.ModelViewSet):
    serializer_class = DeviceTimerSerializer
    renderer_classes = (SyJSONRender,)

    def get_queryset(self):
        device_id = self.request.query_params.get('device_id', None)
        if device_id is None:
            raise serializers.ValidationError(detail={'detail': 'Device id is required!'})

        return DeviceTimer.objects.filter(device_id=device_id)

    def get_object(self, pk):
        return DeviceTimer.objects.get(pk=pk)

    def list(self, request, *args, **kwargs):
        timer_queryset = self.get_queryset()
        count = timer_queryset.count()
        timer_queryset = self.paginate_queryset(timer_queryset)

        timers = []
        for timer_obj in timer_queryset:
            timer = {
                'id': timer_obj.id,
                'name': timer_obj.name,
                'repeat': timer_obj.repeat,
                'run_date': timer_obj.run_date,
                'run_time': timer_obj.run_time,
                'wdays': timer_obj.wdays,
                'enabled': timer_obj.enabled,
                'is_finished': timer_obj.is_finished,
                'time_ranges': timer_obj.time_ranges if timer_obj.time_ranges else None
            }

            aps = []
            idfs = json.loads(timer_obj.identifiers)

            for ati in idfs:
                ap = AttributePrototype.objects.filter(
                    device_prototype_id=timer_obj.device.device_prototype_id,
                    identifier=ati['identifier']
                ).last()
                if ap is None:
                    continue
                ap_fields = {
                    'id': ap.id,
                    'name': ap.name,
                    'identifier': ap.identifier,
                    'data_type': ap.data_type,
                    'value': ati['value']
                }
                aps.append(ap_fields)

            timer['attribute_prototypes'] = aps
            timers.append(timer)

        res_data = {
            'timers': timers,
            'count': count
        }

        return Response(res_data)

    def retrieve(self, request, *args, **kwargs):
        timer = self.get_object(kwargs['pk'])

        timer_data = {
            'id': timer.id,
            'name': timer.name,
            'repeat': timer.repeat,
            'run_date': timer.run_date,
            'run_time': timer.run_time,
            'wdays': timer.wdays,
            'enabled': timer.enabled,
            'is_finished': timer.is_finished,
            'time_ranges': timer.time_ranges if timer.time_ranges else None
        }

        aps = []
        idfs = json.loads(timer.identifiers)

        for ati in idfs:
            try:
                ap = AttributePrototype.objects.get(device_prototype_id=timer.device.device_prototype_id, identifier=ati['identifier'])
                ap_fields = {
                    'id': ap.id,
                    'name': ap.name,
                    'identifier': ap.identifier,
                    'data_type': ap.data_type,
                    'value': ati['value'],
                    'unit': ap.unit,
                    'pre_cision': ap.pre_cision,
                    'in_crement': ap.in_crement,
                    'min_value': ap.min_value,
                    'max_value': ap.max_value,
                    'options': ap.options,
                    'default_value': ap.default_value
                }
                aps.append(ap_fields)
            except AttributePrototype.DoesNotExist:
                pass

        timer_data['attribute_prototypes'] = aps

        res_data = {
            'timer': timer_data
        }

        return Response(res_data)

    @classmethod
    def create_device_timer(cls, data, project_id):
        serializer = DeviceTimerSerializer(data=data)
        serializer.is_valid(raise_exception=True)

        with transaction.atomic(using=f'prj{project_id}db'):
            device_id = serializer.validated_data['device_id']
            name = serializer.validated_data['name']
            repeat = data.get('repeat', False)
            run_date = data.get('run_date', None)
            run_time = data.get('run_time', None)
            wdays = data.get('wdays', None)
            enabled = data.get('enabled', False)
            aps = serializer.validated_data['aps']
            time_ranges = data.get('time_ranges', None)

            timer = DeviceTimer.objects.create(
                device_id=device_id,
                name=name,
                repeat=repeat,
                run_date=run_date,
                run_time=run_time,
                wdays=wdays,
                enabled=enabled,
                is_finished=False,
                identifiers=json.dumps(aps),
                time_ranges=time_ranges
            )
            AcStrategies.objects.create(
                st_type=40,
                st_id=timer.id,
                status=20 if timer.is_finished else 10,
                created_at=timer.created_at,
                updated_at=timer.updated_at
            )

        return timer

    def create(self, request, *args, **kwargs):
        # serializer = DeviceTimerSerializer(data=request.data)
        # serializer.is_valid(raise_exception=True)
        #
        # device_id = serializer.validated_data['device_id']
        # name = serializer.validated_data['name']
        # repeat = request.data.get('repeat', False)
        # run_date = request.data.get('run_date', None)
        # run_time = request.data.get('run_time', None)
        # wdays = request.data.get('wdays', None)
        # enabled = request.data.get('enabled', False)
        # aps = serializer.validated_data['aps']
        #
        # timer = DeviceTimer.objects.create(
        #     device_id=device_id,
        #     name=name,
        #     repeat=repeat,
        #     run_date=run_date,
        #     run_time=run_time,
        #     wdays=wdays,
        #     enabled=enabled,
        #     is_finished=False,
        #     identifiers=json.dumps(aps)
        # )
        # AcStrategies.objects.create(
        #     st_type=40,
        #     st_id=timer.id,
        #     status=20 if timer.is_finished else 10,
        #     created_at=timer.created_at,
        #     updated_at=timer.updated_at
        # )
        timer = self.create_device_timer(request.data, request.user['project_id'])

        res_data = {
            'timer': {
                'id': timer.id,
                'name': timer.name,
                'repeat': timer.repeat,
                'run_date': timer.run_date,
                'run_time': timer.run_time,
                'wdays': timer.wdays,
                'enabled': timer.enabled,
                'is_finished': timer.is_finished,
                'time_ranges': timer.time_ranges if timer.time_ranges else None
            }
        }

        return Response(res_data)

    def partial_update(self, request, *args, **kwargs):
        timer = self.get_object(kwargs['pk'])
        serializer = DeviceTimerSerializer(timer, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)

        device_id = serializer.validated_data.get('device_id', None)
        name = serializer.validated_data.get('name', None)
        repeat = serializer.validated_data.get('repeat', None)
        run_date = serializer.validated_data.get('run_date', None)
        run_time = serializer.validated_data.get('run_time', None)
        wdays = serializer.validated_data.get('wdays', None)
        enabled = serializer.validated_data.get('enabled', None)
        aps = serializer.validated_data.get('aps', None)
        time_ranges = serializer.validated_data.get('time_ranges', None)

        if device_id is not None:
            timer.device_id = device_id

        if name is not None:
            timer.name = name

        if repeat is not None:
            timer.repeat = repeat

        if run_date is not None:
            timer.run_date = run_date
            if wdays is None:
                timer.wdays = None

        if run_time is not None:
            timer.run_time = run_time

        if wdays is not None:
            timer.wdays = wdays
            if run_date is None:
                timer.run_date = None

        if enabled is not None:
            timer.enabled = enabled

        if aps is not None:
            try:
                timer.identifiers = json.dumps(aps)
            except Exception:
                pass

        if 'time_ranges' in serializer.validated_data:
            timer.time_ranges = time_ranges

        timer.save()

        res_data = {
            'timer': {
                'id': timer.id,
                'name': timer.name,
                'repeat': timer.repeat,
                'run_date': timer.run_date,
                'run_time': timer.run_time,
                'wdays': timer.wdays,
                'enabled': timer.enabled,
                'is_finished': timer.is_finished,
                'time_ranges': timer.time_ranges if timer.time_ranges else None
            }
        }

        return Response(res_data)

    def destroy(self, request, *args, **kwargs):
        timer = self.get_object(kwargs['pk'])
        timer.delete()

        ac_strategies = AcStrategies.objects.filter(st_type=40, st_id=kwargs['pk'])
        ac_strategies.delete()

        res_data = {
            'timer': {
                'id': timer.id,
                'name': timer.name,
                'repeat': timer.repeat,
                'run_date': timer.run_date,
                'run_time': timer.run_time,
                'wdays': timer.wdays,
                'enabled': timer.enabled,
                'is_finished': timer.is_finished,
                'time_ranges': timer.time_ranges if timer.time_ranges else None
            }
        }

        return Response(res_data)


class DeviceCtrlLogViewSet(viewsets.ModelViewSet):
    serializer_class = DeviceCtrlLogSerializer
    renderer_classes = (SyJSONRender,)

    def get_queryset(self):
        queryset = DeviceCtrlLog.objects.filter(project_id=self.request.user['project_id'])
        web_user_id = self.request.user['id']
        device_id = self.request.query_params.get('did', None)
        mac = self.request.query_params.get('mac', None)
        from_at = self.request.query_params.get('from', None)
        till_at = self.request.query_params.get('till', None)
        is_succ = self.request.query_params.get('is_succ', None)
        exe_id = self.request.query_params.get('exe_id', None)
        exec = self.request.query_params.get('exec', None)
        action_log_id = self.request.query_params.get('action_log_id', None)

        if device_id is not None:
            queryset = queryset.filter(device_id=device_id)

        if mac is not None:
            queryset = queryset.filter(mac=mac)

        if from_at is not None and till_at is not None:
            from_dt = datetime.datetime.strptime(from_at, '%Y%m%d%H%M%S')
            till_dt = datetime.datetime.strptime(till_at, '%Y%m%d%H%M%S')
            queryset = queryset.filter(created_at__range=[from_dt, till_dt])

        if is_succ is not None:
            if int(is_succ) == 0:
                queryset = queryset.filter(errcode=0)
            else:
                queryset = queryset.filter(errcode__gt=0)

        if exe_id is not None:
            queryset = queryset.filter(executor_id=exe_id, executor_type='WebUser')

        if exec is not None and int(exec) == 1:
            queryset = queryset.filter(errcode=1, executor_id=web_user_id, created_at__gte=(datetime.datetime.now() - datetime.timedelta(hours=1)))

        if action_log_id is not None:
            queryset = queryset.filter(action_log_id=action_log_id)

        return queryset

    def list(self, request, *args, **kwargs):
        logs = super().list(request, *args, **kwargs).data

        device_cache = {}
        action_timer_cache = {}
        linkage_rule_cache = {}
        dp_cache = {}
        user_cache = {}
        for log in logs['results']:
            # device details
            device_id = log['device_id']
            device = device_cache.get(log['device_id'], None)
            if device is None:
                device = Device.objects.get(pk=device_id)
                device_cache[device_id] = device

            # device prototype details
            dp_id = device.device_prototype_id
            dp = dp_cache.get(dp_id, None)
            if dp is None:
                dp = DevicePrototype.objects.get(pk=dp_id)
                dp_cache[dp_id] = dp

            log['device'] = {
                'id': device.id,
                'nick_name': device.nick_name,
                'device_prototype_id': device.device_prototype_id,
                'custz_detail': bool(dp.content),
                'device_prototype': {
                    'id': dp.id,
                    'name': dp.name,
                    'uni_name': dp.uni_name,
                }

            }

            if log['retries'] is None:
                log['retries'] = 0

            # 下发方信息
            executor = log['executor']
            if log.get('executor_type', None) == 'WebUser':
                web_user_id = executor['id']
                user = user_cache.get(web_user_id, None)
                if user is None:
                    user = WebUser.objects.get_executor_info(request, web_user_id)
                    user_cache[web_user_id] = user
                log['executor'] = user
            elif executor.get('executor_type', None) == 'ActionTimer':
                try:
                    timer_id = executor.get('id')
                    timer = action_timer_cache.get(timer_id, None)
                    if timer is None:
                        timer = ActionTimer.objects.get(pk=timer_id)
                        action_timer_cache[timer_id] = timer
                    group_action = timer.group_action
                    group = group_action.group
                    executor['name'] = f'{group.name}-{group_action.name}'

                except ActionTimer.DoesNotExist:
                    pass
            elif executor.get('executor_type', None) == 'LinkageRule':
                try:
                    linkage_id = executor.get('id')
                    linkage = linkage_rule_cache.get(linkage_id, None)
                    if linkage is None:
                        linkage = LinkageRule.objects.get(pk=linkage_id)
                        linkage_rule_cache[linkage_id] = linkage
                    executor['name'] = f'{linkage.name}'

                except LinkageRule.DoesNotExist:
                    pass

            elif executor.get('executor_type', None) == 'Admin':
                executor['name'] = '管理后台'
            # else:
            #     log['executor'] = user_cache.get(log['executor_id'])

            log['meaning'] = getCommandMeaning(device, log['data'])

        res_data = {
            "count": logs['count'],
            'ctrl_logs': logs['results']
        }

        return Response(res_data)

    def retrieve(self, request, *args, **kwargs):
        log = super().retrieve(request, *args, **kwargs).data

        log['executor'] = WebUser.objects.get_executor_info(request, log['executor_id'])

        res_data = {
            'ctrl_log': log
        }

        return Response(res_data)
    
    def partial_update(self, request, *args, **kwargs):
        
        retries = request.data.get('retries', None)

        if retries is None:
            return Response({
                "status": status.HTTP_400_BAD_REQUEST,
                "error": "retries is required."
            })

        if retries != -1:
            return Response({
                "status": status.HTTP_400_BAD_REQUEST,
                "error": "retries is the specified value."
            })
        
        dcl = DeviceCtrlLog.objects.get(pk=kwargs.get('pk'))
        dcl.retries = -1
        dcl.save()

        return Response({
            "status": status.HTTP_200_OK,
            "data": None
        })

class DeviceDebugViewSet(viewsets.ViewSet):
    def list(self, request):
        device_id = request.query_params.get('device_id', None)
        if device_id is None:
            raise serializers.ValidationError(detail={'detail': 'Devie id is required!'})
        device = Device.objects.get(pk=device_id)
        debug_attrs = AttributePrototype.objects.filter(
            device_prototype_id=device.device_prototype_id,
            can_debug=True
        ).values_list('id', flat=True)

        device_attributes = []

        for da in DeviceAttribute.objects.query_object_list(device, debug_attrs):
            ap = AttributePrototype.objects.get(pk=da.attribute_prototype_id)
            da_fields = {
                'id': da.id,
                'value': da.value,
                'name': ap.name,
                'unit': ap.unit,
                'attribute_prototype': {
                    'id': ap.id,
                    'identifier': ap.identifier,
                    'data_type': ap.data_type,
                    'in_crement': ap.in_crement,
                    'min_value': ap.min_value,
                    'max_value': ap.max_value,
                    'options': ap.options,
                    'default_value': ap.default_value,
                    'icon': ap.icon,
                    'unit': ap.unit
                }
            }

            device_attributes.append(da_fields)

        res_data = {
            'status': status.HTTP_200_OK,
            "total": len(device_attributes),
            'data': {
                'device_attributes': device_attributes
            }
        }

        return Response(res_data)


class DeviceEventViewSet(viewsets.ModelViewSet):
    serializer_class = DeviceEventSerializer
    renderer_classes = (SyJSONRender,)

    def get_queryset(self):
        queryset = DeviceEvent.objects.filter(project_id=self.request.user['project_id'])

        search = self.request.query_params.get('search', None)
        mac = self.request.query_params.get('mac', None)
        dpid = self.request.query_params.get('dpid', None)
        did = self.request.query_params.get('did', None)
        from_at = self.request.query_params.get('from', None)
        till_at = self.request.query_params.get('till', None)

        if search is not None:
            queryset = queryset.filter(nick_name__contains=search)

        if mac is not None:
            queryset = queryset.filter(mac=mac)

        if dpid is not None:
            queryset = queryset.filter(device_prototype_id=dpid)

        if did is not None:
            queryset = queryset.filter(device_id=did)

        if from_at is not None and till_at is not None:
            from_dt = datetime.datetime.strptime(from_at, '%Y%m%d%H%M%S')
            till_dt = datetime.datetime.strptime(till_at, '%Y%m%d%H%M%S')
            queryset = queryset.filter(created_at__range=[from_dt, till_dt])

        return queryset

    def list(self, request, *args, **kwargs):
        events = super().list(request, *args, **kwargs).data

        res_data = {
            "count": events['count'],
            'data': {
                'device_events': events['results']
            }
        }

        return Response(res_data)

    def retrieve(self, request, *args, **kwargs):
        event = super().retrieve(request, *args, **kwargs).data

        res_data = {
            'device_event': event
        }

        return Response(res_data)


class DeviceLiveStatViewSet(viewsets.ViewSet):
    def list(self, request):
        project = Project.objects.get(pk=request.user['project_id'])

        res_data = {
            'status': status.HTTP_200_OK,
            'data': {
                'total_online': project.total_online(),
                'total_offline': project.total_offline(),
                'data': project.device_stat_data()
            }
        }

        return Response(res_data)


class WebDeviceViewSet(viewsets.ViewSet, StandardResultsSetPagination):
    def list(self, request):
        project = Project.objects.get(pk=request.user['project_id'])
        # stats = {
        #     'total_run': project.total_inrun(),
        #     'total_offline': project.total_offline(),
        #     'total_alarm': project.total_inalarm(),
        #     'total_fault': project.total_infault()
        # }

        devices_fields = []

        # 过滤条件
        page = request.query_params.get('page', None)
        per_page = request.query_params.get('per_page', None)
        device_types = request.query_params.get('device_type', None)
        protoid = request.query_params.get('protoid', None)
        in_alarm = request.query_params.get('in_alarm', None)
        in_fault = request.query_params.get('in_fault', None)
        is_online = request.query_params.get('is_online', None)
        building_id = request.query_params.get('building_id', None)
        floor_id = request.query_params.get('floor_id', None)
        room_id = request.query_params.get('room_id', None)
        search = request.query_params.get('search', None)

        queryset = Device.objects.filter(project_id=request.user['project_id'])

        if device_types is not None:
            if isinstance(eval(device_types), list):
                queryset = queryset.filter(device_type_id__in=eval(device_types))
            else:
                queryset = queryset.filter(device_type_id=device_types)

        if protoid is not None:
            queryset = queryset.filter(device_prototype_id=protoid)

        if in_alarm is not None and in_alarm:
            queryset = queryset.filter(in_alarm=True)

        if in_fault is not None and in_fault:
            queryset = queryset.filter(in_fault=True)

        if is_online is not None:
            if is_online == '1':
                queryset = queryset.filter(online=True)
            else:
                queryset = queryset.filter(online=False)

        if search is not None:
            queryset = queryset.filter(Q(nick_name__contains=search) | Q(mac=search) | Q(id=to_int(search)))

        room_ids = None
        if room_id is not None:
            room_ids = [room_id]
        elif floor_id is not None:
            room_ids = ActiveRoom.objects.filter(floor_id=floor_id).values_list('id', flat=True)
        elif building_id is not None:
            floor_ids = Floor.objects.filter(building_id=building_id).values_list('id', flat=True)
            room_ids = ActiveRoom.objects.filter(floor_id__in=floor_ids).values_list('id', flat=True)

        if room_ids is not None:
            device_model = ContentType.objects.get_for_model(Device)
            device_ids = RoomDevice.objects.filter(content_type=device_model, active_room_id__in=room_ids).values_list('object_id', flat=True)
            queryset = queryset.filter(id__in=device_ids)

        stats = {
            'total_run': queryset.filter(sw_on=True, online=True).count(),
            'total_offline': queryset.filter(online=False).count(),
            'total_alarm': queryset.filter(in_alarm=True, online=True).count(),
            'total_fault': queryset.filter(in_fault=True, online=True).count()
        }

        # 符合过滤条件的所有设备个数
        all_count = queryset.count()
        queryset = queryset.order_by(Convert('nick_name', 'gbk'))

        # 分页
        queryset = self.paginate_queryset(queryset, request)
        for device in queryset:
            device_type = device.device_type
            device_prototype = device.device_prototype
            device_fileds = {
                'id': device.id,
                'nick_name': device.nick_name,
                'status': device.status,
                'mac': device.mac,
                'in_alarm': device.in_alarm,
                'address': device.address,
                'device_type': device_type.name,
                'device_prototype': device_prototype.name,
                'dpt_uni_name': device_prototype.uni_name,
                'floor_name': device.active_room.floor.name if device.active_room is not None else '',
                'room_name': device.active_room.name if device.active_room is not None else '',
                'temp': device.current_temp(),
                'hum': device.current_hum(),
                'runtime': device.run_time(),
                'dewpoint': device.dew_point(),
                'icon': device_type.icon,
                'sw': device.sw_on,
                'key': device.id,
                'label': device.nick_name if device.nick_name is not None else device_type.name,
                'custz_detail': True if device_prototype.web_content is not None else False,
                'dashboard_attres': device.dashboard_attres(),
                'key_attributes': []
            }
            # 电表状态,Web 设备列表统计仪表在线数，其他情况下 key_attributes 是不需要的
            ka_fields = []
            if '网关' in device_prototype.uni_name:
                aps = AttributePrototype.objects.filter(identifier__contains='MeterStatus', device_prototype=device_prototype)
                for ap in aps:
                    # da = DeviceAttribute.objects.get_by_idf(device, ap.identifier)
                    # da = DeviceAttribute.objects.get_by_ap(device, ap)
                    da = DeviceAttribute.objects.query_object_by_ap(device, ap)
                    if da is not None:
                        ka_fields.append({
                            'id': da.id,
                            'value': da.value,
                            'name': ap.label if ap.label is not None else ap.name,
                            'unit': ap.unit,
                            'identifier': ap.identifier
                        })
                device_fileds['key_attributes'] = ka_fields

            devices_fields.append(device_fileds)

        res_data = {
            'status': status.HTTP_200_OK,
            'total': all_count,
            'data': {
                'stats': stats,
                'devices': devices_fields
            }
        }

        return Response(res_data)

    def retrieve(self, request, pk=None):
        queryset = Device.objects.all()
        # pk 可以是设备 id 或者 mac
        if len(pk) >= 10:
            device = get_object_or_404(queryset, mac=pk, project_id__isnull=False)
        else:
            device = get_object_or_404(queryset, pk=int(pk))
        device_type = device.device_type
        device_prototype = device.device_prototype

        device_attributes = []
        device_limits = []
        has_permission = AuthUtils.has_permissions(request.user.get('id'), 8)
        if not has_permission:
            device_limits = DeviceLimit.objects.filter(enabled=True, device_prototype_id=device.device_prototype_id)

        # das = list(device.deviceattribute_set.order_by('attribute_prototype_id').all())
        aps = list(AttributePrototype.objects.filter(device_prototype_id=device.device_prototype_id))
        ap_ids = [ap.id for ap in aps]
        das = DeviceAttribute.objects.query_object_list(device, ap_ids)
        # ap_ids = [da.attribute_prototype_id for da in das]
        # aps = list(AttributePrototype.objects.filter(id__in=ap_ids))
        for da in das:
            da_aps = list(filter(lambda x: x.id == da.attribute_prototype_id, aps))
            if len(da_aps):
                ap = da_aps[0]
                da_fields = {
                    'id': da.id,
                    'value': da.value,
                    'identifier': ap.identifier,
                    'name': ap.name,
                    'icon': ap.icon,
                    'unit': ap.unit,
                    'seq': ap.seq,
                    'data_type': ap.data_type,
                    'remark': ap.remark,
                    'options': ap.options,
                    'read_only': ap.read_only,
                    'min_value': ap.min_value,
                    'max_value': ap.max_value,
                }

                if not has_permission:
                    AttributePrototype.objects.affect_by_device_limit(device_limits, device, ap, da_fields)

                device_attributes.append(da_fields)

        room = None
        if device.active_room is not None:
            ar = device.active_room
            room = {
                'id': ar.id,
                'name': ar.name
            }

        set_attres = DevicePrototype.objects.web_content(device)

        et_types = []

        device_et_types = device.et_types(parse_dashboard=True)

        for ett in device_et_types:
            tmp_fields = {
                'name': ett['name']
            }
            extensions = []
            for ext in ett['extensions']:
                ext_fields = {
                    'idx': ext['idx'],
                    'nick_name': ext['nick_name']
                }

                attres = []
                for attr in ext['detail_attres']:
                    if attr.get('id', None) is not None:
                        attr_fields = {
                            'id': attr['id'],
                            'name': attr['name'],
                            'data_type': attr['data_type'],
                            'options': attr['options'],
                            'identifier': attr['identifier'],
                            'unit': attr['unit'],
                            'value': attr['value']
                        }
                        attres.append(attr_fields)
                for attr in ext['attres']:
                    if attr.get('id', None) is not None:
                        attr_fields = {
                            'id': attr['id'],
                            'name': attr['name'],
                            'data_type': attr['data_type'],
                            'options': attr['options'],
                            'identifier': attr['identifier'],
                            'unit': attr['unit'],
                            'value': attr['value']
                        }
                        attres.append(attr_fields)

                ext_fields['attres'] = {item["id"]: item for item in attres}.values()
                sub_dp = device.sub_dp(ett['prefix'])
                if sub_dp:
                    ext_fields['set_attres'] = DevicePrototype.objects.parse_content(device, sub_dp.web_content,
                                                                                     prefix=ett['prefix'], idx=ext['idx'])
                extensions.append(ext_fields)

            tmp_fields['extensions'] = extensions

            et_types.append(tmp_fields)

        device_type_fields = {
            'id': device_type.id,
            'name': device_type.name,
            'uni_name': device_type.uni_name
        }
        device_prototype_fields = {
            'id': device_prototype.id,
            'name': device_prototype.name,
            'uni_name': device_prototype.uni_name
        }

        attribute_prototype = AttributePrototype.objects.filter(
            device_prototype_id=device_prototype.id, show_in_list=True).order_by('id')
        attrs = []
        for attr in attribute_prototype:
            attrs.append({
                'id': attr.id,
                'name': attr.name,
                'identifier': attr.identifier
            })

        res_data = {
            'status': status.HTTP_200_OK,
            'data': {
                'device': {
                    'id': device.id,
                    'nick_name': device.nick_name,
                    'status': device.status,
                    'wudid': device.wudid,
                    'in_alarm': device.in_alarm,
                    'address': device.address,
                    'mac': device.mac,
                    'remark': device.remark,
                    'created_at': device.created_at,
                    'updated_at': device.updated_at,
                    'icon': device_type.icon,
                    'sw': device.sw_on,
                    'device_attributes': device_attributes,
                    'last_update_at': device.last_update_at(),
                    'room': room,
                    'detail_attres': device.detail_attres(),
                    'set_attres': set_attres,
                    'et_types': et_types,
                    'device_type': device_type_fields,
                    'device_prototype': device_prototype_fields,
                    'attrs': attrs,
                    'issue_name': device.last_issue(),
                }
            }
        }

        # 单位设备控制权限
        if Dimension.objects.filter(type_name='单位').exists():
            unit_ids = list(Dimension.objects.filter(type_name='单位').values_list('id', flat=True))
            terminal = Terminal.objects.filter(device_id=device.id, idx__isnull=True, prefix__isnull=True).first()
            if terminal is not None and DimensionTerminal.objects.filter(terminal_id=terminal.id, dimension_id__in=unit_ids).exists():
                user_id = request.user['id']
                user_units = list(DimensionUser.objects.filter(id__in=unit_ids, web_user_id=user_id).values_list('dimension_id', flat=True))
                unit_permission = DimensionTerminal.objects.filter(terminal_id=terminal.id, dimension_id__in=user_units).exists()
                res_data['data']['device']['unit_permission'] = unit_permission

        return Response(res_data)

    def partial_update(self, request, pk=None):
        queryset = Device.objects.all()
        device = get_object_or_404(queryset, pk=pk)

        serializer = WebDeviceSerializer(
            device, data=request.data, partial=True)

        if serializer.is_valid(raise_exception=True):
            serializer.save()

        # 向管理后台同步设备信息
        data = {
            'nick_name': device.nick_name,
            'project_id': device.project_id,
            'remark': device.remark
        }
        r = AdminDeviceApi.update_device(device.id, data)
        if r.status_code != 200:
            logging.warning(f'向 admin3 更新设备信息失败. msg: {r.text}')

        # 更新设备昵称同时更新对应的终端昵称
        nick_name = request.data.get('nick_name', None)
        if nick_name is not None:
            terminals = Terminal.objects.filter(device=device, idx=None, prefix=None)
            if terminals.count() == 1:
                terminal = terminals.first()
                terminal.nick_name = nick_name
                terminal.save()

        res_data = {
            'status': status.HTTP_200_OK,
            'data': {
                'device': {
                    'id': device.id,
                    'nick_name': device.nick_name,
                    'status': device.status,
                    'wudid': device.wudid,
                    'in_alarm': device.in_alarm,
                    'in_fault': device.in_fault,
                    'online': device.online,
                    'address': device.address,
                    'created_at': device.created_at,
                    'updated_at': device.updated_at
                }
            }
        }

        return Response(res_data)


class WebDeviceV2ViewSet(viewsets.ModelViewSet):
    serializer_class = DeviceListV2Serializer

    def get_queryset(self):
        query_set = Device.objects.filter(project__isnull=False)

        device_type = self.request.query_params.get('device_type', None)
        if device_type is not None:
            query_set = query_set.filter(device_type_id=device_type)

        device_prototype = self.request.query_params.get('device_prototype', None)
        if device_prototype is not None:
            query_set = query_set.filter(device_prototype_id=device_prototype)

        device_prototype = self.request.query_params.get('protoid', None)
        if device_prototype is not None:
            query_set = query_set.filter(device_prototype_id__in=device_prototype.split(','))

        search = self.request.query_params.get('search', None)
        if search is not None:
            query_set = query_set.filter(Q(nick_name__icontains=search) | Q(address__icontains=search) | Q(mac__icontains=search))

        query_set = query_set.order_by(Convert('nick_name', 'gbk'))

        return query_set

    def list(self, request, *args, **kwargs):
        query_set = self.get_queryset()

        # 分组ID，分组成员排在前面
        group_id = request.query_params.get('group_id', None)
        total_group = None
        if group_id is not None:
            group_objects = GroupDevice.objects.filter(group_id=group_id, content_type=ContentType.objects.get_for_model(Device))
            group_devices = group_objects.values_list('object_id', flat=True)
            total_group = group_devices.count()

            selected = query_set.filter(id__in=group_devices)
            unselect = query_set.exclude(id__in=group_devices)

            query_set = selected.union(unselect)

        per_page = request.query_params.get('per_page', None)
        page = request.query_params.get('page', '1')
        if per_page is not None and per_page.isdigit() and page.isdigit():
            per_page = int(per_page)
            page = int(page)
            begin = (page - 1) * per_page
            end = page * per_page
            query_set = query_set[begin: end]
        else:
            query_set = query_set[:200]
        devices = self.serializer_class(query_set, many=True).data

        is_web = request.query_params.get('web', None)

        dp_cache = {}
        dt_cache = {}
        for device in devices:
            dp_id = device.get('device_prototype_id')
            dp = dp_cache.get(dp_id, None)
            if dp is None:
                dp = DevicePrototype.objects.get(pk=dp_id)
                dp_cache[dp_id] = dp
            device['custz_detail'] = True if (dp.web_content if is_web else dp.content) else False
            device['device_prototype'] = dp.name

            dt_id = device.get('device_type_id')
            dt = dt_cache.get(dt_id, None)
            if dt is None:
                dt = DeviceType.objects.get(pk=dt_id)
                dt_cache['dt_id'] = dt
            device['device_type'] = dt.name

        return Response({
            "status": status.HTTP_200_OK,
            "data": {
                'devices': devices,
                'total_selected': total_group
            },
            "total": self.get_queryset().count(),
        })


class WebUnbindDeviceViewSet(viewsets.ModelViewSet):
    def list(self, request, *args, **kwargs):
        room_type = request.query_params.get('type', 'device')

        objects_fields = []
        unbind_objects_fields = []
        bind_object_ids = []

        dt_cache = {}
        devices = []
        for device in Device.objects.filter(project_id=request.user['project_id']):
            device_type = dt_cache.get(device.device_type_id, None)
            if device_type is None:
                device_type = device.device_type
                dt_cache[device.device_type_id] = device_type

            devices.append({
                'id': device.id,
                'nick_name': device.nick_name,
                'mac': device.mac,
                'device_type': device_type.name
            })

        if room_type == 'device':
            device_model = ContentType.objects.get_for_model(Device)
            bind_device_ids = RoomDevice.objects.filter(content_type=device_model).values_list('object_id', flat=True)
            unbind_devices = list(filter(lambda x: x['id'] not in bind_device_ids, devices))

            objects_fields = devices
            unbind_objects_fields = unbind_devices
            bind_object_ids = bind_device_ids

        elif room_type == 'terminal':
            terminals = []

            for terminal in Terminal.objects.filter(show_en=True):
                device = None
                terminal_devices = list(filter(lambda x: x['id'] == terminal.device_id, devices))
                if len(terminal_devices):
                    device = terminal_devices[0]

                terminals.append({
                    'id': terminal.id,
                    'nick_name': terminal.nick_name,
                    'mac': device['mac'] if device is not None else None,
                    'device_type': device['device_type'] if device is not None else None
                })

            terminal_model = ContentType.objects.get_for_model(Terminal)
            bind_terminal_ids = RoomDevice.objects.filter(content_type=terminal_model).values_list('object_id', flat=True)
            unbind_terminals = list(filter(lambda x: x['id'] not in bind_terminal_ids, terminals))

            objects_fields = terminals
            unbind_objects_fields = unbind_terminals
            bind_object_ids = bind_terminal_ids

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'all_devices': objects_fields,
                'unbind_devices': unbind_objects_fields,
                'bind_devices': bind_object_ids
            }
        })


class LivingDetectionsViewSet(viewsets.ModelViewSet):
    serializer_class = LivingDetectionSerializer
    renderer_classes = (SyJSONRender,)

    def get_queryset(self):
        return LivingDetection.objects.filter(device_id=self.kwargs.get('device_id')).order_by('-created_at')[:10]

    def list(self, request, *args, **kwargs):
        records = super(LivingDetectionsViewSet, self).list(
            request, *args, **kwargs).data

        res_data = {
            'living_records': records['results'],
            'count': len(records['results'])
        }

        return Response(res_data)


class DeviceChangesViewSet(viewsets.ModelViewSet):
    def update(self, request, *args, **kwargs):
        # if settings.NGINX_DOMAIN is not None:
        #     url = settings.NGINX_DOMAIN + '/saianapi/v1/device_changes'
        #     headers = {'Authorization': request.headers.get('Authorization')}
        #     r = requests.post(url, headers=headers, data=request.data)
        # else:

        device_id = request.data.get('device_id')
        changes = request.data.get('changes')

        device = Device.objects.get(pk=device_id)
        changes = json.loads(changes)

        user_id = request.user.get('id')
        web_user = WebUser.objects.get(pk=user_id)

        log_id = device.send_ctrls(web_user, changes, False)
        if log_id is not None:
            log = DeviceCtrlLog.objects.get(pk=log_id)

            if log.errcode == 0:
                return Response({'status': status.HTTP_200_OK, 'data': None})
            else:
                return Response({'status': status.HTTP_400_BAD_REQUEST, 'data': None})
        else:
            return Response({'status': status.HTTP_200_OK, 'data': None})

class DeviceV2ViewSet(viewsets.GenericViewSet):
    def list(self, request):
        project_id = request.user['project_id']
        id = request.user['id']
        queryset = Device.objects.filter(project_id=project_id, status__in=[20, 30, 40])

        dtid = request.query_params.get('dtid', None)
        subdtid = request.query_params.get('subdtid', None)
        search = request.query_params.get('search', None)
        device_status = request.query_params.get('status', None)
        alarm = request.query_params.get('alarm', None)
        dpid = request.query_params.get('dpid', None)
        sw_on = request.query_params.get('sw_on', None)
        mac = request.query_params.get('mac', None)
        nick_name = request.query_params.get('nick_name', None)
        room = request.query_params.get('room', None)

        dtids = None
        if dtid is not None:
            dtids = DeviceType.objects.filter(parent_id=dtid).values_list('id', flat=True)

        if subdtid is not None:
            dtids = [subdtid]

        if search is not None:
            queryset = queryset.filter(Q(nick_name__icontains=search) | Q(address__icontains=search) | Q(mac__icontains=search))

            user_search = UserSearches(key_words=search, web_user_id=id)
            user_search.save(using=f'prj{project_id}db')

        if device_status is not None:
            queryset = queryset.filter(status=device_status)

        if alarm is not None:
            queryset = queryset.filter(in_alarm=alarm)

        if dtids is not None:
            queryset = queryset.filter(device_type_id__in=dtids)

        if dpid is not None:
            queryset = queryset.filter(device_prototype_id=dpid)

        if sw_on is not None:
            queryset = queryset.filter(sw_on=sw_on)

        if search is None and nick_name is not None:
            queryset = queryset.filter(nick_name__contains=nick_name)

        if search is None and mac is not None:
            queryset = queryset.filter(mac__contains=mac)

        if room is not None:
            device_model = ContentType.objects.get_for_model(Device)
            device_ids = RoomDevice.objects.filter(active_room_id=room, content_type=device_model).values_list('object_id')
            queryset = queryset.filter(pk__in=device_ids)

        # 分页处理
        count = queryset.count()
        queryset = queryset.order_by(Convert('nick_name', 'gbk'))
        queryset = self.paginate_queryset(queryset)

        # 记录用户搜索记录

        devices_fields = []
        device_fields = None
        for device in queryset:
            device_prototype = DevicePrototype.objects.get(pk=device.device_prototype_id)

            device_type = device.device_type
            user_device = UserDevice.objects.filter(web_user_id=request.user['id'], device_id=device.id).first()
            device_fields = {
                'id': device.id,
                'nick_name': device.nick_name,
                'status': device.status,
                'sw_on': device.sw_on,
                'in_fault': device.in_fault,
                'online': device.online,
                'mac': device.mac,
                'in_alarm': device.in_alarm,
                'created_at': device.created_at,
                'updated_at': device.updated_at,
                'device_prototype': {
                    'id': device_prototype.id,
                    'name': device_prototype.name,
                    'uni_name': device_prototype.uni_name
                },
                'device_type': {
                    'id': device_type.id,
                    'name': device_type.name,
                    'uni_name': device_type.uni_name
                },
                'icon': device.device_type.icon,
                'is_following': True if user_device is not None else False,
                'following_id': user_device.id if user_device is not None else None,
                'key_attributes': device.key_attributes(),
                'today_power_cons': device.today_power_cons(),
                'today_cool_cons': device.today_cool_cons(),
                'custz_detail': True if device_prototype.content is not None else False,
                'dashboard_attres': device.dashboard_attres()}

            if device_fields is not None:
                devices_fields.append(device_fields)

        res_data = {
            'status': status.HTTP_200_OK,
            'total': count,
            'data': {
                'devices': devices_fields
            }
        }

        return Response(res_data)

    def retrieve(self, request, pk=None):
        device = get_object_or_404(Device, id=pk)

        device_prototype = device.device_prototype
        device_type = device.device_type

        user_device = UserDevice.objects.filter(
            web_user_id=request.user['id'], device_id=device.id).first()

        living_records = []

        for ld in device.last_10_living_records():
            ld_fields = {
                'desc': ld.desc,
                'created_at': ld.created_at
            }

            living_records.append(ld_fields)

        res_data = {
            'status': status.HTTP_200_OK,
            'data': {
                'device': {
                    'id': device.id,
                    'nick_name': device.nick_name,
                    'status': device.status,
                    'sw_on': device.sw_on,
                    'in_fault': device.in_fault,
                    'online': device.online,
                    'mac': device.mac,
                    'in_alarm': device.in_alarm,
                    'created_at': device.created_at,
                    'updated_at': device.updated_at,
                    'device_attributes': device.key_attributes(),
                    'last_update_at': device.last_update_at(),
                    'device_type': {
                        'id': device_type.id,
                        'name': device_type.name,
                        'uni_name': device_type.uni_name
                    },
                    'device_prototype': {
                        'id': device_prototype.id,
                        'name': device_prototype.name,
                        'uni_name': device_prototype.uni_name
                    },
                    'icon': device_type.icon,
                    'issue_name': device.last_issue(),
                    'is_following': True if user_device is not None else False,
                    'following_id': user_device.id if user_device is not None else None,
                    'living_records': living_records,
                    'today_power_cons': device.today_power_cons(),
                    'today_cool_cons': device.today_cool_cons(),
                    'custz_detail': True if device_prototype.content is not None else False
                }
            }
        }

        return Response(res_data)


class DeviceV3ViewSet(viewsets.ModelViewSet):
    @classmethod
    def content_attr_limit(cls, device, components, device_limits):
        try:
            for component in components:
                attr = component.get('attr', None)
                if attr is None:
                    continue
                if component.get('components', None) is not None:
                    cls.content_attr_limit(device, component['components'], device_limits)
                if isinstance(attr, list):
                    attres = attr
                else:
                    attres = [attr]
                for limit in device_limits:
                    limit_aps = limit.attribute_prototypes.replace(' ', '').split(',')
                    for attr in attres:
                        if str(attr.get('id')) in limit_aps:
                            run_mode_aps = AttributePrototype.objects.filter(label__contains='运行模式',
                                                                             device_prototype_id=device.device_prototype_id)
                            if run_mode_aps.exists():
                                run_mode_ap = None
                                if run_mode_aps.count() == 1:
                                    run_mode_ap = run_mode_aps.first()
                                else:
                                    # 找出 idx
                                    idx = re.findall(r'\d+', attr.get('identifier'))
                                    if len(idx):
                                        idx = idx[0]
                                        run_mode_ap = run_mode_aps.filter(identifier__contains=f'_{idx}_')
                                        if run_mode_ap.exists():
                                            run_mode_ap = run_mode_ap.first()
                                        else:
                                            run_mode_ap = None

                                if run_mode_ap is not None:
                                    # run_mode = DeviceAttribute.objects.get_by_idf(device, run_mode_ap.identifier)
                                    # run_mode = DeviceAttribute.objects.get_by_ap(device, run_mode_ap)
                                    run_mode = DeviceAttribute.objects.query_object_by_ap(device, run_mode_ap)
                                    if run_mode is not None:
                                        if run_mode.value == '制暖':
                                            attr['max_value'] = limit.up_value
                                        elif run_mode.value == '制冷':
                                            attr['min_value'] = limit.low_value
        except KeyError:
            pass

    def get_object(self) -> Device:
        obj = Device.objects.filter(wudid=self.kwargs['str'])
        if obj.count() == 0 and (self.kwargs['str']).isdigit():
            obj = Device.objects.filter(pk=self.kwargs['str'])
        if obj.count() == 0:
            obj = Device.objects.filter(mac=self.kwargs['str'])
        return obj.first()

    def retrieve(self, request, *args, **kwargs):
        prefix = request.query_params.get('prefix', None)
        if not prefix:
            prefix = None
        idx = request.query_params.get('idx', None)
        device = self.get_object()
        if device is None:
            raise Http404(f"设备不存在。query: {self.kwargs['str']}")
        device_prototype = device.device_prototype
        device_type = device.device_type

        user_device = {}

        if prefix is not None and idx is not None:
            user_device = UserDevice.objects.filter(web_user_id=request.user['id'], device_id=device.id, unit_prefix=prefix, unit_idx=idx).first()
        else:
            user_device = UserDevice.objects.filter(web_user_id=request.user['id'], device_id=device.id).first()

        living_records = []
        detail_attres = []

        for ld in device.last_10_living_records():
            ld_fields = {
                'desc': ld.desc,
                'created_at': ld.created_at
            }

            living_records.append(ld_fields)

        terminal = None
        terminal_id = None
        child_dp = None
        issue_name = device.last_issue()

        if prefix is not None and idx is not None:
            terminals = Terminal.objects.filter(device=device, idx=idx, prefix=prefix)
            if terminals.exists():
                terminal = terminals.first()
                if not terminal.in_alarm and not terminal.in_fault:
                    issue_name = ''

            child_dps = DevicePrototype.objects.filter(parent_id=device_prototype.id, prefix=prefix)
            if child_dps.exists():
                child_dp = child_dps.first()

            idx = int(idx)
            et_types = device.et_types(prefix, idx, parse_dashboard=False)
            if len(et_types):
                sub_extensions = list(filter(lambda item: item['idx'] == idx, et_types[0]['extensions']))
                if len(sub_extensions):
                    attres = list(sub_extensions[0]['detail_attres'])
                    dash_attres = terminal.dashboard_attres()
                    if dash_attres:
                        attres += dash_attres

                    idfs = []
                    detail_attres = []
                    for attr in attres:
                        if attr['idf'] not in idfs:
                            idfs.append(attr['idf'])
                            detail_attres.append(attr)
            # for et_type in device.et_types(prefix, idx, parse_dashboard=False):
            #     if et_type['prefix'] == prefix:
            #         for exten in et_type['extensions']:
            #             if exten['idx'] == idx:
            #                 detail_attres = exten['detail_attres']
            #                 break
            #         break

        else:
            terminal = Terminal.objects.filter(device_id=device.id, prefix__isnull=True, idx__isnull=True).last()

        dp_content = DevicePrototype.objects.content(device, prefix, idx)

        dp = {
            'id': device_prototype.id,
            'name': device_prototype.name,
            'uni_name': device_prototype.uni_name,
        }
        if child_dp:
            device_dp = {
                'id': child_dp.id,
                'name': child_dp.name,
                'uni_name': child_dp.uni_name,
                'content': dp_content,
                'parent': dp
            }
        else:
            device_dp = dp
            device_dp['content'] = dp_content

        # 末端温度设定限制
        has_permission = AuthUtils.has_permissions(request.user.get('id'), 7)
        if not has_permission:
            device_limits = DeviceLimit.objects.filter(enabled=True, device_prototype_id=device.device_prototype_id)
            for content in dp_content:
                if content.get('components', None) is not None:
                    self.content_attr_limit(device, content['components'], device_limits)

        # 开关
        sw_on = device.sw_on
        if terminal is not None:
            # 子设备开关处理，依据终端状态参数或终端开关
            if prefix is not None and idx is not None:
                ap_ids = AttributePrototype.objects.filter(device_prototype_id=device.device_prototype_id,
                                                           identifier__startswith=prefix, identifier__icontains=f'_{idx}').values_list('id',
                                                                                                                                       flat=True)
                sub_status_tas = TerminalAttribute.objects.filter(terminal=terminal, attribute_prototype_id__in=ap_ids).filter(
                    Q(identifier__endswith='RunStatus') | Q(identifier__endswith='_Status'),
                    identifier__icontains=f'_{idx}', ).order_by('-updated_at')
                # 终端属性可能有多个负荷条件的参数,优先选择只包含 prefix，idx 和 "status"的参数
                sub_status = sub_status_tas.first()
                for status_ta in sub_status_tas:
                    if len(status_ta.identifier.split('_')) == 3:
                        sub_status = status_ta
                        break

                # if sub_status:
                #     sub_status = TerminalAttribute.objects.query_object_by_ta(sub_status, request.user['project_id'])
                if sub_status and isinstance(sub_status.value, str):
                    sw_on = sub_status.value != '停止' and sub_status.value != '关到位' and '停止' not in str(sub_status.value)
                else:
                    sw_on = terminal.sw_on

            terminal_id = terminal.id

        # 关键属性
        key_attributes = device.key_attributes()

        # 最后更新时间
        updated_at = device.updated_at
        if terminal is not None and prefix is not None:
            updated_at = terminal.updated_at
        else:
            if any(attr['identifier'] == 'update_time' for attr in key_attributes):
                updated_at = next((attr['value'] for attr in key_attributes if attr['identifier'] == 'update_time'), None)
            else:
                last_event = DeviceEvent.objects.filter(device_id=device.id).order_by('-created_at').first()
                if last_event is not None:
                    updated_at = last_event.created_at

        res_data = {
            'status': status.HTTP_200_OK,
            'data': {
                'device': {
                    'id': device.id,
                    'terminal_id': terminal_id,
                    'address': device.address,
                    # 'nick_name': nickname if prefix is not None and idx is not None else device.nick_name,
                    'nick_name': terminal.nick_name if terminal is not None else device.nick_name,
                    'remark': device.remark,
                    'status': device.status,
                    'sw_on': sw_on,
                    # 'in_fault': in_fault if prefix is not None and idx is not None else device.in_fault,
                    'in_fault': terminal.in_fault if terminal is not None else device.in_fault,
                    'online': terminal.online if terminal is not None else device.online,
                    'mac': device.mac,
                    'wudid': device.wudid,
                    'platform': device.platform,
                    # 'in_alarm': in_alarm if prefix is not None and idx is not None else device.in_alarm,
                    'in_alarm': terminal.in_alarm if terminal is not None else device.in_alarm,
                    'created_at': device.created_at,
                    'updated_at': updated_at,
                    'device_attributes': key_attributes,
                    'last_update_at': updated_at,
                    'device_type': {
                        'id': device_type.id,
                        'name': device_type.name,
                        'uni_name': device_type.uni_name
                    },
                    'device_prototype': device_dp,
                    'icon': device_type.icon,
                    'issue_name': issue_name,
                    'is_following': True if user_device is not None else False,
                    'following_id': user_device.id if user_device is not None else None,
                    'living_records': living_records,
                    'today_power_cons': device.today_power_cons(),
                    'today_cool_cons': device.today_cool_cons(),
                    'custz_detail': True if dp_content is not None else False,
                    'detail_attres': detail_attres if prefix is not None and idx is not None else device.detail_attres(),
                    'attach_devices': DyAttribute.objects.get_attach_devices(device.id)
                }
            }
        }

        return Response(res_data)


class DeviceLogsViewSet(viewsets.ModelViewSet):
    renderer_classes = (SyJSONRender,)
    # authentication_classes = ()

    @action(detail=True, methods=['get'])
    def get_authenticators(self):
        if 'intapi' in self.request.path:
            return [IntapiAuth(), ]
        return [LegacyAuth(), ]

    def get_serializer_class(self):
        if self.request.query_params.get('type') == 'down':
            return DeviceCtrlLogSerializer(context={'request': self.request})
        return DeviceEventSerializer

    def get_queryset(self):
        log_type = self.request.query_params.get('type')
        if log_type == 'up':
            queryset = DeviceEvent.objects.all()
        else:
            queryset = DeviceCtrlLog.objects.all()

        dp_id = self.request.query_params.get('dp_id', None)
        if dp_id is not None:
            queryset = queryset.filter(device__device_prototype_id=dp_id)

        did = self.request.query_params.get('did', None)
        if did is not None:
            queryset = queryset.filter(device_id=did)

        search = self.request.query_params.get('search', None)
        if search is not None and log_type == 'up':
            queryset = queryset.filter(nick_name__icontains=search)

        nick_name = self.request.query_params.get('nick_name', None)
        if nick_name is not None:
            if log_type == 'down':
                queryset = queryset.filter(device__nick_name__contains=nick_name)
            else:
                queryset = queryset.filter(nick_name__icontains=nick_name)

        mac = self.request.query_params.get('mac', None)
        if mac is not None:
            queryset = queryset.filter(mac=mac)

        identifier = self.request.query_params.get('identifier', None)
        if identifier is not None:
            queryset = queryset.filter(data__contains=identifier)

        # executor_type 可多选，executor_id 只用户id
        executor_type = self.request.query_params.get('executor_type', None)
        executor_id = self.request.query_params.get('executor_id', None)
        if executor_type is not None and log_type == 'down':
            executor_types = executor_type.split(',')
            query = Q()
            for et in executor_types:
                if et == 'WebUser' and executor_id is not None:
                    query |= Q(executor_type=et, executor_id__in=executor_id.split(','))
                else:
                    query |= Q(executor_type=et)

            queryset = queryset.filter(query)

        from_at = self.request.query_params.get('from', None)
        till_at = self.request.query_params.get('till', None)
        if from_at is not None and till_at is not None:
            from_dt = datetime.datetime.strptime(from_at, '%Y%m%d%H%M%S')
            till_dt = datetime.datetime.strptime(till_at, '%Y%m%d%H%M%S')
            queryset = queryset.filter(created_at__range=[from_dt, till_dt])

        return queryset.order_by('-id').select_related('device')

    def list(self, request, *args, **kwargs):
        log_type = self.request.query_params.get('type', None)
        if log_type is None:
            return Response(status=status.HTTP_400_BAD_REQUEST, data=None)

        # 查询操作记录的执行日志
        action_log_id = request.query_params.get('action_log_id', None)
        # 是否 Web 请求
        is_web = request.query_params.get('web', None)
        # 查询执行进度
        exec = request.query_params.get('exec', None)

        per_page = request.query_params.get('per_page', None)
        queryset = self.get_queryset()
        count = queryset.count()
        if per_page is None:
            queryset = queryset[:200]
        else:
            queryset = self.paginate_queryset(queryset)

        if log_type == 'up':
            # 是否只返回日志内容的前五个
            is_summary = request.query_params.get('summary', None)
            device_prototype_ids = [item.device_prototype_id for item in queryset]
            dps = DevicePrototype.objects.filter(id__in=device_prototype_ids).select_related('device_type')
            dp_map = {p.id: p for p in dps}
            device_map = {d.id: d.device for d in queryset}
            serializer = DeviceEventSerializer(queryset, many=True, context={'summary': is_summary and is_summary != '0'})
            results = []
            for item_data in serializer.data:
                device = device_map.get(item_data['device'])
                dp = dp_map.get(item_data['device_prototype_id'])
                if device:
                    item_data['device'] = {
                        'id': device.id,
                        'nick_name': device.nick_name,
                        'device_prototype': dp.name
                    }

                    item_data['device_info'] = '%s - %s' % (dp.device_type.name, device.nick_name)
                else:
                    item_data['device_info'] = ''
                results.append(item_data)

        else:
            # 下发日志
            user_cache = {}
            device_cache = {}
            dp_cache = {}
            project_cache = {}
            action_timer_cache = {}
            linkage_rule_cache = {}
            device_timer_cache = {}
            results = []
            # project = Project.objects.get(pk=request.user['project_id'])
            device_map = {d.device.id: d.device for d in queryset}
            device_prototype_ids = [d.device.device_prototype_id for d in queryset]
            dps = DevicePrototype.objects.filter(id__in=device_prototype_ids).select_related('device_type')
            dp_map = {p.id: p for p in dps}
            project_ids = [d.device.project_id for d in queryset]

            serializer = DeviceCtrlLogSerializer(queryset, many=True, context={'request': request})
            for item in serializer.data:
                if exec is None:
                    device = device_map.get(item['device_id'])
                    dp = dp_map.get(device.device_prototype_id)
                    # 设备信息
                    item['device'] = {
                        'id': device.id,
                        'nick_name': device.nick_name,
                        'mac': device.mac,
                        'device_prototype': dp.name,
                        'online': device.online,
                        'status': device.status,
                        'custz_detail': dp.content is not None
                    }
                    # 项目信息
                    project = project_cache.get(f'{device.project_id}', None)
                    if project is None:
                        project = device.project
                        project_cache[f'{device.project_id}'] = project
                    item['project'] = {
                        'id': project.id,
                        'name': project.name
                    }

                    # 下发方信息
                    executor = item['executor']
                    executor_type = executor.get('executor_type', None)
                    if executor_type == 'WebUser':
                        web_user_id = executor['id']
                        user = user_cache.get(web_user_id, None)
                        if user is None:
                            user = WebUser.objects.get_executor_info(request, web_user_id)
                            user_cache[web_user_id] = user
                        item['executor'] = user

                    elif executor_type == 'ActionTimer':
                        try:
                            timer_id = executor.get('id')
                            timer = action_timer_cache.get(timer_id, None)
                            if timer is None:
                                timer = ActionTimer.objects.get(pk=timer_id)
                                action_timer_cache[timer_id] = timer
                            group_action = timer.group_action
                            group = group_action.group
                            item['executor']['name'] = f'{group.name}-{group_action.name}'

                        except ActionTimer.DoesNotExist:
                            pass
                    elif executor_type == 'LinkageRule':
                        try:
                            linkage_id = executor.get('id')
                            linkage = linkage_rule_cache.get(linkage_id, None)
                            if linkage is None:
                                linkage = LinkageRule.objects.get(pk=linkage_id)
                                linkage_rule_cache[linkage_id] = linkage
                            item['executor']['name'] = f'{linkage.name}'

                        except LinkageRule.DoesNotExist:
                            pass

                    elif executor_type == 'DeviceTimer':
                        try:
                            device_timer_id = executor.get('id')
                            device_timer = device_timer_cache.get(device_timer_id, None)
                            if device_timer is None:
                                device_timer = DeviceTimer.objects.get(pk=device_timer_id)
                                device_timer_cache[device_timer_id] = device_timer
                            item['executor']['name'] = device_timer.name

                        except Exception as e:
                            pass

                    elif executor_type == 'Admin':
                        item['executor']['name'] = '管理后台'

                    # project 信息
                    if action_log_id is not None:
                        item['project'] = {
                            'id': project.id,
                            'name': project.name
                        }

                    # meaning
                    if not is_web:
                        item['meaning'] = getCommandMeaning(device, item['data'])

                    item['retries'] = item['retries'] if item['retries'] else 0

                    # clean
                    item.pop('executor_id')
                    item.pop('executor_type')
                    item.pop('project_id')
                    item.pop('device_id')
                results.append(item)

        return Response({
            'status': status.HTTP_200_OK,
            'logs': results,
            'count': count
        })

    def retrieve(self, request, *args, **kwargs):
        log_type = self.request.query_params.get('type', None)
        if log_type is None:
            return Response({
                "status": status.HTTP_400_BAD_REQUEST,
                "error": "type is required."
            })

        if log_type == 'up':
            log = DeviceEvent.objects.get(pk=kwargs.get('pk'))
            device = log.device
            dp = device.device_prototype
            serializer = DeviceEventSerializer(log)
            log = serializer.data
            if device:
                log['device'] = {
                    'id': device.id,
                    'nick_name': device.nick_name,
                    'device_prototype': dp.name
                }

                log['device_info'] = '%s - %s' % (dp.device_type.name, device.nick_name)
            else:
                log['device_info'] = ''
            log['meaning'] = getCommandMeaning(device, log['data'])
        else:
            log = DeviceCtrlLog.objects.get(pk=kwargs.get('pk'))
            device = log.device
            project = device.project
            serializer = DeviceCtrlLogSerializer(log, context={'request': request})
            log = serializer.data

            log['executor'] = WebUser.objects.get_executor_info(request, log['executor_id'])
            log['device'] = {
                'id': device.id,
                'nick_name': device.nick_name,
                'mac': device.mac
            }
            log['project'] = {
                'id': project.id,
                'name': project.name
            }

        return Response({
            'status': 200,
            'log': log
        })


class DeviceLogMeaning(viewsets.ModelViewSet):
    renderer_classes = (SyJSONRender,)

    def retrieve(self, request, *args, **kwargs):
        from django.core import exceptions

        log_type = request.query_params.get('log_type', None)
        log_id = request.query_params.get('log_id', None)

        if log_type is None or log_id is None:
            return Response({
                'status': status.HTTP_400_BAD_REQUEST,
                'error': "log_type and log_id is required."
            })

        try:
            if log_type == 'up':
                obj = DeviceEvent.objects.get(pk=log_id)
                device_prototype_id = obj.device_prototype_id
            else:
                obj = DeviceCtrlLog.objects.get(pk=log_id)
                device_prototype_id = obj.device.device_prototype_id
        except exceptions.ObjectDoesNotExist:
            return Response({
                'status': status.HTTP_404_NOT_FOUND,
                'error': "ObjectDoesNotExist"
            })

        result = {}
        try:
            data = json.loads(obj.data.replace("'", '"').replace(" True", " true").replace(" False", " false"))
            for k, v in data.items():
                value = v
                ap = AttributePrototype.objects.filter(device_prototype_id=device_prototype_id, identifier=k)
                if ap.exists():
                    ap = ap.order_by('created_at').last()
                    try:
                        if ap.data_type == 30 and isinstance(v, int):
                            value = ap.options.split(',')[v]
                        elif ap.data_type == 10:
                            if ap.options and ap.options.find(',') >= 0:
                                idx = 1 if (v is True or v == '1') else 0
                                value = ap.options.split(',')[idx]

                    except IndexError:
                        logging.error(f'日志内容转译出错：options-{ap.options}, idx-{v}')
                    result[ap.name] = value
                else:
                    result[k] = v
        except JSONDecodeError:
            return Response({
                'status': status.HTTP_500_INTERNAL_SERVER_ERROR,
                'error': "解析数据出错。"
            })

        return Response({
            'status': status.HTTP_200_OK,
            'meaning': result
        })


class DeviceLimitViewSet(viewsets.ModelViewSet):
    serializer_class = DeviceLimitSerializer
    renderer_classes = (SyJSONRender,)

    def get_authenticators(self):
        if 'intapi' in self.request.path:
            return [IntapiAuth(), ]
        return [LegacyAuth(), ]

    def get_queryset(self):
        queryset = DeviceLimit.objects.all()

        limit_type = self.request.query_params.get('limit_type', None)
        if limit_type is not None:
            queryset = queryset.filter(limit_type=limit_type)

        dp_id = self.request.query_params.get('dp_id', None)
        if dp_id is not None:
            queryset = queryset.filter(device_prototype_id=dp_id)

        search = self.request.query_params.get('search', None)
        if search is not None:
            queryset = queryset.filter(Q(name__icontains=search) | Q(remark__icontains=search))

        enabled = self.request.query_params.get('enabled', None)
        if enabled is not None:
            queryset = queryset.filter(enabled=enabled != '0')

        return queryset

    def list(self, request, *args, **kwargs):
        device_limits = super(DeviceLimitViewSet, self).list(request, *args, **kwargs).data.get('results', None)
        return Response({
            'status': status.HTTP_200_OK,
            'device_limits': device_limits,
            'count': self.get_queryset().count()
        })

    def create(self, request, *args, **kwargs):
        device_limit = super(DeviceLimitViewSet, self).create(request, *args, **kwargs).data
        AcStrategies.objects.create(
            st_type=50,
            st_id=device_limit.get('id'),
            status=10,
            created_at=device_limit.get('created_at'),
            updated_at=device_limit.get('updated_at')
        )
        return Response({
            'status': status.HTTP_200_OK,
            'device_limit': device_limit
        })

    def partial_update(self, request, *args, **kwargs):
        device_limit = super(DeviceLimitViewSet, self).partial_update(request, *args, **kwargs).data

        return Response({
            'status': status.HTTP_200_OK,
            'device_limit': device_limit
        })

    def retrieve(self, request, *args, **kwargs):
        device_limit = super(DeviceLimitViewSet, self).retrieve(request, *args, **kwargs).data
        # user = WebUser.objects.get(pk=request.user.get('id'))
        return Response({
            'status': status.HTTP_200_OK,
            'device_limit': device_limit
        })

    def destroy(self, request, *args, **kwargs):
        ac_strategies = AcStrategies.objects.filter(st_type=50, st_id=kwargs['pk'])
        ac_strategies.delete()

        super().destroy(request, *args, **kwargs)
        return Response()


class DeviceRuntimeViewSet(viewsets.ModelViewSet):
    serializer_class = DeviceRuntimeSerializer
    renderer_classes = (SyJSONRender,)

    def get_queryset(self):
        queryset = DeviceRuntime.objects.filter(run_time__gt=0)

        device_id = self.request.query_params.get('device_id', None)
        if device_id is not None:
            queryset = queryset.filter(device_id=device_id)

        time_from = self.request.query_params.get('from', None)
        time_till = self.request.query_params.get('till', None)

        if time_from is not None and time_till is not None:
            begin = datetime.datetime.strptime(time_from, '%Y%m%d%H%M%S')
            end = datetime.datetime.strptime(time_till, '%Y%m%d%H%M%S')
            queryset = queryset.filter(created_at__range=[begin, end])

        return queryset

    def list(self, request, *args, **kwargs):
        device_runtime = super(DeviceRuntimeViewSet, self).list(request, *args, **kwargs).data.get('results', None)
        return Response({
            'status': status.HTTP_200_OK,
            'device_runtime': device_runtime,
            'count': self.get_queryset().count()
        })

    def retrieve(self, request, *args, **kwargs):
        device_runtime = super(DeviceRuntimeViewSet, self).retrieve(request, *args, **kwargs).data
        return Response({
            'status': status.HTTP_200_OK,
            'device_runtime': device_runtime
        })

class FcuAttachDeviceView(viewsets.ModelViewSet):
    def create(self, request, *args, **kwargs):
        device_id = request.data.get('device_id', None)
        attach_device = request.data.get('attach_device', None)

        if device_id is None or attach_device is None:
            raise exceptions.ValidationError(detail={'detail': 'data fields device_id and attach_device is required!'})

        device = Device.objects.get(pk=device_id)
        web_user = WebUser.objects.get(pk=request.user.get('id'))
        log_id = device.send_ctrls(web_user, {'AddNewAtttachDevice': attach_device}, False)

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'log_id': log_id
            }
        })

    def list(self, request, *args, **kwargs):
        device_id = request.query_params.get('device_id', None)
        if device_id is None:
            raise exceptions.ValidationError(detail={'detail': 'device_id is required!'})
        device = Device.objects.get(pk=device_id)

        attach_devices = DyAttribute.objects.get_attach_devices(device_id)

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'device': SimpleDeviceSerializer(device).data,
                'attach_devices': attach_devices
            }
        })


class NewFcubdStatusView(viewsets.ModelViewSet):
    """查询添加蓝牙设备的状态"""
    def retrieve(self, request, *args, **kwargs):
        device_id = request.query_params.get('device_id', None)
        log_id = request.query_params.get('log_id', None)

        if device_id is None or log_id is None:
            raise exceptions.ValidationError(detail={'detail': 'device_id and log_id is required!'})

        new_attach_status = '无'

        ctrl_log = DeviceCtrlLog.objects.filter(id=log_id).last()
        if ctrl_log is not None:
            last_status_event = DeviceEvent.objects.filter(device_id=device_id,
                                                           data__contains='NewAttachDeviceStatus').order_by('created_at').last()
            if last_status_event is not None and last_status_event.created_at > ctrl_log.created_at:
                ap = AttributePrototype.objects.get_by_idf(Device.objects.get(pk=device_id), 'NewAttachDeviceStatus')
                if ap is not None:
                    options = ap.options.split(',')
                    new_attach_status = json.loads(last_status_event.data)
                    idx = int(new_attach_status['NewAttachDeviceStatus'])
                    new_attach_status = options[idx]

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'attach_status': new_attach_status
            }
        })


class DataRouteView(viewsets.ModelViewSet):
    serializer_class = DataRouteSerializer
    renderer_classes = (SyJSONRender,)

    def get_authenticators(self):
        if 'intapi' in self.request.path:
            return [IntapiAuth(), ]
        return [LegacyAuth(), ]

    def get_queryset(self):
        queryset = DataRoute.objects.all()

        src_mac = self.request.query_params.get('src_mac', None)
        if src_mac is not None:
            queryset = queryset.filter(src_mac=src_mac)

        src_idf = self.request.query_params.get('src_idf', None)
        if src_idf is not None:
            queryset = queryset.filter(src_idf=src_idf)

        dest_mac = self.request.query_params.get('dest_mac', None)
        if dest_mac is not None:
            queryset = queryset.filter(dest_mac=dest_mac)

        dest_idf = self.request.query_params.get('dest_idf', None)
        if dest_idf is not None:
            queryset = queryset.filter(dest_idf=dest_idf)

        enabled = self.request.query_params.get('enabled', None)
        if enabled is not None:
            queryset = queryset.filter(enabled=enabled)

        return queryset

    def list(self, request, *args, **kwargs):
        data_routes = super().list(request, *args, **kwargs).data

        return Response({
            'status': status.HTTP_200_OK,
            'data': data_routes['results'],
            'count': data_routes['count']
        })

    def create(self, request, *args, **kwargs):
        data = super().create(request, *args, **kwargs).data
        return Response({
            'status': status.HTTP_200_OK,
            'data': data
        })

    def destroy(self, request, *args, **kwargs):
        super().destroy(request, *args, **kwargs)
        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def partial_update(self, request, *args, **kwargs):
        data = super().partial_update(request, *args, **kwargs).data
        return Response({
            'status': status.HTTP_200_OK,
            'data': data
        })

class SySimView(viewsets.ModelViewSet):
    serializer_class = SySimSerializer
    renderer_classes = (SyJSONRender,)

    @classmethod
    def get_admin_sim(cls, pk):
        res = f'saianadmin/intapi/sy_sims/{pk}'
        headers = BaseAPI.admin_intapi_header(res)
        url = f'{DOMAIN}/{res}'

        r = requests.get(url, headers=headers)
        if r.status_code == status.HTTP_200_OK:
            sim_data = r.json().get('sy_sim')
            return SySim(**sim_data)
        else:
            logging.error(f'从Admin3查询sim卡信息失败, status_code: {r.status_code}, text: {r.text}')
            return None

    def retrieve(self, request, *args, **kwargs):
        obj_id = self.kwargs.get('id')
        if '-' in obj_id:
            # vrv 子设备
            if len(obj_id) > 20:
                obj_id = obj_id[:-9]
        else:
            # dtu 电表
            if len(obj_id) > 15:
                obj_id = obj_id[:15]

        sim = SySim.objects.filter(Q(iccid=obj_id) | Q(sn=obj_id) | Q(imei=obj_id) | Q(id=obj_id if obj_id.isdigit() else 0)).last()
        admin_sim = None
        if sim is None:
            admin_sim = self.get_admin_sim(obj_id)

        elif sim.sync_at < datetime.datetime.now() - datetime.timedelta(days=1):
            admin_sim = self.get_admin_sim(obj_id)

        if admin_sim is not None:
            admin_sim.save()
            sim = admin_sim

        return Response({
            'status': status.HTTP_200_OK,
            'sy_sim': SySimSerializer(sim).data if sim is not None else None
        })

class SpacVariantView(viewsets.ModelViewSet):
    """查询空调品牌"""
    def list(self, request, *args, **kwargs):
        res = f'saianadmin/intapi/spac_variants'
        headers = BaseAPI.admin_intapi_header(res)
        url = f'{DOMAIN}/{res}'
        r = requests.get(url, headers=headers, params=request.query_params)
        data = r.json()
        return Response(data)

class InfraredCodeView(viewsets.ModelViewSet):
    """查询分体空调红外码"""
    def list(self, request, *args, **kwargs):
        res = f'saianadmin/intapi/infrared_codes'
        headers = BaseAPI.admin_intapi_header(res)
        url = f'{DOMAIN}/{res}'
        r = requests.get(url, headers=headers, params=request.query_params)
        data = r.json()
        return Response(data)


class AttributeValueView(viewsets.ModelViewSet):
    """直接修改设备的多个参数。"""
    def update(self, request, *args, **kwargs):
        changes_list = request.data.get('values', None)
        if not isinstance(changes_list, list):
            return Response({
                'status': status.HTTP_400_BAD_REQUEST,
                'data': None,
                'error': "发送的数据必须是数组!"
            })
        device_cache = {}
        for changes in changes_list:
            device = device_cache.get(changes['device_id'], None)
            if device is None:
                device = Device.objects.get(pk=changes['device_id'])
                device_cache[device.id] = device
            da = DeviceAttribute.objects.query_object_by_idf(device, changes['identifier'])
            da.value = changes['value']
            da.save()
            DeviceAttribute.objects.save_to_redis(device, da)

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

class NNADeviceView(viewsets.ModelViewSet):
    """插座控制单页"""
    authentication_classes = []

    @classmethod
    def is_wechat_mini_program(cls, request):
        """判断请求是否来自于微信"""
        referer = request.META.get('HTTP_REFERER', '')
        return 'https://servicewechat.com/' in referer

    def retrieve(self, request, *args, **kwargs):
        mac = kwargs['mac']

        # 拒绝非微信小程序的请求
        if not self.is_wechat_mini_program(request):
            return Response(
                status=status.HTTP_403_FORBIDDEN,
                data={
                    'status': status.HTTP_403_FORBIDDEN,
                    'data': None,
                    'message': '权限不足！'
                })

        r = AdminDeviceApi.get_device(mac)
        device = None

        if r.status_code == status.HTTP_200_OK:
            data = r.json().get('row')
            project_id = data['project_id']
            device_id = data['id']
            set_global_db(f'prj{project_id}db')
            device = Device.objects.get(pk=device_id)
        else:
            logging.error(f'从Admin3查询设备失败，status_code: {r.status_code}, text: {r.text}')

        if device:
            device_prototype = device.device_prototype
            if '智能插座' not in device_prototype.uni_name:
                return Response(
                    status=status.HTTP_403_FORBIDDEN,
                    data={
                        'status': status.HTTP_403_FORBIDDEN,
                        'data': None,
                        'message': '权限不足！'
                    })

            sw_ap = AttributePrototype.objects.get(device_prototype_id=device_prototype.id, identifier='SW')
            ap_type = ContentType.objects.get_for_model(AttributePrototype)
            # 查找定时关机任务
            device_content = ContentType.objects.get_for_model(Device)

            linkage_off_time = None
            group_ids = GroupDevice.objects.filter(content_type=device_content, object_id=device.id).values_list('group_id', flat=True)
            group_linkage_ids = LinkageRule.objects.filter(group_id__in=group_ids, name__icontains='断电').values_list('id', flat=True)
            target_linkage_ids = LinkageTarget.objects.filter(target_type=device_content, target_id=device.id, linkage_rule__name__icontains='断电'
                                                              ).values_list('linkage_rule_id', flat=True)
            linkage_trigger = LinkageTrigger.objects.filter(linkage_rule_id__in=list(group_linkage_ids) + list(target_linkage_ids),
                                                            linkage_rule__enabled=True, repeat=True,
                                                            is_finished=False, run_time__isnull=False).first()
            if linkage_trigger is not None:
                linkage_off_time = linkage_trigger.run_time

            # group_ids = GroupDevice.objects.filter(content_type=device_content, object_id=device.id).values_list('group_id', flat=True)
            # action_ids = GroupAction.objects.filter(group_id__in=group_ids).values_list('id', flat=True)
            # action_timers = ActionTimer.objects.filter(group_action_id__in=action_ids,
            #                                            is_finished=False,
            #                                            enabled=True)
            # # 单参数分组操作定时
            # group_off_time = None
            # for action_timer in action_timers:
            #     if action_timer.action_attributes.count() == 1:
            #         action_attribute = action_timer.action_attributes.filter(content_type=ap_type, object_id=sw_ap.id).first()
            #         if action_attribute:
            #             sw_off_timer = ActionTimerAttribute.objects.filter(action_attribute=action_attribute, value='0').first()
            #             if sw_off_timer:
            #                 group_off_time = action_timer.run_time
            #                 break

            # 检查是否有设备云定时
            device_off_time = None
            off_identifiers = json.dumps([{"identifier": "SW", "value": "0"}])
            device_timer = DeviceTimer.objects.filter(device_id=device.id, name__startswith='延时',
                                                      run_date__isnull=False, run_time__isnull=False,
                                                      enabled=True, repeat=False, is_finished=False,
                                                      identifiers=off_identifiers).first()
            if device_timer:
                device_off_time = device_timer.run_time

            # 是否设置了今晚不断电
            now = datetime.datetime.now()
            disable_off_timer = False
            if linkage_off_time and device_timer:
                if device_timer.run_date == (now + datetime.timedelta(days=1)).date() and linkage_off_time == device_timer.run_time:
                    disable_off_timer = True

            # 联系人信息
            contact = None
            if device.project_id == 91:
                # 广安项目联系人
                contact = {
                    'name': '余术建',
                    'mobile': '13547513340'
                }

            return Response({
                'status': status.HTTP_200_OK,
                'data': {
                    'device': {
                        'id': device.id,
                        'mac': device.mac,
                        'nick_name': device.nick_name,
                        'linkage_off_time': linkage_off_time,
                        'device_off_time': device_off_time,
                        'disable_off_timer': disable_off_timer,
                        'operable_after_time': '18:00',
                        'device_prototype': {
                            'id': device_prototype.id,
                            'name': device_prototype.name,
                            'uni_name': device_prototype.uni_name
                        },
                        'project_id': device.project_id,
                        'contact': contact,
                        'sw_on': device.sw_on,
                        'status': device.status,
                        'online': device.online,
                        'idx': None,
                        'prefix': None,
                        'custz_detail': True if device_prototype.content else False,
                        'device_attributes': device.key_attributes()
                    }
                }
            })
        return Response(status=status.HTTP_404_NOT_FOUND, data={
            'status': status.HTTP_404_NOT_FOUND,
            'data': None,
            'message': '找不到设备！'
        })

    def create(self, request, *args, **kwargs):
        logging.info(f'nna device ctrl: {request.data}')
        # 拒绝非微信小程序的请求
        if not self.is_wechat_mini_program(request):
            return Response(
                status=status.HTTP_403_FORBIDDEN,
                data={
                    'status': status.HTTP_403_FORBIDDEN,
                    'data': None,
                    'message': '权限不足！'
                })

        wudid = request.data.get('wudid', None)
        if wudid is None:
            raise exceptions.ValidationError(detail={'detail': 'wudid is required!'})

        # 查询设备
        r = AdminDeviceApi.get_device(wudid)
        device = None

        if r.status_code == status.HTTP_200_OK:
            data = r.json().get('row')
            project_id = data['project_id']
            device_id = data['id']
            set_global_db(f'prj{project_id}db')
            device = Device.objects.get(pk=device_id)
        else:
            logging.error(f'从Admin3查询设备失败，status_code: {r.status_code}, text: {r.text}')
            # admin3出错就默认使用广安项目
            set_global_db(f'prj91db')
            device = Device.objects.filter(wudid=wudid).first()

        if device:
            device_prototype = device.device_prototype
            # 判断设备类型
            if '智能插座' not in device_prototype.uni_name:
                return Response(
                    status=status.HTTP_403_FORBIDDEN,
                    data={
                        'status': status.HTTP_403_FORBIDDEN,
                        'data': None,
                        'message': '权限不足！'
                    })

            # 判断位置
            lat = request.data.get('lat', None)
            lng = request.data.get('lng', None)
            if not lat or not lng:
                return Response(
                    status=status.HTTP_403_FORBIDDEN,
                    data={
                        'status': status.HTTP_403_FORBIDDEN,
                        'data': None,
                        'message': '无法获取位置！'
                    })
            project = Project.objects.get(pk=device.project_id)
            project_settings = json.loads(project.settings)

            project_locations = project_settings.get('locations', None)
            if project_locations is None:
                return Response(
                    status=status.HTTP_403_FORBIDDEN,
                    data={
                        'status': status.HTTP_403_FORBIDDEN,
                        'data': None,
                        'message': '项目没有配置位置信息！'
                    })
            in_project_location = False
            for location in project_locations:
                project_lat = location[0]
                project_lng = location[1]

                if (project_lat - 0.0002) <= float(lat) <= (project_lat + 0.0002) and (project_lng - 0.0002) <= float(lng) <= (project_lng + 0.0002):
                    in_project_location = True
                    continue

            if not in_project_location:
                logging.error(f'当前位置不在有效范围内！')
                pass
                # return Response(
                #     status=status.HTTP_403_FORBIDDEN,
                #     data={
                #         'status': status.HTTP_403_FORBIDDEN,
                #         'data': None,
                #         'message': '当前位置不在有效范围内！'
                #     })

            devices = [device]
            # 控制同一房间的其他插座
            terminal = Terminal.objects.filter(device_id=device.id).first()
            if terminal:
                terminal_model = ContentType.objects.get_for_model(Terminal)
                room_ids = RoomDevice.objects.filter(content_type_id=terminal_model.id, object_id=terminal.id
                                                     ).values_list('active_room_id', flat=True)
                terminal_ids = RoomDevice.objects.filter(content_type_id=terminal_model.id,
                                                         active_room_id__in=room_ids).values_list('object_id', flat=True)
                device_ids = Terminal.objects.filter(id__in=terminal_ids, device_prototype_id=terminal.device_prototype_id
                                                     ).values_list('device_id', flat=True)
                devices = list(Device.objects.filter(id__in=device_ids))

            try:
                changes = json.loads(request.data.get('changes'))
                scheduled_time = request.data.get('scheduled_time', None)
                scheduled_date = request.data.get('scheduled_date', None)
                if scheduled_date is None:
                    scheduled_date = request.data.get('date', None)

                for device in devices:
                    if scheduled_time:
                        now = datetime.datetime.now()
                        run_time = datetime.datetime.strptime(scheduled_time, '%H:%M:%S').time()
                        run_time = run_time.replace(second=0, microsecond=0)
                        if scheduled_date is not None:
                            run_date = datetime.datetime.strptime(scheduled_date, '%Y-%m-%d').date()
                        else:
                            run_date = now.date()

                            if now.time() > run_time:
                                run_date = (now + datetime.timedelta(days=1)).date()

                        if 'SW' not in changes:
                            return

                        if datetime.datetime.combine(run_date, run_time) < now:
                            device_timer = DeviceTimer.objects.filter(device_id=device.id, name__startswith='延时断电',
                                                                      run_date__isnull=False, run_time__isnull=False,
                                                                      enabled=True, repeat=False, is_finished=False).first()
                            if device_timer:
                                device_timer.run_date = run_date
                                device_timer.run_time = run_time
                                device_timer.enabled = False
                                device_timer.save()
                        else:
                            identifiers = json.dumps([{"identifier": "SW", "value": "0"}])
                            device_timer = DeviceTimer.objects.filter(device_id=device.id, name__startswith='延时',
                                                                      run_date__isnull=False, run_time__isnull=False,
                                                                      enabled=True, repeat=False, is_finished=False,
                                                                      identifiers=identifiers).first()
                            if device_timer is not None:
                                device_timer.run_date = run_date
                                device_timer.run_time = run_time
                                device_timer.save()
                            else:
                                DeviceTimer.objects.create(
                                    device_id=device.id,
                                    name='延时断电',
                                    identifiers=identifiers,
                                    repeat=False,
                                    run_date=run_date,
                                    run_time=run_time,
                                    wdays=None,
                                    enabled=True,
                                    is_finished=False
                                )
                    executor = {"name": "公共策略"}
                    device.send_ctrls(executor, changes, False)

            except Exception as e:
                logging.error(f"下发失败！err: {e.__str__()}")
                return Response(status=status.HTTP_500_INTERNAL_SERVER_ERROR, data={
                    'status': status.HTTP_500_INTERNAL_SERVER_ERROR,
                    'data': None,
                    'message': '出错了！'
                })

        else:
            return Response(status=status.HTTP_404_NOT_FOUND, data={
                'status': status.HTTP_404_NOT_FOUND,
                'data': None,
                'message': '找不到设备！'
            })
        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })


class LTAMeterCollectionView(viewsets.ModelViewSet):
    """校正LTA仪表的读数"""
    def list(self, request, *args, **kwargs):
        devices = Device.objects.filter(device_prototype_id=537, project_id__isnull=False).order_by('nick_name')

        results = []
        for device in devices:

            cons_da = DeviceAttribute.objects.query_object_by_idf(device, 'Meter_Cons')
            latest_event = DeviceEvent.objects.filter(device_id=device.id).filter(
                Q(data__contains='photo') & Q(data__icontains='update_time')
            ).order_by('-id').first()

            meter_cons = cons_da.value if cons_da else None
            meter_reading = None
            update_time = None
            meter_state = '未处理'
            photo_url = None
            correct_value = None
            correct_time = None

            if latest_event:
                event_data = json.loads(latest_event.data)
                update_time = event_data.get('update_time', None)
                if update_time:
                    latest_event = DeviceEvent.objects.filter(device_id=device.id).filter(
                        Q(data__contains='photo') & Q(data__icontains='update_time') &
                        Q(data__contains=f'{update_time}')).order_by('id').first()

                event_data = json.loads(latest_event.data)
                update_time = event_data.get('update_time', None)
                meter_reading = event_data.get('Meter_Cons', None)
                photo_url = event_data.get('photo', None)
                correct_value = event_data.get('correct_value', None)
                correct_time = event_data.get('correct_time', None)
                state = event_data.get('state', None)

                if correct_value and correct_time:
                    meter_state = '人工校正'
                elif state is not None:
                    meter_state = '未处理'
                    try:
                        states = ['未处理', '人工校正', '已确认', '可靠数据', '自动校正', '不可靠数据']
                        meter_state = states[state]
                    except Exception as e:
                        logging.error(f'处理仪表状态时出错，state: {state}, error: {e.__str__()}')
                        pass
                    if float(meter_reading) != float(meter_cons):
                        meter_state = '不可靠数据'

            device_info = {
                'id': device.id,
                'mac': device.mac,
                'nick_name': device.nick_name,
                'meter_reading': meter_reading,
                'meter_cons': meter_cons,
                'photo_url': photo_url,
                'update_time': update_time,
                'meter_state': meter_state,
                'event_id': latest_event.id if latest_event else None,
                'correct_value': correct_value,
                'correct_time': correct_time
            }
            results.append(device_info)

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'meters': results
            }
        })

    def update(self, request, *args, **kwargs):
        project_id = request.user.get('project_id')

        device_id = request.data.get('device_id', None)
        value = request.data.get('value', None)
        event_id = request.data.get('event_id', None)

        now = datetime.datetime.now()
        identifier = 'Meter_Cons'

        if device_id is None or value is None:
            return Response({
                'status': status.HTTP_400_BAD_REQUEST,
                'data': None,
                'msg': '设备ID和人工校正值不能为空！'
            })

        if not is_number(value):
            return Response({
                'status': status.HTTP_400_BAD_REQUEST,
                'data': None,
                'msg': '请输入有效数值！'
            })

        device = Device.objects.get(pk=device_id)

        cons_da = DeviceAttribute.objects.query_object_by_idf(device, 'Meter_Cons')
        if cons_da and float(value) < float(cons_da.value):
            return Response({
                'status': status.HTTP_400_BAD_REQUEST,
                'data': None,
                'msg': '请输入有效数值！'
            })

        with transaction.atomic(using=f"prj{project_id}db"):

            latest_event = DeviceEvent.objects.get(pk=event_id)

            event_data = json.loads(latest_event.data)
            event_data['correct_value'] = value
            event_data['correct_time'] = now.strftime('%Y-%m-%d %H:%M:%S')
            latest_event.data = json.dumps(event_data)
            latest_event.save()

            da = DeviceAttribute.objects.get_by_idf(device, identifier)
            da.value = value
            DeviceAttribute.objects.save_to_redis(device, da)

            event_time = latest_event.created_at
            pr_created_at = (event_time + datetime.timedelta(hours=1)).strftime('%Y-%m-%d %H:05:00')
            pr = ParamRecord.objects.filter(device_id=device_id, identifier=identifier, created_at=pr_created_at).first()

            if pr is None:
                logging.error(f'人工校正仪表: {device_id}, event_time: {event_time}，找不到pr! created_at={pr_created_at}')
                return Response()

            # 更新pr值
            origin_pr_value = pr.value

            # 校验
            diff = float(value) - float(origin_pr_value)
            logging.debug(f'人工校正仪表: {device_id}, event_time: {event_time}, 原pr: {origin_pr_value}，校正值: {value}，差值: {diff}')

            if diff < 0:
                logging.error(f'人工校正仪表: {device_id}, event_time: {event_time}，pr diff 小于0! created_at={pr_created_at}')
                raise ValueError('人工校正失败：校正值小于原值，禁止操作')

            pr.value = value
            pr.save()

            ParamRecord.objects.filter(device_id=device_id, identifier=identifier, created_at__gte=pr_created_at).update(value=value)

            pr_begin_at = min((now - datetime.timedelta(hours=3)), event_time)
            pr_begin_at = pr_begin_at.replace(minute=0, second=0, microsecond=0)
            while pr_begin_at <= now:
                pr_redis_key = f'param_record_{pr_begin_at.strftime("%Y%m%d%H")}:{device_id}_{identifier}'
                RedisHelper.delete(device.project_id, pr_redis_key)
                pr_begin_at = pr_begin_at + datetime.timedelta(hours=1)

            # 小时统计
            dhs_created_at = event_time.strftime('%Y-%m-%d %H:59:59')
            dhs = DeviceHourlyStat.objects.filter(device_id=device_id, identifier=identifier, created_at=dhs_created_at).first()
            if dhs is not None:
                origin_dhs_avg = dhs.avg
                dhs.avg = round(float(dhs.avg) + diff, 2)
                dhs.save()
                logging.info(f'人工校正仪表: {device_id}, 更新小时统计 created_at: {dhs_created_at}，原: {origin_dhs_avg}, 新: {dhs.avg}')

            # 日统计
            if event_time.hour == 23:
                dds_created_at = event_time.strftime('%Y-%m-%d %H:59:59')
                dds = DeviceDailyStat.objects.filter(device_id=device_id, identifier=identifier, created_at=dds_created_at).first()
                if dds is not None:
                    origin_dds_avg = dds.avg
                    dds.avg = round(float(dds.avg) + diff, 2)
                    dds.save()
                    logging.info(f'人工校正仪表: {device_id}, 更新日统计 created_at: {dds_created_at}，原: {origin_dds_avg}, 新: {dds.avg}')

            # 月统计
            if (event_time + datetime.timedelta(days=1)).day == 1 and event_time.hour == 23:
                dms_created_at = event_time.strftime('%Y-%m-%d %H:59:59')
                dms = DeviceMonthlyStat.objects.filter(device_id=device_id, identifier=identifier, created_at=dms_created_at).first()
                if dms is not None:
                    origin_dms_avg = dms.avg
                    dms.avg = round(float(dms.avg) + diff, 2)
                    dms.save()
                    logging.info(f'人工校正仪表: {device_id}, 更新月统计 created_at: {dms_created_at}，原: {origin_dms_avg}, 新: {dms.avg}')

            # 维度统计
            terminal = Terminal.objects.get(device_id=device_id)
            ta = TerminalAttribute.objects.get_by_idf(terminal, identifier)
            if ta is not None:
                dimension_attrs = DimensionAttribute.objects.filter(Q(ta_ids__startswith=f'{ta.id,}') | Q(ta_ids__endswith=f',{ta.id}') |
                                                                    Q(ta_ids__icontains=f',{ta.id},') | Q(ta_ids=f'{ta.id}'))
                for da in dimension_attrs:
                    formula_das = DimensionAttribute.objects.filter(ta_ids__isnull=True, formula__icontains=f'{{{{{da.id}}}}}')

                    # 小时统计
                    dhs_created_at = event_time.strftime('%Y-%m-%d %H:59:59')
                    dhs = DimensionHourlyStat.objects.filter(dimension_attribute_id=da.id, created_at=dhs_created_at).first()
                    if dhs is not None:
                        origin_dhs_avg = dhs.avg
                        dhs.avg = round(float(dhs.avg) + diff, 2)
                        dhs.save()
                        logging.info(f'人工校正仪表: {device_id}, 维度属性: {da.id}-{da.name}, '
                                     f'更新小时统计 created_at: {dhs_created_at}，原: {origin_dhs_avg}, 新: {dhs.avg}')

                        for formula_da in formula_das:
                            dhs = DimensionHourlyStat.objects.filter(dimension_attribute_id=formula_da.id, created_at=dhs_created_at).first()
                            if dhs is not None:
                                origin_dhs_avg = dhs.avg
                                dhs.avg = round(float(dhs.avg) + diff, 2)
                                dhs.save()
                                logging.info(f'人工校正仪表: {device_id}, 维度属性: {formula_da.id}-{formula_da.name}, '
                                             f'更新小时统计 created_at: {dhs_created_at}，原: {origin_dhs_avg}, 新: {dhs.avg}')

                    # 日统计
                    if event_time.hour == 23:
                        dds_created_at = event_time.strftime('%Y-%m-%d %H:59:59')
                        dds = DimensionDailyStat.objects.filter(dimension_attribute_id=da.id, created_at=dds_created_at).first()
                        if dds is not None:
                            origin_dds_avg = dds.avg
                            dds.avg = round(float(dds.avg) + diff, 2)
                            dds.save()
                            logging.info(f'人工校正仪表: {device_id}, 维度属性: {da.id}-{da.name}, '
                                         f'更新日统计 created_at: {dds_created_at}，原: {origin_dds_avg}, 新: {dds.avg}')

                            for formula_da in formula_das:
                                dds = DimensionDailyStat.objects.filter(dimension_attribute_id=formula_da.id, created_at=dds_created_at).first()
                                if dds is not None:
                                    origin_dds_avg = dds.avg
                                    dds.avg = round(float(dds.avg) + diff, 2)
                                    dds.save()
                                    logging.info(f'人工校正仪表: {device_id}, 维度属性: {formula_da.id}-{formula_da.name}, '
                                                 f'更新日统计 created_at: {dds_created_at}，原: {origin_dds_avg}, 新: {dds.avg}')

                    # 月统计
                    if (event_time + datetime.timedelta(days=1)).day == 1 and event_time.hour == 23:
                        dms_created_at = event_time.strftime('%Y-%m-%d %H:59:59')
                        dms = DimensionMonthlyStat.objects.filter(dimension_attribute_id=da.id, created_at=dms_created_at).first()
                        if dms is not None:
                            origin_dms_avg = dms.avg
                            dms.avg = round(float(dms.avg) + diff, 2)
                            dms.save()
                            logging.info(f'人工校正仪表: {device_id}, 维度属性: {da.id}-{da.name}, '
                                         f'更新月统计 created_at: {dms_created_at}，原: {origin_dms_avg}, 新: {dms.avg}')

                            for formula_da in formula_das:
                                dms = DimensionMonthlyStat.objects.filter(dimension_attribute_id=formula_da.id, created_at=dms_created_at).first()
                                if dms is not None:
                                    origin_dms_avg = dms.avg
                                    dms.avg = round(float(dms.avg) + diff, 2)
                                    dms.save()
                                    logging.info(f'人工校正仪表: {device_id}, 维度属性: {formula_da.id}-{formula_da.name}, '
                                                 f'更新月统计 created_at: {dms_created_at}，原: {origin_dms_avg}, 新: {dms.avg}')

            device.online = True
            device.save()
            terminal.online = True
            terminal.save()

            return Response({
                'status': status.HTTP_200_OK,
                'data': None
            })
