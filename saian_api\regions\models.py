from django.db import models

# Create your models here.
class Region(models.Model):
    # 行政代号
    id = models.IntegerField(primary_key=True)
    # 地区名字
    name = models.CharField(max_length=255)
    # 缩写
    short_name = models.CharField(max_length=255)
    # 省市区 位于哪一级
    level = models.IntegerField()
    # 天气接口所用的位置编号
    weather_code = models.CharField(max_length=255)

    class Meta:
        db_table = 'web_regions'

    def __str__(self):
        return self.name