"""
  定时任务检查设备是否到达保养时间，如果需要保养则更新设备needs_m为true
  当前只支持盘管
"""
import datetime
import traceback

from django.core.management import CommandError, BaseCommand

from saian_api.device.models import Device
from saian_api.message.models import Message
from saian_api.notifi.models import Notification
from saian_api.project.models import Project
from saian_api.scheduledtask.utils import get_projects, set_global_db

class Command(BaseCommand):
    help = "设备保养检查"

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目ID列表
        projects = get_projects(self, options)
        now = datetime.datetime.now()

        for project_id in projects:
            try:
                # 设置全局数据库
                set_global_db(project_id)
                project = Project.objects.get(pk=project_id)

                self.stdout.write(f"设备保养检查开始: {project.name}", ending='\n')

                devices = Device.objects.filter(device_type_id=14, needs_m=False, project_id=project_id)

                if devices.exists():
                    for device in devices:
                        if device.cal_run_time() > project.fan_time * 0.8:
                            device.needs_m = True
                            device.save()

                            Message.objects.maintain(device)
                            data = {
                                'content': f'设备保养通知: 设备【{device.id}-{device.nick_name}】需要保养',
                                'device_id': device.id,
                                'device_nickname': device.nick_name
                            }
                            Notification.objects.notify(10, device, data)

                self.stdout.write(f"设备保养检查任务完成: {project.name}", ending='\n')

            except CommandError:
                self.stderr.write(f"运行'设备保养检查'任务失败，项目ID：{project_id}，命令参数不合法！", ending='\n')
                continue
            except Exception as e:
                self.stderr.write(f"运行'设备保养检查'任务失败，项目ID：{project_id}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
                continue
