import json
import logging
import asyncio

from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
# from django.core import exceptions
from django.db import models, transaction
from django.utils.module_loading import import_string
from rest_framework import exceptions

import numpy as np
from rest_framework import status

from saian_api.devdefine.models import AttributePrototype
from saian_api.device.models import Device, DeviceEvent, DeviceAttribute, DeviceTimer
from saian_api.group.models import Group, ActionLog, AcStrategies, GroupDevice
from saian_api.issue.models import DeviceIssue
from saian_api.project.models import Project
from saian_api.regions.models import Region
from saian_api.terminal.models import Terminal, TerminalAttribute
from saian_api.user.models import WebUser
from saian_api.utils.httpapi.weather import WeatherApi
from saian_api.utils.inthttpapi.device import AdminDeviceApi

logger = logging.getLogger('django')

loop = asyncio.new_event_loop()
asyncio.set_event_loop(loop)

# Create your models here.
"""
  联动规则
"""

class LinkageRuleManager(models.Manager):
    # @transaction.atomic()
    def create_rule(self, request, serializer):
        project_id = request.user.get('project_id')

        code = request.data.get('code', None)

        if code is not None and code != '':
            # 校验代码
            self.compile_code(code)

        with transaction.atomic(using=f'prj{project_id}db'):
            rule = serializer.save(project_id=request.user['project_id'], web_user_id=request.user['id'])

            # 联动目标设定
            targets = request.data.get('targets')
            if targets is not None:
                LinkageTarget.objects.create_by_rule(rule, targets, project_id)

            # 联动变量设定
            vars = request.data.get('variables')
            if vars is not None:
                for linkage_var in vars:
                    # 规则变量
                    if linkage_var['scope_type'] == 30:
                        from .serializers import LinkageVarSerializer

                        linkage_var['linkage_rule'] = rule.id

                        var_serializer = LinkageVarSerializer(data=linkage_var)
                        if var_serializer.is_valid(raise_exception=True):
                            var_serializer.save()

                    # 项目变量
                    elif linkage_var['scope_type'] == 20:
                        name = linkage_var['name']
                        # 项目级变量名必须以"P_"开头
                        if name is None or not name.startswith('P_'):
                            raise exceptions.ValidationError(detail={'detail': 'Name must start with P_ '})

                        data = {
                            'name': name,
                            'mac': linkage_var['mac'],
                            'identifier': linkage_var['identifier'],
                            'var_type': 10,
                            'project': project_id
                        }
                        SnpVar.objects.create_by_rule(rule.id, data, linkage_var['is_trigger'])

                    # 设备变量
                    elif linkage_var['scope_type'] == 40:
                        name = linkage_var['name']
                        # 设备在线状态变量必须以"D_"开头
                        if name is None or not name.startswith('D_'):
                            raise exceptions.ValidationError(detail={'detail': 'Name must start with D_ '})

                        data = {
                            'name': name,
                            'mac': linkage_var['mac'],
                            'identifier': linkage_var['identifier'],
                            'var_type': 10,
                            'project': project_id
                        }
                        SnpVar.objects.create_by_rule(rule.id, data, linkage_var['is_trigger'])

            # 联动执行设定
            exe_setting = request.data.get('exe_setting', None)
            trigger_type = request.data.get('trigger_type', None)
            if exe_setting is not None and trigger_type is not None and trigger_type == 10:  # 但触发方式是"定时"时，才创建 LinkageTrigger
                LinkageTrigger.objects.create_by_rule(rule, exe_setting)

            return rule

    # 对用户编辑的代码预编译
    def compile_code(self, code):
        key_words = ['try', 'except', 'finally', 'raise', 'import', 'from', 'as', 'def', 'return', 'class', 'del',
                     'global', 'nonlocal']
        for key in key_words:
            if key in code:
                raise exceptions.ValidationError(detail={'detail': f'预留关键字：{str(key_words)}，不允许使用！'})

        if 'result' not in code and 'results' not in code:
            raise exceptions.ValidationError(detail={'detail': f'代码必须包含结果（result）或结果集（results）！'})

        return None

    # 变量上报触发规则处理
    def data_push(self, device, data):
        if data is not None:

            # 变量上报,联动规则应用于分组设备
            linkage_rules = LinkageRule.objects.filter(group_id__isnull=False, trigger_type=20, enabled=True)
            for rule in linkage_rules:
                group = rule.group
                if device in group.devices():
                    linkage_target = LinkageTarget.objects.filter(linkage_rule=rule, target_type=ContentType.objects.get_for_model(Device)).first()
                    if linkage_target is not None:
                        linkage_target_device = linkage_target.target
                        for lv in LinkageVar.objects.filter(linkage_rule=rule, mac=linkage_target_device.mac, is_trigger=True):
                            if lv.identifier in data:
                                rule.apply_to_group(lv, for_the_target=device)

            # 规则级变量
            lvs = LinkageVar.objects.filter(mac=device.mac, is_trigger=True)
            for lv in lvs:
                if lv.identifier in data:
                    if lv.linkage_rule.enabled:
                        lv.linkage_rule.execute(lv, False)

            # 项目或系统变量
            lnvs = LinkageSnpVar.objects.filter(is_trigger=True)
            for lnv in lnvs:
                snp_vars = SnpVar.objects.filter(id=lnv.snp_var_id, mac=device.mac)
                for snp_var in snp_vars:
                    if snp_var.identifier in data:
                        if lnv.linkage_rule.enabled:
                            lnv.linkage_rule.execute(lnv, False)


class LinkageRule(models.Model):
    # 所属项目
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    # 创建的web用户
    web_user = models.ForeignKey(WebUser, on_delete=models.CASCADE)
    # 名字
    name = models.CharField(max_length=255)
    # 触发方式，10-定时，20-当联动参数有更新时，30-手动，40-系统总控设定时
    trigger_type = models.IntegerField()
    # 源码
    code = models.TextField(null=True)
    # 是否启用
    enabled = models.BooleanField(default=False)
    # 备注
    remark = models.CharField(max_length=255, null=True)
    # 是否在快捷操作显示
    show_in_so = models.BooleanField(default=False)
    # 在快捷操作显示的名字
    so_name = models.CharField(max_length=255, null=True)
    # 设备联动规则应用到分组
    group = models.ForeignKey(Group, on_delete=models.CASCADE, null=True)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'linkage_rules'
        ordering = ['-created_at']

    objects = LinkageRuleManager()

    # @transaction.atomic()
    def update_rule(self, request, serializer):
        project_id = request.user.get('project_id')

        with transaction.atomic(using=f'prj{project_id}db'):
            pass

    # ==================== 联动代码的辅助函数 =======================
    def get_device_online(self, mac):
        """根据设备在线状态"""
        device = Device.objects.filter(mac=mac).first()
        if not device:
            return False
        return device.online

    def set_value(self, var, value, is_trial=False):
        """直接更新参数值"""
        if is_trial:
            return

        prefix = var[:2]
        da = None
        device = None

        # 系统变量 或 项目变量 或 设备变量
        if prefix == 'S_' or prefix == 'P_' or prefix == 'D_':
            snp_var = SnpVar.objects.filter(var_type=10, name=var, mac__isnull=False, identifier__isnull=False).last()
            if snp_var is not None:
                device = Device.objects.filter(mac=snp_var.mac, project_id__isnull=False).last()
                if device is not None:
                    da = DeviceAttribute.objects.get_by_idf(device, snp_var.identifier)
        # 规则变量
        else:
            name = var
            if '.' in var:
                name = var.split('.')[0]
            lv = LinkageVar.objects.filter(linkage_rule_id=self.id, name=name, identifier__isnull=False, mac__isnull=False).first()
            if lv is not None:
                device = Device.objects.filter(mac=lv.mac, project_id__isnull=False).first()
                if device is not None:
                    da = DeviceAttribute.objects.get_by_idf(device, lv.identifier)

        # 更新参数值
        if da is not None:
            da.value = value
            da.save()
            DeviceAttribute.objects.save_to_redis(device, da)

    def get_weather(self):
        """查询项目所在地的温湿度"""
        project = Project.objects.filter(admin_region_id__isnull=False).first()
        region = Region.objects.get(pk=project.admin_region_id)
        result = WeatherApi.get_weather(region.weather_code)
        temp = result.get('tem', None)
        hum = result.get('humidity', None)
        pressure = result.get('pressure', None)
        if temp:
            temp = float(temp)
        if hum:
            hum = float(hum.replace('%', ''))
        if pressure:
            pressure = float(pressure)

        return temp, hum, pressure

    def calc_wetbulb_temp(self, temp, humidity, pressure):
        """计算湿球温度"""
        wetbulb, _, _ = WeatherApi.web_bulb(np.array([float(temp)]), np.array([float(pressure) * 1000]), np.array([float(humidity)]), 1)
        if len(wetbulb) and wetbulb[0] != np.nan:
            wetbulb_temp = wetbulb[0]
            return round(float(wetbulb_temp), 2)
        return None

    def handle_issue(self, issue_type, glossary, recovery=False):
        """联动触发故障报警"""
        lts = LinkageTarget.objects.filter(linkage_rule_id=self.id)
        target_devices = set()
        if lts.count() == 1:
            link_target = lts.first()
            target = link_target.target

            if isinstance(target, Device):
                target_devices.add(target)

                if self.group_id is not None:
                    device_ids = list(GroupDevice.objects.filter(group_id=self.group_id).values_list('object_id', flat=True))
                    devices = list(Device.objects.filter(id__in=device_ids))
                    target_devices.update(devices)

        elif lts.count() > 1:
            for link_target in lts:
                target = link_target.target

                if isinstance(target, Device):
                    target_devices.add(target)

        if issue_type == 'alarm':
            for device in target_devices:
                if recovery:
                    DeviceIssue.objects.recover_alarm(device, glossary)
                else:
                    DeviceIssue.objects.add_alarm(device, glossary)
        elif issue_type == 'fault':
            for device in target_devices:
                if recovery:
                    DeviceIssue.objects.recover_fault(device, glossary)
                else:
                    DeviceIssue.objects.add_fault(device, glossary)

    # ============================================================

    def get_admin_object(self, pk) -> Device | None:
        device = None
        r = AdminDeviceApi.get_device(pk)
        if r.status_code == status.HTTP_200_OK:
            data = r.json().get('row')
            # 过滤不在 Device 类声明的字段
            device_fields = {f.name if not f.is_relation else f.attname for f in Device._meta.fields}
            filtered_data = {k: v for k, v in data.items() if k in device_fields}
            device = Device(**filtered_data)
        else:
            logging.error(f'从Admin3查询设备失败，status_code: {r.status_code}, text: {r.text}')

        return device

    def parse_code(self, linkage_target_device=None, group_target=None, is_trial=False):
        import re

        new_str = self.code

        if is_trial:
            # 试执行注释掉函数，
            comment_pattern = re.compile(r'^self\.set\w*', re.MULTILINE)
            for result in comment_pattern.findall(self.code):
                new_str = re.sub(result, f'#{result}', new_str)

        # 函数参数解析
        da_pattern = re.compile(r'<<\w*\.*\w*>>')
        for result in da_pattern.findall(self.code):
            value = result[2:-2]
            new_str = re.sub(result, f"'{value}'", new_str)

        # 参数变量解析
        pattern = re.compile(r'{{\w*\.*\w*}}')

        results = pattern.findall(self.code)
        for result in results:
            v_name = result[2:len(result) - 2]
            v_pre = v_name[0:2]

            # 系统变量
            if v_pre == 'S_':
                value = SnpVar.objects.var_value(v_name, self.project)
                new_str = re.sub(result, f"'{value}'", new_str)

            elif v_pre == 'P_':
                # 项目变量
                snpv = SnpVar.objects.filter(project_id=self.project.id, name=v_name).first()

                if snpv is not None and snpv.var_type == 10:
                    device = Device.objects.filter(mac=snpv.mac).first()
                    if device is not None:
                        value = device.get_value_by_idf(snpv.identifier)
                        new_str = re.sub(result, f"'{value}'", new_str)

            elif v_pre == 'T_':
                # 获取目标设备对象的属性
                # 例如 T_id 获取目标对象的id; T_mac 获取目标对象的mac
                if group_target is not None:
                    target = group_target
                elif linkage_target_device is not None:
                    target = linkage_target_device
                else:
                    target = self.linkagetarget_set.first().target
                attr_name = v_name.replace(v_pre, '')
                value = getattr(target, attr_name)
                new_str = re.sub(result, f"'{value}'", new_str)

            elif v_pre == 'D_':
                # 设备变量
                snpv = SnpVar.objects.filter(project_id=self.project.id, name=v_name).first()

                if snpv is not None and snpv.var_type == 40:
                    device = Device.objects.filter(mac=snpv.mac).first()
                    if device is not None:
                        value = SnpVar.objects.var_value(v_name, self.project, device)
                        new_str = re.sub(result, f"'{value}'", new_str)

            else:
                # 规则变量
                name = v_name
                if '.' in v_name:
                    name = v_name.split('.')[0]
                lv = LinkageVar.objects.filter(linkage_rule_id=self.id, name=name).first()
                if lv is not None:
                    if lv.var_type == 10:
                        device = Device.objects.filter(mac=lv.mac)
                        if not device.exists():
                            device = self.get_admin_object(lv.mac)
                        else:
                            device = device.first()

                        if device is not None:
                            if group_target is not None and linkage_target_device is not None and linkage_target_device.mac == lv.mac:
                                value = group_target.get_value_by_idf(lv.identifier)
                            else:
                                value = device.get_value_by_idf(lv.identifier, f'prj{device.project_id}db')
                            new_str = re.sub(result, f"'{value}'", new_str)
                    else:
                        value = lv.value
                        # 90-时间段处理
                        if lv.data_type == 90:
                            value = getattr(lv, v_name.split('.')[1])
                            new_str = re.sub(result, f"'{value}'", new_str)
                        # 10-布尔值处理
                        elif lv.data_type == 10:
                            value = json.loads(value)
                            new_str = re.sub(result, f"{value}", new_str)
                        else:
                            new_str = re.sub(result, f"{value}", new_str)

        return new_str
    
    

    def apply_to_group(self, executor, for_the_target=None):
        """
            executor: 发起对象，有 WebUser，LinkageTarget, LinkageVar
            for_the_target: 指定目标设备，仅联动规则应用于分组设备时，分组内的设备数据上报时可用
                            即上报数据的设备，而不是联动目标的设备
        """
        linkage_target = LinkageTarget.objects.filter(linkage_rule=self, target_type=ContentType.objects.get_for_model(Device)).first()
        if linkage_target is None:
            return
        linkage_target_device = linkage_target.target

        if for_the_target is None:
            group_devices = list(self.group.devices())

            if linkage_target_device not in group_devices:
                group_devices.append(linkage_target_device)
        else:
            group_devices = [for_the_target]

        for target in group_devices:
            # 如果分组设备与联动目标设备的设备类型不一致，则跳过
            if target.device_prototype_id != linkage_target_device.device_prototype_id:
                continue
            code = self.parse_code(linkage_target_device=linkage_target_device, group_target=target)
            try:
                import datetime
                from saian_api.utils.tools import to_int, is_number
                from chinese_calendar import is_workday
                exec(code, None, locals())
            except Exception as e:
                logger.warning(e.__str__())
                raise exceptions.ValidationError(detail={'detail': e.__str__()})

            data = locals().get('result')
            if data is None:
                continue

            try:
                json.dumps(data)

                executor_type = ContentType.objects.get_for_model(executor)
                action_log = ActionLog.objects.create(
                    executor_id=executor.id,
                    executor_type=executor_type,
                    actor_name=executor_type.model_class().__name__,
                    action_name=self.name,
                    values=json.dumps(data, ensure_ascii=False),
                    op_type=20,
                    op_id=self.id
                )

                linkage_attributes = linkage_target.linkageattribute_set.all()
                if linkage_attributes.exists():
                    filtered_changes = {}
                    for la in linkage_attributes:
                        idf = la.attribute.identifier
                        if idf in data and data[idf] is not None:
                            filtered_changes[idf] = data[idf]

                    if len(filtered_changes) == 0:
                        return

                    target.send_ctrls(self, filtered_changes, True, action_log.id)

            except ValueError as e:
                raise exceptions.ValidationError(detail={'detail': '输出结果不符合要求！'})

    # 执行规则

    def execute(self, executor, is_trial):
        """
            执行规则
            executor: 发起对象，有 WebUser，LinkageTarget, LinkageVar
            is_trial: 是否试执行
        """
        # result = None
        # 联动代码需要用到的库和函数
        import datetime
        from saian_api.utils.tools import to_int, is_number
        from chinese_calendar import is_workday

        if self.code is not None and self.code != '':
            if self.group_id is not None and not is_trial:
                self.apply_to_group(executor)
                return

            code = self.parse_code(is_trial=is_trial)
            try:
                # exec('result = 32\nresult = self.get_value(\'12345\', \'test\')', None, locals())
                exec(code, None, locals())
            except Exception as e:
                logger.warning(e.__str__())
                raise exceptions.ValidationError(detail={'detail': e.__str__()})

            data = locals().get('result')
            if is_trial:
                return data
            else:
                # 执行联动操作
                # 如果 data 是 None，则不执行
                if data is None:
                    return
                try:
                    # result在exec的代码中定义，存入本地作用域
                    # [{"target_id": 1, "target_type": "Group", "params": {"TargetTemp": 23, "FCUWS": 1}}]
                    # {"TargetTemp": 23, "FCUWS": 1}

                    # 校验结果是否是json格式
                    json.dumps(data)

                    executor_type = ContentType.objects.get_for_model(executor)
                    action_log = ActionLog.objects.create(
                        executor_id=executor.id,
                        executor_type=executor_type,
                        actor_name=executor_type.model_class().__name__,
                        action_name=self.name,
                        values=json.dumps(data, ensure_ascii=False),
                        op_type=20,
                        op_id=self.id
                    )
                    # 处理列表格式的结果
                    if isinstance(data, list):
                        for item in data:
                            # target_type = ContentType.objects.get(app_label=item['target_type'].lower(), model='linkageattribute')
                            target = LinkageTarget.objects.get(pk=item['target_id'])
                            loop.run_until_complete(self.send_changes(target, item['params'], action_log.id))

                    else:
                        targets = self.linkagetarget_set.all()
                        # 可以设置多个目标的多个参数值
                        # 发送指令
                        for target in targets:
                            loop.run_until_complete(self.send_changes(target, data, action_log.id))

                except ValueError as e:
                    raise exceptions.ValidationError(detail={'detail': '输出结果不符合要求！'})
                # except Exception as e:
                # raise exceptions.APIException(detail={'detail': '处理异常，请检查！'})
        else:
            raise exceptions.ValidationError(detail={'detail': '代码为空！'})

    # 向目标下发参数
    # changes为规则执行输出的结果，为json格式，包含需要下发的目标参数
    async def send_changes(self, target, changes, action_log_id):
        linkage_attributes = target.linkageattribute_set.all()
        if len(linkage_attributes) != 0:
            filter_changes = {}
            for la in linkage_attributes:
                idf = str(la.attribute.identifier)
                # 值为None的参数不下
                if idf in changes and changes[idf] is not None:
                    value = changes[idf]
                    filter_changes[idf] = value

            # 如果没参数需要下发则直接返回
            if len(filter_changes) == 0:
                return

            target_type = ContentType.objects.get_for_model(target.target)
            if target_type.name == 'device':
                device = Device.objects.get(pk=target.target_id)
                device.send_ctrls(self, filter_changes, True, action_log_id)

            if target_type.name == 'group':
                group = Group.objects.get(pk=target.target_id)
                # 如果跨类型分组，则需要替换为真实设备的idf
                if group.is_cross():
                    for gd in group.groupdevice_set.all():
                        device = None
                        if gd.content_type.name == 'device':
                            device = Device.objects.get(pk=gd.object_id)
                        elif gd.content_type.name == 'terminal':
                            try:
                                terminal = Terminal.objects.get(pk=gd.object_id)
                                device = terminal.device
                            except Terminal.DoesNotExist:
                                device = None

                        if device is not None:
                            # device = Device.objects.get(pk=gd.object_id)
                            device_changes = {}
                            for la in linkage_attributes:
                                for attr in la.attribute.attribute_prototypes.all():
                                    if device.device_prototype == attr.device_prototype:
                                        device_changes[attr.identifier] = filter_changes[la.attribute.identifier]

                            if len(device_changes) != 0:
                                device.send_ctrls(self, device_changes, True, action_log_id)

                else:
                    for gd in group.groupdevice_set.all():
                        if gd.content_type.name == 'device':
                            device = Device.objects.get(pk=gd.object_id)
                            device.send_ctrls(self, filter_changes, True, action_log_id)
                        elif gd.content_type.name == 'terminal':
                            terminal = Terminal.objects.get(pk=gd.object_id)
                            device = terminal.device
                            device.send_ctrls(self, filter_changes, True, action_log_id)

    # 兼容旧版下发单个值到设备
    def send_single_changes(self, value):
        targets = self.linkagetarget_set.all()
        for target in targets:
            try:
                target_type = ContentType.objects.get_for_model(target)
                if target_type == 'device':
                    device = Device.objects.get(pk=target.target_id)
                    attributes = target.linkagetarget_set.all()
                    if len(attributes) == 1:
                        idf = None
                        attr = attributes[0]
                        attr_type = ContentType.objects.get_for_model(attr)
                        if attr_type == 'crossattribute':
                            idf = attr.cross_attribute.identifier

                        if attr_type == 'attributeprototype':
                            idf = attr.attribute_prototype.identifier

                        changes = {
                            f'"{idf}"': value
                        }
                        device.send_ctrls(changes)

                if target_type == 'group':
                    group = Group.objects.get(pk=target.target_id)
                    attributes = target.linkagetarget_set.all()
                    if len(attributes) == 1:
                        pass

            except Exception as e:
                logger.error(e.with_traceback)

    # 根据设备idf查询当前值
    @classmethod
    def get_value(cls, mac, idf):
        device = Device.objects.get(mac=mac)
        return device.get_value_by_idf(idf)

    # 根据设备idf查询上一次上报值
    @classmethod
    def get_last_value(cls, mac, idf, last_count=1):
        events = DeviceEvent.objects.filter(mac=mac, data__contains=idf).order_by('-created_at')
        if events.exists() and events.count() > last_count:
            try:
                event = events[last_count]
                data = json.loads(event.data)
                return data[idf]
            except Exception as e:
                logger.error(f'联动获取历史参数值出错: {e.__str__()}')
                return None
        return None

    # 同步写

    def sync_write(self, device, idf):
        pass

    # 同步读
    def sync_read(self, device, idf):
        pass

    # 异步写
    def async_write(self, device, idf):
        pass

    # 延迟写（异步）
    def delay_write(self, device, idf, time):
        pass


"""
  联动操作目标定义
"""


class LinkageTargetManager(models.Manager):
    # @transaction.atomic()
    def create_by_rule(self, rule, targets, project_id):
        with transaction.atomic(using=f'prj{project_id}db'):
            for target in targets:
                self.create_single(rule, target, project_id)

    def create_single(self, rule, target, project_id):
        attrs = {
            'target_id': target['target_id'],
            'target_type': target['target_type'],
            'rule_id': rule.id,
            'idfs': target['idfs']
        }

        # 校验attribute并将attribute_id和attribute_type_id存入attributes
        self.validate_attrs(attrs)

        if int(target['target_type']) == 10:
            target_type = ContentType.objects.get(app_label='group', model='group')

        if int(target['target_type']) == 20:
            target_type = ContentType.objects.get(app_label='device', model='device')

        linkage_targets = self.filter(target_id=target['target_id'], linkage_rule_id=rule.id, target_type_id=target_type.id)

        if len(linkage_targets) != 0:
            raise exceptions.ValidationError('Target already exist!')

        linkage_target = self.create(
            target_id=target['target_id'],
            linkage_rule_id=rule.id,
            target_type_id=target_type.id
        )

        if attrs['attributes'] is not None:
            for attribute in attrs['attributes']:
                LinkageAttribute.objects.create(
                    attribute_id=attribute['attribute_id'],
                    linkage_target_id=linkage_target.id,
                    attribute_type_id=attribute['attribute_type_id']
                )

        return linkage_target

    # 校验参数，并设置attributes的id和attribute_type
    def validate_attrs(self, attrs):
        # 根据目标类型校验目标是否存在
        # 目标是分组时
        if int(attrs['target_type']) == 10:
            target = 'saian_api.group.models.Group'

        # 目标是设备时
        if int(attrs['target_type']) == 20:
            target = 'saian_api.device.models.Device'

        target_model = import_string(target)
        if target_model is not None:
            if target_model.objects.filter(id=attrs['target_id']).exists() is not True:
                raise exceptions.ValidationError("Target not exist!")
        else:
            raise exceptions.ValidationError("Target model not exist!")

        # 校验联动规则是否存在
        linkage_rule = LinkageRule.objects.filter(id=attrs['rule_id'])
        if linkage_rule.exists() is not True:
            raise exceptions.ValidationError("Linkage rule not exist!")

        # 如果设置了参数标识，校验参数标识是否存在
        if attrs['idfs'] is not None:
            attrs['attributes'] = []

            for identifier in attrs['idfs']:
                # 目标是分组
                if int(attrs['target_type']) == 10:
                    group = Group.objects.filter(id=attrs['target_id']).first()
                    # 如果分组是跨类型分组，则校验跨设备类型参数
                    # 否则校验设备类型参数
                    if group.is_cross():
                        attribute = CrossAttribute.objects.filter(identifier=identifier,
                                                                  project_id=linkage_rule.first().project_id)
                        if attribute.exists() is not True:
                            raise exceptions.ValidationError("CrossAttribute (%s) not exist!" % (identifier))

                        attribute_type = ContentType.objects.get(app_label='linkage', model='crossattribute')

                    else:
                        # 分组的设备类型
                        gdp = group.device_prototype()
                        attribute = gdp.attributeprototype_set.filter(identifier=identifier)

                        if attribute.exists() is not True:
                            raise exceptions.ValidationError("Attribute (%s) not exist!" % (identifier))

                        attribute_type = ContentType.objects.get(app_label='devdefine', model='attributeprototype')

                # 目标是设备，只需校验设备类型参数
                if int(attrs['target_type']) == 20:
                    device = Device.objects.filter(id=attrs['target_id']).first()
                    attribute = AttributePrototype.objects.filter(identifier=identifier,
                                                                  device_prototype_id=device.device_prototype_id)

                    if attribute.exists() is not True:
                        raise exceptions.ValidationError("Attribute (%s) not exist!" % (identifier))

                    attribute_type = ContentType.objects.get(app_label='devdefine', model='attributeprototype')

                # 校验后返回idf对应的id，方便后续使用
                attrs['attributes'].append({
                    'attribute_id': attribute.first().id,
                    'attribute_type_id': attribute_type.id
                })
        return attrs


class LinkageTarget(models.Model):
    # 联动目标，可能是分组、或者单个设备
    target_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    target_id = models.PositiveBigIntegerField()
    target = GenericForeignKey('target_type', 'target_id')

    # 所属的rule
    linkage_rule = models.ForeignKey(LinkageRule, on_delete=models.CASCADE)

    class Meta:
        db_table = 'linkage_targets'

    objects = LinkageTargetManager()

    # 更新目标
    def update_by_rule(self, rule, target):
        attrs = {
            'target_id': target['target_id'],
            'target_type': target['target_type'],
            'rule_id': rule.id,
            'idfs': target['idfs']
        }
        LinkageTarget.objects.validate_attrs(attrs)

        if int(target['target_type']) == 10:
            target_type = ContentType.objects.get(app_label='group', model='group')

        if int(target['target_type']) == 20:
            target_type = ContentType.objects.get(app_label='device', model='device')

        with transaction.atomic(using=f'prj{self.linkage_rule.project_id}db'):
            self.target_id = target['target_id']
            self.linkage_rule_id = rule.id
            self.target_type_id = target_type.id
            self.save()

            # 新增参数
            if attrs['attributes'] is not None:
                for attribute in attrs['attributes']:
                    attres = LinkageAttribute.objects.filter(linkage_target_id=self.id, attribute_id=attribute['attribute_id'],
                                                             attribute_type_id=attribute['attribute_type_id'])

                    if len(attres) == 0:
                        LinkageAttribute.objects.create(
                            attribute_id=attribute['attribute_id'],
                            linkage_target_id=self.id,
                            attribute_type_id=attribute['attribute_type_id']
                        )

            # 删除旧参数
            for exist_attr in self.linkageattribute_set.all():
                if exist_attr.attribute.identifier not in target['idfs']:
                    exist_attr.delete()

    def target_to_json(self):
        name, type = '', ''

        target_type = ContentType.objects.get(pk=self.target_type_id)

        # 设备分组
        if target_type.name == 'group':
            group = Group.objects.get(pk=self.target_id)
            name, type = group.name, 10
            identifiers = self.linkageattribute_set.all()

        # 设备
        elif target_type.name == 'device':
            device = Device.objects.get(pk=self.target_id)
            name, type = device.nick_name, 20

            identifiers = self.linkageattribute_set.all()

        else:
            identifiers = []

        idfs = []

        for idf in identifiers:
            if idf is None or idf.attribute is None:
                continue
            idfs.append({
                'id': idf.id,
                'name': idf.attribute.name,
                'identifier': idf.attribute.identifier
            })

        return {
            'id': self.id,
            'name': name,
            'target_id': self.target.id,
            'target_type': type,
            'attributes': idfs
        }


"""
  联动操作目标属性定义
"""


class LinkageAttributeManager(models.Manager):
    # 参数校验过后的新增
    # attrs: { 'linkage_target_id', 'identifier', 'target_type', 'target_id'}
    def create_validated(self, attrs):
        # 目标是分组
        if int(attrs['target_type']) == 10:
            group = Group.objects.filter(id=attrs['target_id']).first()

            if group.is_cross():
                content_type = ContentType.objects.get(app_label='linkage', model='crossattribute')
                attribute = CrossAttribute.objects.get(identifier=attrs['identifier'], project_id=group.project_id)
            else:
                content_type = ContentType.objects.get(app_label='devdefine', model='attributeprototype')
                gdp = group.device_prototype()
                attribute = gdp.attributeprototype_set.get(identifier=attrs['identifier'])
        # 目标是单个设备
        if int(attrs['target_type']) == 20:
            content_type = ContentType.objects.get(app_label='devdefine', model='attributeprototype')
            device = Device.objects.get(pk=attrs['target_id'])
            attribute = AttributePrototype.objects.get(device_prototype_id=device.device_prototype_id,
                                                       identifier=attrs['identifier'])

        self.create(
            attribute_id=attribute.id,
            linkage_target_id=attrs['linkage_target_id'],
            attribute_type_id=content_type.id
        )

    def update_validated(self, attrs):
        # 目标是分组
        if attrs['target_type'] == 10:
            group = Group.objects.filter(id=attrs['target_id']).first()

            if group.is_cross():
                content_type = ContentType.objects.get(app_label='linkage', model='crossattribute')
                attribute = CrossAttribute.object.get(identifier=attrs['identifier'], project_id=group.project_id)
            else:
                content_type = ContentType.objects.get(app_label='devdefine', model='attributeprototype')
                gdp = group.device_prototype()
                attribute = gdp.attributeprototype_set.get(identifier=attrs['identifier'])
        # 目标是单个设备
        if attrs['target_type'] == 20:
            content_type = ContentType.objects.get(app_label='devdefine', model='attributeprototype')
            device = Device.objects.get(pk=attrs['target_id'])
            attribute = AttributePrototype.objects.get(device_prototype_id=device.device_prototype_id,
                                                       identifier=attrs['identifier'])

        self.update(
            attribute_id=attribute.id,
            linkage_target_id=attrs['linkage_target_id'],
            attribute_type_id=content_type.id
        )


class LinkageAttribute(models.Model):
    # 联动目标属性，可能是跨类型属性、或者单个属性
    attribute_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    attribute_id = models.PositiveBigIntegerField()
    attribute = GenericForeignKey('attribute_type', 'attribute_id')

    # 所属的target
    linkage_target = models.ForeignKey(LinkageTarget, on_delete=models.CASCADE)

    class Meta:
        db_table = 'linkage_attributes'

    objects = LinkageAttributeManager()

    def validate_idf(self):
        idf = None
        attr_type = ContentType.objects.get_for_model(self)
        if attr_type.name == 'crossattribute':
            idf = self.cross_attribute.identifier

        elif attr_type.name == 'attributeprototype':
            idf = self.attribute_prototype.identifier
        return idf


class LinkageVarManager(models.Manager):
    def create_by_rule(self, rule, vars, project_id):
        pass


class LinkageVar(models.Model):
    # 所属的rule
    linkage_rule = models.ForeignKey(LinkageRule, on_delete=models.CASCADE)

    # 规则内的变量标识名
    name = models.CharField(max_length=255)
    # mac
    mac = models.CharField(max_length=255, null=True)
    # 设备的标识名
    identifier = models.CharField(max_length=255)
    # 是否是触发器
    is_trigger = models.BooleanField(default=False)
    # 变量类型，10-设备参数变量，20-自定义变量，自定义变量的作用域只能是“本规则”
    var_type = models.IntegerField(default=10)
    # 变量名称
    var_name = models.CharField(max_length=255, default='')
    # 自定义变量设定值
    value = models.CharField(max_length=255, null=True, default=None)
    # 自定义变量数据类型，当var_type=20时有效，10-布尔值，20-数值，30-枚举，40-BCD，80-字符串，90-时间段，91-时间
    data_type = models.IntegerField(null=True, default=None)
    # 精度
    pre_cision = models.CharField(max_length=255, null=True, default=None)
    # 步进
    in_crement = models.CharField(max_length=255, null=True)
    # 最小值
    min_value = models.CharField(max_length=255, null=True)
    # 最大值
    max_value = models.CharField(max_length=255, null=True)
    # 枚举类型时的选项
    options = models.CharField(max_length=255, null=True)
    # 单位
    unit = models.CharField(max_length=255, null=True)
    # 是否支持用户设定，默认为false
    set_by_user = models.BooleanField(default=False)

    objects = LinkageVarManager()

    class Meta:
        db_table = 'linkage_vars'


class SnpVarManager(models.Manager):
    def var_value(self, name, project, device=None):
        result = None

        # 环境（天气）变量
        if len(SnpVar.objects.filter(name=name, var_type=20)) != 0:
            region = Region.objects.get(pk=project.admin_region_id)

            data = WeatherApi.get_weather(region.weather_code)

            # 实时温度
            if name == 'S_OutDoorTemp':
                result = data['tem']

            # 湿度
            # 去除百分号
            if name == 'S_OutDoorHumi"' and data['humidity'] is not None:
                result = data['humidity'][0:result['humidity'].length - 1]

            # 日落时间
            if name == 'S_Sunset':
                result = data["sunset"]

            # 日出时间
            if name == 'S_Sunrise':
                result = data['sunrise']

            # 气压
            if name == 'S_Pressure':
                result = data['pressure']

        # 系统变量
        elif len(SnpVar.objects.filter(name=name, var_type=30)) != 0:
            # 运行模式
            if name == 'S_RunMode':
                result = getattr(project, 'run_mode')

        # 设备变量
        elif len(SnpVar.objects.filter(name=name, var_type=40)) != 0:
            # 在线状态
            if name.startswith('D_OnlineStatus'):
                result = device.online

        return result

    def create_by_rule(self, rule_id, data, is_trigger):
        from .serializers import SnpVarSerializer, LinkageSnpVarSerializer

        pvar_serializer = SnpVarSerializer(data=data)

        if pvar_serializer.is_valid(raise_exception=True):
            pvar = pvar_serializer.save()

        if is_trigger is not None and is_trigger:
            lsnpvar_serializer = LinkageSnpVarSerializer(data={
                'linkage_rule': rule_id,
                'snp_var': pvar.id,
                'is_trigger': True
            })

            if lsnpvar_serializer.is_valid(raise_exception=True):
                lsnpvar_serializer.save()


"""
  系统和项目级变量定义
"""


class SnpVar(models.Model):
    # 所属项目
    project = models.ForeignKey(Project, on_delete=models.CASCADE, null=True)
    # 规则里的变量标识名
    name = models.CharField(max_length=255)
    # mac
    mac = models.CharField(max_length=255, null=True)
    # 设备的标识名
    identifier = models.CharField(max_length=255)
    # 变量类型，10-设备参数，20-环境参数，30-项目参数
    var_type = models.IntegerField()

    objects = SnpVarManager()

    class Meta:
        db_table = 'snp_vars'


"""
  规则与系统或项目级变量关系
"""


class LinkageSnpVar(models.Model):
    # 对应的rule
    linkage_rule = models.ForeignKey(LinkageRule, on_delete=models.CASCADE)
    # 对应的变量
    snp_var = models.ForeignKey(SnpVar, on_delete=models.CASCADE)
    # 是否是触发器
    is_trigger = models.BooleanField(default=False)

    class Meta:
        db_table = 'linkage_snp_vars'


"""
  联动定时配置
"""


class LinkageTriggerManager(models.Manager):
    def create_by_rule(self, rule, exe_setting):
        from .serializers import LinkageTriggerSerializer

        serializer = LinkageTriggerSerializer(data=exe_setting)
        if serializer.is_valid(raise_exception=True):
            serializer.save(linkage_rule_id=rule.id)


class LinkageTrigger(models.Model):
    # 所属的快捷操作
    linkage_rule = models.ForeignKey(LinkageRule, on_delete=models.CASCADE)
    # 是否重复
    repeat = models.BooleanField(default=False)
    # 执行日期
    run_date = models.DateField(null=True)
    # 执行时间
    run_time = models.TimeField(null=True)
    # 周x，多个时用逗号隔开
    wdays = models.CharField(max_length=255, null=True)
    # 是否已完成
    is_finished = models.BooleanField(default=False)
    # 时间间隔，单位：秒
    interval = models.IntegerField(null=True)
    # 最后运行时间
    last_run_at = models.DateTimeField(null=True)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    # 有效时间范围
    time_ranges = models.TextField(null=True, blank=True)

    class Meta:
        db_table = 'linkage_triggers'
        ordering = ['-created_at']

    objects = LinkageTriggerManager()


class CrossAttribute(models.Model):
    """
      跨设备类型参数定义
    """
    # 所属项目
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    # 名称
    name = models.CharField(max_length=255)
    # 标识
    identifier = models.CharField(max_length=255, unique=True)
    # 数据类型
    data_type = models.IntegerField()
    # 默认值
    default_value = models.CharField(max_length=255, null=True)
    # 备注
    remark = models.CharField(max_length=255, null=True, blank=True)
    # 最小值
    min = models.CharField(max_length=255, null=True)
    # 最大值
    max = models.CharField(max_length=255, null=True)

    # 多个attribute_prototype对应
    attribute_prototypes = models.ManyToManyField(
        AttributePrototype,
        through='CrossAttributePrototype',
        through_fields=('cross_attribute', 'attribute_prototype'),
    )

    class Meta:
        db_table = 'cross_attributes'


"""
  跨设备类型参数与设备属性类型关联
"""


class CrossAttributePrototype(models.Model):
    # 对应的跨设备类型参数
    cross_attribute = models.ForeignKey(CrossAttribute, on_delete=models.CASCADE)
    # 对应设备参数类型
    attribute_prototype = models.ForeignKey(AttributePrototype, on_delete=models.CASCADE)

    class Meta:
        db_table = 'cross_attribute_prototypes'
