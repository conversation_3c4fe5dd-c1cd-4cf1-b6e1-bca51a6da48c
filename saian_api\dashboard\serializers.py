from rest_framework.serializers import ModelSerializer

from saian_api.dashboard.models import <PERSON><PERSON>eather, EcCriteria, EcTranscription

class ProjectWeatherSerializer(ModelSerializer):
    class Meta:
        model = ProjectWeather
        exclude = ('project',)
        read_only_fields = ('id', 'created_at', 'updated_at')

class EcCriteriaSerializer(ModelSerializer):
    class Meta:
        model = EcCriteria
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at')

class EcTranscriptionSerializer(ModelSerializer):
    class Meta:
        model = EcTranscription
        read_only_fields = ('created_at', 'updated_at')
        fields = '__all__'
