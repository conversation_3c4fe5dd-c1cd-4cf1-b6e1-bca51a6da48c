"""
    用户数据统计（小时、日、周、月、年）
"""

import traceback, datetime
from django.core.management import BaseCommand, CommandError
from saian_api.scheduledtask.utils import get_projects, set_global_db
from saian_api.user.models import UserLog, UserProject, WebUser, UserStat, PageUrl
from django.db.models import Q, Count
from functools import reduce

class Command(BaseCommand):
    help = "用户数据统计（小时、日、周、月、年）"

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目ID列表
        projects = get_projects(self, options)

        now = datetime.datetime.now()

        for project_id in projects:
            try:
                # 设置全局数据库
                set_global_db(project_id)

                # 当前项目的所有用户
                user_ids = list(UserProject.objects.values_list('web_user_id', flat=True))
                web_user_list = WebUser.objects.filter(id__in=user_ids, status=10)

                if not web_user_list.exists():
                    continue

                page_url = PageUrl.objects.values('api_url', 'http_method').annotate(count=Count('id')).order_by()

                # 小时
                hourly_begin_time = (now - datetime.timedelta(hours=1)).strftime('%Y-%m-%d %H:00:00')
                hourly_end_time = now.strftime('%Y-%m-%d %H:00:00')
                hourly_judgment = UserStat.objects.filter(target_dt=hourly_begin_time, periods=10).exists()
                hourly_user_stat_list = []
                if not hourly_judgment:
                    for web_user_item in web_user_list:
                        hourly_user_stat_item = self.calculate_statistical_data(web_user_item, 10, hourly_begin_time, hourly_end_time, page_url)
                        hourly_user_stat_list.append(hourly_user_stat_item)
                if len(hourly_user_stat_list) > 0:
                    self.create_user_stat(hourly_user_stat_list)

                # 日
                daily_begin_time = (now - datetime.timedelta(days=1)).strftime('%Y-%m-%d 00:00:00')
                daily_end_time = now.strftime('%Y-%m-%d 00:00:00')
                daily_judgment = UserStat.objects.filter(target_dt=daily_begin_time, periods=20).exists()
                daily_user_stat_list = []
                if not daily_judgment:
                    for web_user_item in web_user_list:
                        daily_user_stat_item = self.calculate_statistical_data(web_user_item, 20, daily_begin_time, daily_end_time, page_url)
                        daily_user_stat_list.append(daily_user_stat_item)
                if len(daily_user_stat_list) > 0:
                    self.create_user_stat(daily_user_stat_list)

                # 周
                weekly_begin_time = (now - datetime.timedelta(days=7) - datetime.timedelta(days=now.weekday())).strftime('%Y-%m-%d 00:00:00')
                weekly_end_time = (now - datetime.timedelta(days=now.weekday())).strftime('%Y-%m-%d 00:00:00')
                weekly_judgment = UserStat.objects.filter(target_dt=weekly_begin_time, periods=30).exists()
                weekly_user_stat_list = []
                if not weekly_judgment:
                    for web_user_item in web_user_list:
                        weekly_user_stat_item = self.calculate_statistical_data(web_user_item, 30, weekly_begin_time, weekly_end_time, page_url)
                        weekly_user_stat_list.append(weekly_user_stat_item)
                if len(weekly_user_stat_list) > 0:
                    self.create_user_stat(weekly_user_stat_list)

                # 月
                year = now.year - 1 if now.month == 1 else now.year
                month = 12 if now.month == 1 else now.month - 1
                monthly_begin_time = datetime.datetime.strptime('%s-%s-01 00:00:00' % (year, month), '%Y-%m-%d %H:%M:%S')
                monthly_end_time = now.strftime('%Y-%m-01 00:00:00')
                monthly_judgment = UserStat.objects.filter(target_dt=monthly_begin_time, periods=40).exists()
                monthly_user_stat_list = []
                if not monthly_judgment:
                    for web_user_item in web_user_list:
                        monthly_user_stat_item = self.calculate_statistical_data(web_user_item, 40, monthly_begin_time, monthly_end_time, page_url)
                        monthly_user_stat_list.append(monthly_user_stat_item)
                if len(monthly_user_stat_list) > 0:
                    self.create_user_stat(monthly_user_stat_list)

                # 年
                yearly_begin_time = datetime.datetime.strptime('%s-%s-01 00:00:00' % (now.year - 1, 1), '%Y-%m-%d %H:%M:%S')
                yearly_end_time = now.strftime('%Y-01-01 00:00:00')
                yearly_judgment = UserStat.objects.filter(target_dt=yearly_begin_time, periods=50).exists()
                yearly_user_stat_list = []
                if not yearly_judgment:
                    for web_user_item in web_user_list:
                        yearly_user_stat_item = self.calculate_statistical_data(web_user_item, 50, yearly_begin_time, yearly_end_time, page_url)
                        yearly_user_stat_list.append(yearly_user_stat_item)
                if len(yearly_user_stat_list) > 0:
                    self.create_user_stat(yearly_user_stat_list)

            except CommandError:
                self.stderr.write(f"运行'用户数据统计（小时、日、周、月、年）'任务失败，项目ID：{project_id}，命令参数不合法！", ending='\n')
                continue
            except Exception as e:
                self.stderr.write(f"运行'用户数据统计（小时、日、周、月、年）'任务失败，项目ID：{project_id}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
                continue

    def calculate_statistical_data(self, web_user_item, periods, begin_time, end_time, page_url):

        user_stat_item = {
            'periods': periods,
            'web_user_id': web_user_item.id,
            'user_name': web_user_item.name,
            'user_mobile': web_user_item.mobile,
            'ranking': 1,
            'acc_times': 0,
            'online_time': 0,
            'acc_pages': 0,
            'target_dt': begin_time
        }

        user_logs = UserLog.objects.filter(created_at__gte=begin_time, created_at__lt=end_time, web_user_id=web_user_item.id).order_by('created_at')

        if user_logs.exists():
            # 在线时长
            for session in self.split_list_by_difference(user_logs.values('created_at').order_by('created_at')):
                user_stat_item['online_time'] += (session[-1]['created_at'] - session[0]['created_at']).total_seconds()

            # 访问页面数
            if page_url:
                acc_pages_queries = [Q(**{f'api_url__icontains': item['api_url']}) & Q(**{f'http_method__icontains': item['http_method']}) for item in page_url]
                acc_pages_user_logs = user_logs.filter(reduce(lambda q1, q2: q1 | q2, acc_pages_queries))
                user_stat_item['acc_pages'] = len(acc_pages_user_logs)

            # 打开平台数
            increase_number = len(user_logs.filter(Q(api_url__icontains='/saianapi/user_logs/acc_times/increase')))
            reduce_number = len(user_logs.filter(Q(api_url__icontains='/saianapi/user_logs/acc_times/reduce')))
            user_stat_item['acc_times'] = int(increase_number - reduce_number) if increase_number > reduce_number else 0

        return user_stat_item

    @classmethod
    def split_list_by_difference(cls, numbers, max_difference=15):

        result = []

        current_sublist = [numbers[0]]

        for num in numbers[1:]:
            if (num['created_at'] - current_sublist[-1]['created_at']).total_seconds() > max_difference:
                result.append(current_sublist)
                current_sublist = [num]
            else:
                current_sublist.append(num)

        result.append(current_sublist)

        return result
    
    def create_user_stat(self, user_stat_list):

        online_time_list = sorted(list(set([int(item['online_time'] / 60) for item in user_stat_list])), reverse=True)

        for user_stat_item in user_stat_list:
            create_item = UserStat(
                periods = user_stat_item['periods'],
                web_user_id = user_stat_item['web_user_id'],
                user_name = user_stat_item['user_name'],
                user_mobile = user_stat_item['user_mobile'],
                ranking = online_time_list.index(int(user_stat_item['online_time'] / 60)) + 1,
                acc_times = user_stat_item['acc_times'],
                online_time = int(user_stat_item['online_time'] / 60),
                acc_pages = user_stat_item['acc_pages'],
                target_dt = user_stat_item['target_dt']
            )
            create_item.save()
