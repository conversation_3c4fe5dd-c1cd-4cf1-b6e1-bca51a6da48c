import datetime
import math
from collections import defaultdict

import numpy as np
from django.contrib.contenttypes.models import ContentType
from django.core.management.base import BaseCommand
from django.db.models import Q
from django.utils import timezone

from saian_api.devdefine.models import DevicePrototype
from saian_api.device.models import Device
from saian_api.report.models import DeviceHourlyStat, ReportConfigurer
from saian_api.scheduledtask.utils import get_projects, set_global_db
from saian_api.utils.db.Redis import RedisHelper


class Command(BaseCommand):
    help = "计算每个电表设备的动态阀值并写入 Redis，用于判断电表读数是否异常。"

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

        parser.add_argument(
            "--days",
            type=int,
            default=7,
            help="取最近多少天的数据来计算阀值，默认7天",
        )

        parser.add_argument(
            "--tolerance",
            type=float,
            default=0.8,
            help="阈值容忍误差比例，默认0.8（80%）",
        )

    def handle(self, *args, **options):
        days = options["days"]
        tolerance = options["tolerance"]
        end = datetime.datetime.now()
        start = end - datetime.timedelta(days=days)

        # 从命令行解释的项目ID列表
        projects = get_projects(self, options)

        for project_id in projects:

            set_global_db(project_id)

            dp_type = ContentType.objects.get_for_model(DevicePrototype)
            report_config = ReportConfigurer.objects.filter(target_type=dp_type,
                                                            identifier__icontains='power_cons'
                                                            ).exclude(Q(identifier__icontains='cur') | Q(identifier__icontains='today'))
            report_conf_dp_ids = report_config.values_list('target_id', flat=True)
            devices = Device.objects.filter(device_prototype_id__in=report_conf_dp_ids, online=True)

            dp_identifier_map = defaultdict(list)
            for rc in report_config:
                dp_identifier_map[rc.target_id].append(rc.identifier)

            dp_device_map = defaultdict(list)
            for device in devices:
                dp_device_map[device.device_prototype_id].append(device)

            for dp_id, device_list in dp_device_map.items():
                identifiers = dp_identifier_map.get(dp_id, [])
                for device in device_list:
                    for identifier in identifiers:
                        try:
                            stats = DeviceHourlyStat.objects.filter(
                                device_id=device.id,
                                identifier=identifier,
                                created_at__gte=start,
                                created_at__lt=end,
                            ).exclude(avg='0.0').values_list("avg", flat=True)

                            stats_float = [float(v) for v in stats if v]
                            if stats_float:
                                median = np.median(stats_float)
                                std = np.std(stats_float)
                                filtered_stats = [v for v in stats_float if v < median + 5 * std]
                            else:
                                filtered_stats = []

                            per_minute_rates = [v / 60 for v in filtered_stats if v > 0]

                            if not per_minute_rates:
                                threshold = 120  # 默认阀值
                            else:
                                base_threshold = float(np.percentile(per_minute_rates, 99))
                                # 加上容忍误差
                                threshold = base_threshold * (1 + tolerance)
                                threshold = math.ceil(threshold)

                            key = f"meter_threshold:{device.id}_{identifier}"
                            RedisHelper.set_value(project_id, key, threshold, True, 24 * 3600)

                            self.stdout.write(
                                self.style.SUCCESS(
                                    f"设备 {device.id} mac: {device.mac} nick-name: {device.nick_name} identifier: {identifier} 阀值={threshold:.2f} "
                                )
                            )
                        except Exception as e:
                            self.stdout.write(
                                self.style.ERROR(
                                    f"设备 {device.id} 阀值计算失败! error: {e}"
                                )
                            )
