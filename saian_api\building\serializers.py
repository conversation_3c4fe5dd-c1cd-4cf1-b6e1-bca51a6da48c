from rest_framework import serializers
from rest_framework.serializers import PrimaryKeyRelatedField

from saian_api.device.models import Device, RoomDevice
from saian_api.utils.httpapi.image import ImageAPI
from saian_api.utils.tools import fill_element
from .models import ActiveRoom, Building, Floor
from ..report.models import RoomHourlyStat
from ..terminal.models import Terminal

class BuildingSerializer(serializers.ModelSerializer):
    class Meta:
        model = Building
        fields = ['id', 'name', 'image', 'address', 'building_no', 'created_at', 'updated_at']
        read_only_fields = ('id', 'created_at', 'updated_at')

class FloorSerializer(serializers.ModelSerializer):
    building = PrimaryKeyRelatedField(queryset=Building.objects.all())

    class Meta:
        model = Floor
        fields = ['id', 'floor_no', 'name', 'image', 'thumb', 'coords_arr', 'building', 'created_at']
        read_only_fields = ('id', 'created_at', 'updated_at')

    # 执行完view之后再来这里执行
    # def update(self, instance, validated_data):
    #     validated_data['building'] = instance.building_id
    #     return validated_data

    def to_representation(self, floor_ins):
        ret = super().to_representation(floor_ins)
        building = Building.objects.get(pk=ret['building'])
        # 返回的数据去掉building_id，因为buidling里包含了
        ret.pop('building')
        ret['building'] = {
            'id': building.id,
            'name': building.name
        }

        images = ImageAPI.get_urls(request=self.context['request'], image_id=ret['image'], size='originals')
        if images is not None and len(images) > 0:
            ret['image'] = images[0]['image']

        thumbs = ImageAPI.get_urls(request=self.context['request'], image_id=ret['thumb'], size='originals')
        if thumbs is not None and len(thumbs) > 0:
            ret['thumb'] = thumbs[0]['image']

        # 处理楼层平面图数据
        # ret['coords_arr'] = floor_ins.fill_ele()
        no_coords_arr = self.context['request'].query_params.get('no_coords_arr', None)
        # 在【建筑管理】中获取楼层信息，不需要 "coords_arr"， 跳过处理楼层平面图数据
        if no_coords_arr is None:
            ret['coords_arr'] = fill_element(floor_ins.coords_arr)
        else:
            ret['coords_arr'] = []
        return ret

    # 这个是在serializer.is_valid()之后执行
    # def validate(self, data):
    #     if data['building'] is None:
    #         obj = Floor.objects.get(pk=self.validated_data['pk'])
    #         data['building'] = obj.building_id
    #     return data

class RoomSerializer(serializers.ModelSerializer):
    building = PrimaryKeyRelatedField(queryset=Building.objects.all())
    floor = PrimaryKeyRelatedField(queryset=Floor.objects.all())

    class Meta:
        model = ActiveRoom
        fields = ['id', 'name', 'min_temp', 'max_temp', 'min_humidity', 'max_humidity', 'min_tvoc', 'max_tvoc', 'min_co2', 'max_co2',
                  'min_pm25', 'max_pm25', 'show_in_datav', 'building', 'floor', 'active_room_no', 'created_at']
        read_only_fields = ('id', 'created_at', 'updated_at')

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        # building = Building.objects.get(pk=ret['building'])
        # floor = Floor.objects.get(pk=ret['floor'])
        # # 返回数据兼容旧版
        # ret['building_id'] = building.id
        # ret['building'] = building.name
        # ret['floor_id'] = floor.id
        # ret['floor_name'] = floor.name
        # 房间设备统计
        ret['stats'] = instance.stats()
        ret['image'] = None
        # ret['manager_test'] = ActiveRoom.objects.manager_test()

        # room_hum_stat = RoomHourlyStat.objects.filter(active_room_id=ret['id'], identifier='LocalRH')
        # room_temp_stat = RoomHourlyStat.objects.filter(active_room_id=ret['id'], identifier='LocalTemp')
        # if room_hum_stat.exists():
        #     ret['hum'] = room_hum_stat.last().avg
        # if room_temp_stat.exists():
        #     ret['temp'] = room_temp_stat.last().avg

        # 从管理平台查询图片路径
        # if ret['image'] is not None:
        #     images = ImageAPI.get_urls(self.context['request'], image_id=ret['image'])
        #     if images is not None:
        #         ret['image'] = images[0]['image']

        return ret

class SimpleRoomSerializer(serializers.ModelSerializer):
    class Meta:
        model = ActiveRoom
        fields = ['id', 'name', 'building', 'floor', 'active_room_no', 'created_at']


class RoomItemSerializer(serializers.ModelSerializer):
    class Meta:
        model = ActiveRoom
        fields = ['id', 'name', 'image', 'min_temp', 'max_temp', 'min_humidity', 'max_humidity', 'min_tvoc', 'max_tvoc', 'min_co2', 'max_co2',
                  'min_pm25', 'max_pm25', 'show_in_datav', 'building', 'floor', 'active_room_no', 'created_at']
        read_only_fields = ('id', 'created_at', 'updated_at')


class RoomTemphumSerializer(serializers.ModelSerializer):
    class Meta:
        model = ActiveRoom
        fields = ['id', 'name']

    def to_representation(self, instance):
        ret = super(RoomTemphumSerializer, self).to_representation(instance)
        ret['location'] = instance.location()
        stats = instance.stats()
        ret['run'] = stats['total_run']
        ret['stop'] = stats['total_stop']
        ret['total'] = stats['total']
        ret['fault'] = stats['total_fault']

        room_temp_stats = RoomHourlyStat.objects.filter(identifier='LocalTemp', active_room_id=ret['id'])
        room_hum_stats = RoomHourlyStat.objects.filter(identifier='LocalRH', active_room_id=ret['id'])
        if room_temp_stats.exists():
            ret['temp'] = room_temp_stats.last().avg
        if room_hum_stats.exists():
            ret['hum'] = room_hum_stats.last().avg

        return ret

class RoomDeviceSerializer(serializers.ModelSerializer):
    class Meta:
        model = RoomDevice
        fields = '__all__'


class FloorV5Serializer(serializers.ModelSerializer):
    class Meta:
        model = Floor
        exclude = ('coords_arr',)

class FloorTerminalSerializer(serializers.ModelSerializer):
    class Meta:
        model = Terminal
        fields = ['id', 'idx', 'prefix', 'in_fault', 'in_alarm', 'online', 'sw_on',
                  'nick_name', 'terminal_type', 'terminal_label', 'device_id', 'device_prototype_id']

    def to_representation(self, instance):
        ret = super().to_representation(instance)

        return ret


class FloorDeviceSerializer(serializers.ModelSerializer):
    class Meta:
        model = Device
        fields = ['id', 'nick_name', 'status', 'in_alarm', 'in_fault', 'sw_on', 'online', 'device_prototype_id']
