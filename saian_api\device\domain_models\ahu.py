import logging

from rest_framework import exceptions

from saian_api.devdefine.models import AttributePrototype
from saian_api.device.models import DeviceAttribute
from saian_api.device.models import WaterLeakage
from saian_api.issue.models import DeviceIssue
from .base import BaseDevice
from ...terminal.models import Terminal
from ...utils.db.Redis import RedisHelper
from ...utils.tools import is_number


class Ahu(BaseDevice):
    """风柜"""

    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)

        data = event.get('data', {})

        # 开关状态处理
        if 'AHUStatus' in data:
            value = data['AHUStatus']
            stop_values = ['旋钮停止', '本地停止', '停机']
            device.sw_on = value not in stop_values
            device.save()
            Terminal.objects.bulk_update_by_device(device, 'sw_on', device.sw_on)

class AhuV2(BaseDevice):
    """风柜v2"""
    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)

        data = event.get('data', {})

        # 开关状态处理
        if 'AHUStatus' in data:
            status_ap = AttributePrototype.objects.query_by_idf(device, 'AHUStatus')
            if status_ap is not None:
                idx = data['AHUStatus']
                value = status_ap.options.split(',')[int(idx)]
            else:
                value = data['AHUStatus']
            stop_values = ['旋钮停止', '本地停止', '停机']

            device.sw_on = value not in stop_values
            device.save()
            Terminal.objects.bulk_update_by_device(device, 'sw_on', device.sw_on)


class PLCAhu(BaseDevice):
    """
        PLC 设备
        prefix：
            风柜: AHU_
            新风机: FAU_
            组合风柜: CAHU_
    """
    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)

        data = event.get('data', {})

        """
            开关状态处理
        """
        stop_values = ['BA停止', '原PLC停止', '节能停止', '旋钮停止', '本地停止', '停机', '停止', '关机', '现场停止', '远程停止']
        for prefix in ['AHU_', 'AHU_Run', 'FAU_', 'CAHU_']:
            status_idf = f"{prefix}Status"
            if status_idf in data:
                value = data[status_idf]
                status_ap = AttributePrototype.objects.get_by_idf(device, status_idf)
                if status_ap is not None:
                    options = status_ap.options.split(',')
                    try:
                        value = options[int(value)]
                        device.sw_on = value not in stop_values
                        device.save()
                        Terminal.objects.bulk_update_by_device(device, 'sw_on', device.sw_on)

                    except Exception as e:
                        logging.error(f'处理设备{device.id}-{device.nick_name} 开关状态时出错：{e.__str__()}')
                break

    @classmethod
    def fault(cls, device, event):
        data = event.get('data', {})
        fault_keys = list(filter(lambda key: 'Fault' in key, data.keys()))

        for k in fault_keys:
            try:
                ap = AttributePrototype.objects.get_by_idf(device, k)
                options = ap.options.split(',')
                if ap is not None:
                    if k == 'Ahu_SyFaultType':
                        for i, v in enumerate(data[k]):
                            if i + 1 > len(options):
                                break
                            if int(v) == 1:
                                DeviceIssue.objects.add_fault(device, options[i])
                            else:
                                DeviceIssue.objects.recover_fault(device, options[i])
                    else:
                        idx = int(data[k])
                        if idx != 0:
                            DeviceIssue.objects.add_fault(device, options[idx])
                        else:
                            # 恢复所有类型故障
                            for fault in options:
                                DeviceIssue.objects.recover_fault(device, fault)
            except Exception as e:
                logging.error(e)

    @classmethod
    def alarm(cls, device, event):
        data = event.get('data', {})
        alarm_keys = list(filter(lambda key: 'Alarm' in key, data.keys()))

        for k in alarm_keys:
            try:
                idx = int(data[k])
                ap = AttributePrototype.objects.get_by_idf(device, k)
                if ap is not None:
                    options = ap.options.split(',')
                    if idx != 0:
                        DeviceIssue.objects.add_alarm(device, options[idx])
                    else:
                        # 恢复所有类型
                        for alarm in options:
                            DeviceIssue.objects.recover_alarm(device, alarm)

            except Exception as e:
                logging.error(e)


class AhuGateway(BaseDevice):
    """
      风柜网关
    """
    @classmethod
    def update_attres(cls, device, event):
        # 更新设备参数
        super().update_attres(device, event)

        data = event.get('data', {})

        """
          开关状态处理
        """
        if 'AHUStatus' in data:
            value = data['AHUStatus']
            if is_number(value):
                ap = AttributePrototype.objects.query_by_idf(device, 'AHUStatus')
                idx = int(value)
                if ap is not None and ap.data_type == 30 and ap.options:
                    options = ap.options.split(',')
                    try:
                        value = options[idx]
                    except Exception as e:
                        logging.error(f'风柜网关处理开关状态出错，err: {e.__str__()}')
            stop_values = ['BA停止', '原PLC停止', '节能停止', '旋钮停止', '本地停止', '停机', '关机', '现场停止', '远程停止']
            if value in stop_values:
                device.sw_on = False
            else:
                device.sw_on = True
            device.save()
            Terminal.objects.bulk_update_by_device(device, 'sw_on', device.sw_on)

    @classmethod
    def fault(cls, device, event):
        super().fault(device, event)
        data = event.get('data', {})
        faults = ['风柜相序错误', '风柜变频故障', '风柜消防联动', '风柜启动失败', '风阀开启异常', '风柜工频故障']
        if 'AHUFault' in data:
            if data['AHUFault'] == 1 and data['AHUFaultType'] != 0:
                idx = data['AHUFaultType'] - 1
                try:
                    DeviceIssue.objects.add_fault(device, faults[idx])
                except IndexError:
                    # logging.error(f"风柜网关报警越界错误，faults length: {len(faults)}, idx: {idx}")
                    pass
            else:
                # 不管目前的故障是什么，只要参数表示恢复了，就全部恢复
                for idx, fault in enumerate(faults):
                    DeviceIssue.objects.recover_fault(device, fault)

    @classmethod
    def alarm(cls, device, event):
        super().alarm(device, event)
        data = event.get('data', {})
        if 'GatewayAlarm' in data:
            if data['GatewayAlarm'] == 1 and data['GatewayAlarmType'] != 0:
                if data['GatewayAlarmType'] == 1:
                    DeviceIssue.objects.add_alarm(device, '通讯超时')
                if data['GatewayAlarmType'] == 2:
                    DeviceIssue.objects.add_alarm(device, '通讯错误')
            else:
                DeviceIssue.objects.recover_alarm(device, '通讯超时')
                DeviceIssue.objects.recover_alarm(device, '通讯错误')

class CthAhuGateway(BaseDevice):
    """
      恒温恒湿风柜网关
    """
    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)
        data = event.get('data', {})

        # 漏水处理
        WaterLeakage.objects.check_create(device, data)

        if 'RoomAHUBootSignal' in data:
            if data['RoomAHUBootSignal'] == '0' or data['RoomAHUBootSignal'] == 0:
                device.sw_on = False
            else:
                device.sw_on = True

            device.save()
            Terminal.objects.bulk_update_by_device(device, 'sw_on', device.sw_on)

    @classmethod
    def fault(cls, device, event):
        data = event.get('data', {})
        # 目前只有3个风柜（档案馆项目）
        for i in range(3):
            idx = str(i + 1)
            idf = ''.join(['CTHAHU_', idx, '_Fault'])
            idf_type = ''.join(['CTHAHU_', idx, '_FaultType'])
            if idf in data:
                faults = [
                    ''.join([idx, '#风柜相序错误']),
                    ''.join([idx, '#风柜变频故障']),
                    ''.join([idx, '#风柜消防联动']),
                    ''.join([idx, '#风柜工频故障']),
                    ''.join([idx, '#风柜启动失败']),
                    ''.join([idx, '#风柜风阀开启异常'])
                ]

                if data[idf] == 1:
                    if 1 <= data[idf_type] <= 6:
                        DeviceIssue.objects.add_fault(device, faults[data[idf_type] - 1])
                else:
                    for issue in faults:
                        DeviceIssue.objects.recover_fault(device, issue)

            # 漏水处理
            idf = ''.join(['CTHAHU_', idx, '_WaterLeakageAlarm'])
            if idf in data:
                if data[idf] != 0:
                    DeviceIssue.objects.add_fault(device, ''.join([idx, '#风柜漏水']))
                else:
                    DeviceIssue.objects.recover_fault(device, ''.join([idx, '#风柜漏水']))

    @classmethod
    def alarm(cls, device, event):
        data = event.get('data', {})
        if 'GatewayAlarm' in data:
            if data['GatewayAlarm'] == 1 and data['GatewayAlarmType'] != 0:
                if data['GatewayAlarmType'] == 1:
                    DeviceIssue.objects.add_alarm(device, '通讯超时')
                if data['GatewayAlarmType'] == 2:
                    DeviceIssue.objects.add_alarm(device, '通讯错误')
            else:
                DeviceIssue.objects.recover_alarm(device, '通讯超时')
                DeviceIssue.objects.recover_alarm(device, '通讯错误')

        # 目前只有3个风柜（档案馆项目）
        for i in range(3):
            idx = str(i + 1)
            idf = ''.join(['CTHAHU_', idx, '_Alarm'])
            idf_type = ''.join(['CTHAHU_', idx, '_AlarmType'])
            if idf in data:
                alarms = [
                    ''.join([idx, '#风柜缺风']),
                    ''.join([idx, '#风柜超温']),
                    ''.join([idx, '#风柜新风阀开阀异常']),
                    ''.join([idx, '#风柜新风阀关阀异常']),
                    ''.join([idx, '#风柜送风阀开阀异常']),
                    ''.join([idx, '#风柜送风阀关阀异常']),
                ]
                if data[idf] == 1:
                    if 1 <= data[idf_type] <= 6:
                        if data[idf_type] == 2:
                            DeviceIssue.objects.add_fault(device, alarms[data[idf_type] - 1])
                        else:
                            DeviceIssue.objects.add_alarm(device, alarms[data[idf_type] - 1])
                else:
                    for issue in alarms:
                        if '风柜超温' in issue:
                            DeviceIssue.objects.recover_fault(device, issue)
                        else:
                            DeviceIssue.objects.recover_alarm(device, issue)

class AliAhuV2(BaseDevice):
    """
      阿里云风柜V2和新风机
    """
    @classmethod
    def update_attres(cls, device, event):
        # 更新设备参数
        super().update_attres(device, event)

        data = event.get('data', {})

        # 风柜和新风机共用代码
        prefix = 'AHU'
        if device.device_prototype.uni_name == '阿里新风机':
            prefix = 'FAU'

        # 按钮状态和频率处理
        # 是否要检查频率
        check_freq = False
        # 状态值
        status_value = None
        # 频率值
        freq_value = None

        # 状态变化处理，设置设备开停机
        status_key = ''.join([prefix, 'Status'])
        if status_key in data:
            try:
                attr = AttributePrototype.objects.get(device_prototype_id=device.device_prototype_id, identifier=status_key)
            except AttributePrototype.DoesNotExist:
                raise exceptions.NotFound('找不到对应AP资源！')

            if str(data[status_key]).isdigit():
                value = attr.options.split(",")[int(data[status_key])] if attr else ''
            else:
                value = data[status_key]

            status_value = value
            check_freq = True

            if value == "旋钮停止" or value == "本地停止" or value == "停机":
                device.sw_on = False
            else:
                device.sw_on = True

            device.save()
            Terminal.objects.bulk_update_by_device(device, 'sw_on', device.sw_on)
        # 变频器频率输出上报处理
        # 当为设备为停止时，输出频率为0
        # 当为工频时，输出频率为50
        # 当为变频时，输出频率为频率返回值
        if 'InverterFreqBack' in data:
            status_value = device.get_value_by_idf(''.join([prefix, 'Status']))
            freq_value = data['InverterFreqBack']
            check_freq = True

        if check_freq:
            if status_value == "旋钮停止" or status_value == "本地停止" or status_value == "停机":
                freq_value = 0
            else:
                if status_value == "本地工频输出" or status_value == "远程手动工频" or status_value == "自动工频":
                    freq_value = 50
                else:
                    # 状态变化为变频运行
                    if freq_value is None:
                        freq = device.get_value_by_idf('InverterFreqBack')
                        if freq is not None:
                            freq_value = freq

            # 从缓存或数据库查询
            freq_out_da = DeviceAttribute.objects.query_object_by_idf(device, "FreqOutput")
            if freq_out_da is not None:
                freq_out_da.value = freq_value
                # 更新到缓存
                DeviceAttribute.objects.save_to_redis(device, freq_out_da)

    @classmethod
    def fault(cls, device, event):
        """
          故障处理
        """
        cls.__fault_alarm(device, event, 'Fault')

    @classmethod
    def alarm(cls, device, event):
        """
          报警处理
        """
        cls.__fault_alarm(device, event, 'Alarm')

    @classmethod
    def __fault_alarm(cls, device, event, issue_type):
        """
          故障和报警通用处理
        """
        data = event.get('data', {})
        # 风柜和新风机共用代码
        prefix = 'AHU'
        if device.device_prototype.uni_name == '阿里新风机':
            prefix = 'FAU'

        # 如果数据中包含故障或报警数据
        issue_idf = ''.join([prefix, issue_type, 'Type'])
        if issue_idf in data:
            try:
                attr = AttributePrototype.objects.get(device_prototype_id=device.device_prototype_id, identifier=issue_idf)
            except AttributePrototype.DoesNotExist:
                raise exceptions.NotFound(f'找不到对应AP资源: dp_id-{device.device_prototype_id}, identifier: {issue_idf}')

            if attr.data_type == 30:
                # da = DeviceAttribute.objects.get_by_idf(device, issue_idf)
                issue = "无故障"
                if issue_type == 'Alarm':
                    issue = '无报警'
                for idx, value in enumerate(data[issue_idf]):
                    try:
                        if int(value) == 1:
                            issue = attr.options.split(',')[idx]
                            # 添加问题，如果是故障则发送短信通知
                            method = 'add_%s' % str.lower(issue_type)
                            getattr(DeviceIssue.objects, method)(device, issue)
                        else:
                            r_issue = attr.options.split(',')[idx]
                            # 恢复问题
                            method = 'recover_%s' % str.lower(issue_type)
                            getattr(DeviceIssue.objects, method)(device, r_issue)
                    except IndexError:
                        # logging.error(f'阿里云风柜V2,新风机故障报警越界错误，options length: {len(attr.options.split(","))}, idx: {idx}')
                        pass
                da = DeviceAttribute.objects.query_object_by_idf(device, issue_idf)
                if da is not None:
                    da.value = issue
                    DeviceAttribute.objects.save_to_redis(device, da)

            # 记录报警或故障的原始数据
            raw_idf = ''.join([prefix, issue_type, 'TypeRaw'])

            raw_da = DeviceAttribute.objects.query_object_by_idf(device, raw_idf)
            if raw_da is not None:
                raw_da.value = data[issue_idf]
                DeviceAttribute.objects.save_to_redis(device, raw_da)
            # try:
            #     raw_attr = AttributePrototype.objects.get(device_prototype_id=device.device_prototype_id, identifier=raw_idf)
            #
            #     if raw_attr is not None:
            #         raw_da = DeviceAttribute.objects.get_by_idf(device, raw_idf)
            #         if raw_da is not None:
            #             raw_da.value = data[issue_idf]
            #             raw_da.save()
            # except AttributePrototype.DoesNotExist:
            #     # logging.error(f'AttributePrototype not found, device_prototype_id = {device.device_prototype_id}, idf = {raw_idf}')
            #     pass

class RdhAhuGateway(CthAhuGateway):
    """
      组合式转轮除湿风柜网关
    """
    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)

    @classmethod
    def fault(cls, device, event):
        data = event.get('data', {})
        # 目前只有2个风柜（档案馆项目）
        for i in range(2):
            idx = str(i + 1)
            idf = ''.join(['RDHAHU_', idx, '_Fault'])
            idf_type = ''.join(['RDHAHU_', idx, '_FaultType'])
            if idf in data:
                faults = [
                    ''.join([idx, '#风柜相序错误']),
                    ''.join([idx, '#风柜消防联动']),
                    ''.join([idx, '#风柜再生风机过载']),
                    ''.join([idx, '#风柜转轮电机过载']),
                    ''.join([idx, '#风柜超温保护']),
                    ''.join([idx, '#风柜转轮异常']),
                    ''.join([idx, '#风柜送风风机变频故障'])
                ]

                if data[idf] == 1:
                    if idf_type in data:
                        if 1 <= data[idf_type] <= 7:
                            DeviceIssue.objects.add_fault(device, faults[data[idf_type] - 1])
                else:
                    for issue in faults:
                        DeviceIssue.objects.recover_fault(device, issue)

            # 漏水处理
            idf = ''.join(['CTHAHU_', idx, '_WaterLeakageAlarm'])
            if idf in data:
                if data[idf] != 0:
                    DeviceIssue.objects.add_fault(device, ''.join([idx, '#风柜漏水']))
                else:
                    DeviceIssue.objects.recover_fault(device, ''.join([idx, '#风柜漏水']))

    @classmethod
    def alarm(cls, device, event):
        data = event.get('data', {})
        if 'GatewayAlarm' in data:
            if data['GatewayAlarm'] == 1 and data['GatewayAlarmType'] != 0:
                if data['GatewayAlarmType'] == 1:
                    DeviceIssue.objects.add_alarm(device, '通讯超时')
                if data['GatewayAlarmType'] == 2:
                    DeviceIssue.objects.add_alarm(device, '通讯错误')
            else:
                DeviceIssue.objects.recover_alarm(device, '通讯超时')
                DeviceIssue.objects.recover_alarm(device, '通讯错误')

        # 目前只有2个风柜（档案馆项目）
        for i in range(2):
            idx = str(i + 1)
            idf = ''.join(['RDHAHU_', idx, '_Alarm'])
            idf_type = ''.join(['RDHAHU_', idx, '_AlarmType'])
            if idf in data:
                alarms = [
                    ''.join([idx, '#风柜再生风缺风']),
                    ''.join([idx, '#风柜再生风超温'])
                ]
                if data[idf] == 1:
                    if idf_type in data:
                        if 1 <= data[idf_type] <= 2:
                            DeviceIssue.objects.add_alarm(device, alarms[data[idf_type] - 1])
                else:
                    for issue in alarms:
                        DeviceIssue.objects.recover_alarm(device, issue)

class PLCFan(BaseDevice):
    """PLC排风机"""
    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)
        data = event.get('data', {})

        # 开关状态处理
        if 'Fan_SW' in data:
            device.sw_on = data['Fan_SW']
            device.save()
            Terminal.objects.bulk_update_by_device(device, 'sw_on', device.sw_on)

    @classmethod
    def fault(cls, device, event):
        data = event.get('data', {})
        # 处理故障类型
        fault_idf = 'Fan_FaultType'
        if fault_idf in data:
            idx = int(data.get(fault_idf))
            if idx != 0:
                fault_ap = AttributePrototype.objects.get_by_idf(device, fault_idf)
                if fault_ap is not None:
                    options = fault_ap.options.split(',')
                    try:
                        fault = options[idx]
                        DeviceIssue.objects.add_fault(device, fault)
                    except Exception as e:
                        logging.error(f'FCU排风机，处理故障失败。error: {e.__str__()}')
            else:
                DeviceIssue.objects.recover_fault(device, '')

        # 处理系统故障
        fault_idf = 'Fan_SyFaultType'
        if fault_idf in data:
            fault_ap = AttributePrototype.objects.get_by_idf(device, fault_idf)
            if fault_ap is not None:
                options = fault_ap.options.split(',')
                for i, v in enumerate(data[fault_idf]):
                    if i + 1 > len(options):
                        break
                    try:
                        if int(v) == 1:
                            DeviceIssue.objects.add_fault(device, options[i])
                        else:
                            DeviceIssue.objects.recover_fault(device, options[i])
                    except IndexError:
                        pass

    @classmethod
    def alarm(cls, device, event):
        data = event.get('data', {})
        alarm_idf = 'Fan_AlarmType'
        if alarm_idf in data:
            idx = int(data.get(alarm_idf))
            if idx != 0:
                alarm_ap = AttributePrototype.objects.get_by_idf(device, alarm_idf)
                if alarm_idf is not None:
                    options = alarm_ap.options.split(',')
                    try:
                        alarm = options[idx]
                        DeviceIssue.objects.add_alarm(device, alarm)
                    except Exception as e:
                        logging.error(f'FCU排风机，处理报警失败。error: {e.__str__()}')
            else:
                DeviceIssue.objects.recover_alarm(device, '')

class WaterBalanceControllerAHU(BaseDevice):
    """
    水力平衡控制器AHU
    """
    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)

        data = event.get('data', {})
        if 'RunStatus' in data:
            status_da = DeviceAttribute.objects.query_object_by_idf(device, 'RunStatus')
            if status_da is not None:
                if '停止' in status_da.value:
                    device.sw_on = False
                else:
                    device.sw_on = True
                device.save()
                Terminal.objects.bulk_update_by_device(device, 'sw_on', device.sw_on)

    @classmethod
    def fault(cls, device, event):
        super().fault(device, event)

    @classmethod
    def alarm(cls, device, event):
        super().alarm(device, event)
