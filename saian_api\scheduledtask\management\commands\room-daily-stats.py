import datetime
import logging
import traceback

from django.core.management import CommandError, BaseCommand

from saian_api.building.models import ActiveRoom
from saian_api.project.models import Project
from saian_api.report.models import ReportConfigurer, RoomHourlyStat, RoomDailyStat
from saian_api.scheduledtask.utils import set_global_db, get_projects

class Command(BaseCommand):
    help = '每日统计房间的数据'

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目ID列表
        projects = get_projects(self, options)
        now = datetime.datetime.now()
        # now = datetime.datetime.strptime('20240516000500', '%Y%m%d%H%M%S')
        for project_id in projects:
            try:
                # 设置全局数据库
                set_global_db(project_id)
                project = Project.objects.get(pk=project_id)
                self.stdout.write(f"房间日统计开始: {project.name}", ending='\n')

                yesterday = now - datetime.timedelta(days=1)
                begin = datetime.datetime.combine(yesterday.date(), datetime.time(0, 0, 0))
                end = begin + datetime.timedelta(days=1)

                report_cfgs = list(ReportConfigurer.objects.filter(target_type__model='ActiveRoom'))

                room_ids = [report.target_id for report in report_cfgs]
                # room_ids = [247]
                rooms = list(ActiveRoom.objects.filter(id__in=room_ids))

                for rc in report_cfgs:
                    # rc_devices = list(filter(lambda x: x.device_prototype_id == rc.target_id, devices))
                    rc_rooms = list(filter(lambda x: x.id == rc.target_id, rooms))
                    for room in rc_rooms:
                        stats = RoomHourlyStat.objects.filter(active_room_id=room.id, identifier=rc.identifier,
                                                              created_at__gte=begin, created_at__lte=end)
                        values = [float(stat.avg) for stat in stats]
                        if len(values):
                            value = round(sum(values) / len(values), 2)
                            self.cus_create(room, rc.identifier, value, min_value=min(values), max_value=max(values), now=now)

            except CommandError:
                self.stderr.write(f"运行'房间日统计'任务失败，项目ID：{project_id}，命令参数不合法！", ending='\n')
                continue
            except Exception as e:
                self.stderr.write(f"运行'房间日统计'任务失败，项目ID：{project_id}, error: {e.__str__()}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
                continue

    @classmethod
    def cus_create(cls, room, identifier, value, min_value=None, max_value=None, now=None):
        if now is None:
            now = datetime.datetime.now()
        created_at = (now - datetime.timedelta(hours=1)).strftime('%Y-%m-%d 23:59:59')
        rds = RoomDailyStat.objects.filter(active_room_id=room.id, identifier=identifier, created_at=created_at).last()
        if rds is None:
            RoomDailyStat.objects.create(
                active_room_id=room.id,
                identifier=identifier,
                avg=value,
                min=min_value,
                max=max_value,
                created_at=created_at
            )
        else:
            if rds.avg != str(value):
                logging.info(f'\n{room.name}, {identifier}, {rds.id}, {created_at}, old: {rds.avg}, new: {value}')
                rds.avg = value
                rds.save()
                pass
