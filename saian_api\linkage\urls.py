from django.urls import path

from .views import LinkageRuleViewSet, LinkageAllVarViewSet, CrossAttributeViewSet, ExecuteRuleViewSet, LinkageTargetViewSet

rule_list = LinkageRuleViewSet.as_view({
    'get': 'list',
    'post': 'create'
})

rule_detail = LinkageRuleViewSet.as_view({
    'get': 'retrieve',
    'put': 'partial_update',
    # 'patch': 'partial_update',
    'delete': 'destroy'
})

var_list = LinkageAllVarViewSet.as_view({
    'get': 'list',
    'post': 'create'
})

var_detail = LinkageAllVarViewSet.as_view({
    'put': 'partial_update',
    'delete': 'destroy'
})

cross_list = CrossAttributeViewSet.as_view({
    'get': 'list',
    'post': 'create'
})

cross_detail = CrossAttributeViewSet.as_view({
    'put': 'partial_update',
    'delete': 'destroy'
})

execute_rule = ExecuteRuleViewSet.as_view({
    'post': 'create'
})

target_list = LinkageTargetViewSet.as_view({
    'get': 'list',
    'post': 'create'
})

target_detail = LinkageTargetViewSet.as_view({
    'put': 'partial_update',
    'delete': 'destroy'
})

urlpatterns = [
    path('saianapi/v1/linkage_rules', rule_list, name='rule-list'),
    path('saianapi/v1/linkage_rules/<int:pk>', rule_detail, name='rule-detail'),
    path('saianapi/v1/linkage_vars', var_list, name='var-list'),
    path('saianapi/v1/linkage_vars/<int:pk>', var_detail, name='var-detail'),
    path('saianapi/v1/cross_attributes', cross_list, name='cross-list'),
    path('saianapi/v1/cross_attributes/<int:pk>', cross_detail, name='cross-detail'),
    path('saianapi/v1/execute_rules', execute_rule, name='execute-rule'),
    path('saianapi/v1/linkage_targets', target_list, name='target-list'),
    path('saianapi/v1/linkage_targets/<int:pk>', target_detail, name='target-detail'),
]
