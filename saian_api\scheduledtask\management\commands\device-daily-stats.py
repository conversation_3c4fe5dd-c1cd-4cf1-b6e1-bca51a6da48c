"""
    设备日统计
"""
import datetime
import traceback

from django.core.management import BaseCommand, CommandError

from saian_api.project.models import Project
from saian_api.report.models import DeviceHourlyStat, DeviceDailyStat
from saian_api.scheduledtask.utils import get_projects, set_global_db
from saian_api.utils.report import do_stat_cce
from saian_api.utils.tools import is_number

def cus_create(device, value, ap, yesterday, min_value: [str, float] = '--', max_value: [str, float] = '--'):
    value = round(value, 3)
    if is_number(min_value):
        min_value = round(min_value, 3)
    if is_number(max_value):
        max_value = round(max_value, 3)

    dds = DeviceDailyStat.objects.filter(device_id=device.id, identifier=ap.identifier, created_at=yesterday).last()
    if dds is None:
        DeviceDailyStat.objects.create(
            device_id=device.id,
            mac=device.mac,
            identifier=ap.identifier,
            avg=value,
            min=min_value,
            max=max_value,
            created_at=yesterday
        )
    else:
        if dds.avg != str(value):
            print(f'{device.id}-{device.nick_name}, {ap.identifier} daily stats: {yesterday}, old={dds.avg}, new={value}')
            dds.avg = value
            # dds.save()

class Command(BaseCommand):
    help = '每日统计设备的数据'

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目ID列表
        projects = get_projects(self, options)
        now = datetime.datetime.now()
        # now = datetime.datetime.strptime('20240501010500', '%Y%m%d%H%M%S')

        for project_id in projects:
            try:
                # 设置全局数据库
                set_global_db(project_id)
                project = Project.objects.get(pk=project_id)

                self.stdout.write(f"设备日统计开始: {project.name}", ending='\n')

                yesterday = now - datetime.timedelta(days=1)
                begin = yesterday.strftime('%Y-%m-%d 00:00:00')
                end = yesterday.strftime('%Y-%m-%d 23:59:59')

                do_stat_cce(cus_create, DeviceHourlyStat.objects, begin, end, 'di')

                self.stdout.write(f"设备日统计任务完成: {project.name}", ending='\n')

            except CommandError:
                self.stderr.write(f"运行'设备日统计'任务失败，项目ID：{project_id}，命令参数不合法！", ending='\n')
                continue
            except Exception as e:
                self.stderr.write(f"运行'设备日统计'任务失败，项目ID：{project_id}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
                continue
