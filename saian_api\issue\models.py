from datetime import time, datetime, timedelta

from django.core.exceptions import ObjectDoesNotExist, MultipleObjectsReturned
from django.db import models, DatabaseError, transaction, IntegrityError
from django.db.models import Count

from saian_api.devdefine.models import DeviceType
from saian_api.device.models import Device
from saian_api.message.models import Message
from saian_api.notifi.models import Notification
from saian_api.project.models import Project
from saian_api.terminal.models import Terminal
from saian_api.user.models import WebUser

# Create your models here.
class IssueWhitelistManager(models.Manager):
    # 判断设备的故障报警是否没被屏蔽
    def not_blocked(self, device, glossary):
        result = True
        try:
            wl = self.get(device_id=device.id, issue_name=glossary)
            if int(wl.left_days) >= 1 or int(wl.left_days) == 0:
                result = False
        except ObjectDoesNotExist:
            # 如果找不到记录就当没被屏蔽
            pass

        return result

"""
  问题白名单
"""
class IssueWhitelist(models.Model):
    # 所属设备
    device = models.ForeignKey(Device, on_delete=models.CASCADE)
    # 问题名称
    issue_name = models.CharField(max_length=255)
    # 屏蔽天数，0-永久
    days = models.IntegerField()

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'issue_whitelists'
        ordering = ['-created_at']

    objects = IssueWhitelistManager()

    # 屏蔽的剩余天数
    @property
    def left_days(self):
        import datetime

        if self.days == 0:
            return 0
        else:
            return abs((datetime.datetime.now() - self.created_at).days)


class DeviceIssueManager(models.Manager):
    # 过去 30 天内故障率
    def fault_rate_30(self, project):
        _30_day_before = datetime.now() - timedelta(days=30)
        faults = self.filter(issue_type=10, project=project, created_at__gt=_30_day_before)
        faults_count = faults.count()
        # 记录每种设备分类的故障数
        faultsColByTypeLimit10 = faults.values('device_type_id').annotate(count=Count('id')).order_by('-count')[:10]
        top10 = []
        for item in faultsColByTypeLimit10:
            device_type = DeviceType.objects.get(pk=item['device_type_id']).name
            top10.append({
                'device_type': device_type,
                'count': item['count'],
                'rate': '%.2f%%' % (item['count'] / faults_count * 100 if faults_count else 0)
            })

        result = {
            'total_fault': faults_count,
            'solved': faults.filter(is_solved=True).count(),
            'top10': top10
        }
        return result

    # 故障原因统计
    def fault_reason_rate(self, project):
        faults = self.filter(issue_type=10, project=project)
        faults_count = faults.count()

        top10 = faults.values('name', 'device_type_id').annotate(count=Count('id')).order_by('-count')[:10]
        for item in top10:
            item['rate'] = '%.2f%%' % (item['count'] / faults_count * 100 if faults_count else 0)
            device_type_id = item.pop('device_type_id')
            fault_device_type = DeviceType.objects.get(pk=device_type_id).name
            item['device_type'] = fault_device_type

        result = {
            'total_fault': faults_count,
            'solved': faults.filter(is_solved=True).count(),
            'top10': top10,
        }
        return result

    # n 天内某个设备类型的故障或报警数, issue_type: 10-故障，20-报警
    def issue_stats_count(self, device_type, days, issue_type):
        time_till = datetime.today()
        time_from = time_till - timedelta(days=days)
        device_ids = Device.objects.filter(device_type_id=device_type.id).values_list('id', flat=True)

        return self.filter(created_at__range=(time_from, time_till), device_type_id=device_type.id, device_id__in=device_ids,
                           issue_type=issue_type).count()


    # 报警简单统计
    def alarm_summary(self, project):
        alarms = self.filter(issue_type=20, project=project)
        solved_alarms = alarms.filter(is_solved=True)
        today_alarms = alarms.filter(created_at__gt=datetime.today())

        alarms_count = alarms.count()
        solved_alarms_count = solved_alarms.count()
        today_alarms_count = today_alarms.count()

        return {
            'total_alarm': alarms_count,
            'solved': solved_alarms_count,
            'today_alarm': today_alarms_count,
        }

    # 新增报警
    # 20 - 报警
    def add_alarm(self, device, glossary):
        self.__add_issue(20, device, glossary)

    # 恢复报警
    def recover_alarm(self, device, glossary):
        self.__recover_issue(20, device, glossary)

    # 新增故障
    # 10 - 故障
    def add_fault(self, device, glossary):
        self.__add_issue(10, device, glossary)

    # 恢复故障
    def recover_fault(self, device, glossary):
        self.__recover_issue(10, device, glossary)

    # 根据描述查询最后一个未解决的issue
    def last_not_solved_issue_by_glossary(self, issue_type, device, glossary):
        try:
            issue = self.get(device_id=device.id, is_solved=False, issue_type=issue_type, display_name=glossary)
        except ObjectDoesNotExist:
            if glossary and glossary.endswith(':'):
                # vrv 从故障中恢复时，值是0，glossary 将会是 '内机x:' 这种形式
                issues = self.filter(device_id=device.id, is_solved=False, issue_type=issue_type, display_name__startswith=glossary)
                issue = None if not issues.exists() else issues.last()
            else:
                issue = None
        except MultipleObjectsReturned:
            issue = self.filter(device_id=device.id, is_solved=False, issue_type=issue_type, display_name=glossary).last()
        return issue

    # 查询最后一个没恢复的issue
    def last_not_solved_issue(self, issue_type, device):
        try:
            issue = self.get(device_id=device.id, is_solved=False, issue_type=issue_type)
        except ObjectDoesNotExist:
            issue = None
        except MultipleObjectsReturned:
            issue = self.filter(device_id=device.id, is_solved=False, issue_type=issue_type).last()
        return issue

    # 查找相同的没恢复的issue
    def get_same_issue(self, device, glossary):
        try:
            issue = self.get(device_id=device.id, is_solved=False, display_name=glossary)
        except ObjectDoesNotExist:
            issue = None
        except MultipleObjectsReturned:
            issue = self.filter(device_id=device.id, is_solved=False, display_name=glossary).first()
        return issue

    # 添加issue
    def __add_issue(self, issue_type, device, glossary):
        # 首次发生故障时才记录issue
        issue = self.last_not_solved_issue_by_glossary(issue_type, device, glossary)
        not_blocked = IssueWhitelist.objects.not_blocked(device, glossary)

        # 如果故障已存在，则更新故障的更新时间
        if issue is not None:
            issue.save()

        if issue is None and not_blocked:
            try:
                # with transaction.atomic(using="default"):
                with transaction.atomic(using=f"prj{device.project_id}db"):
                    issue = self.create(
                        name=glossary,
                        display_name=glossary,
                        is_solved=False,
                        issue_type=issue_type,
                        device_type_id=device.device_type_id,
                        device_id=device.id,
                        project_id=device.project_id
                    )
                    issue.save()

                    # 报警
                    if issue_type == 20:
                        device.in_alarm = True
                        device.save(update_fields=['in_alarm', 'in_fault', 'status'])
                        # 如果设备是冷源，则找出冷源本身的终端，并更新为报警状态
                        if device.device_type_id == 53:
                            cs_terminal = Terminal.objects.get_terminal_of_device(device)
                            if cs_terminal is not None:
                                cs_terminal.in_alarm = True
                                cs_terminal.save()
                        # 添加报警消息
                        Message.objects.alarm(device, glossary)
                        data = {
                            'content': f'设备报警: 设备【{device.id}-{device.nick_name}】报警: {glossary}',
                            'device_id': device.id,
                            'device_nickname': device.nick_name,
                            'glossary': glossary
                        }
                        Notification.objects.notify(9, device, data)
                        # 更新终端状态
                        Terminal.objects.update_by_issue_add(issue_type, device, glossary)
                    elif issue_type == 10:
                        # 添加故障消息
                        Message.objects.fault(device, glossary)
                        data = {
                            'content': f'设备故障: 设备【{device.id}-{device.nick_name}】发生故障: {glossary}',
                            'device_id': device.id,
                            'device_nickname': device.nick_name,
                            'glossary': glossary
                        }
                        Notification.objects.notify(8, device, data)

            except IntegrityError:
                print('新增设备问题出错！')
            except DatabaseError:
                print('新增设备问题数据库出错！')

        # 故障通知处理
        if issue_type == 10:
            # 当故障没有被屏蔽时，新故障即时发送通知，旧故障只在早上9点30分后
            if not_blocked:
                same_issue = self.get_same_issue(device, glossary)
                if same_issue is not None:
                    time_now = datetime.now().time()
                    day_time = time(hour=9, minute=30, second=0)
                    # 当前时间是否在早上9点30分后
                    if time_now >= day_time:
                        # 发送短信通知
                        Notification.objects.send_fault_notify(device, glossary)
                else:
                    # 马上发送短信通知
                    Notification.objects.send_fault_notify(device, glossary)

            # 最后更新设备为故障状态
            device.status = 40
            device.in_fault = True
            device.save(update_fields=['status', 'in_fault', 'in_alarm'])
            # 更新终端状态
            Terminal.objects.update_by_issue_add(issue_type, device, glossary)
            # 如果设备是冷源，则找出冷源本身的终端，并更新为故障状态
            if device.device_type_id == 53:
                cs_terminal = Terminal.objects.get_terminal_of_device(device)
                if cs_terminal is not None:
                    cs_terminal.in_fault = True
                    cs_terminal.save()

    # 恢复issue
    def __recover_issue(self, issue_type, device, glossary):
        # 如果设备本身已经无报警或故障，则不需要进行下面操作
        if issue_type == 10 and not device.in_fault:
            return
        if issue_type == 20 and not device.in_alarm:
            return

        issue = None
        if glossary == '':
            issue = self.last_not_solved_issue(issue_type, device)
        else:
            issue = self.last_not_solved_issue_by_glossary(issue_type, device, glossary)

        if issue is not None:
            issue.is_solved = True
            issue.save()
            data = {
                'device_id': device.id,
                'device_nickname': device.nick_name,
                'glossary': issue.display_name
            }
            if issue_type == 10:
                # 故障恢复消息
                Message.objects.fault_recover(device, issue.display_name)
                data['content'] = f'设备故障恢复: 设备【{device.id}-{device.nick_name}】故障恢复：{issue.display_name}'
                Notification.objects.notify(8, device, data)
            if issue_type == 20:
                # 报警解除消息
                Message.objects.alarm_recover(device, issue.display_name)
                data['content'] = f'设备报警解除: 设备【{device.id}-{device.nick_name}】报警解除：{issue.display_name}'
                Notification.objects.notify(9, device, data)

        other_issues = device.deviceissue_set.filter(issue_type=issue_type, is_solved=False).exists()
        # 更新设备报警状态为无问题
        if not other_issues:
            if issue_type == 10:
                device.status = 20
                device.in_fault = False
                # 更新设备关联的终端故障状态
                Terminal.objects.bulk_update_by_device(device, 'in_fault', False)
                # 故障恢复消息
                # Message.objects.fault_recover(device, glossary)

            if issue_type == 20:
                device.in_alarm = False
                # 更新设备关联的终端报警状态
                Terminal.objects.bulk_update_by_device(device, 'in_alarm', False)
                # 报警解除消息
                # Message.objects.alarm_recover(device, glossary)

            # 如果设备是冷源，则更新冷源本身终端的状态
            if device.device_type_id == 53:
                cs_terminal = Terminal.objects.get_terminal_of_device(device)
                if cs_terminal is not None:
                    if issue_type == 10:
                        cs_terminal.in_fault = False
                    if issue_type == 20:
                        cs_terminal.in_alarm = False
                    cs_terminal.save()
            device.save(update_fields=['status', 'in_alarm', 'in_fault'])

        Terminal.objects.update_by_issue_recovery(issue_type, device, glossary)

"""
  设备问题（报警和故障）记录
"""
class DeviceIssue(models.Model):
    # 所属项目
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    # 所属设备
    device = models.ForeignKey(Device, on_delete=models.CASCADE)
    # 发生问题是的数据事件id
    eventid = models.CharField(max_length=255, null=True)
    # 问题名称
    name = models.CharField(max_length=255, null=True)
    # 问题显示名称
    display_name = models.CharField(max_length=255, null=True)
    # 是否已解决
    is_solved = models.BooleanField(default=False)
    # 问题类型, 10-故障，20-报警
    issue_type = models.IntegerField(default=20)
    # 所属设备分类
    device_type_id = models.BigIntegerField(db_index=True)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'device_issues'
        ordering = ['-created_at']

    objects = DeviceIssueManager()

"""
  设备问题检查记录
"""
class CheckingRecord(models.Model):
    # 对应设备问题记录
    device_issue = models.ForeignKey(DeviceIssue, on_delete=models.CASCADE)
    # 检查的用户
    user_id = models.BigIntegerField(db_index=True)
    # 现场图片
    images = models.CharField(max_length=255, null=True)
    # 备注
    remark = models.CharField(max_length=255, null=True)
    # 用户提交记录时的设备状态
    device_status = models.IntegerField()

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'checking_records'
        ordering = ['-created_at']

    @property
    def user(self):
        return WebUser.objects.get(pk=self.user_id)
