import logging

from django.contrib.contenttypes.models import ContentType
from django.db import transaction

from saian_api.celery import celery_app
from saian_api.device.models import ParamR<PERSON>ord, Device, DaSnapshotHistory
from saian_api.group.models import GroupDevice
from saian_api.linkage.models import LinkageTarget
from saian_api.maint.models import DeviceMaintenance
from saian_api.scheduledtask.utils import set_global_db
from saian_api.terminal.models import Terminal

@celery_app.task
def task_calc_cum():
    # ParamRecord.objects.calc_cum(device_id, event_str)
    ParamRecord.objects.cron_calc_cum()

@celery_app.task
def task_check_device_maint():
    DeviceMaintenance.objects.cron_check_m()

@celery_app.task
def task_sync_meters(device_id, event_str):
    from saian_api.coldsource.models import EcMeter
    EcMeter.objects.update_meters(device_id, event_str)

@celery_app.task
def task_check_device_online():
    Device.objects.cron_check_online()

@celery_app.task(name='saian_api.device.tasks.unbind_device', max_retries=3)
def task_unbind_device(project_id, device_id):
    """
    celery 任务，从项目数据库中删除解绑设备
    """
    set_global_db(project_id)

    device = Device.objects.get(pk=device_id)

    with transaction.atomic(using=f'prj{project_id}db'):
        device_type_id = ContentType.objects.get(model='device').id
        terminal_type_id = ContentType.objects.get(model='terminal').id

        gd = GroupDevice.objects.filter(content_type_id=device_type_id, object_id=device.id)
        if gd.exists():
            logging.info(f"从设备分组中删除该设备")
            gd.delete()

        gt = GroupDevice.objects.filter(content_type_id=terminal_type_id, object_id__in=[t.id for t in device.terminal_set.all()])
        if gt.exists():
            logging.info(f"从终端分组中删除该设备的终端")
            gt.delete()

        ld = LinkageTarget.objects.filter(target_type=device_type_id, target_id=device.id)
        if ld.exists():
            logging.info(f"从联动目标中删除该设备")
            ld.delete()

        snap_histories = DaSnapshotHistory.objects.filter(device_id=device.id)
        if snap_histories.exists():
            logging.info(f"从历史快照中删除该设备")
            snap_histories.delete()

        Terminal.objects.filter(device=device).delete()
        device.delete()
