# Generated by Django 3.2.19 on 2023-12-25 15:36

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('project', '0008_webchart_data_name'),
        ('dimension', '0004_dimensionattribute_ec_type'),
    ]

    operations = [
        migrations.CreateModel(
            name='PpvConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('begin_at', models.TimeField()),
                ('price', models.CharField(max_length=20)),
                ('unit', models.CharField(max_length=20)),
                ('ec_category', models.Char<PERSON>ield(default=None, max_length=255, null=True)),
                ('ec_type', models.IntegerField()),
                ('ppv_type', models.IntegerField()),
                ('project_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='project.project')),
            ],
            options={
                'db_table': 'ppv_configs',
            },
        ),
    ]
