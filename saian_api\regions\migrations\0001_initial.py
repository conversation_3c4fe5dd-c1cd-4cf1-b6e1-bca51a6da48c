# Generated by Django 3.2.8 on 2021-12-30 15:16

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Region',
            fields=[
                ('id', models.IntegerField(primary_key=True, serialize=False)),
                ('name', models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ('short_name', models.<PERSON>r<PERSON>ield(max_length=255)),
                ('level', models.IntegerField()),
                ('weather_code', models.Char<PERSON>ield(max_length=255)),
            ],
            options={
                'db_table': 'web_regions',
            },
        ),
    ]
