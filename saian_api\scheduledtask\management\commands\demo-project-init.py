import datetime
import logging

from django.contrib.contenttypes.models import ContentType
from django.core.management import BaseCommand
from django.db.models import Q

from saian_api.act.models import ActColdStat
from saian_api.building.models import Building, Floor, ActiveRoom
from saian_api.coldsource.models import ColdSource, EcSource, CsEerAnalyse
from saian_api.devdefine.models import DevicePrototype, AttributeType, AttributePrototype, DeviceType
from saian_api.device.models import Device, DeviceAttribute, ParamRecord, RoomDevice, DeviceLimit
from saian_api.dimension.models import DimensionAttribute, Dimension, DimensionHourlyStat, DimensionDailyStat, DimensionMonthlyStat, \
    DimensionYearlyStat
from saian_api.group.models import Group, GroupDevice, GroupAction, ActionAttribute, ActionTimer, ActionTimerAttribute, AcStrategies
from saian_api.linkage.models import LinkageRule, LinkageTarget, LinkageAttribute, LinkageVar, LinkageSnpVar, LinkageTrigger
from saian_api.scheduledtask.utils import set_global_db
from saian_api.terminal.models import Terminal, TerminalAttribute, Category, CategoryTerminal
from saian_api.user.models import WebUser, WebRole

class Command(BaseCommand):
    help = '删除解除项目绑定的设备'
    __PROJECT_ID = 81
    __INTERVAL = 10 * 6

    def __init__(self, stdout=None, stderr=None, no_color=False, force_color=False):
        super().__init__(stdout, stderr, no_color, force_color)
        self.now = datetime.datetime.now()

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 设置全局数据库
        set_global_db(self.__PROJECT_ID)

        self.init_dimensions()
        self.init_cold_sources()
        self.init_cs_analysis()
        self.init_devices()
        self.init_terminal_categories()
        self.init_buildings()
        self.init_groups()
        self.init_device_limits()
        self.init_linkages()

    def init_terminal_categories(self):
        # 终端分类-保利-74
        db = 'prj74db'
        demo_categories = list(Category.objects.values_list('id', flat=True))
        orig_categories = Category.objects.using(db).exclude(id__in=demo_categories)
        Category.objects.bulk_create(orig_categories)

        # 分类绑定终端
        demo_cts = list(CategoryTerminal.objects.values_list('id', flat=True))
        orig_cts = CategoryTerminal.objects.using(db).exclude(id__in=demo_cts)
        for ct in orig_cts:
            orig_terminal = Terminal.objects.using(db).get(pk=ct.terminal_id)
            demo_terminal = Terminal.objects.filter(device_id=orig_terminal.device_id, prefix=orig_terminal.prefix, idx=orig_terminal.idx).last()
            if demo_terminal is not None:
                ct.terminal_id = demo_terminal.id

        CategoryTerminal.objects.bulk_create(orig_cts)



    def init_linkages(self):
        # 设备联动-德兴人医-80
        db = 'prj80db'
        demo_linkages = list(LinkageRule.objects.values_list('id', flat=True))
        orig_linkages = LinkageRule.objects.using(db).exclude(id__in=demo_linkages)
        for linkage in orig_linkages:
            linkage.project_id = self.__PROJECT_ID
        LinkageRule.objects.bulk_create(orig_linkages)

        # 联动目标
        demo_targets = list(LinkageTarget.objects.values_list('id', flat=True))
        orig_targets = LinkageTarget.objects.using(db).exclude(id__in=demo_targets)
        if orig_targets.exists():
            orig_group_model = ContentType.objects.using(db).get(model='group', app_label='group')
            demo_group_model = ContentType.objects.get_for_model(Group)
            orig_device_model = ContentType.objects.using(db).get(model='device')
            demo_device_model = ContentType.objects.get_for_model(Device)

            for target in orig_targets:
                if target.target_type_id == orig_device_model.id:
                    target.target_type_id = demo_device_model.id
                elif target.target_type_id == orig_group_model.id:
                    target.target_type_id = demo_group_model.id

            LinkageTarget.objects.bulk_create(orig_targets)

        # 目标参数
        demo_attrs = list(LinkageAttribute.objects.values_list('id', flat=True))
        orig_attrs = LinkageAttribute.objects.using(db).exclude(id__in=demo_attrs)
        if orig_attrs.exists():
            orig_ap_model = ContentType.objects.using(db).get(model='attributeprototype')
            demo_ap_model = ContentType.objects.get_for_model(AttributePrototype)

            for target in orig_targets:
                if target.target_type_id == orig_ap_model.id:
                    target.target_type_id = demo_ap_model.id

            LinkageAttribute.objects.bulk_create(orig_attrs)

        # 联动变量
        demo_vars = list(LinkageVar.objects.values_list('id', flat=True))
        orig_vars = LinkageVar.objects.using(db).exclude(id__in=demo_vars)
        LinkageVar.objects.bulk_create(orig_vars)

        demo_snp_vars = list(LinkageSnpVar.objects.values_list('id', flat=True))
        orig_snp_vars = LinkageSnpVar.objects.using(db).exclude(id__in=demo_snp_vars)
        LinkageSnpVar.objects.bulk_create(orig_snp_vars)

        # 触发器
        demo_trigger = list(LinkageTrigger.objects.values_list('id', flat=True))
        orig_trigger = LinkageTrigger.objects.using(db).exclude(id__in=demo_trigger)
        LinkageTrigger.objects.bulk_create(orig_trigger)





    def init_device_limits(self):
        # 末端温度设定-地大-58
        db = 'prj58db'
        demo_limits = list(DeviceLimit.objects.values_list('id', flat=True))
        orig_limits = DeviceLimit.objects.using(db).exclude(id__in=demo_limits)
        for limit in orig_limits:
            limit.project_id = self.__PROJECT_ID
        DeviceLimit.objects.bulk_create(orig_limits)


    def init_groups(self):
        # 设备分组-黄山百大-70
        db = 'prj70db'

        demo_web_users = list(WebUser.objects.all().values_list('id', flat=True))
        orig_web_users = WebUser.objects.using(db).exclude(id__in=demo_web_users)
        WebUser.objects.bulk_create(orig_web_users)

        demo_groups = list(Group.objects.values_list('id', flat=True))
        orig_groups = Group.objects.using(db).exclude(id__in=demo_groups)
        for group in orig_groups:
            group.project_id = self.__PROJECT_ID
        Group.objects.bulk_create(orig_groups)

        demo_group_devices = list(GroupDevice.objects.values_list('id', flat=True))
        orig_group_devices = list(GroupDevice.objects.using(db).exclude(id__in=demo_group_devices))
        if len(orig_group_devices):
            demo_device_model = ContentType.objects.get_for_model(Device)
            demo_terminal_model = ContentType.objects.get_for_model(Terminal)
            orig_device_model = ContentType.objects.using(db).get(model='device')
            orig_terminal_model = ContentType.objects.using(db).get(model='terminal')

            for gd in orig_group_devices:
                if gd.content_type_id == orig_device_model.id:
                    gd.content_type_id = demo_device_model.id
                else:
                    terminal = Terminal.objects.using(db).get(pk=gd.object_id)
                    demo_terminal = Terminal.objects.filter(device_id=terminal.device_id, prefix=terminal.prefix, idx=terminal.idx).last()
                    if demo_terminal is not None:
                        gd.content_type_id = demo_terminal_model.id
                        gd.object_id = demo_terminal.id

            GroupDevice.objects.bulk_create(orig_group_devices)

        # 分组操作
        demo_gas = list(GroupAction.objects.values_list('id', flat=True))
        orig_gas = GroupAction.objects.using(db).exclude(id__in=demo_gas)
        GroupAction.objects.bulk_create(orig_gas)

        # 分组操作参数
        demo_aas = list(ActionAttribute.objects.values_list('id', flat=True))
        orig_aas = ActionAttribute.objects.using(db).exclude(id__in=demo_aas)
        ActionAttribute.objects.bulk_create(orig_aas)

        # 定时器
        demo_timers = list(ActionTimer.objects.values_list('id', flat=True))
        orig_timer = ActionTimer.objects.using(db).exclude(id__in=demo_timers)
        ActionTimer.objects.bulk_create(orig_timer)

        # 定时参数
        demo_tas = list(ActionTimerAttribute.objects.values_list('id', flat=True))
        orig_tas = ActionTimerAttribute.objects.using(db).exclude(id__in=demo_tas)
        ActionTimerAttribute.objects.bulk_create(orig_tas)

        # 策略
        demo_strategies = list(AcStrategies.objects.values_list('id', flat=True))
        orig_strategies = AcStrategies.objects.using(db).exclude(id__in=demo_strategies)
        AcStrategies.objects.bulk_create(orig_strategies)


    def init_buildings(self):
        # 建筑楼层房间-黄山百大-70, 房间终端-德兴人医-80
        projects = [70, 80]
        for project_id in projects:
            db = f'prj{project_id}db'
            demo_buildings = list(Building.objects.values_list('id', flat=True))
            buildings = Building.objects.using(db).exclude(id__in=demo_buildings)
            for building in buildings:
                building.project_id = self.__PROJECT_ID
            Building.objects.bulk_create(buildings)

            demo_floors = list(Floor.objects.values_list('id', flat=True))
            floors = Floor.objects.using(db).exclude(id__in=demo_floors)
            Floor.objects.bulk_create(floors)

            demo_rooms = list(ActiveRoom.objects.values_list('id', flat=True))
            rooms = ActiveRoom.objects.using(db).exclude(id__in=demo_rooms)
            ActiveRoom.objects.bulk_create(rooms)

            # 房间终端关系只用德兴人医-80
            if project_id != 80:
                continue
            device_model_id = ContentType.objects.using(db).get(model='device').id
            terminal_model_id = ContentType.objects.using(db).get(model='terminal').id
            demo_device_model_id = ContentType.objects.get(model='device').id
            demo_terminal_model_id = ContentType.objects.get(model='terminal').id

            room_devices = list(RoomDevice.objects.using(db).all())
            room_terminal_ids = [rd.object_id for rd in room_devices if rd.content_type_id == terminal_model_id]
            demo_room_devices = list(RoomDevice.objects.all())
            demo_room_terminal_ids = [rd.object_id for rd in demo_room_devices if rd.content_type_id == demo_terminal_model_id]

            if len(room_terminal_ids) != len(demo_room_terminal_ids):
                room_terminals = list(Terminal.objects.using(db).filter(id__in=room_terminal_ids))
                demo_terminals = list(Terminal.objects.exclude(id__in=demo_room_terminal_ids))
                for rd in room_devices:
                    if rd.content_type_id == device_model_id:
                        rd.content_type_id = demo_device_model_id
                    elif rd.content_type_id == terminal_model_id:
                        rd.content_type_id = demo_terminal_model_id

                        terminal = list(filter(lambda x: x.id == rd.object_id, room_terminals))
                        if len(terminal):
                            terminal = terminal[0]
                            demo_terminal = list(filter(lambda x: x.device_id == terminal.device_id and x.prefix == terminal.prefix
                                                        and x.idx == terminal.idx, demo_terminals))
                            if len(demo_terminal):
                                rd.object_id = demo_terminal[0].id

                RoomDevice.objects.bulk_create(room_devices, ignore_conflicts=True)


    def init_devices(self):
        # 设备列表，黄山百大-70 德兴人医-80 保利-74
        projects = [70, 74, 80]
        for project in projects:
            db = f'prj{project}db'
            devices = Device.objects.using(db).filter(project_id__isnull=False)
            for device in devices:
                self.init_device(device, db)

    def init_cs_analysis(self):
        # 冷源能耗分析使用云浮-27
        db = 'prj27db'
        try:
            cs_mac = []
            for cs_id in set(EcSource.objects.using(db).values_list('cold_source_id', flat=True)):
                cs = ColdSource.objects.using(db).get(pk=cs_id)
                cs_mac.append(cs.mac)
                cs.project_id = self.__PROJECT_ID
                cs.save()

            for device in Device.objects.using(db).filter(mac__in=cs_mac):
                self.init_device(device, db)

                demo_cs_eer = list(CsEerAnalyse.objects.values_list('id', flat=True))
                orig_cs_eer = list(CsEerAnalyse.objects.using(db).exclude(id__in=demo_cs_eer))
                CsEerAnalyse.objects.bulk_create(orig_cs_eer)


        except Exception as e:
            logging.error(f'同步冷源能耗分析数据出错: {e.__str__()}')

    def init_cold_sources(self):
        # 冷源使用保利-74（id=1，2），版换系统-11（id=3）
        db = 'prj74db'
        cs_mac = set()
        demo_cs = list(ColdSource.objects.values_list('id', flat=True))
        orig_cs = ColdSource.objects.using(db).exclude(id__in=demo_cs)
        for cs in orig_cs:
            cs_mac.add(cs.mac)
            cs.project_id = self.__PROJECT_ID
            cs.save()

        cs_devices = Device.objects.using(db).filter(mac__in=cs_mac)
        for device in cs_devices:
            self.init_device(device, db)

        # 版换系统
        db = 'prj11db'
        demo_cs = ColdSource.objects.filter(name__contains="板换").last()
        for cs in ColdSource.objects.using(db).filter(name__contains="板换"):
            cs.project_id = self.__PROJECT_ID
            if demo_cs is None:
                cs.id = None
                cs.save()

            device = Device.objects.using(db).get(mac=cs.mac)
            self.init_device(device, db)


    def init_dimensions(self):
        # 维度只使用保利-74
        db = 'prj74db'

        # 维度 dimensions
        demo_dimension_ids = list(Dimension.objects.values_list('id', flat=True))
        orig_dimensions = list(Dimension.objects.using(db).exclude(id__in=demo_dimension_ids).order_by('parent_id'))

        for dimension in orig_dimensions:
            dimension.save()

        # 维度属性 dimension_attributes
        demo_da_ids = list(DimensionAttribute.objects.values_list('id', flat=True))
        orig_dimension_attrs = list(DimensionAttribute.objects.using(db).exclude(id__in=demo_da_ids))
        for da in orig_dimension_attrs:
            da.save()

        # - 维度属性绑定的终端属性
        demo_ta_ids = []
        for ta_ids in DimensionAttribute.objects.filter(ta_ids__isnull=False).values_list('ta_ids', flat=True):
            demo_ta_ids += ta_ids.split(',')

        da_tas = TerminalAttribute.objects.using(db).filter(id__in=demo_ta_ids)
        for ta in da_tas:
            terminal = Terminal.objects.using(db).get(pk=ta.terminal_id)
            if not Terminal.objects.filter(device_id=terminal.device_id, prefix=terminal.prefix, idx=terminal.idx).exists():
                device = Device.objects.using(db).get(pk=terminal.device_id)
                self.init_device(device, db)
                new_terminal = Terminal.objects.get(device_id=device.id, prefix=terminal.prefix, idx=terminal.idx)
                ta.terminal_id = new_terminal.id
                ta.save()

        # 维度统计数据
        self.init_dimension_stats(db, DimensionHourlyStat.objects)
        self.init_dimension_stats(db, DimensionDailyStat.objects)
        self.init_dimension_stats(db, DimensionMonthlyStat.objects)
        self.init_dimension_stats(db, DimensionYearlyStat.objects)

        # param_records pass

    def init_dimension_stats(self, db, data_from):
        demo_ids = list(data_from.values_list('id', flat=True))
        orig_stats = data_from.using(db).exclude(id__in=demo_ids)
        data_from.bulk_create(orig_stats)

    def init_device(self, device, db):
        # 设备id唯一
        device.project_id = self.__PROJECT_ID
        device.save()

        # # das
        # demo_apids = list(DeviceAttribute.objects.filter(device_id=device.id).values_list('attribute_prototype_id', flat=True))
        # das = list(DeviceAttribute.objects.using(db).filter(device_id=device.id).exclude(attribute_prototype_id__in=demo_apids))
        #
        # for da in das:
        #     da.id = None
        # DeviceAttribute.objects.bulk_create(das)

        # 对应的终端以及终端属性
        self.init_terminals(device, db)

    def init_terminals(self, device, db):
        # 终端id每个项目不同，需要处理冲突
        orig_terminals = list(Terminal.objects.using(db).filter(device_id=device.id))
        demo_terminals = list(Terminal.objects.filter(device_id=device.id))

        for orig_terminal in orig_terminals:
            orig_terminal_id = orig_terminal.id
            demo_terminal = next(filter(lambda x: x.prefix == orig_terminal.prefix and x.idx == orig_terminal.idx, demo_terminals), None)
            if demo_terminal is None:
                orig_terminal.id = None
                orig_terminal.save()
                demo_terminal_id = orig_terminal.id
            else:
                demo_terminal_id = demo_terminal.id

            # # tas
            # demo_aps = list(TerminalAttribute.objects.filter(terminal_id=demo_terminal_id).values_list('attribute_prototype_id', flat=True))
            # tas = list(TerminalAttribute.objects.using(db).filter(terminal_id=orig_terminal_id).exclude(attribute_prototype_id__in=demo_aps))
            # for ta in tas:
            #     ta.id = None
            #     ta.terminal_id = demo_terminal_id
            # TerminalAttribute.objects.bulk_create(tas)
