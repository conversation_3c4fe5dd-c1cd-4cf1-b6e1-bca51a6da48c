import datetime
import re

from django.core.management import BaseCommand
from django.db.models import Sum

from saian_api.device.models import ParamRecordHistory
from saian_api.dimension.models import DimensionHourlyStat, DimensionAttribute
from saian_api.scheduledtask.utils import set_global_db
from saian_api.utils.tools import is_number

class Command(BaseCommand):
    help = '删除解除项目绑定的设备'

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        project_ids = [47]
        # project_ids = settings.PROJECTS

        # 从命令行解释的项目ID列表
        for project_id in project_ids:
            print(f'project: {project_id}')
            # 设置全局数据库
            set_global_db(project_id)

            device_id = 8310
            identifier = 'Meter_Power_Cons_7'

            target_time_begin = datetime.datetime.strptime('20240706120500', '%Y%m%d%H%M%S')
            target_time_till = datetime.datetime.strptime('20240718120500', '%Y%m%d%H%M%S')
            target_records = list(ParamRecordHistory.objects.filter(device_id=device_id, identifier=identifier,
                                                                    created_at__range=[target_time_begin, target_time_till]).filter(
                created_at__minute=5, created_at__second=0).order_by('created_at'))

            refer_time_begin = datetime.datetime.strptime('20240615120500', '%Y%m%d%H%M%S')
            refer_time_till = datetime.datetime.strptime('20240627120500', '%Y%m%d%H%M%S')
            refer_records = list(ParamRecordHistory.objects.filter(device_id=device_id, identifier=identifier,
                                                                   created_at__range=[refer_time_begin, refer_time_till]).filter(
                created_at__minute=5, created_at__second=0).order_by('created_at'))

            print(f'target records: {len(target_records)}, refer targets: {len(refer_records)}')

            # refer_records.pop()
            if len(target_records) != len(refer_records):
                print(f'ERROR, 目标和参考数据的数目不一样')
                return

            count = len(target_records)

            refer_diff = float(refer_records[-1].value) - float(refer_records[0].value)
            target_diff = float(target_records[-1].value) - float(target_records[0].value)

            refer_values = []
            target_values = []
            ratios = []

            print(refer_diff, target_diff)
            for idx, refer_record in enumerate(refer_records):
                refer_values.append(refer_record.value)
                if idx == 0 or idx == len(refer_records) - 1:
                    target_values.append(float(target_records[idx].value))
                    continue
                diff = float(refer_record.value) - float(refer_records[idx - 1].value)
                ratio = diff / refer_diff
                ratios.append(ratio)
                incre = ratio * target_diff
                target_value = round(target_values[idx - 1] + incre, 3)
                target_values.append(target_value)

            # print(f'refer_values: {refer_values}')
            # print(f'target_values: {target_values}')
            # print(f'sum ratios: {sum(ratios)}')

            target_values.reverse()
            for value in target_values:
                print(value)

