"""
    设备月统计
"""
import logging
import traceback

from django.core.management import BaseCommand, CommandError

from saian_api.project.models import Project
from saian_api.report.models import DeviceDailyStat, DeviceMonthlyStat
from saian_api.scheduledtask.utils import get_projects, set_global_db
from saian_api.utils.report import last_month_time_range, do_stat_cce
from saian_api.utils.tools import is_number

def cus_create(device, value, ap, end_time, min_value: [float, str] = '--', max_value: [float, str] = '--'):
    value = round(value, 3)
    if is_number(min_value):
        min_value = round(min_value, 3)
    if is_number(max_value):
        max_value = round(max_value, 3)

    dms = DeviceMonthlyStat.objects.filter(device_id=device.id, identifier=ap.identifier, created_at=end_time).last()
    if dms is None:
        DeviceMonthlyStat.objects.create(
            device_id=device.id,
            mac=device.mac,
            identifier=ap.identifier,
            avg=value,
            min=min_value,
            max=max_value,
            created_at=end_time
        )
    else:
        if str(dms.avg) != str(value):
            logging.info(f'\n{device.nick_name}, {ap.identifier}, {dms.id}, {end_time}, old: {dms.avg}, new: {value}')
            dms.avg = value
            # dms.save()


class Command(BaseCommand):
    help = '每月统计设备的数据'

    def add_arguments(self, parser):
        parser.add_argument('projects', nargs='+', type=str, help='项目名称和ID，格式：项目名-ID')

    def handle(self, *args, **options):
        # 从命令行解释的项目ID列表
        projects = get_projects(self, options)

        for project_id in projects:
            try:
                # 设置全局数据库
                set_global_db(project_id)
                project = Project.objects.get(pk=project_id)

                self.stdout.write(f"设备月统计开始: {project.name}", ending='\n')

                begin, end = last_month_time_range()

                do_stat_cce(cus_create, DeviceDailyStat.objects, begin, end, 'mo')

                self.stdout.write(f"设备月统计任务完成: {project.name}", ending='\n')

            except CommandError:
                self.stderr.write(f"运行'设备月统计'任务失败，项目ID：{project_id}，命令参数不合法！", ending='\n')
                continue
            except Exception as e:
                self.stderr.write(f"运行'设备月统计'任务失败，项目ID：{project_id}", ending='\n')
                self.stderr.write(traceback.format_exc(), ending='')
                continue
