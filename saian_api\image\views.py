from rest_framework import viewsets

from .models import Image
from .serializers import ImageSerializer
# Create your views here.
from ..utils.sy_jsonrenderer import SyJSONRender

class ImageViewSet(viewsets.ModelViewSet):
    renderer_classes = (SyJSONRender,)
    serializer_class = ImageSerializer
    queryset = Image.objects.all()
    authentication_classes = []

    # 返回的图片地址中去除自动补全的域名，仅保留相对路径
    def get_serializer_context(self):
        context = super(ImageViewSet, self).get_serializer_context()
        context.update({'request': None})
        return context

    # def get_authenticators(self):
    #     if self.action == 'retrieve':
    #         return []
    #     return self.authentication_classes

    # def retrieve(self, request, *args, **kwargs):
    #     try:
    #         instance = self.get_object()
    #         serializer = self.get_serializer(instance)
    #     except Http404:
    #         return Response(data={'image': '/assets/images/default.png'})
    #     return Response(serializer.data)

# class LegacyImageView(APIView):
#     """
#         从旧接口取得图片
#     """
#     def get(self, request):
#         url = DOMAIN + '/saianapi/v1/images?' + request.GET.urlencode()
#         print('LegacyImageView: ', url)
#         if request is not None:
#             r = requests.get(url, headers=BaseAPI.headers(request))
#         else:
#             r = requests.get(url)
#
#         if r.status_code != 200:
#             raise exceptions.AuthenticationFailed('获取图片路径出错！')
#
#         try:
#             res = r.json()
#         except(JSONDecodeError):
#             raise exceptions.ParseError('获取图片路径，响应格式错误或解析json数据出错！')
#
#         return Response({
#             'status': status.HTTP_200_OK,
#             'data': res['data']
#         })
