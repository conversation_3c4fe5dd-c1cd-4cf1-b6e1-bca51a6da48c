import base64
import hmac
import os
import time
from urllib.parse import quote


class BaseAPI():
    """
      构造鉴权header
    """
    @staticmethod
    def headers(res):
        # 过期时间 + 30秒，即签名有效时间为30秒
        et = int(time.time()) + 30

        # 从环境变量中查找appid和对应的access_key
        # appid = os.environ['dev_appid']
        # access_key = os.environ['dev_access_key']

        # appid = "sa75ada9a1"
        # access_key = "cb87f7a073e0a92709584a86cc95960b"
        appid = "sab153cfdd"
        access_key = "244c83f17bf931b1a8c528c53657ca04"

        # 采用sha1算法
        method = 'sha1'
        key = base64.b64decode(access_key)

        org = appid + '&' + str(et) + '&' + res
        sign_b = hmac.new(key=key, msg=org.encode(), digestmod=method)
        # 生成签名
        sign = base64.b64encode(sign_b.digest()).decode()
        sign = quote(sign, safe='')

        auth_str = f"appid={appid}&et={et}&res={res}&sign={sign}"
        headers = {'Authorization': auth_str}

        return headers

    @staticmethod
    def admin_intapi_header(res):
        # 过期时间 + 30秒，即签名有效时间为30秒
        et = int(time.time()) + 30

        # 从环境变量中查找appid和对应的access_key
        # appid = os.environ['dev_appid']
        # access_key = os.environ['dev_access_key']

        # appid = "sa75ada9a1"
        # access_key = "cb87f7a073e0a92709584a86cc95960b"
        appid = os.environ['adminappid']
        access_key = os.environ['adminsecret']

        # 采用sha1算法
        method = 'sha1'
        key = base64.b64decode(access_key)

        org = appid + '&' + str(et) + '&' + res
        sign_b = hmac.new(key=key, msg=org.encode(), digestmod=method)
        # 生成签名
        sign = base64.b64encode(sign_b.digest()).decode()
        sign = quote(sign, safe='')

        auth_str = f"appid={appid}&et={et}&res={res}&sign={sign}"
        headers = {'Authorization': auth_str}

        return headers

    @staticmethod
    def open_entapi_header(res):
        # 过期时间 + 30秒，即签名有效时间为30秒
        et = int(time.time()) + 30

        # 从环境变量中查找appid和对应的access_key
        # appid = os.environ['dev_appid']
        # access_key = os.environ['dev_access_key']

        # appid = "sa75ada9a1"
        # access_key = "cb87f7a073e0a92709584a86cc95960b"
        appid = os.environ.get('openapiappid', '')
        access_key = os.environ.get('openapisecret', '')

        # 采用sha1算法
        method = 'sha1'
        key = base64.b64decode(access_key)

        org = appid + '&' + str(et) + '&' + res
        sign_b = hmac.new(key=key, msg=org.encode(), digestmod=method)
        # 生成签名
        sign = base64.b64encode(sign_b.digest()).decode()
        sign = quote(sign, safe='')

        auth_str = f"appid={appid}&et={et}&res={res}&sign={sign}"
        headers = {'Authorization': auth_str}

        return headers
