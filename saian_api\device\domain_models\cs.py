import logging
import re
import traceback

from django.core.exceptions import ObjectDoesNotExist

from saian_api.devdefine.models import AttributePrototype, DevicePrototype
from saian_api.device.models import DeviceAttribute
from saian_api.issue.models import DeviceIssue
from .base import BaseDevice
from django.db.models import Q
from saian_api.utils.tools import to_int
from ...terminal.models import Terminal


class CommCs(BaseDevice):
    """
      标准版冷源（百特，ecat，白云区档案馆，云浮等）
    """
    @classmethod
    def sub_device_prefix(cls, device):
        sub_device_prefix = {}
        sub_dps = DevicePrototype.objects.filter(parent_id=device.device_prototype_id)
        for sub_dp in sub_dps:
            if sub_dp.prefix and sub_dp.name:
                sub_device_prefix[sub_dp.prefix] = sub_dp.name

        return sub_device_prefix

    @classmethod
    def update_attres(cls, device, event):
        # 更新设备参数
        super().update_attres(device, event)
        data = event.get('data', {})
        dp_uni_name = device.device_prototype.uni_name

        # 开关机状态
        run_status = None
        if 'RunStatus' in data:
            run_status = data['RunStatus']
        elif 'CSRunStatus' in data:
            run_status = data['CSRunStatus']
        elif 'HSRunStatus' in data:
            run_status = data['HSRunStatus']
        elif 'CHSRunStatus' in data:
            run_status = data['CHSRunStatus']
        if run_status is not None:
            if int(run_status) == 0:
                device.sw_on = False
            else:
                device.sw_on = True
            device.save(update_fields=['sw_on'])
            Terminal.objects.filter(device_id=device.id, prefix__isnull=True, idx__isnull=True).update(sw_on=device.sw_on)
            # Terminal.objects.bulk_update_by_device(device, 'sw_on', device.sw_on)

        for key, value in data.items():
            try:
                da = None
                # 海骏达冷源电表计算：总有功功率
                if device.device_prototype_id == 518:
                    search_obj = re.search('Meter_W_A_(\d+)', key)
                    if search_obj is not None:
                        da_a = device.get_value_by_idf(''.join(['Meter_W_A_', search_obj.group(1)]))
                        da_b = device.get_value_by_idf(''.join(['Meter_W_B_', search_obj.group(1)]))
                        da_c = device.get_value_by_idf(''.join(['Meter_W_C_', search_obj.group(1)]))
                        da = DeviceAttribute.objects.get_by_idf(device, ''.join(['Meter_W_', search_obj.group(1)]))
                        if da is not None:
                            da.value = round((float(da_a) + float(da_b) + float(da_c)), 2)
                            da.save()
                            DeviceAttribute.objects.save_to_redis(device, da)

            # 避免影响其他参数处理
            except ValueError as e:
                logging.error(f'DTUMeter.update_attres处理异常，key={key}, value={value}, error: {e.__str__()}')
                traceback.print_exc()
                pass
            except Exception as e:
                logging.error(f'DTUMeter.update_attres处理异常，key={key}, value={value}, error: {e.__str__()}')
                traceback.print_exc()
                pass

        cls.__sub_run_status(device, event)

    # 冷源子设备开关机状态key
    sub_run_status_pattern = re.compile(r'^(\w+)_(\d+)_(RunStatus|Status)$')

    @classmethod
    def __sub_run_status(cls, device, event):
        """冷源子设备开关机状态处理"""
        data = event.get('data', {})

        # 筛选出状态相关的数据
        status_items = (
            (key, value) for key, value in data.items()
            if key.endswith("RunStatus") or key.endswith("_Status")
        )

        for key, value in status_items:
            match = cls.sub_run_status_pattern.match(key)
            if match:
                try:
                    prefix = match.group(1)
                    index = int(match.group(2))
                    terminal = Terminal.objects.filter(device_id=device.id, prefix=prefix, idx=index)
                    if terminal.count() != 1:
                        logging.error(f'更新冷源子设备终端sw失败，找到多个终端！device-{device.id}, mac-{device.mac}, prefix-{prefix}, idx: {index}')
                        continue
                    if int(value) == 0:
                        terminal.update(sw_on=False)
                    else:
                        terminal.update(sw_on=True)
                except ValueError:
                    pass

        pass

    @classmethod
    def fault(cls, device, event):
        data = event.get('data', {})
        fault_type_attrs = AttributePrototype.objects.filter(device_prototype_id=device.device_prototype_id).filter(
            Q(name__contains='系统故障') | Q(name__contains='故障详情'))
        if fault_type_attrs.exists():
            try:
                for attr in fault_type_attrs:
                    if attr.data_type == 30 and attr.identifier in data:
                        da = DeviceAttribute.objects.get_by_idf(device, attr.identifier)
                        issue = '无故障'
                        issues = ''
                        options = attr.options.split(',')
                        fault_str = data[attr.identifier]

                        # options 长度比上报的故障字符串长，则默认后面的故障都是"0"
                        if len(options) > len(fault_str):
                            fault_str += '0' * (len(options) - len(fault_str))

                        for idx, value in enumerate(fault_str):
                            try:
                                if int(value) == 1:
                                    issue = options[idx]
                                    # 每1位代表1个故障类型，可以同时有多个
                                    issues = issue if issues == '' else issues + ',' + issue
                                    DeviceIssue.objects.add_fault(device, issue)
                                else:
                                    issue = options[idx]
                                    DeviceIssue.objects.recover_fault(device, issue)
                            except IndexError:
                                # logging.error(f"CommCs 故障越界错误，options length: {len(attr.options.split(','))}, idx: {idx}")
                                pass
                        da.value = '无故障' if issues == '' else issues
                        da.save()
                        DeviceAttribute.objects.save_to_redis(device, da)
            except ObjectDoesNotExist as e:
                logging.error(e.args)

        # 子设备故障处理
        cls.__sub_alarm_fault('Fault', device, event)

    @classmethod
    def alarm(cls, device, event):
        # 子报警故障处理
        cls.__sub_alarm_fault('Alarm', device, event)

    # 子设备的报警和故障处理
    @classmethod
    def __sub_alarm_fault(cls, issue_type, device, event):
        data = event.get('data', {})
        sub_device_prefix = cls.sub_device_prefix(device)
        # sub_device_prefix = {
        #     'Host': '主机',
        #     'FPump': '冷冻泵',
        #     'CPump': '冷却泵',
        #     'CYPump': '循环泵',
        #     'FValve': '冷冻阀',
        #     'CValve': '冷却阀',
        #     'CYValve': '循环阀',
        #     'CTower': '冷却塔',
        #     'HRCYPump': '热循环泵'
        # }

        for key, value in sub_device_prefix.items():
            qty = device.get_value_by_idf(''.join([str(key), 'Qty']))

            if qty is None:
                qty = 0

            cls.__alarm_fault(device, data, qty, key, value, issue_type)

    # 报警和故障处理
    @classmethod
    def __alarm_fault(cls, device, data, qty, prefix, name, issue_type):
        for i in range(int(qty)):
            idx = str(i + 1)
            # 构造标识，比如：Host_1_FaultType

            idf = ''.join([prefix, '_', idx, '_', issue_type, 'Type'])
            
            # 如果不存在故障类型的标识，则构造故障状态的标识
            if idf not in data:
                idf = ''.join([prefix, '_', idx, '_', issue_type, 'Status'])

            # 电表状态标识
            if issue_type == 'Fault' and prefix == 'Meter':
                idf = ''.join([prefix, '_MeterStatus_', idx])

            if idf in data:
                try:
                    # attr = AttributePrototype.objects.get(
                    #     device_prototype_id=device.device_prototype_id, identifier=idf)
                    attr = AttributePrototype.objects.get_by_idf(device, idf)
                    value = to_int(data[idf])
                    if attr.options is None:
                        return

                    options = attr.options.split(',')

                    # 查询设备昵称，如果没，则为xx号xx
                    nick_name_idf = ''.join([prefix, '_', idx, '_NickName'])
                    nick_name = device.get_value_by_idf(nick_name_idf)
                    if not nick_name or nick_name == '0':
                        nick_name = ''.join([idx, '号', name])

                    if (value > 0 and prefix != 'Meter') or (prefix == 'Meter' and (options[value] == '通讯超时' or options[value] == '通讯错误')):
                        try:
                            content = ''.join([nick_name, options[value]])
                            method = 'add_%s' % str.lower(issue_type)
                            getattr(DeviceIssue.objects, method)(device, content)
                        except IndexError:
                            # logging.error(f'标准版冷源, 获取故障报警内容时，越界错误，options length: {len(options)}, idx: {value}')
                            pass
                    else:
                        # 恢复就全部恢复
                        for op in options:
                            method = 'recover_%s' % str.lower(issue_type)
                            content = ''.join([nick_name, op])
                            getattr(DeviceIssue.objects, method)(device, content)

                except ObjectDoesNotExist as e:
                    logging.error(e.with_traceback)


class AirCool(BaseDevice):
    """
     冷风机冷源
    """
    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)
        pass

    @classmethod
    def fault(cls, device, event):
        # 子设备故障处理
        cls.__sub_alarm_fault('Fault', device, event)

    @classmethod
    def alarm(cls, device, event):
        # 子设备报警处理
        cls.__sub_alarm_fault('Alarm', device, event)

    @classmethod
    def __sub_alarm_fault(cls, issue_type, device, event):
        data = event.get('data', {})
        sub_device_prefix = CommCs.sub_device_prefix(device)
        # sub_device_prefix = {
        #     'Host': '主机',
        #     'FPump': '冷冻泵',
        #     'FValve': '冷冻阀'
        # }

        for key, value in sub_device_prefix.items():
            qty = device.get_value_by_idf(''.join([str(key), 'Qty']))

            if qty is not None and str(qty).isdigit():
                cls.__alarm_fault(device, data, int(qty), key, value, issue_type)

    @classmethod
    def __alarm_fault(cls, device, data, qty, prefix, name, issue_type):
        #  主机故障报警列表
        host_faults = '气分进口故障, 低压压力传感器故障/低压保护, 排气温度故障, 环境温度故障, 系统出水温度故障, 模块回水温度故障, 模块出水温度故障, 翅片温度故障, 经济器进口温度故障, 经济器出口温度故障, 水流故障, 高压故障, 电源保护, 通讯故障, 制冷环温过低, 制热环温过高, 系统制冷出水温度过低, 系统制热出水温度过高, 机组未解锁, 与T8600或BAS通信故障, 冬季防冻失效保护, 模块制冷出水温度过低1, 模块制热出水温度过高1, 模块制热出水温度过低1, 压缩机排气温度过高1, 板换冻结保护1, 四通阀切换失败保护1, 机型冲突, 模块与变频器通讯故障, 变频器型号不匹配1, 驱动器或压缩机故障, 风机1高速报警, 风机2高速报警, 风机1低速报警, 风机2低速报警, 主板硬件故障, 主板与风机1通讯故障, 与风机2通讯故障'
        host_alarms = '启动失败, 未知报警'

        # 冷冻泵故障报警列表
        fpump_faults = '工频故障, 变频故障'
        fpump_alarms = '工频启动失败, 变频启动失败'

        # 冷冻阀故障报警列表
        fvalve_faults = '启动失败'
        fvalve_alarms = ''

        # 函数内执行exec，变量都为局部
        lc = locals()
        assign_code = ''.join(['options = ', str.lower(prefix), '_', str.lower(issue_type), 's'])
        exec(assign_code)
        options = lc['options']

        for i in range(qty):
            idx = str(i + 1)
            # 构造标识，比如：Host_1_FaultType
            idf = ''.join([prefix, '_', idx, '_', issue_type, 'Type'])
            if idf in data:
                try:
                    value = int(data[idf])
                    options = options.split(',')

                    # 查询设备昵称，如果没，则为xx号xx
                    nick_name_idf = ''.join([prefix, '_', idx, '_NickName'])
                    nick_name = device.get_value_by_idf(nick_name_idf)
                    if nick_name is None:
                        nick_name = ''.join([idx, '号', name])

                    if value > 0:
                        try:
                            content = ''.join([nick_name, options[value]])
                            method = 'add_%s' % str.lower(issue_type)
                            getattr(DeviceIssue.objects, method)(device, content)
                        except IndexError:
                            # logging.error(f'冷风机冷源, 获取故障报警内容时，越界错误，options length: {len(options)}, idx: {value}')
                            pass

                    else:
                        # 恢复就全部恢复
                        for op in options:
                            content = ''.join([nick_name, op])
                            method = 'recover_%s' % str.lower(issue_type)
                            getattr(DeviceIssue.objects, method)(device, content)
                except ObjectDoesNotExist as e:
                    logging.error(e.with_traceback)
                except IndexError:
                    continue


class CsGateway(BaseDevice):
    """
      冷源网关
    """
    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)

        data = event.get('data', {})

        # 开关机状态
        if 'CSRunStatus' in data:
            if int(data['CSRunStatus']) == 1:
                device.sw_on = True
            else:
                device.sw_on = False
            device.save(update_fields=['sw_on'])
            Terminal.objects.bulk_update_by_device(device, 'sw_on', device.sw_on)

    @classmethod
    def fault(cls, device, event):
        # 目前只有档案馆用，主机、冷冻泵和冷冻阀都是3个
        data = event.get('data', {})
        for i in range(3):
            idx = str(i + 1)

            # 主机
            idf = ''.join(['Host_', idx, '_Fault'])
            idf_type = ''.join(['Host_', idx, '_FaultType'])
            if idf in data:
                issue = ''.join([idx, '#主机故障'])
                if int(data[idf]) == 1 and int(data[idf_type]) != 0:
                    DeviceIssue.objects.add_fault(device, issue)
                else:
                    DeviceIssue.objects.recover_fault(device, issue)

            # 冷冻泵
            idf = ''.join(['FPump_', idx, '_Fault'])
            idf_type = ''.join(['FPump_', idx, '_FaultType'])
            if idf in data:
                issue = ''.join([idx, '#冷动泵故障'])
                if int(data[idf]) == 1 and int(data[idf_type]) != 0:
                    DeviceIssue.objects.add_fault(device, issue)
                else:
                    DeviceIssue.objects.recover_fault(device, issue)

            # 冷冻阀
            idf = ''.join(['FValve_', idx, '_Fault'])
            idf_type = ''.join(['FValve_', idx, '_FaultType'])
            if idf in data:
                issue_1 = ''.join([idx, '#冷动阀开阀故障'])
                issue_2 = ''.join([idx, '#冷动阀关阀故障'])
                if int(data[idf]) == 1 and int(data[idf_type]) != 0:
                    if int(data[idf_type]) == 1:
                        DeviceIssue.objects.add_fault(device, issue_1)

                    if int(data[idf_type]) == 2:
                        DeviceIssue.objects.add_fault(device, issue_2)
                else:
                    DeviceIssue.objects.recover_fault(device, issue_1)
                    DeviceIssue.objects.recover_fault(device, issue_2)

    @classmethod
    def alarm(cls, device, event):
        data = event.get('data', {})
        if 'GatewayAlarm' in data:
            if int(data['GatewayAlarm']) == 1 and int(data['GatewayAlarmType']) != 0:
                if int(data['GatewayAlarmType']) == 1:
                    DeviceIssue.objects.add_alarm(device, '通讯超时')
                if int(data['GatewayAlarmType']) == 2:
                    DeviceIssue.objects.add_alarm(device, '通讯错误')
            else:
                DeviceIssue.objects.recover_alarm(device, '通讯超时')
                DeviceIssue.objects.recover_alarm(device, '通讯超时')

        # 目前只有档案馆用，主机、冷冻泵和冷冻阀都是3个
        for i in range(3):
            idx = str(i + 1)

            # 主机
            idf = ''.join(['Host_', idx, '_Alarm'])
            idf_type = ''.join(['Host_', idx, '_AlarmType'])
            if idf in data:
                issue = ''.join([idx, '#主机启动失败'])
                if int(data[idf]) == 1 and int(data[idf_type]) != 0:
                    DeviceIssue.objects.add_alarm(device, issue)
                else:
                    DeviceIssue.objects.recover_alarm(device, issue)

            # 冷冻泵
            idf = ''.join(['FPump_', idx, '_Alarm'])
            idf_type = ''.join(['FPump_', idx, '_AlarmType'])
            if idf in data:
                issue = ''.join([idx, '#冷动泵启动失败'])
                if int(data[idf]) == 1 and int(data[idf_type]) != 0:
                    DeviceIssue.objects.add_alarm(device, issue)
                else:
                    DeviceIssue.objects.recover_alarm(device, issue)

            # 冷冻阀
            idf = ''.join(['FValve_', idx, '_Alarm'])
            idf_type = ''.join(['FValve_', idx, '_AlarmType'])
            if idf in data:
                issue = ''.join([idx, '#冷动阀报警'])
                if int(data[idf]) == 1 and int(data[idf_type]) != 0:
                    DeviceIssue.objects.add_alarm(device, issue)
                else:
                    DeviceIssue.objects.recover_alarm(device, issue)


class Ecat(BaseDevice):
    """
     Ecat冷源
    """
    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)
        CommCs.update_attres(device, event)

    @classmethod
    def fault(cls, device, event):
        CommCs.fault(device, event)

    @classmethod
    def alarm(cls, device, event):
        CommCs.alarm(device, event)


class Baxter(BaseDevice):
    """
     百特冷源
    """
    @classmethod
    def update_attres(cls, device, event):
        super().update_attres(device, event)
        CommCs.update_attres(device, event)

    @classmethod
    def fault(cls, device, event):
        CommCs.fault(device, event)

    @classmethod
    def alarm(cls, device, event):
        CommCs.alarm(device, event)
