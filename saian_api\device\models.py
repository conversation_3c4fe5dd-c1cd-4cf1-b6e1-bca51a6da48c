import copy
import datetime
import json
import logging
import os
import re
import traceback

from asgiref.sync import sync_to_async
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.core import exceptions
from django.db import IntegrityError
from django.db import models
from django.db import transaction
from django.db.models import Q, Subquery, OuterRef
from django.db.models.base import ModelState
from django.utils.module_loading import import_string

from saian_api.building.models import ActiveRoom
from saian_api.devdefine.models import AttributePrototype, DevicePrototype, DeviceType
from saian_api.dimension.models import Dimension, DimensionAttribute
from saian_api.project.models import Project
from saian_api.report.models import DeviceDailyStat, DeviceHourlyStat, DeviceMonthlyStat, DeviceYearlyStat, ReportConfigurer
from saian_api.terminal.models import TerminalAttribute, Terminal
from saian_api.utils.db.Redis import RedisHelper
from saian_api.utils.inthttpapi.device import Device<PERSON>pi, AdminDeviceApi
from saian_api.utils.utils import CeleryTaskUtils

logger = logging.getLogger('django')


class DeviceManager(models.Manager):
    """
      设备管理器
    """

    def offline_rate(self, project):
        """统计设备离线率前5"""

        offline_devices = self.filter(online=False, project=project)
        total = self.all().count()

        offlineDevices = offline_devices.values('device_type_id').annotate(
            count=models.Count('id')).order_by('-count')[:10]

        top10 = []
        for device in offlineDevices:
            proto_name = DeviceType.objects.get(
                pk=device['device_type_id']).name
            top10.append({
                'device_type': proto_name,
                'count': device['count'],
                'rate': '%.2f%%' % (device['count'] / total * 100 if total else 0),
            })

        return {'total_offline': offline_devices.count(), 'top10': top10}

    # @transaction.atomic()
    def create_single(self, request, serializer):
        """添加单个设备"""
        project_id = request.user.get('project_id')
        try:
            with transaction.atomic(using=f"prj{project_id}db"):
                from .models import DeviceAttribute

                dp = DevicePrototype.objects.get(
                    pk=request.data['device_prototype_id'])
                device = serializer.save()
                if dp is not None:
                    for ap in enumerate(dp.attributeprototype_set.all()):
                        if not DeviceAttribute.objects.filter(device_id=device.id, attribute_prototype_id=ap[1].id).exists():
                            DeviceAttribute.objects.create(
                                attribute_prototype_id=ap[1].id,
                                value=ap[1].default_value,
                                show_in_list=True,
                                device_id=device.id
                            )

                return serializer.data
        except IntegrityError:
            print('添加单个设备出错！')

    def name_by_mac(self, mac):
        """根据mac查询设备"""
        if mac is None:
            return None

        device = Device.objects.filter(mac=mac).first()
        name = None
        if device is not None:
            name = device.nick_name

        return name

    def cron_check_online(self):
        """定时任务，检查设备是否在线()"""
        from saian_api.terminal.models import Terminal
        from saian_api.message.models import Message

        projects = CeleryTaskUtils.get_all_project()
        # projects = [{'db_name': 'prj1db', 'project': Project.objects.using('prj1db').first(), 'project_id': 1}]
        for project in projects:
            two_hour_ago = datetime.datetime.now() - datetime.timedelta(hours=2)
            print(f"设备在线检查: {project['project'].name}")
            db = project['db_name']
            offline_devices = Device.objects.using(db).filter(updated_at__lt=two_hour_ago, status__in=[20, 40], project__isnull=False)
            if offline_devices.exists():
                for device in offline_devices:
                    device.online = False
                    device.status = 30
                    Message.objects.device_offline(device, db)
                Device.objects.using(db).bulk_update(offline_devices, fields=['online', 'status'])
                # Device.objects.using('syadmindb').bulk_update(offline_devices, fields=['online', 'status'])
                # 更新对应的终端在线状态
                offline_terminals = Terminal.objects.using(db).filter(device__in=offline_devices)
                for terminal in offline_terminals:
                    terminal.online = False
                Terminal.objects.using(db).bulk_update(offline_terminals, fields=['online'])

    def get_or_create_iu(self, p_device, mac, addr, idx, prefix=None):
        """查询或创建子设备"""
        try:
            device = self.get(mac=mac)
            return device
        except Device.DoesNotExist:
            c_dp = DevicePrototype.objects.filter(parent_id=p_device.device_prototype_id, prefix=prefix)
            if c_dp.count() == 0:
                logging.error(f'创建子设备失败, 子设备类型不存在: pid={p_device.device_prototype_id}, prefix={prefix}')
                return None
            elif c_dp.count() != 1:
                logging.error(f'创建子设备失败, 找到多个子设备类型: pid={p_device.device_prototype_id}, prefix={prefix}')
                return None
            c_dp = c_dp.last()

            device_data = {
                "project": p_device.project_id,
                "device_type_id": c_dp.device_type_id,
                "device_prototype_id": c_dp.id,
                "nick_name": f'{p_device.nick_name}#{addr}-{idx}',
                "mac": mac,
                "did": p_device.did,
                "batch_no": p_device.batch_no,
                "status": p_device.status,
                "address": p_device.address,
                "remark": p_device.remark,
                "in_alarm": p_device.in_alarm,
                "wudid": p_device.wudid,
                "platform": p_device.platform,
                "prd_key": p_device.prd_key,
                "in_fault": p_device.in_fault,
                "online": p_device.online,
                "gw_end_point": p_device.gw_end_point,
                "agent_id": p_device.agent_id,
                "ali_secret": p_device.ali_secret,
                "ali_region": p_device.ali_region,
                "ali_device_name": p_device.ali_device_name,

            }

            r = AdminDeviceApi.create_device(device_data)
            device = self.get(mac=mac) if r.status_code == 200 else None
            if device is not None:
                DeviceAttribute.objects.create_by_device(device)
                Terminal.objects.create_by_device(device)
                # TerminalAttribute.objects.create_by_device(device)
            return device

        except Device.MultipleObjectsReturned:
            devices = self.filter(mac=mac).order_by('created_at')
            devices.first().delete()
            return devices.last()

    def get_or_create_device(self, derive_from, nick_name, mac, dp: DevicePrototype):
        """查询或创建设备"""
        try:
            device = self.get(mac=mac)
            return device
        except Device.DoesNotExist:
            device_data = {
                "project": derive_from.project_id,
                "device_type_id": dp.device_type_id,
                "device_prototype_id": dp.id,
                "nick_name": nick_name,
                "mac": mac,
                "did": derive_from.did,
                "batch_no": derive_from.batch_no,
                "status": derive_from.status,
                "address": derive_from.address,
                "remark": derive_from.remark,
                "in_alarm": derive_from.in_alarm,
                "wudid": derive_from.wudid,
                "platform": derive_from.platform,
                "prd_key": derive_from.prd_key,
                "in_fault": derive_from.in_fault,
                "online": derive_from.online,
                "gw_end_point": derive_from.gw_end_point,
                "agent_id": derive_from.agent_id,
                "ali_secret": derive_from.ali_secret,
                "ali_region": derive_from.ali_region,
                "ali_device_name": derive_from.ali_device_name,

            }
            r = AdminDeviceApi.create_device(device_data)
            if r.status_code != 200:
                logging.error(f'向admin2创建设备失败: status_code-{r.status_code}, mac-{mac}, device_prototype: {dp.uni_name}')
                return None
            else:
                device = self.filter(mac=mac).last()
                device.online = True
                device.status = 20
                device.save()
                if device is not None:
                    DeviceAttribute.objects.create_by_device(device)
                    Terminal.objects.create_by_device(device)
                return device

        except Device.MultipleObjectsReturned:
            devices = self.filter(mac=mac).order_by('created_at')
            devices.first().delete()
            return devices.last()


class Device(models.Model):
    """
      设备信息
    """
    # id
    id = models.BigIntegerField(primary_key=id)
    # 所属项目（设备取消项目关联时，将 project 设置为 None）
    project = models.ForeignKey(Project, on_delete=models.CASCADE, null=True, blank=True)
    # 所属设备分类
    device_type_id = models.BigIntegerField(db_index=True)
    # 所属设备类型
    device_prototype_id = models.BigIntegerField(db_index=True)
    # 昵称
    nick_name = models.CharField(max_length=255, blank=True, null=True, db_index=True)
    # Mac
    mac = models.CharField(max_length=255, blank=True, null=True)
    # 设备did
    did = models.CharField(max_length=255, blank=True, null=True)
    # 批量导入时的批次号
    batch_no = models.CharField(max_length=255, blank=True, null=True)
    # 安装地址
    address = models.CharField(max_length=255, blank=True, null=True)
    # 状态 10 - 初始化，20 - 上线，30-下线，40-故障
    status = models.IntegerField(blank=True, null=True)
    # 备注
    remark = models.CharField(max_length=255, blank=True, null=True)
    # 是否处于报警
    in_alarm = models.BooleanField(default=False)
    # wudid
    wudid = models.CharField(max_length=255, blank=True, null=True)
    # 平台
    platform = models.IntegerField(blank=True, null=True)
    # prd_key
    prd_key = models.CharField(max_length=255, blank=True, null=True)
    # 是否处于故障
    in_fault = models.BooleanField(default=False)
    # 是否在线
    online = models.BooleanField(default=False)
    # 开光状态
    sw_on = models.BooleanField(default=False)
    # 网关地址
    gw_end_point = models.CharField(max_length=255, blank=True, null=True)
    # 所属代理商
    agent_id = models.BigIntegerField(null=True)
    # 是否在验收模式
    in_acc = models.BooleanField(default=False, null=True)
    # 阿里云secret
    ali_secret = models.CharField(max_length=255, blank=True, null=True)
    # 阿里云region
    ali_region = models.CharField(max_length=255, blank=True, null=True)
    # 阿里云device name
    ali_device_name = models.CharField(max_length=255, blank=True, null=True)
    # 是否需要维护
    needs_m = models.BooleanField(default=False, null=True)
    # 是否开启实时数据更新，目前针对冷源
    live_update = models.BooleanField(default=False, null=True)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    # 绑定的房间
    # room_device = models.OneToOneField(RoomDevice, on_delete = models.CASCADE)

    class Meta:
        db_table = 'devices'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['project_id', 'nick_name']),
            models.Index(fields=['project_id', 'device_type_id']),
            models.Index(fields=['project_id', 'device_type_id', 'status']),
            models.Index(fields=['project_id', 'device_type_id', 'in_fault']),
            models.Index(fields=['project_id', 'device_type_id', 'in_alarm']),
        ]

    objects = DeviceManager()

    @property
    def device_prototype(self):
        """设备所属类型"""
        return DevicePrototype.objects.get(pk=self.device_prototype_id)

    @property
    def device_type(self):
        return DeviceType.objects.get(pk=self.device_type_id)

    @property
    def active_room(self):
        """绑定的房间，如果有"""
        from .models import RoomDevice

        active_room = None
        device_model = ContentType.objects.get_for_model(self)
        try:
            rd = RoomDevice.objects.filter(object_id=self.id, content_type=device_model)
            if rd.exists():
                rd = rd.last()
                active_room = ActiveRoom.objects.get(pk=rd.active_room_id)
        except exceptions.ObjectDoesNotExist:
            active_room = None

        return active_room

    @property
    def cs_attres(self):
        """Web冷源列表属性列表"""
        from .models import DeviceAttribute
        attres = []
        idfs = ['CWOPTemp', 'CWRPTemp', 'CWPPressDiff', 'CWPTempDiff']
        aps = AttributePrototype.objects.filter(device_prototype_id=self.device_prototype_id, identifier__in=idfs)
        for ap in aps:
            # da = DeviceAttribute.objects.get_by_ap(self, ap)
            da = DeviceAttribute.objects.query_object_by_ap(self, ap)
            attres.append({
                'identifier': ap.identifier,
                'unit': ap.unit,
                'value': da.value
            })

        return attres

    @property
    def ro_attres(self):
        """只读参数列表"""
        from .models import DeviceAttribute

        attr_ids = AttributePrototype.objects.filter(device_prototype_id=self.device_prototype_id, read_only=True).values_list('id', flat=True)
        # return DeviceAttribute.objects.filter(device_id=self.id).extra(
        #     select={
        #         'name': 'attribute_prototypes.name',
        #         'identifier': 'attribute_prototypes.identifier',
        #         'unit': 'attribute_prototypes.unit'
        #     },
        #     tables = ['attribute_prototypes'],
        #     where=['attribute_prototypes.read_only = 1']
        #     )

        # return DeviceAttribute.objects.filter(attribute_prototype_id__in=attr_ids)
        return DeviceAttribute.objects.query_object_list(self, attr_ids)

    def get_value_by_idf(self, idf, db=None):
        """
          根据identifier查询参数值
        """
        da = self.get_attr_by_name(idf, db)

        return da.value if da is not None else None

    def set_online(self):
        """更新设备在线"""
        if not self.online:
            from saian_api.message.models import Message
            from saian_api.notifi.models import Notification

            # 如果设备是派诺分体空调或烟感，不能依据上报来更新是否在线
            uni_name = self.device_prototype.uni_name
            if uni_name == '派诺分体空调' or uni_name == 'NB烟感':
                return

            self.online = True

            if self.status != 40:
                self.status = 20

            Device.objects.filter(id=self.id).update(status=self.status, online=self.online)
            # self.save(update_fields=['online', 'status'])

            # 更新设备本体的终端在线状态
            for terminal in Terminal.objects.filter(device=self, idx__isnull=True, prefix__isnull=True, online=False):
                terminal.online = True
                terminal.save()

            Message.objects.device_online(self)
            data = {
                'content': f'设备恢复在线: 设备【{self.id}-{self.nick_name}】恢复在线',
                'device_id': self.id,
                'device_nickname': self.nick_name
            }
            Notification.objects.notify(4, self, data)

        if Device.objects.filter(mac__startswith=self.mac[:15] if len(self.mac) >= 15 else self.mac, online=False).exists():  # DTU 设备在线状态处理
            if len(self.mac) > 15:
                dtu_mac = self.mac[:15]
                dtu_devices = Device.objects.filter(mac=dtu_mac, online=False)
                if dtu_devices.count() == 1:
                    dtu_device = dtu_devices.last()
                    dtu_device.online = True
                    dtu_device.status = 20
                    dtu_device.save()
                    Terminal.objects.bulk_update_by_device(dtu_device, 'online', dtu_device.online)

            # 内机设备在线状态处理
            iu_devices = Device.objects.filter(mac__startswith=self.mac, online=False)
            for iu_device in iu_devices:
                iu_device.status = 20
                iu_device.online = True
                iu_device.save(update_fields=['online', 'status'])
                Terminal.objects.bulk_update_by_device(iu_device, 'online', iu_device.online)

        # Terminal.objects.bulk_update_by_device(self, 'online', self.online)

    def set_offline(self):
        """更新设备离线"""
        if self.online:
            from saian_api.message.models import Message
            from saian_api.notifi.models import Notification
            self.online = False
            self.status = 30
            Message.objects.device_offline(self)
            data = {
                'content': f'设备离线: 设备【{self.id}-{self.nick_name}】离线',
                'device_id': self.id,
                'device_nickname': self.nick_name
            }
            Notification.objects.notify(4, self, data)

            Device.objects.filter(id=self.id).update(online=self.online, status=self.status)
            # self.save(update_fields=['online', 'status'])

            Terminal.objects.bulk_update_by_device(self, 'online', self.online)

    def data_route(self, data, event_id=None):
        """
            数据流转，取平均值更新到目标设备的参数
        """
        src_config = DataRoute.objects.filter(src_mac=self.mac, enabled=True)
        # 源设备处理
        if src_config.exists():
            for config in src_config:
                if config.src_idf in data:
                    # 与 config 拥有相同目标，相同源idf 的其它配置项，other_routes 存在时，取平均值作为目标设备的更新值
                    other_routes = DataRoute.objects.filter(dest_mac=config.dest_mac, dest_idf=config.dest_idf,
                                                            src_idf=config.src_idf, enabled=True).exclude(id=config.id)

                    other_src_mac = list(other_routes.values_list('src_mac', flat=True))
                    other_dest_mac = list(other_routes.values_list('dest_mac', flat=True))

                    values = [float(data[config.src_idf])]

                    if len(other_src_mac):
                        # 只计算在线设备
                        other_devices = Device.objects.filter(mac__in=other_src_mac, online=True)
                        for device in other_devices:
                            # da = DeviceAttribute.objects.get_by_idf(device, config.src_idf)
                            da = DeviceAttribute.objects.query_object_by_idf(device, config.src_idf)
                            if da is not None:
                                values.append(float(da.value))

                    avg = round(sum(values) / len(values), 1)

                    # 目标设备和目标参数
                    dest_idf = config.dest_idf
                    dest_devices = Device.objects.filter(mac__in=[config.dest_mac] + other_dest_mac)

                    executor = {"name": "数据流转"}
                    for device in dest_devices:
                        changes = {
                            dest_idf: avg
                        }
                        device.send_ctrls(executor, changes)

    def update_by_pushing(self, event, event_id):
        """
          根据上报数据更新设备参数值
          data是一个字段,
          event_id: 该上报数据在 device_events 表的id，用来更新转换后的 data 值
        """
        from saian_api.terminal.models import Terminal
        from saian_api.message.models import Message
        from saian_api.notifi.models import Notification

        data = event.get('data', None)

        # 是否有数据上报就更新设备的在线状态。
        update_online = True
        if data is not None:
            # 当上报的数据中有 "unixTime" 并且 "unixTime" 比设备的 unixTime 属性值小时，不更新设备的在线状态
            if 'unixTime' in data:
                unix_time = self.get_value_by_idf('unixTime')
                if unix_time is not None and float(unix_time) > float(data['unixTime']):
                    update_online = False

            # 阿里云平台数据推送, 设备在线状态处理
            if event.get('event_type', None) == 'aliiot_data_push':
                cmd = event.get('cmd', '')
                if cmd.startswith('/as/mqtt/status'):  # 阿里云设备在线状态上报
                    # 当上报的数据中有 "lastTime" 和 "status" 时，设备的在线状态由 status 决定
                    if 'lastTime' in data and 'status' in data:
                        update_online = False
                        last_time = datetime.datetime.strptime(data.get('lastTime'), '%Y-%m-%d %H:%M:%S.%f')
                        # unix_time_da = DeviceAttribute.objects.get_by_idf(self, 'unixTime')
                        unix_time_da = DeviceAttribute.objects.query_object_by_idf(self, 'unixTime')
                        if not (unix_time_da is not None and unix_time_da.value is not None and last_time.timestamp() < float(unix_time_da.value)):
                            if data['status'] == 'online':
                                self.set_online()

                            elif data['status'] == 'offline':
                                self.set_offline()

                            if unix_time_da is not None:
                                unix_time_da.value = last_time.timestamp()
                                unix_time_da.save()
                                DeviceAttribute.objects.save_to_redis(self, unix_time_da)
                elif '/thing/' in cmd:  # thing 不更新
                    update_online = False
                else:
                    update_online = True

                # topic 是物联网平台推送的消息, 不应以此为依据更新设备在线状态
                if 'topic' in data:
                    update_online = False

            # 机智云事件处理
            if self.platform == 10:
                if event.get('event_type', None) == 'device_online':
                    self.set_online()
                elif event.get('event_type', None) == 'device_offline':
                    self.set_offline()
                # 设备状态事件才是设备真实上报 "device_status_kv"
                elif event.get('event_type', None) == 'device_status_kv':
                    update_online = True
                else:
                    update_online = False

        if update_online and not self.in_acc:
            # 如果设备不在在线状态，并且不在验收模式，有新数据上报时就更新为在线
            self.set_online()

        try:
            # self.save(update_fields=['updated_at'])
            Device.objects.filter(id=self.id).update(updated_at=datetime.datetime.now())
            # self.save(using='syadmindb')
            # AdminDeviceApi.update_device(self.id, {})
        except Exception as e:
            logger.error(f'failed to save device updated_at. error: {e.__str__()}')
        # 到特定的设备模块处理上报数据、故障和报警
        model_name = self.device_prototype.m_name
        if model_name is not None:
            # 动态导入对应的设备处理模块
            try:
                device_model = import_string('saian_api.device.domain_models.%s' % model_name)
                if device_model is not None:
                    event['event_id'] = event_id
                    # 设备参数处理
                    device_model.update_attres(self, event)
                    # 故障处理
                    device_model.fault(self, event)
                    # 报警处理
                    device_model.alarm(self, event)
            except ImportError:
                logging.error(f'找不到设备处理模块：{model_name}, 设备类型：{self.device_prototype.id}-{self.device_prototype.name}')
        else:
            from saian_api.device.domain_models.base import BaseDevice
            BaseDevice.update_attres(self, event)
            BaseDevice.fault(self, event)
            BaseDevice.alarm(self, event)

    def check_acc(self):
        """检查是否处于验收模式"""
        result = False
        if self.project is not None:
            if self.project.in_acc or self.in_acc:
                result = True
        return result

    def cal_run_time(self, db=None):
        """
           目的：计算设备的综合总保养运行时间，盘管和其他设备计算方式不一样
           对于盘管正常公式为：设备高速运行时间 x 高速运行时间系数 + 设备中速运行时间 x 中速运行时间系数 + 设备低速运行时间 x 低速运行时间系数
           其他设备公式为：当前时间 - 上次保养时间
           假设：设备上报的运行时间为A, 上次保养时设备上报的运行时间为B，
           当A小于等于B时（即设备运行时间已经重置，运行时间由小程序接管维护），计算公式为：当前时间 - 上次保养时间
           当A大于B时（即设备累计的时间有效，没有重置），采用正常计算公式
           当没有历史保养记录时，则直接用设备上报的时间
           单位：分钟
        """
        import datetime
        last_m = self.maintenance_set.last() if db is None else self.maintenance_set.using(db).last()
        device_type = DeviceType.objects.using(db).get(pk=self.device_type_id)
        if device_type.uni_name == '盘管':

            high_speed_time = self.get_value_by_idf("HighSpeedTime", db)
            high_speed_time = int(high_speed_time) if high_speed_time else 0
            mid_speed_time = self.get_value_by_idf("MidSpeedTime", db)
            mid_speed_time = int(mid_speed_time) if mid_speed_time else 0
            low_speed_time = self.get_value_by_idf("LowSpeedTime", db)
            low_speed_time = int(low_speed_time) if low_speed_time else 0
            if last_m is not None:
                if (
                        high_speed_time <= last_m.high_speed_time or
                        mid_speed_time <= last_m.mid_speed_time or
                        low_speed_time <= last_m.low_speed_time
                ):
                    # 设备运行时间已经被重置，即已经经过维护，直接用当前时间 - 上次维护时间来计算设备的运行时间
                    run_time_sec = (datetime.datetime.now() - last_m.created_at).total_seconds()
                    run_time, s = divmod(run_time_sec, 60)
                else:
                    hs_run_time = high_speed_time - last_m.high_speed_time
                    ms_run_time = mid_speed_time - last_m.mid_speed_time
                    ls_run_time = low_speed_time - last_m.low_speed_time

                    run_time = hs_run_time * self.project.hs_weight / 100 + ms_run_time * self.project.ms_weight / 100 + ls_run_time * self.project.ls_weight / 100
            else:
                run_time = high_speed_time * self.project.hs_weight / 100 + mid_speed_time * \
                    self.project.ms_weight / 100 + low_speed_time * self.project.ls_weight / 100
        else:
            if last_m is not None:
                run_time_sec = (datetime.datetime.now() - last_m.created_at).total_seconds()
                run_time, s = divmod(run_time_sec, 60)
            else:
                run_time = 0

        return run_time

    def today_power_cons(self):
        """当天用电量"""
        return 0

    def today_cool_cons(self):
        """当天用冷量"""
        return 0

    def parse_attres(self, attres, prefix=None, idx=None):
        """解释参数列表，并补全参数其他信息，如：最小值、最小值、默认值等"""
        from saian_api.device.models import DeviceAttribute

        idf_list = [attr['idf'] for attr in attres]
        target_aps = list(AttributePrototype.objects.filter(device_prototype_id=self.device_prototype_id, identifier__in=idf_list))
        ap_ids = [ap.id for ap in target_aps]
        # target_das = list(DeviceAttribute.objects.filter(attribute_prototype_id__in=ap_ids, device_id=self.id))
        target_das = DeviceAttribute.objects.query_object_list(self, ap_ids)

        # 温控器v2酒店版，特殊功能处理
        dehumi = None
        if self.device_prototype_id == 223 and DeviceAttribute.objects.query_object_by_idf(self, 'SpecialFunc').value == '湿度控制':
            dehumi = True

        for index, attr in enumerate(attres):
            da = None
            ap = None
            name = attr['name'] if 'name' in attr else None
            attr['identifier'] = attr['idf']
            aps = list(filter(lambda x: x.identifier == attr['idf'], target_aps))
            if len(aps):
                ap = aps[0]
                das = list(filter(lambda x: x.attribute_prototype_id == ap.id, target_das))
                if len(das):
                    da = das[0]
            if da is None:
                # da = DeviceAttribute.objects.get_by_idf(self, attr['idf'])
                da = DeviceAttribute.objects.query_object_by_idf(self, attr['idf'])
            if da is not None:
                DevicePrototype.objects.ap_values(attres[index], da, ap)
                if name is not None:
                    attres[index]['name'] = name
                if dehumi and attr['identifier'] == 'FCUStatus':
                    attres[index]['value'] = '除湿'

            # 解析更多详情
            details = attr.get('detail', None)
            if details is not None:
                details_str = json.dumps(details)
                idf_list = re.findall(r'"idf":\s?"(\w+)"', details_str)
                target_aps = list(AttributePrototype.objects.filter(device_prototype_id=self.device_prototype_id, identifier__in=idf_list))
                ap_ids = [ap.id for ap in target_aps]
                # target_das = list(DeviceAttribute.objects.filter(device_id=device.id, attribute_prototype_id__in=ap_ids))
                target_das = DeviceAttribute.objects.query_object_list(self, ap_ids)
                parsed_details = DevicePrototype.objects.parse_content(self, details_str, das=target_das, aps=target_aps, prefix=prefix, idx=idx)
                attr['detail'] = parsed_details

        return attres

    def dashboard_attres(self):
        """dashboard参数列表"""
        device_prototype = DevicePrototype.objects.get(pk=self.device_prototype_id)

        if device_prototype.dashboard_attres is not None:
            dashboard_attres = json.loads(device_prototype.dashboard_attres)
            if dashboard_attres.get('default', None) is not None:
                try:
                    return self.parse_attres(dashboard_attres['default'])
                except exceptions.FieldError:
                    logging.error('Dashboard attributes invalid!')
            else:
                return None

        return None

    def detail_attres(self):
        """detail_attres参数列表"""
        device_prototype = DevicePrototype.objects.get(pk=self.device_prototype_id)

        if device_prototype.detail_attres is not None:
            detail_attres = json.loads(device_prototype.detail_attres)
            if detail_attres.get('default') is not None:
                return self.parse_attres(detail_attres['default'])
            else:
                # raise exceptions.FieldError('Detail attributes invalid!')
                logger.error(f'设备类型: {device_prototype.id}-{device_prototype.name}, detail attributes 配置不合法！')

        return None

    def get_attr_by_name(self, identifier, db=None):
        """根据identifier查询设备参数"""
        from .models import DeviceAttribute

        try:
            if db is None:
                try:
                    # ap = AttributePrototype.objects.get(
                    #     identifier=identifier, device_prototype_id=self.device_prototype_id)
                    ap = AttributePrototype.objects.query_by_idf(self, identifier)
                    if ap is None:
                        ap = AttributePrototype.objects.filter(
                            identifier=identifier, device_prototype_id=self.device_prototype_id).order_by('id').last()
                    if ap is None:
                        return None
                    # da = DeviceAttribute.objects.get(attribute_prototype_id=ap.id, device_id=self.id)
                    # da = DeviceAttribute.objects.get_by_ap(self, ap)
                    da = DeviceAttribute.objects.query_object_by_ap(self, ap)
                except AttributePrototype.MultipleObjectsReturned:
                    ap = AttributePrototype.objects.filter(
                        identifier=identifier, device_prototype_id=self.device_prototype_id).order_by('id').last()
                    # da = DeviceAttribute.objects.get(attribute_prototype_id=ap.id, device_id=self.id)
                    # da = DeviceAttribute.objects.get_by_ap(self, ap)
                    da = DeviceAttribute.objects.query_object_by_ap(self, ap)
                except exceptions.ObjectDoesNotExist:
                    da = None
            else:
                try:
                    ap = AttributePrototype.objects.using(db).filter(
                        identifier=identifier, device_prototype_id=self.device_prototype_id).order_by('id').last()
                    if ap is None:
                        da = None
                    else:
                        da = DeviceAttribute.objects.using(db).filter(attribute_prototype_id=ap.id, device_id=self.id).order_by('id').last()
                except DeviceAttribute.MultipleObjectsReturned:
                    ap = AttributePrototype.objects.using(db).filter(
                        identifier=identifier, device_prototype_id=self.device_prototype_id).order_by('id').last()
                    da = DeviceAttribute.objects.using(db).get(attribute_prototype_id=ap.id, device_id=self.id)
                except exceptions.ObjectDoesNotExist:
                    da = None

        except exceptions.ObjectDoesNotExist:
            da = None
        return da

    def et_types(self, prefix=None, index=None, parse_detail=True, parse_dashboard=True):
        """
            外设配置数据
            prefix 和 index 指定了子设备(终端)的设备类型和序号。当只需要某一个具体的子设备外设配置数据时，提供 prefix 和 index 参数可以跳过其它子设备外设配置数据的查询计算
            parse_detail: 是否解析设备类型的 detail_attres，一般作为设备列表调用时为 False
            parse_dashboard: 是否解析设备类型的 dashboard_attres，一般作为设备详情调用时为 False
        """
        types = []
        extens = self.device_prototype.children.order_by('id')
        if prefix is not None:
            extens = extens.filter(prefix=prefix)
        if extens.count() == 0:
            extens = DevicePrototype.objects.filter(pk=self.device_prototype_id)

        if extens is not None and len(extens.all()) > 0:
            for exten in extens.all():
                # 如果 dashboard_attres 和 detail_attres 都没有配置，则隐藏
                if (not exten.dashboard_attres or exten.dashboard_attres == '{}') and (not exten.detail_attres or exten.detail_attres == '{}'):
                    # logging.info(f'{exten.id}-{exten.name} 没有配置列表参数和详情参数，跳过')
                    continue
                # 子设备类型不一定有 prefix
                if not exten.prefix:
                    continue
                qty = self.get_attr_by_name(exten.prefix + "Qty")
                if qty is None:
                    qty = self.get_attr_by_name(exten.prefix + "_Qty")
                if qty is not None:
                    qty = qty.value
                else:
                    # raise exceptions.FieldError('%s_qty not found!' % exten.prefix)
                    logging.error('%s_qty not found!' % exten.prefix)

                if qty is not None and int(qty) > 0:
                    type = {
                        'name': exten.name,
                        'prefix': exten.prefix,
                        'extensions': []
                    }

                    extensions = []

                    for idx in range(0, int(qty)):
                        if exten.dashboard_attres is not None or exten.detail_attres is not None:
                            idx = idx + 1
                            if index is not None and idx != index:
                                continue

                            dashboard_attres = {}
                            detail_attres = {}

                            # 查询昵称参数，参数有两种形式。prefix_idx_NickName 和 prefix_NickName_idx
                            nick_name_idf = f'{exten.prefix}_{idx}_NickName'
                            nick_name = self.get_attr_by_name(nick_name_idf)
                            if nick_name is None:
                                nick_name_idf = f'{exten.prefix}_NickName_{idx}'
                                nick_name = self.get_attr_by_name(nick_name_idf)
                            if nick_name is None:
                                raise exceptions.FieldError('%s not found!' % nick_name_idf)

                            sub_device_idf = ''.join([exten.prefix, '_', str(idx)])

                            # 解析设备的 dashboard_attres
                            if exten.dashboard_attres is not None and parse_dashboard:
                                dashboard_attres_str = json.loads(exten.dashboard_attres)
                                if sub_device_idf not in dashboard_attres_str:
                                    continue
                                    # raise exceptions.FieldError(
                                    #     ''.join([sub_device_idf, ' not in dashboard attres, idf = ', sub_device_idf])
                                    # )
                                dashboard_attres = self.parse_attres(dashboard_attres_str[sub_device_idf], prefix=prefix, idx=idx)
                            # 解析设备的 detail_attres
                            if exten.detail_attres is not None and parse_detail:
                                detail_attres_str = json.loads(exten.detail_attres)
                                if sub_device_idf not in detail_attres_str:
                                    raise exceptions.FieldError(
                                        ''.join([sub_device_idf, ' not in detail attres, idf = ', sub_device_idf])
                                    )
                                detail_attres = self.parse_attres(detail_attres_str[sub_device_idf], prefix=prefix, idx=idx)

                            sub_device = {
                                'idx': idx,
                                'nick_name': nick_name.value if nick_name.value is not None else f'{idx}#{exten.name}',
                                'attres': dashboard_attres,
                                'detail_attres': detail_attres
                            }

                            ap = AttributePrototype.objects.filter(identifier=f'{exten.prefix}_{idx}_ShowEN',
                                                                   device_prototype_id=self.device_prototype_id)
                            if ap.exists():
                                # da = DeviceAttribute.objects.filter(device_id=self.id, attribute_prototype_id=ap.first().id)
                                da = DeviceAttribute.objects.query_object_by_ap(self, ap.last())
                                if da:
                                    sub_device['show_en'] = da.value != '0'

                            extensions.append(sub_device)

                    type['extensions'] = extensions
                    types.append(type)

        return types

    def last_update_at(self):
        """设备最后更新时间"""
        # dts = self.deviceattribute_set
        return self.updated_at

    def current_temp(self):
        """设备当前温度，如果有"""
        from saian_api.utils.tools import is_number

        temp = 0
        ap = AttributePrototype.objects.filter(device_prototype_id=self.device_prototype_id).filter(
            Q(identifier='LocalTemp') | Q(identifier='OutletAirTemp'))
        if ap.exists():
            # da = DeviceAttribute.objects.filter(attribute_prototype_id=ap.last().id, device_id=self.id)
            da = DeviceAttribute.objects.query_object_by_ap(self, ap.last())
            if da:
                temp = '%.2f' % float(da.value) if is_number(da.value) and da.value != '--' else 0
        return temp

    def current_hum(self):
        """当前湿度，如有"""
        from saian_api.utils.tools import is_number

        # hourly_stat = DeviceHourlyStat.objects.filter(identifier='LocalRH', device_id=self.id)
        # da = DeviceAttribute.objects.get_by_idf(self, 'LocalRH')
        da = DeviceAttribute.objects.query_object_by_idf(self, 'LocalRH')
        if da is not None:
            return '%.2f' % float(da.value) if is_number(da.value) and da.value != '--' else 0
        return 0

    def dew_point(self):
        """露点温度"""
        return 0

    def run_time(self):
        """运行时间"""
        return 0

    def key_attributes(self):
        """关键属性列表"""
        key_aps = list(AttributePrototype.objects.filter(device_prototype_id=self.device_prototype_id, show_in_list=True))
        # key_das = list(DeviceAttribute.objects.filter(device_id=self.id, attribute_prototype_id__in=[ap.id for ap in key_aps]))
        key_das = DeviceAttribute.objects.query_object_list(self, [ap.id for ap in key_aps])
        ka_fields = []

        for ap in key_aps:
            das = list(filter(lambda x: x.attribute_prototype_id == ap.id, key_das))
            da = das[0] if len(das) else None
            if da is None:
                # da = DeviceAttribute.objects.get_by_ap(self, ap)
                da = DeviceAttribute.objects.query_object_by_ap(self, ap)
            ka_fields.append({
                'id': da.id,
                'value': da.value if ap.identifier != 'RSSI' else int(da.value, 16) if da.value is not None and da.value.isdigit() else 0,
                'name': ap.label if ap.label is not None else ap.name,
                'unit': ap.unit,
                'identifier': ap.identifier,
                'min_value': ap.min_value,
                'max_value': ap.max_value,
                'in_crement': ap.in_crement,
                'pre_cision': ap.pre_cision,
                'options': ap.options
            })

        # key_attributes = self.deviceattribute_set.filter(show_in_list=True)
        # for ka in key_attributes:
        #     try:
        #         ka_ap = AttributePrototype.objects.get(pk=ka.attribute_prototype_id)
        #         ka_fields.append({
        #             'id': ka.id,
        #             'value': ka.value if ka_ap.identifier != 'RSSI' else int(ka.value, 16) if ka.value is not None and ka.value.isdigit() else 0,
        #             'name': ka_ap.label if ka_ap.label is not None else ka_ap.name,
        #             'unit': ka_ap.unit,
        #             'identifier': ka_ap.identifier
        #         })
        #     except AttributePrototype.DoesNotExist:
        #         ka_ap = None

        return ka_fields

    def sub_dp(self, prefix):
        """根据前缀查询子设备类型"""
        return DevicePrototype.objects.filter(parent_id=self.device_prototype_id, prefix=prefix).last()

    def last_issue(self):
        """最后一个没恢复的issue"""
        from saian_api.issue.models import DeviceIssue
        device_issue = DeviceIssue.objects.filter(is_solved=False, device_id=self.id).order_by('-updated_at').first()
        return device_issue.name if device_issue is not None else ''

    def last_10_living_records(self):
        """最后10个活物监测记录，如果有"""
        return LivingDetection.objects.filter(device_id=self.id).order_by('-created_at')[:10]

    def send_ctrls(self, executor, changes, do_filter=True, action_log_id=None, project_id=None):
        from saian_api.scheduledtask.utils import set_global_db
        from saian_api.group.models import ActionLog
        if project_id is not None:
            # 设置全局数据库
            set_global_db(project_id)

        """ 下发指令。 do_filter 是否过滤下发参数。默认过滤。 """
        if isinstance(changes, str):
            try:
                changes = json.loads(changes)
            except json.decoder.JSONDecodeError:
                logging.error(f'下发指令解析 changes 字符串到 json 时出错，字符串为：{changes}')
                return

        # 温控器v2酒店版，下发运行模式为除湿即下发特殊功能为湿度控制
        if changes.get('FCURunningMode', None) is not None and changes['FCURunningMode'] == '除湿' and self.device_prototype_id == 223:
            filtered_changes = {"SpecialFunc": "湿度控制"}
            changes = {"SpecialFunc": "湿度控制"}
        else:
            filtered_changes = {}

        for k, v in changes.items():
            ap = AttributePrototype.objects.get_by_idf(self, k)
            if ap is not None:
                if not ap.do_send:
                    # do_send 是 False, 则不发送，直接更新数据库
                    # da = DeviceAttribute.objects.get_by_idf(self, k)
                    da = DeviceAttribute.objects.query_object_by_idf(self, k)
                    # ta = TerminalAttribute.objects.get_by_da(da)
                    if da is not None:
                        prev_value = da.value
                        da.value = v
                        da.save()
                        DeviceAttribute.objects.save_to_redis(self, da)
                        TerminalAttribute.objects.update_with_da(da, ap=ap, project_id=self.project_id)

                        # 特殊处理：洁净空调联动下发记录缓存到redies
                        # if self.device_prototype_id == 544 and action_log_id is not None:
                        #     actor_name = ActionLog.objects.get(pk=action_log_id).actor_name
                        #     if actor_name == 'LinkageTrigger':
                        now = datetime.datetime.now()
                        RedisHelper.add_set(self.project_id, f'recently_updated_{now.minute:02}:{self.device_prototype_id}',
                                            ap.id, True, 120)
                        RedisHelper.push_list(self.project_id, f'device_event_{now.strftime("%Y%m%d%H")}:{self.id}_{k}', v, True, 7200)

                        if ap.identifier.endswith('Qty'):
                            device = da.device
                            cur_count = int(da.value) if da.value else 0
                            prev_count = int(prev_value) if prev_value else 0
                            Terminal.objects.handle_qty_change(device, ap, prev_count, cur_count)

                        # 温控器网关，修改湿度调整值后，立即应用到设备属性值中
                        try:
                            if k == 'HumiCorrect' and float(v) and self.device_prototype_id == 237:
                                humi_aps = AttributePrototype.objects.filter(device_prototype_id=self.device_prototype_id,
                                                                             identifier__endswith='LocalRH').values_list('id', flat=True)
                                # 设备初始化后默认湿度是0，这时不需要应用湿度调制值
                                das = DeviceAttribute.objects.filter(attribute_prototype_id__in=humi_aps, device=self).exclude(value='0')
                                for da in das:
                                    da.value = float(da.value) + float(v)
                                    if float(prev_value) != 0:
                                        da.value = float(da.value) - float(prev_value)
                                    da.save()
                                    DeviceAttribute.objects.save_to_redis(self, da)
                        except Exception as e:
                            logging.error(f'修改湿度调整值后，立即应用到设备属性值中出错: {e}')

                else:
                    # 过滤下发参数
                    # 通过 saianapi/v1/device_changes 接口发送的下发指令,do_filter 为 False，不过滤

                    if executor is None or isinstance(executor, dict) or (ContentType.objects.get_for_model(executor)
                                                                          .model_class().__name__ == "WebUser" and not do_filter):
                        filtered_changes[k] = v
                    else:
                        # da = DeviceAttribute.objects.get_by_idf(self, k)
                        da = DeviceAttribute.objects.query_object_by_idf(self, k)
                        if da is not None:
                            if isinstance(v, bool):
                                v = '1' if v else '0'
                            if da.value != v and da.value != str(v):
                                try:
                                    num_v = float(da.value)
                                    if num_v == float(v):
                                        continue
                                except ValueError:
                                    pass
                                # 数据类型为布尔值时，下发的值转换为布尔值
                                if ap.data_type == 10:
                                    v = bool(float(v))
                                filtered_changes[k] = v

        # 如果解析代码调用了函数parse_up_attrs，则执行一次下发解析
        dp = self.device_prototype
        if dp.down_parser is not None and dp.down_parser != '' and 'self.parse_up_attrs' in dp.down_parser:
            dp.parse_down_data(changes, self.mac)

        # 下发日志存储的是未经解析的指令
        if len(filtered_changes):
            mac = self.mac
            addr = None
            iu = None
            dev_id = None
            log_id = DeviceCtrlLog.objects.create_cus(executor, self, filtered_changes, action_log_id)

            log = DeviceCtrlLog.objects.get(pk=log_id)
            dp = self.device_prototype
            # 如果该设备的设备类型是子设备类型并且拥有参数, VRV 空调网关处理
            if dp.parent and AttributePrototype.objects.filter(device_prototype_id=dp.id).exists():
                try:
                    dp = dp.parent
                    [mac, addr, iu] = self.mac.rsplit('-', 2)
                except Exception as e:
                    logging.error(f'处理内机设备下发数据时出错: mac-{self.mac}, err-{e.__str__()}')
            # PLC网关XS 下发处理
            elif dp.uni_name in ['冷源XS', '三相电表V2', '南沙城冷源', '南沙城冷却塔']:
                [mac, dev_id] = self.mac.split('_', 1)
                dp = Device.objects.get(mac=mac).device_prototype
            elif dp.uni_name == '派诺分体空调':
                filtered_changes = self.pihyun_ctrl(self, filtered_changes)
            # 分体空调下发处理
            elif '分体空调' in dp.uni_name:
                filtered_changes = self.sac_ctrl(self, filtered_changes)
            # 下发数据解析
            if dp.down_parser is not None and dp.down_parser != '':
                filtered_changes = dp.parse_down_data(filtered_changes, self.mac, addr=addr, iu=iu, dev_id=dev_id)
            # 解析出错，结果是 None，不发送
            if filtered_changes is not None and len(filtered_changes):
                # 如果结果是列表，则分多次下发
                if isinstance(filtered_changes, list):
                    for change in filtered_changes:
                        DeviceApi.send_ctrl(self, change, mac)
                    log.errcode = 0
                    log.result = '完成'
                else:
                    try:
                        r = DeviceApi.send_ctrl(self, filtered_changes, mac)
                        if r.status_code == 200:
                            log.errcode = 0
                            log.result = '完成' if "设备离线" not in log.result else '离线下发完成'
                        else:
                            msg = f'下发出错! device: {self.id}-{self.nick_name}, changes: {changes}, status_code: {r.status_code}, res: {r.text}'
                            logging.error(msg)
                            log.errcode = 2
                            log.result = '下发出错' if "设备离线" not in log.result else "设备离线，下发失败！"
                    except Exception as e:
                        logging.error(f'下发指令失败：device-{self.id}, data-{filtered_changes}, err-{e.__str__()}')
                        log.errcode = 3
                        log.result = '下发超时'
            else:
                log.errcode = 3
                log.result = '解析出错'
            log.save()
            return log.id
        else:
            if not do_filter:
                log_id = DeviceCtrlLog.objects.create_cus(executor, self, changes, action_log_id)
                log = DeviceCtrlLog.objects.get(pk=log_id)
                log.errcode = 0
                log.result = '完成'
                log.save()
                return log_id

    @classmethod
    def sac_ctrl(cls, device, changes: dict):
        # 分体空调下发
        pl_changes = {}
        if 'PowerCtrl' in changes:
            pl_changes['PowerCtrl'] = changes['PowerCtrl']
        if 'Light' in changes:
            pl_changes['Light'] = changes['Light']

        if pl_changes:
            return pl_changes

        infrared_idf = ['AcSw', 'TargetTemp', 'RunMode', 'FanSpeed', 'FanDirection']
        # 下发指令不包含红外码idf，直接返回
        if len(set(infrared_idf) & changes.keys()) == 0:
            return changes

        # 指令转换为红外码
        brand_da = DeviceAttribute.objects.query_object_by_idf(device, 'AcBrand')
        model_da = DeviceAttribute.objects.query_object_by_idf(device, 'AcModel')

        # 查询设备品牌和型号
        if brand_da is None or model_da is None:
            logger.error(f'找不到设备的品牌或型号！{device.id}-{device.nick_name}')
            return changes
        brand = brand_da.value
        model = model_da.value

        # 查询红外码参数
        params = {
            'brand': brand,
            'model': model
        }

        ac_sw = changes.get('AcSw', None)
        sw_da = DeviceAttribute.objects.query_object_by_idf(device, 'AcSw')
        if ac_sw is not None and ac_sw is False:
            params['sw'] = '关机'
            params['runmode'] = '无'
            r = AdminDeviceApi.get_infrared_codes(params)
            if r.status_code == 200:
                result = r.json()
                if result['status'] != 200:
                    logger.error(f'查询设备红外码失败, {device.id}-{device.nick_name}, params: {params}, error: {result["error"]}')
                else:
                    infrared_code = result['data']['in_code']
                    del changes['AcSw']
                    changes['InfraredCtrl'] = cls.handle_infrared_code(infrared_code)
                    device.sw_on = False
                    device.save()
                    if sw_da is not None:
                        sw_da.value = 0
                        sw_da.save()
                        DeviceAttribute.objects.save_to_redis(device, sw_da)
                    return changes
            else:
                logger.error(f'查询设备红外码失败, {device.id}-{device.nick_name}, params: {params}, status_code: {r.status_code}')
        else:
            params['sw'] = '开机'
            sw_da.value = 1

        if 'AcSw' in changes:
            del changes['AcSw']

        temp_da = DeviceAttribute.objects.query_object_by_idf(device, 'TargetTemp')
        if 'TargetTemp' in changes:
            params['tempset'] = changes['TargetTemp']
            del changes['TargetTemp']
        else:
            if temp_da is not None:
                params['tempset'] = temp_da.value
        if temp_da is not None:
            temp_da.value = params['tempset']

        mode_da = DeviceAttribute.objects.query_object_by_idf(device, 'RunMode')
        if 'RunMode' in changes:
            params['runmode'] = changes['RunMode']
            del changes['RunMode']
        else:
            if mode_da is not None:
                params['runmode'] = mode_da.value
        if mode_da is not None:
            mode_da.value = params['runmode']

        speed_da = DeviceAttribute.objects.query_object_by_idf(device, 'FanSpeed')
        if 'FanSpeed' in changes:
            params['speed'] = changes['FanSpeed']
            del changes['FanSpeed']
        else:
            if speed_da is not None:
                params['speed'] = speed_da.value
        if speed_da is not None:
            speed_da.value = params['speed']

        direction_da = DeviceAttribute.objects.query_object_by_idf(device, 'FanDirection')
        if 'FanDirection' in changes:
            params['direction'] = changes['FanDirection']
            del changes['FanDirection']
        else:
            if direction_da is not None:
                params['direction'] = direction_da.value
        if direction_da is not None:
            direction_da.value = params['direction']

        r = AdminDeviceApi.get_infrared_codes(params)
        if r.status_code == 200:
            result = r.json()
            if result['status'] != 200:
                logger.error(f'查询设备红外码失败, {device.id}-{device.nick_name}, params: {params}, error: {result["error"]}')
            else:
                infrared_code = result['data']['in_code']
                changes['IRCode'] = cls.handle_infrared_code(infrared_code)

                if sw_da is not None:
                    sw_da.save()
                    DeviceAttribute.objects.save_to_redis(device, sw_da)
                    device.sw_on = True
                    device.save()
                if temp_da is not None:
                    temp_da.save()
                    DeviceAttribute.objects.save_to_redis(device, temp_da)
                if mode_da is not None:
                    mode_da.save()
                    DeviceAttribute.objects.save_to_redis(device, mode_da)
                if speed_da is not None:
                    speed_da.save()
                    DeviceAttribute.objects.save_to_redis(device, speed_da)
                if direction_da is not None:
                    direction_da.save()
                    DeviceAttribute.objects.save_to_redis(device, direction_da)

                return changes
        else:
            logger.error(f'查询设备红外码失败, {device.id}-{device.nick_name}, params: {params}, status_code: {r.status_code}')

        return changes

    @classmethod
    def pihyun_ctrl(cls, device, changes: dict):
        """
        派诺分体空调下发。
        需要同时下发 开关，模式，风力，温度，睡眠模式
        """
        pih_changes = {**changes}
        if 'switch_' not in changes:
            sw_da = DeviceAttribute.objects.query_object_by_idf(device, 'switch_')
            if sw_da is not None:
                pih_changes['switch_'] = sw_da.value
        if 'model' not in changes:
            mode_da = DeviceAttribute.objects.query_object_by_idf(device, 'model')
            if mode_da is not None:
                pih_changes['model'] = mode_da.value
        if 'wind_level' not in pih_changes:
            wind_da = DeviceAttribute.objects.query_object_by_idf(device, 'wind_level')
            if wind_da is not None:
                pih_changes['wind_level'] = wind_da.value
        if 'temperature_set' not in pih_changes:
            temp_da = DeviceAttribute.objects.query_object_by_idf(device, 'temperature_set')
            if temp_da is not None:
                pih_changes['temperature_set'] = temp_da.value
        if 'sleep' not in pih_changes:
            sleep_da = DeviceAttribute.objects.query_object_by_idf(device, 'sleep')
            if sleep_da is not None:
                pih_changes['sleep'] = sleep_da.value

        return pih_changes

    @classmethod
    def handle_infrared_code(cls, code):
        # 红外码的处理
        code_len = int((len(code) / 2) + 4)
        hex_string = format(code_len, '02x')
        return f'{hex_string}800000{code}, 0'

    # 在处理推送数据前，数据注入处理
    def data_inject(self, data):
        # 需要将数据插入data
        if self.device_prototype.uni_name == 'PM800电表网关':
            meter_qty = int(self.get_value_by_idf('MeterQty'))
            for idx in range(1, meter_qty + 1):
                if f'Meter_R1_kWh_{idx}' in data:
                    reg_1 = float(data[f'Meter_R1_kWh_{idx}'])
                    reg_2 = float(data[f'Meter_R2_kWh_{idx}'])
                    reg_3 = float(data[f'Meter_R3_kWh_{idx}'])
                    reg_4 = float(data[f'Meter_R4_kWh_{idx}'])

                    power_cons = (reg_1 + reg_2 * 10000 + reg_3 * 100000000 + reg_4 * 1000000000000) / 1000

                    reg_in_1 = float(data[f'Meter_R1_In_kWh_{idx}'])
                    reg_in_2 = float(data[f'Meter_R2_In_kWh_{idx}'])
                    reg_in_3 = float(data[f'Meter_R3_In_kWh_{idx}'])
                    reg_in_4 = float(data[f'Meter_R4_In_kWh_{idx}'])

                    power_cons_in = (reg_in_1 + reg_in_2 * 10000 + reg_in_3 * 100000000 + reg_in_4 * 1000000000000) / 1000

                    if power_cons and int(power_cons) >= 0:
                        idf = f'Meter_kWh_{idx}'
                        data[idf] = power_cons

                    if power_cons_in and int(power_cons_in) >= 0:
                        idf = f'Meter_Power_Cons_{idx}'
                        data[idf] = power_cons_in

        if self.device_prototype.name == '液位传感器':
            max = int(self.get_value_by_idf('MaximumRange'))

            if 'LiquidLevel' in data:
                value = float(data['LiquidLevel'])
                data['LiquidLevel'] = round(float(max * value / 2000), 2)


class DeviceEventManager(models.Manager):
    # 自定义设备事件处理
    def cus_create(self, event):
        from saian_api.linkage.models import LinkageRule
        from saian_api.notifi.models import NotifyLimit

        try:
            # device = Device.objects.get(mac=event['mac'])
            # 德兴VRV网关上报的数据多了一层 content, content 的内容才是 event
            if event.get('content', None) is not None:
                event = event.get('content')

            device = Device.objects.filter(mac=event['mac']).order_by('created_at').last()
            if device is None:
                logging.warning(f'设备不存在: {event["mac"]}')

            event_list = []
            # 项目限制
            if device and device.project_id:
                from saian_api.scheduledtask.utils import set_global_db
                set_global_db(f'prj{device.project_id}db')
                # 青萍推送不解析
                if device.device_prototype.up_parser and (not event.get('event_type', None) == 'qp_data_push'):
                    event_data = device.device_prototype.parse_up_data(event.get('data', None), mac=device.mac)
                    # 如果解析后得到None，就把保留原始值，适用于网关发送的在线状态消息
                    if event_data is None:
                        event_data = event.get('data', None)
                    # parse_up_data 有可能改变了设备的故障报警状态，所以要重新获取 device 对象
                    device = Device.objects.filter(mac=event['mac']).order_by('created_at').last()
                else:
                    event_data = event.get('data', None)
                if isinstance(event_data, list):
                    for e in event_data:
                        c_event = copy.deepcopy(event)
                        # setattr(c_event, 'data', e)
                        c_event['data'] = e
                        event_list.append(c_event)
                else:
                    # setattr(event, 'data', event_data)
                    event['data'] = event_data
                    event_list.append(event)
            else:
                event_list.append(event)

            if device is not None and device.project is not None and device.status != 50:
                for event in event_list:
                    from saian_api.dimension.models import PpvConfig
                    if event.get('data', None) is None:
                        continue

                    # 峰平谷尖
                    # 是否需要进行峰平谷尖的相关计算
                    is_calc_ppv = False
                    # 设备上报的数据
                    event_data = event.get('data')
                    keys = event_data.keys()
                    # 存储上报数据中有关电量的idf
                    idf_list = list(filter(lambda x: 'Meter_Power_Cons' in x, keys))
                    if len(idf_list) > 0:
                        # 项目级支持
                        project_settings = json.loads(device.project.settings)
                        project_support = project_settings.get('en_ppv', False)
                        if project_support:
                            device_support = DeviceAttribute.objects.query_object_by_idf(device, 'EN_PPV')
                            if device_support is not None:
                                # 设备级不支持
                                if device_support.value is False or device_support.value == '0':
                                    is_calc_ppv = True
                                else:
                                    for idf in idf_list:
                                        meter_ppv_label = idf[-2:]
                                        if meter_ppv_label in ['_P', '_F', '_V', '_S']:
                                            # 变比
                                            ct_idf = 'Meter_CT'
                                            idx = re.findall(r'\d+', idf)
                                            if len(idx):
                                                ct_idf = f'Meter_CT_{idx[0]}'
                                            ct_da = DeviceAttribute.objects.query_object_by_idf(device, ct_idf)
                                            if ct_da is not None:
                                                try:
                                                    event['data'][idf] = round((float(event_data[idf]) * float(ct_da.value)), 2)
                                                except Exception as e:
                                                    logging.error(f'更新峰平谷尖用电量失败. '
                                                                  f'error: {e.__str__()}, value: {event_data[idf]}, ct_value: {ct_da.value}')

                    if is_calc_ppv:
                        # 进行峰平谷尖的相关计算
                        PpvConfig.objects.calc_ppv(device, event, idf_list)

                    # 临时处理：天津电表-峰平谷统计
                    # 忽略上报的峰平谷统计，自行计算
                    # 现场峰平谷时段配置错误，要等施工人员到现场重新配置电表
                    if device.id == 17898 or device.id == 18167 or device.id == 18173:
                        # del_idfs = list(filter(lambda x: ('Meter_Power_Cons' in x or 'Meter_kVarh' in x) and x[-4:] in ['_P_3', '_F_3', '_V_3', '_S_3'] , keys))
                        del_idfs = list(filter(lambda x: ('Meter_Power_Cons_' in x or 'Meter_kVarh_' in x) and x[-3:-1] in ['P_', 'F_', 'V_', 'S_'], keys))

                        for del_idf in del_idfs:
                            del event['data'][del_idf]

                        cum_idfs = list(filter(lambda x: ('Meter_Power_Cons_' in x or 'Meter_kVarh_' in x), event['data'].keys()))
                        PpvConfig.objects.calc_ppv(device, event, cum_idfs)

                    # 天津电表型号不能配置根据月份改变峰平谷尖时段，需进行特殊处理
                    # （星光天地冷源、星光天地供暖、艾丽华热源）尖峰统计处理：
                    # 其中，1、12月高峰时段中11:00-12:00，18:00-19:00为尖峰时段;7、8月高峰时段中11:00-12:00，16:00-17:00为尖峰时段。
                    # if device.id == 17898 or device.id == 18167 or device.id == 18173:
                    #     now = datetime.datetime.now()
                    #     ppv_type = 10
                    #     # 判断：是否在尖峰时段
                    #     if (now.month == 1 or now.month == 12) and (now.hour == 11 or now.hour == 18):
                    #         ppv_type = 40
                    #     elif (now.month == 7 or now.month == 8) and (now.hour == 11 or now.hour == 16):
                    #         ppv_type = 40
                    #     # 筛选：有功峰电能和无功峰电能 idf
                    #     if ppv_type == 40:
                    #         p_idfs = list(filter(lambda x: ('Meter_Power_Cons' in x or 'Meter_kVarh' in x) and x[-4:-2] == '_P', keys))
                    #         if p_idfs.exists():
                    #             for p_idf in p_idfs:
                    #                 # 新上报的数据值
                    #                 reported_value = event['data'][p_idf]
                    #                 # 现存储的数据值
                    #                 store_da = DeviceAttribute.objects.query_object_by_idf(device, p_idf)
                    #                 store_value = float(store_da.value) if store_da is not None else 0
                    #                 # 将高峰电能统计改到尖
                    #                 target_idf = p_idf[:-3] + 'S' + p_idf[-2:]
                    #                 target_da = DeviceAttribute.objects.query_object_by_idf(device, target_idf)
                    #                 new_value = (float(target_da.value) if target_da is not None else 0) + (reported_value - store_value)
                    #                 event['data'][target_idf] = new_value
                    #                 del event['data'][p_idf]

                    device_prototype = device.device_prototype
                    if device_prototype.uni_name == '贴片温度传感器':
                        event_data['Temp'] = round(float(event_data['Temp']) - 200, 2)
                        event['data'] = event_data

                    #
                    # 创建事件记录
                    ev = self.create(
                        device_prototype_id=device.device_prototype_id,
                        cmd=event.get('cmd', None),
                        event_type=event.get('event_type', None),
                        event_id=event.get('event_id', None),
                        delivery_id=event.get('delivery_id', None),
                        product_key=event.get('product_key', None),
                        timestamp=event.get('timestamp', None),
                        ip=event.get('ip', None),
                        latitude=event.get('latitude', None),
                        longitude=event.get('longitude', None),
                        country=event.get('country', None),
                        region=event.get('region', None),
                        data=json.dumps(event.get('data', None), ensure_ascii=False),
                        did=event.get('did', None),
                        mac=event.get('mac', None),
                        nick_name=device.nick_name,
                        device_id=device.id,
                        project_id=device.project_id
                    )

                    ev.save()

                    # 设备更新数据处理
                    device.update_by_pushing(event, ev.id)

                    # PLC网关XS处理，判断是否更改设备
                    data = event.get('data', None)
                    if device_prototype.uni_name in ['PLC网关XS']:
                        if "dev_id" in data:
                            mac = f'{device.mac}_{data["dev_id"]}'
                            try:
                                device = Device.objects.get(mac=mac)
                            except Exception as e:
                                logging.info(f'查询PLC网关XS的目标设备失败！mac: {mac}, error: {e.__str__()}')

                    # 末端供冷需求冷量统计预处理
                    if data is not None:
                        if 'FCUSW' in data or 'ValveLiftBack' in data:
                            from saian_api.act.models import ActColdStat
                            ActColdStat.objects.calc_stats(device)

                    # 房间环境指标（温湿度、气体指标等）限制告警处理

                    # 变量触发联动处理
                    try:
                        LinkageRule.objects.data_push(device, data)
                    except Exception as e:
                        logging.error(f"变量触发联动处理: {e.__str__()}")

                    # 计算值和累计值参数记录处理
                    # task_calc_cum.delay(device.id, json.dumps(event))
                    ParamRecord.objects.calc_cum(device.id, event, ev.id)

                    # 冷源能耗日累计关联处理

                    # 末端供冷需求冷量统计

                    # 计量仪表维护
                    # task_sync_meters.delay(device.id, json.dumps(event))
                    from saian_api.coldsource.models import EcMeter
                    EcMeter.objects.update_meters(device.id, json.dumps(event))

                    # 设备运行时间计算
                    DeviceRuntime.objects.cal_runtime(device, data, ev.created_at)

                    # 设备参数超限通知
                    NotifyLimit.objects.check_limit(device, data)

                    # 维度属性值更新
                    DimensionAttribute.objects.calc_value(device, data)

                    # 数据流转处理
                    device.data_route(data, ev.id)

                    # 国检结束，档案馆温湿度取消限制
                    # if device.project_id == 11:
                    #     from saian_api.device.domain_models.project_specialize import Project11
                    #     Project11.temp_and_rh(device, data, ev)
                    # 保留温湿度显示屏的策略
                    # if device.project_id == 18:
                    #     from saian_api.device.domain_models.project_specialize import Project18
                    #     Project18.temp_and_rh(device, data)

        except Device.DoesNotExist:
            logging.error(f'事件对应的MAC：{event["mac"]} 设备不存在')
        except Device.MultipleObjectsReturned:
            logging.error(f'MAC {event["mac"]} 返回多个设备')

    #  根据identifier和时间范围查询设备的值
    def get_data_by_idf(self, device_id, identifier, from_at, till_at):
        # from_at = datetime.datetime.strptime(from_at, '%Y%m%d%H%M%S')
        # till_at = datetime.datetime.strptime(till_at, '%Y%m%d%H%M%S')
        events = self.filter(device_id=device_id, created_at__range=[from_at, till_at])

        results = []

        for event in events:
            data = None
            # try:
            data = json.loads(event.data.replace('\'', '\"'))
            # except ValueError:
            # pass

            if data is not None and identifier in data:
                value = data[identifier]
                result = {
                    'time': event.created_at,
                    'value': value
                }

                results.append(result)

        return results


class DeviceEvent(models.Model):
    """设备事件，处理数据上报"""
    # 所属项目
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    # 对应的问题id，如果有的话
    device_issue = models.ForeignKey('issue.DeviceIssue', on_delete=models.CASCADE, null=True)
    # 所属设备
    device = models.ForeignKey(Device, on_delete=models.CASCADE)
    # 所属设备类型
    device_prototype_id = models.BigIntegerField(db_index=True)
    # 机智云上报参数，事件命令，如：登录login_req
    cmd = models.CharField(max_length=255, null=True)
    # 机智云上报参数，事件类型，如：设备下线 - device_offline
    event_type = models.CharField(max_length=255, null=True)
    # 机智云上报参数，事件id
    event_id = models.CharField(max_length=255, null=True)
    # 机智云上报参数，事件id
    delivery_id = models.CharField(max_length=255, null=True)
    # 机智云上报参数，product_key
    product_key = models.CharField(max_length=255, null=True)
    # 机智云上报参数，timestamp
    timestamp = models.FloatField(null=True)
    # 机智云上报参数，ip
    ip = models.CharField(max_length=255, null=True)
    # 纬度
    latitude = models.DecimalField(null=True, decimal_places=6, max_digits=10)
    # 经度
    longitude = models.DecimalField(null=True, decimal_places=6, max_digits=10)
    # 机智云上报参数，国家
    country = models.CharField(max_length=255, null=True)
    # 机智云上报参数，地区
    region = models.CharField(max_length=255, null=True)
    # 上报的数据
    data = models.TextField(null=True)
    # 设备did
    did = models.CharField(max_length=255, null=True)
    # 设备mac
    mac = models.CharField(max_length=255, null=True)
    # 昵称
    nick_name = models.CharField(max_length=255, blank=True, null=True, db_index=True)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'device_events'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['project_id', 'nick_name']),
            models.Index(fields=['nick_name', 'device_prototype_id']),
            models.Index(fields=['nick_name', 'created_at']),
            models.Index(fields=['device_prototype_id', 'created_at']),
            models.Index(fields=['project_id', 'created_at']),
            models.Index(
                fields=['nick_name', 'project_id', 'device_prototype_id']),
            models.Index(
                fields=['nick_name', 'device_prototype_id', 'created_at']),
            models.Index(
                fields=['project_id', 'device_prototype_id', 'created_at']),
            models.Index(fields=['nick_name', 'project_id', 'created_at']),
            models.Index(fields=['nick_name', 'project_id',
                                 'device_prototype_id', 'created_at'])
        ]

    objects = DeviceEventManager()


class DeviceTimer(models.Model):
    """设备软定时器"""
    # 所属设备
    device = models.ForeignKey(Device, on_delete=models.CASCADE)
    # 名字
    name = models.CharField(max_length=255)
    # 定时设置的参数和值，json格式
    identifiers = models.TextField(null=True)
    # 是否重复
    repeat = models.BooleanField(default=False)
    # 运行日期，如：2020-08-11
    run_date = models.DateField(null=True)
    # 运行时间, 如：10点12分12秒，10:12:12
    run_time = models.TimeField(null=True)
    # 周，如：周一,周二
    wdays = models.CharField(max_length=255, null=True)
    # 是否启用
    enabled = models.BooleanField(default=False)
    # 是否已完成
    is_finished = models.BooleanField(default=True)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    # 有效时间范围
    time_ranges = models.TextField(null=True, blank=True)

    class Meta:
        db_table = 'device_timers'
        ordering = ['-created_at']

    # 执行任务
    def execute_job(self):
        from saian_api.group.models import ActionLog

        changes = '{'
        params = json.loads(self.identifiers)
        for idx, param in enumerate(params):
            if idx == len(params) - 1:
                changes = changes + '"' + param['identifier'] + '": "' + str(param['value']) + '"}'
            else:
                changes = changes + '"' + param['identifier'] + '": "' + str(param['value']) + '", '

        try:
            json.dumps(changes)

            # 记录运行日志
            executor_type = ContentType.objects.get_for_model(self)

            action_log = ActionLog.objects.create(
                executor_id=self.id,
                executor_type=executor_type,
                actor_name=executor_type.model_class().__name__,
                action_name=self.name,
                values=json.dumps(changes, ensure_ascii=False),
                remark='{"repeat": "' + str('是' if self.repeat else '否') + '", "run_date": "' + str(
                    self.run_date if self.run_date is not None else '无') + '", "run_time": "' + str(
                    self.run_time if self.run_time is not None else '无') + '", "week": "' + str(
                    self.wdays if self.wdays is not None else '无') + '", "is_finished": "' + str(
                    '是' if self.is_finished else '否') + '", "enabled": "' + str('是' if self.enabled else '否' + '"}'),
                op_type=30,
                op_id=self.device_id
            )

            if self.device is not None:
                self.device.send_ctrls(self, changes, True, action_log.id)
        except Exception:
            logger.exception(traceback.format_exc())


class DeviceAttributeManager(models.Manager):
    @classmethod
    def calc_hourly_cum_stat(cls, device_id, identifier, from_at: datetime.datetime, till_at: datetime.datetime):
        """ 计算在时间dt小时内，设备参数的累计值 """
        now = datetime.datetime.now()
        one_day_ago = (now - datetime.timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
        result = 0.0

        meter_ct = None
        if from_at.minute != 0 or till_at.minute != 0:
            device = Device.objects.get(pk=device_id)
            device_prototype = device.device_prototype
            if device_prototype.uni_name == 'ADL400电表' or device_prototype.uni_name == 'GL3012电表':
                meter_ct = device.get_value_by_idf('Meter_CT')
                if meter_ct is not None:
                    meter_ct = float(meter_ct)

        if from_at < one_day_ago:
            from_data = ParamRecordHistory.objects
        else:
            from_data = ParamRecord.objects
        if from_at.minute == 0:
            pre_hour = from_at.strftime('%Y-%m-%d %H:05:00')
            pre_reading = from_data.filter(created_at=pre_hour, device_id=device_id, identifier=identifier).order_by('created_at').last()
        else:
            pre_reading = from_data.filter(device_id=device_id, identifier=identifier, created_at__gte=from_at).order_by('created_at').first()
            if meter_ct is not None and pre_reading is not None:
                if pre_reading.created_at.minute != 5 and pre_reading.created_at.second != 0:
                    pre_reading.value = float(pre_reading.value) * meter_ct

        if till_at < one_day_ago:
            till_data = ParamRecordHistory.objects
        else:
            till_data = ParamRecord.objects
        if till_at.minute == 0:
            current_hour = till_at.strftime('%Y-%m-%d %H:05:00')
            current_read = till_data.filter(created_at=current_hour, device_id=device_id, identifier=identifier).order_by('created_at').last()
        else:
            current_read = till_data.filter(device_id=device_id, identifier=identifier, created_at__lte=till_at).order_by('created_at').last()
            if meter_ct is not None and current_read is not None:
                if current_read.created_at.minute != 5 and current_read.created_at.second != 0:
                    current_read.value = float(current_read.value) * meter_ct

        if pre_reading is not None and current_read is not None:
            if float(pre_reading.value) > float(current_read.value):
                return 0
            try:
                result = round(float(current_read.value) - float(pre_reading.value), 2)
            except Exception as e:
                logger.info(f'计算小时累计值出错，device_id: {device_id}, idf: {identifier}, datetime: {from_at}, error: {e.__str__()}')

        return result

    @classmethod
    def calc_hourly_val_stat(cls, device_id, identifier, from_at, till_at):
        """ 计算在时间dt小时内，设备参数的平均值 """

        begin = from_at.strftime('%Y-%m-%d %H:00:00')
        end = till_at.strftime('%Y-%m-%d %H:00:00')

        records = DeviceEvent.objects.filter(device_id=device_id, created_at__range=(begin, end))

        values = []

        for record in records:
            try:
                data = json.loads(record.data)
                if identifier in data:
                    values.append(float(data[identifier]))
            except Exception as e:
                logger.error(f'计算小时平均值出错，device_id: {device_id}, idf: {identifier}, datetime: {begin}, error: {e.__str__()}')
                continue

        if len(values):
            avg_value = sum(values) / len(values)
            return avg_value

    @classmethod
    def save_to_redis(cls, device, da):
        """更新 DeviceAttribute 和 TerminalAttribute 对象到缓存"""
        da.save(update_fields=['value', 'updated_at'])
        RedisHelper.set_value(device.project_id, f'device_attribute:{device.id}_{da.attribute_prototype_id}', da, False)
        TerminalAttribute.objects.update_with_da(da, project_id=device.project_id)

    @classmethod
    def query_object_by_ap(cls, device, ap):
        """从缓存或数据库查询 DeviceAttribute 对象"""
        ap_id = ap.id
        name = f'device_attribute:{device.id}_{ap_id}'

        result = RedisHelper.get_value(device.project_id, name, False)
        if result is None:
            # ap = AttributePrototype.objects.get(pk=ap_id)
            result = cls.get_by_ap(device, ap)

            if result is not None:
                RedisHelper.set_value(device.project_id, name, result, False)

        return result

    @classmethod
    def query_object_by_idf(cls, device, idf):
        """从缓存或数据库查询 DeviceAttribute 对象"""
        result = None
        ap = AttributePrototype.objects.filter(device_prototype_id=device.device_prototype_id, identifier=idf).last()
        if ap is not None:
            result = cls.query_object_by_ap(device, ap)

        return result

    @classmethod
    def query_object_list(cls, device, ap_ids):
        """ 查询一个设备的多个参数 """

        names = [f'device_attribute:{device.id}_{ap_id}' for ap_id in ap_ids]

        results = RedisHelper.get_multi_value(device.project_id, names, False)

        not_cache_ap_ids = []
        das = []

        for idx, result in enumerate(results):
            if not result:
                not_cache_ap_ids.append(ap_ids[idx])
            else:
                das.append(result)

        not_cache_das = list(DeviceAttribute.objects.filter(device_id=device.id, attribute_prototype_id__in=not_cache_ap_ids))

        if len(not_cache_das):
            values = {}
            for da in not_cache_das:
                name = f'device_attribute:{da.device_id}_{da.attribute_prototype_id}'
                values[name] = da
            RedisHelper.set_multi_value(device.project_id, values, False)

        return das + not_cache_das

    @classmethod
    def query_multi_device_object(cls, devices, ap_id):
        """ 查询多个设备的同一个参数 """
        project_id = devices[0].project_id
        names = [f'device_attribute:{device.id}_{ap_id}' for device in devices]

        results = RedisHelper.get_multi_value(project_id, names, False)

        not_cache_device_ids = []
        das = []

        for idx, result in enumerate(results):
            if not result:
                not_cache_device_ids.append(devices[idx].id)
            else:
                das.append(result)

        not_cache_das = list(DeviceAttribute.objects.filter(device_id__in=not_cache_device_ids, attribute_prototype_id=ap_id))

        if len(not_cache_das):
            values = {}
            for da in not_cache_das:
                name = f'device_attribute:{da.device_id}_{da.attribute_prototype_id}'
                values[name] = da
            RedisHelper.set_multi_value(project_id, values, False)

        return das + not_cache_das

    def get_by_idf(self, device, idf):
        """根据设备和idf查询对应的device attribute"""
        da = None
        ap = None
        try:
            ap = AttributePrototype.objects.get(identifier=idf, device_prototype_id=device.device_prototype_id)

        except exceptions.ObjectDoesNotExist:
            # logging.warning(''.join(['AttributePrototype not found, deviceid = ', str(device.id), ', idf = ', idf]))
            pass
        except exceptions.MultipleObjectsReturned:
            ap = AttributePrototype.objects.filter(identifier=idf, device_prototype_id=device.device_prototype_id).order_by('created_at').last()

        if ap is not None:
            da = self.get_by_ap(device, ap)

        return da

    @classmethod
    def get_by_ap(cls, device, ap: AttributePrototype, init_value=None):
        """根据设备和ap查询对应的device attribute"""
        try:
            da = DeviceAttribute.objects.get(device_id=device.id, attribute_prototype_id=ap.id)
        except exceptions.ObjectDoesNotExist:
            logging.warning(''.join(['DeviceAttribute not found, device_id = ', str(device.id), ', ap_id = ', str(ap.id)]))
            default_value = ap.default_value
            if init_value is not None:
                from saian_api.utils.tools import to_int

                default_value = init_value
                if ap.data_type == 10:
                    # 布尔值统一处理
                    if init_value == 'true':
                        default_value = 1
                    if init_value == 'false':
                        default_value = 0
                elif ap.data_type == 30:
                    # 枚举值
                    try:
                        enum_idx = to_int(init_value)
                        if enum_idx >= 0:
                            # 枚举类型更新为枚举值
                            default_value = ap.options.split(",")[enum_idx]
                    except Exception:
                        default_value = ap.default_value

            da = DeviceAttribute.objects.create(
                attribute_prototype_id=ap.id,
                device_id=device.id,
                value=default_value,
                show_in_list=False if ap.show_in_list is None else ap.show_in_list
            )
            TerminalAttribute.objects.create_by_device(device, ap.id)
            logging.info(f'创建缺失的 DeviceAttribute: device={device.id}, ap={ap.id}-{ap.name}')
        except DeviceAttribute.MultipleObjectsReturned:
            logging.error(f'Project{device.project_id} :DA Return 2 Object: attribute_prototype_id={ap.id}, device_id={device.id}')
            DeviceAttribute.objects.filter(device_id=device.id, attribute_prototype_id=ap.id).order_by('created_at').first().delete()
            logging.info(f"删除重复的 DeviceAttribute 中旧的一个记录")
            da = DeviceAttribute.objects.filter(device_id=device.id, attribute_prototype_id=ap.id).order_by('created_at').last()

        return da

    def create_by_device(self, device):
        """根据设备创建设备属性"""
        from .models import DeviceAttribute

        dp = DevicePrototype.objects.filter(pk=device.device_prototype_id).last()
        if dp:
            for ap in enumerate(dp.attributeprototype_set.all()):
                if not DeviceAttribute.objects.filter(device_id=device.id, attribute_prototype_id=ap[1].id).exists():
                    da = DeviceAttribute.objects.create(
                        attribute_prototype_id=ap[1].id,
                        value=ap[1].default_value,
                        show_in_list=False if ap[1].show_in_list is None else ap[1].show_in_list,
                        device_id=device.id
                    )

                    updated_at = datetime.datetime.now() - datetime.timedelta(minutes=30)
                    DeviceAttribute.objects.filter(pk=da.id).update(updated_at=updated_at)


class DeviceAttribute(models.Model):
    """
      设备当前参数值
    """
    # 所属设备
    device = models.ForeignKey(Device, on_delete=models.CASCADE)
    # 属性类型id
    attribute_prototype_id = models.BigIntegerField(db_index=True)
    # 属性值
    value = models.CharField(max_length=255, null=True)
    # 是否在小程序列表显示
    show_in_list = models.BooleanField()

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'device_attributes'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['device_id', 'attribute_prototype_id']),
        ]

    objects = DeviceAttributeManager()

    @property
    def attribute_prototype(self):
        return AttributePrototype.objects.get(pk=self.attribute_prototype_id)

    def __getstate__(self):
        """自定义对象序列化时的状态"""
        state = self.__dict__.copy()  # 获取对象的所有属性
        result = {}
        for k, v in state.items():
            if k == '_state':
                state_adding = v.adding
                state_db = v.db
                v = {
                    "adding": state_adding,
                    "db": state_db
                }

            if k.endswith('at') and isinstance(v, datetime.datetime):
                v = v.strftime('%Y-%m-%d %H:%M:%S')
            result[k] = v
        return result

    def __setstate__(self, state):
        """自定义反序列化时的状态"""
        for k, v in state.items():
            if k.endswith('at') and isinstance(v, str):
                try:
                    state[k] = datetime.datetime.strptime(v, '%Y-%m-%d %H:%M:%S')
                except ValueError:
                    pass  # 跳过无法转换的字段
            if k == '_state' and isinstance(v, dict):
                state_adding = v.get('adding', False)
                state_db = v.get('db', None)
                model_state = ModelState()
                model_state.adding = state_adding
                model_state.db = state_db
                model_state.fields_cache = {}
                state[k] = model_state
        self.__dict__.update(state)


class ParamRecordManager(models.Manager):
    """
      根据设备参数定义，计算参数的累计值、公式值
    """

    def calc_cum(self, device_id, event, event_id):
        from saian_api.device.domain_models.meter import Lxly80Meter

        data = event.get('data', None)
        now = datetime.datetime.now()

        if data is not None:
            device = Device.objects.get(pk=device_id)

            # 处理累计值，由于计算值是有N个设备上报值计算所得，而不会是设备直接上报
            # 所以既是计算值又是累计值时，不会触发下面的代码，导致重复处理
            device_prototype_uni_name = device.device_prototype.uni_name
            for key, value in data.items():
                try:
                    # ap = AttributePrototype.objects.get(device_prototype_id=device.device_prototype_id, identifier=key)
                    ap = AttributePrototype.objects.get_by_idf(device, key)
                    if ap is not None and ap.is_cum:
                        # 地大项目，冷量表需要转MJ为kWh
                        if device_prototype_uni_name == "流量计网关" and device.project_id == 35:
                            value = round(float(value) / 3.6, 2)

                        # DTU 水表网关，保利水表用水量解析
                        if device_prototype_uni_name == 'DTU水表网关' and device.project_id == 74:
                            if key.startswith('Meter_Water_Cons'):
                                idx = key.split('_')[-1]
                                # addr = DeviceAttribute.objects.get_by_idf(device, f'MeterAddr_{idx}')
                                addr = DeviceAttribute.objects.query_object_by_idf(device, f'MeterAddr_{idx}')
                                if addr is not None:
                                    value = Lxly80Meter.get_water_consumption(addr.value, value)

                        pr = self.create(
                            device_id=device.id,
                            mac=device.mac,
                            identifier=ap.identifier,
                            value=value,
                            created_at=now
                        )
                        pr.save()

                        # 时段用电量矫正
                        # 一些项目的设备，时段用电量不准确，所以只矫正天津节能大厦项目
                        if device.project_id == 89 or device.project_id == 91:
                            from saian_api.utils.tools import is_tou_identifier
                            from saian_api.utils.tools import get_idx

                            if now.minute <= 2 and is_tou_identifier(ap.identifier):
                                # 当前小时读数
                                created_at = now.strftime('%Y-%m-%d %H:05:00')
                                hour_pr = self.filter(device_id=device.id, identifier=ap.identifier, created_at=created_at).last()
                                current_hour = now.replace(minute=0, second=0, microsecond=0)
                                prev_hour = current_hour - datetime.timedelta(hours=1)
                                last10_data = list(self.filter(device_id=device.id, identifier=ap.identifier,
                                                               created_at__gt=prev_hour, created_at__lt=current_hour).order_by('-id')[5:15])
                                diff = False
                                if len(last10_data) and last10_data[0] is not None and last10_data[-1] is not None:
                                    diff = float(last10_data[0].value) != float(last10_data[-1].value)

                                if hour_pr is not None:
                                    # 读数有变化，把这两分钟内增长的读数归入上一小时
                                    if diff:
                                        da = DeviceAttribute.objects.query_object_by_ap(device, ap)
                                        if da is not None:
                                            if float(da.value) > float(hour_pr.value):
                                                increment = float(da.value) - float(hour_pr.value)
                                                # logging.info(f'time: {now}, created_at: {created_at}. '
                                                #              f'device-{device.id}, identifier:{ap.identifier},'
                                                #              f' hour_pr: {hour_pr.value}, now da_value: {da.value}')
                                                hour_pr.value = da.value
                                                hour_pr.save()
                                                name = f'param_record_{now.strftime("%Y%m%d%H")}:{device.id}_{ap.identifier}'
                                                RedisHelper.set_value(device.project_id, name, da.value, True, 7600)

                                                # 同时处理总用电量
                                                power_cons_idf = 'Meter_Power_Cons'
                                                idx = get_idx(ap.identifier)
                                                if idx:
                                                    power_cons_idf = f'{power_cons_idf}_{idx}'
                                                power_hour_pr = self.filter(device_id=device.id, identifier=power_cons_idf,
                                                                            created_at=created_at).last()
                                                power_cons_da = DeviceAttribute.objects.query_object_by_idf(device, power_cons_idf)
                                                if power_cons_da is not None and power_hour_pr is not None:
                                                    # logger.info(f'更新总用电量, idf: {power_cons_idf}, hour_pr: {power_hour_pr.value}, '
                                                    #             f'now da_value: {power_cons_da.value}, increment: {increment}')
                                                    power_hour_pr.value = float(power_hour_pr.value) + increment
                                                    power_hour_pr.save()
                                                    name = f'param_record_{now.strftime("%Y%m%d%H")}:{device.id}_{power_cons_idf}'
                                                    RedisHelper.set_value(device.project_id, name, power_cons_da.value, True, 7600)

                                else:
                                    logging.info(f'time: {now}, created_at: {created_at}, device: {device.id}, idf: {ap.identifier}, 找不到 hour_pr')

                except exceptions.ObjectDoesNotExist:
                    continue

            aps = list(AttributePrototype.objects.filter(device_prototype_id=device.device_prototype_id,
                                                         formula__isnull=False))
            # 处理计算值
            for ap in aps:
                if ap.formula:
                    calc_value = self.execute_formula(device, ap.formula, data)
                    if calc_value is not None:
                        da = DeviceAttribute.objects.query_object_by_ap(device, ap)
                        if da is not None:
                            pr = self.create(
                                device_id=device.id,
                                mac=device.mac,
                                identifier=ap.identifier,
                                value=calc_value,
                                created_at=now
                            )
                            pr.save()

                            da.value = calc_value
                            da.save()
                            DeviceAttribute.objects.save_to_redis(device, da)
                            data[ap.identifier] = da.value
            if len(aps):
                device_event = DeviceEvent.objects.get(pk=event_id)
                device_event.data = json.dumps(event.get('data', None), ensure_ascii=False)
                device_event.save(update_fields=['data'])

    def execute_formula(self, device, formula, data):
        """
          根据公式计算值
        """
        matches = re.findall('\{\{\w+\}\}', formula)
        do_calc = False
        new_str = formula
        result = None
        if matches is not None:
            # 查找公式中的ap是否在data中上报
            # 计算公式只要有一个参数上报，则触发计算
            for m in matches:
                attr_id = int(m[2:len(m) - 2])
                try:
                    ap = AttributePrototype.objects.get(pk=attr_id)
                    if ap.identifier in data:
                        do_calc = True
                        break
                except exceptions.ObjectDoesNotExist:
                    continue

            # 计算
            if do_calc:
                for m in matches:
                    # 如果data中有该参数值，就使用data中的
                    # 如果没则取设备的值
                    attr_id = int(m[2:len(m) - 2])
                    try:
                        ap = AttributePrototype.objects.get(pk=attr_id)
                        if ap.identifier in data:
                            value = data[ap.identifier]
                        else:
                            value = device.get_value_by_idf(ap.identifier)

                        new_str = new_str.replace(m, str(value))
                    except exceptions.ObjectDoesNotExist:
                        continue

                try:
                    result = eval(new_str)
                except:
                    logging.error('Exception in ParamRecord.execute_formula(), formula = %s' % new_str)
                    result = None

        return result

    def get_by_hour(self, time, device, identifier, db=None):
        """
          根据时间查询值
        """
        result = None
        try:
            if db is not None:
                result = self.using(db).get(device_id=device.id, identifier=identifier, created_at=time)
            else:
                result = self.get(device_id=device.id, identifier=identifier, created_at=time)
        except exceptions.ObjectDoesNotExist:
            pass
        except exceptions.MultipleObjectsReturned:
            if db is not None:
                result = self.using(db).filter(device_id=device.id, identifier=identifier, created_at=time).first()
            else:
                result = self.filter(device_id=device.id, identifier=identifier, created_at=time).first()

        return result

    def cron_calc_cum(self):
        """
          固定每小时00分对累计值参数做一次计算
          以保证固定时间都有1条记录
        """

        projects = CeleryTaskUtils.get_all_project()
        for project in projects:
            print(f"设备累计值参数计算: {project['project'].name}")
            db = project['db_name']

            report_cfgs = ReportConfigurer.objects.using(db).filter(target_type__model='DevicePrototype')
            if report_cfgs and len(report_cfgs) > 0:
                for cfg in report_cfgs:
                    devices = Device.objects.using(db).filter(device_prototype_id=cfg.target_id)
                    if devices and len(devices) > 0:
                        for device in devices:
                            aps = AttributePrototype.objects.using(db).filter(
                                device_prototype_id=device.device_prototype_id,
                                identifier=cfg.identifier,
                                is_cum=True
                            )
                            if aps is not None:

                                for ap in aps:
                                    if ap.formula and ap.formula != 0:
                                        result = self.execute_formula_by_current(device, ap.formula, db)
                                    else:
                                        result = device.get_value_by_idf(ap.identifier, db)

                                    if result is not None:
                                        created_at = datetime.datetime.now().strftime('%Y-%m-%d %H:05:00')
                                        pr = self.using(db).create(
                                            device_id=device.id,
                                            mac=device.mac,
                                            identifier=ap.identifier,
                                            value=result,
                                            created_at=created_at
                                        )
                                        pr.save(using=db)
                                        pr.created_at = created_at
                                        pr.save(using=db)

    def execute_formula_by_current(self, device, formula, db):
        """
          根据公式从现有数据获取值，然后计算结果
        """
        result = None
        matches = re.findall('\{\{\w+\}\}', formula)
        new_str = formula
        for m in matches:
            attr_id = m[2:len(m) - 2]
            try:
                ap = AttributePrototype.objects.using(db).get(pk=attr_id)
                value = device.get_value_by_idf(ap.identifier, db)
                new_str = new_str.replace(m, str(value))
            except exceptions.ObjectDoesNotExist:
                continue

        try:
            result = eval(new_str)
        except:
            logging.error('Exception in ParamRecord.execute_formula_by_current(), formula = %s' % new_str)
            result = None

        return result


class ParamRecord(models.Model):
    """
      设备计算值和累计值的预处理，主要用于报表统计，以提高报表统计效率
    """
    # 所属设备
    device = models.ForeignKey(Device, on_delete=models.CASCADE)
    # mac
    mac = models.CharField(max_length=255, db_index=True)
    # 参数标识
    identifier = models.CharField(max_length=255)
    # 处理后的参数值
    value = models.CharField(max_length=255)

    # 数据创建时间
    created_at = models.DateTimeField()
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'param_records'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['mac', 'identifier']),
            models.Index(fields=['device_id', 'identifier']),
            models.Index(fields=['device_id', 'identifier', 'created_at']),
            models.Index(fields=['mac', 'identifier', 'created_at'])
        ]

    objects = ParamRecordManager()


class ParamRecordHistory(models.Model):
    """
      设备计算值和累计值的预处理历史记录
    """
    # 所属设备
    device = models.ForeignKey(Device, on_delete=models.CASCADE)
    # mac
    mac = models.CharField(max_length=255, db_index=True)
    # 参数标识
    identifier = models.CharField(max_length=255)
    # 处理后的参数值
    value = models.CharField(max_length=255)

    # 数据创建时间
    created_at = models.DateTimeField()
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'param_record_histories'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['mac', 'identifier']),
            models.Index(fields=['device_id', 'identifier']),
            models.Index(fields=['device_id', 'identifier', 'created_at']),
            models.Index(fields=['mac', 'identifier', 'created_at'])
        ]

    objects = ParamRecordManager()


class DeviceCtrlLogManager(models.Manager):
    def create_cus(self, executor, device, data, action_log_id):
        executor_id = 0
        if executor is None:
            executor_type = "Admin"
        elif isinstance(executor, dict):
            executor_type = executor.get('name', '')
        else:
            executor_type = ContentType.objects.get_for_model(executor).model_class().__name__
            executor_id = executor.id

        errcode, result = 1, '执行中'

        if device.online is not True:
            errcode, result = 99, '设备离线，没执行下发!'

        log = DeviceCtrlLog.objects.create(
            executor_id=executor_id,
            executor_type=executor_type,
            project_id=device.project_id,
            device_id=device.id,
            mac=device.mac,
            data=json.dumps(data, ensure_ascii=False),
            errcode=errcode,
            result=result,
            action_log_id=action_log_id,
            retries=0
        )
        # TODO 也许可以直接返回log对象？
        return log.id


class DeviceCtrlLog(models.Model):
    """
      设备下发命令记录
    """
    from saian_api.group.models import ActionLog
    # 所属设备
    device = models.ForeignKey(Device, on_delete=models.CASCADE)
    # 所属项目
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    # 发起方id
    executor_id = models.BigIntegerField()
    # 发起方类型，如：小程序用户-User, web用户-WebUser, 联动规则-LinkageRule, 分组操作-GroupAction
    # 设备定时-DeviceTimer，第三方调用，管理后台，末端温度限制策略
    executor_type = models.CharField(max_length=255)
    # mac
    mac = models.CharField(max_length=255)
    # 下发的原始数据，json格式
    data = models.TextField()
    # 操作结果码，0-成功，其他为失败
    errcode = models.IntegerField()
    # 操作结果
    result = models.CharField(max_length=255, null=True)
    # 操作记录id
    action_log = models.ForeignKey(ActionLog, on_delete=models.SET_NULL, null=True)
    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)
    # 指令重试次数
    retries = models.IntegerField(default=0, null=True)

    class Meta:
        db_table = 'device_ctrl_logs'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['executor_id', 'executor_type']),
            models.Index(fields=['project_id', 'executor_type']),
            models.Index(fields=['device_id', 'executor_type']),
            models.Index(fields=['mac', 'executor_type']),
            models.Index(fields=['executor_id', 'executor_type', 'errcode']),
            models.Index(fields=['project_id', 'executor_type', 'errcode']),
            models.Index(fields=['device_id', 'executor_type', 'errcode']),
            models.Index(fields=['mac', 'executor_type', 'errcode']),
        ]

    objects = DeviceCtrlLogManager()


class DeviceEventHistory(models.Model):
    """
      设备原始数据历史记录
    """
    # 所属项目
    project_id = models.BigIntegerField()
    # 对应的问题id，如果有的话
    device_issue_id = models.BigIntegerField(null=True)
    # 所属设备
    device_id = models.BigIntegerField(db_index=True)
    # 所属设备类型
    device_prototype_id = models.BigIntegerField()
    # 机智云上报参数，事件命令，如：登录login_req
    cmd = models.CharField(max_length=255, null=True)
    # 机智云上报参数，事件类型，如：设备下线 - device_offline
    event_type = models.CharField(max_length=255, null=True)
    # 机智云上报参数，事件id
    event_id = models.CharField(max_length=255, null=True)
    # 机智云上报参数，事件id
    delivery_id = models.CharField(max_length=255, null=True)
    # 机智云上报参数，product_key
    product_key = models.CharField(max_length=255, null=True)
    # 机智云上报参数，timestamp
    timestamp = models.FloatField(null=True)
    # 机智云上报参数，ip
    ip = models.CharField(max_length=255, null=True)
    # 纬度
    latitude = models.DecimalField(null=True, decimal_places=6, max_digits=10)
    # 经度
    longitude = models.DecimalField(null=True, decimal_places=6, max_digits=10)
    # 机智云上报参数，国家
    country = models.CharField(max_length=255, null=True)
    # 机智云上报参数，地区
    region = models.CharField(max_length=255, null=True)
    # 上报的数据
    data = models.TextField(null=True)
    # 设备did
    did = models.CharField(max_length=255, null=True)
    # 设备mac
    mac = models.CharField(max_length=255, null=True)
    # 昵称
    nick_name = models.CharField(max_length=255, blank=True, null=True)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'device_event_histories'
        ordering = ['-created_at']
        indexes = [
            # models.Index(fields=['project_id', 'nick_name']),
            # models.Index(fields=['nick_name', 'device_prototype_id']),
            # models.Index(fields=['nick_name', 'created_at']),
            # models.Index(fields=['device_prototype_id', 'created_at']),
            # models.Index(fields=['project_id', 'created_at']),
            # models.Index(
            #     fields=['nick_name', 'project_id', 'device_prototype_id']),
            # models.Index(
            #     fields=['nick_name', 'device_prototype_id', 'created_at']),
            # models.Index(
            #     fields=['project_id', 'device_prototype_id', 'created_at']),
            # models.Index(fields=['nick_name', 'project_id', 'created_at']),
            # models.Index(fields=['nick_name', 'project_id',
            #                      'device_prototype_id', 'created_at'])
        ]


class LivingDetection(models.Model):
    """
      活物传感器-活动记录
    """
    # 对应的设备
    device = models.ForeignKey(Device, on_delete=models.CASCADE)
    # mac
    mac = models.CharField(max_length=255, db_index=True)
    # 参数标识
    identifier = models.CharField(max_length=255)
    # 参数值
    value = models.CharField(max_length=255, null=True)
    # 描述，有人活动或无人活动
    desc = models.CharField(max_length=255)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'living_detections'
        ordering = ['-created_at']


class WaterLeakageManager(models.Manager):
    """恒温恒湿风柜网关漏水检测"""

    def check_create(self, device, data):
        if device.active_room is not None:
            key = None

            if 'CTHAHU_1_WaterLeakageAlarm' in data:
                key = 'CTHAHU_1_WaterLeakageAlarm'

            if 'CTHAHU_2_WaterLeakageAlarm' in data:
                key = 'CTHAHU_2_WaterLeakageAlarm'

            if 'CTHAHU_3_WaterLeakageAlarm' in data:
                key = 'CTHAHU_3_WaterLeakageAlarm'

            if 'RDHAHU_1_WaterLeakageAlarm' in data:
                key = 'RDHAHU_1_WaterLeakageAlarm'

            if 'RDHAHU_2_WaterLeakageAlarm' in data:
                key = 'RDHAHU_2_WaterLeakageAlarm'

            if key is not None:
                desc = '无漏水'
                if data[key] == 1:
                    desc = '漏水'

                wl = self.create(
                    project_id=device.project_id,
                    active_room_id=device.active_room.id,
                    device_id=device.id,
                    identifier=key,
                    status=desc
                )
                wl.save()


class WaterLeakage(models.Model):
    """
      漏水记录，主要用于数据展示
    """
    # 所属项目
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    # 对应的房间，如果有
    active_room = models.ForeignKey(ActiveRoom, on_delete=models.CASCADE, null=True)
    # 所属设备
    device = models.ForeignKey(Device, on_delete=models.CASCADE)
    # 对应的参数点
    identifier = models.CharField(max_length=255)
    # 漏水或无漏水
    status = models.CharField(max_length=255)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'water_leakages'
        ordering = ['-created_at']

    objects = WaterLeakageManager()


class RoomDevice(models.Model):
    """
      房间与设备 或 房间与终端 关系表
    """
    # 对应房间
    active_room = models.ForeignKey(ActiveRoom, on_delete=models.CASCADE)
    # 对应设备或终端
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.BigIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')

    class Meta:
        db_table = 'room_devices'
        ordering = ['active_room']


class DeviceLimit(models.Model):
    """末端温度设定限制"""
    # 关联的项目
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    # 限制的设备类型
    device_prototype = models.ForeignKey(DevicePrototype, on_delete=models.CASCADE)
    # 配置的用途，10-供暖或供冷上下限
    limit_type = models.IntegerField()
    # 配置的名字，主要简化界面命名，让用户更好理解
    name = models.CharField(max_length=100)
    # 限制参数的 attribute_prototype_ids, 属性类型的id
    attribute_prototypes = models.CharField(max_length=255)
    # 简单描述用途
    remark = models.CharField(max_length=255, null=True, blank=True)
    # 限制值上限
    up_value = models.CharField(max_length=20)
    # 限制值下限
    low_value = models.CharField(max_length=20)
    # 是否生效
    enabled = models.BooleanField(default=False)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'device_limits'
        ordering = ['-created_at']


class DeviceRuntimeManager(models.Manager):
    @classmethod
    def cal_runtime(cls, device: Device, data: dict, created_at=None):
        if created_at is None:
            created_at = datetime.datetime.now()
        from saian_api.devdefine.models import DevicePrototypeRatio
        sub_dp_ids = DevicePrototype.objects.filter(parent_id=device.device_prototype_id).values_list('id', flat=True)
        dprs = DevicePrototypeRatio.objects.filter(Q(device_prototype_id=device.device_prototype_id) |
                                                   Q(device_prototype_id__in=sub_dp_ids)).filter(params__isnull=False)

        for dpr in dprs:
            if not dpr.params:
                continue
            try:
                params = json.loads(dpr.params)
                if not isinstance(params, list) or len(params) == 0:
                    continue
            except Exception as e:
                logging.error(f'解析运行时间倍率配置时出错({dpr.params}): {e.__str__()}')
                continue
            # 假设配置列表的每一列的 key 都是相同的
            params_keys = params[0].keys()
            # 是否配置了子设备
            has_idx = any(['_XXX' in k for k in params_keys])
            # s前缀
            prefix = None
            idx_data = {}
            if has_idx:
                # 解析 data 中的子设备数据， key 为子设备的索引
                common_data = {}
                for key, value in data.items():
                    if key in params_keys:
                        common_data[key] = value
                    else:
                        idx = re.findall(r'_\d+', key)
                        if len(idx) and key.replace(idx[0], '_XXX') in params_keys:
                            idx = idx[0].replace('_', '')
                            if Terminal.objects.filter(device=device, idx=idx).exists():
                                prev = idx_data.get(idx, {})
                                prev[key] = value
                                idx_data[idx] = prev
                if len(common_data):
                    for key, value in idx_data.items():
                        idx_data[key] = {**value, **common_data}

                # 解析 prefix，使用 device_prototype 的 prefix 字段
                sub_dp = DevicePrototype.objects.get(pk=dpr.device_prototype_id)
                prefix = sub_dp.prefix

            else:
                # 配置里没有子设备，key 为 '0'
                params_data = {}
                for key, value in data.items():
                    if key in params_keys:
                        params_data[key] = value
                if len(params_data):
                    idx_data['0'] = params_data

            runtimes = DeviceRuntime.objects.filter(device=device, prefix=prefix).order_by('-created_at')
            for idx, value, in idx_data.items():
                idx_runtimes = runtimes.filter(sub_idx=idx) if idx != '0' else runtimes
                device_runtime = idx_runtimes.first() if idx_runtimes.exists() else None

                # 新状态
                new_statuses = {}
                # 上次状态
                statuses = None
                # 是否记录
                should_record = False

                if device_runtime is not None and device_runtime.new_statuses:
                    # 上次 runtime 的状态，与上报数据对比
                    statuses = json.loads(device_runtime.new_statuses)
                    new_statuses = statuses.copy()

                    for k, v in value.items():
                        if statuses.get(k) != v:
                            should_record = True
                            new_statuses[k] = v
                else:
                    should_record = True
                    new_statuses_keys = []
                    if has_idx:
                        for k in params_keys:
                            new_statuses_keys.append(k.replace('_XXX', f'_{idx}'))
                    else:
                        new_statuses_keys = params_keys

                    for k in new_statuses_keys:
                        if k != 'Ratio':
                            if k in data.keys():
                                new_statuses[k] = data.get(k)
                            else:
                                # da = DeviceAttribute.objects.get_by_idf(device, k)
                                da = DeviceAttribute.objects.query_object_by_idf(device, k)
                                new_statuses[k] = da.value if da is not None else ''

                if should_record:
                    runtime = 0
                    ratio = DevicePrototypeRatio.objects.get_match_ratio(params, statuses, idx)
                    new_status_ratio = DevicePrototypeRatio.objects.get_match_ratio(params, new_statuses, idx)
                    if device_runtime is not None and new_status_ratio == 0 and ratio == 0 and device_runtime.run_time == 0:
                        device_runtime.new_statuses = json.dumps(new_statuses, ensure_ascii=False)
                        device_runtime.save()

                    else:
                        if device_runtime is not None:
                            now = created_at
                            last_record_begin = device_runtime.created_at
                            runtime = round((now - last_record_begin).seconds * ratio)

                        DeviceRuntime.objects.create(
                            device_id=device.id,
                            mac=device.mac,
                            nick_name=device.nick_name,
                            sub_idx=idx if idx != '0' else None,
                            prefix=prefix,
                            old_statuses=json.dumps(statuses, ensure_ascii=False) if statuses else None,
                            new_statuses=json.dumps(new_statuses, ensure_ascii=False),
                            run_time=runtime
                        )

                        # 更新设备累计运行时间
                        if runtime:
                            cls.update_device_runtime(device, idx, prefix, runtime)

    @staticmethod
    def update_device_runtime(device, idx, prefix, runtime):
        runtime_da = None
        if idx is not None and idx != '0':
            runtime_ap = AttributePrototype.objects.filter(Q(identifier__icontains=f'_{idx}_') & Q(identifier__endswith='SoftRuntime'),
                                                           device_prototype_id=device.device_prototype_id)
            if prefix is not None:
                runtime_ap = runtime_ap.filter(identifier__icontains=prefix)
            if runtime_ap.exists():
                if runtime_ap.count() == 1:
                    # runtime_da = DeviceAttribute.objects.get_by_ap(device, runtime_ap.first())
                    runtime_da = DeviceAttribute.objects.query_object_by_ap(device, runtime_ap.first())
                else:
                    logging.error(f'查找到多个子设备运行时间ap: idx-{idx}, device_prototype_id: {device.device_prototype_id}')
            else:
                runtime_da = None
            # try:
            #     sub_dp = DevicePrototype.objects.get(parent=device.device_prototype)
            #     runtime_da = DeviceAttribute.objects.get_by_idf(device, f'{sub_dp.prefix}_{idx}_SoftRuntime')
            # except DevicePrototype.DoesNotExist:
            #     logging.error(
            #         F"更新子设备累计运行时间，找不到对应的子设备类型.device-{device.id}, dp-{device.device_prototype_id}")
            # except DevicePrototype.MultipleObjectsReturned:
            #     logging.error(
            #         F"更新子设备累计运行时间，找到多个对应的子设备类型.device-{device.id}, dp-{device.device_prototype_id}")
        else:
            runtime_da = DeviceAttribute.objects.get_by_idf(device, 'SoftRuntime')

        if runtime_da is not None:
            prev = float(runtime_da.value) if runtime_da.value else 0
            runtime_da.value = prev + runtime / 60
            runtime_da.save()
            DeviceAttribute.objects.save_to_redis(device, runtime_da)


class DeviceRuntime(models.Model):
    """设备运行时间记录"""
    # 关联的设备
    device = models.ForeignKey(Device, on_delete=models.CASCADE)
    # 设备 MAC
    mac = models.CharField(max_length=100)
    # 设备昵称
    nick_name = models.CharField(max_length=100)
    # 子设备索引
    sub_idx = models.IntegerField(null=True)
    # 子设备前缀
    prefix = models.CharField(null=True, default=None, max_length=100)
    # 设备目前状态信息
    old_statuses = models.CharField(max_length=255, blank=True, null=True)
    # 设备新的状态信息
    new_statuses = models.CharField(max_length=255)
    # 当前状态下的运行时间，单位：秒
    run_time = models.IntegerField()
    # 创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 更新时间
    update_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'device_runtime'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['device_id']),
            models.Index(fields=['mac']),
            models.Index(fields=['device_id', 'created_at']),
            models.Index(fields=['mac', 'created_at'])
        ]

    objects = DeviceRuntimeManager()


class DataRoute(models.Model):
    """设备数据流转配置"""
    # 源设备
    src_mac = models.CharField(max_length=100)
    # 源参数
    src_idf = models.CharField(max_length=255)
    # 目标设备
    dest_mac = models.CharField(max_length=100)
    # 目标参数
    dest_idf = models.CharField(max_length=255)
    # 是否启用
    enabled = models.BooleanField(default=False)

    class Meta:
        db_table = 'data_routes'


class SyAttributeManager(models.Manager):
    def get_attach_devices(self, device_id):
        attach_devices = []
        dy_attributes = self.filter(device_id=device_id).order_by('mac')
        if not dy_attributes.exists():
            return []
        mac_list = list(set(dy_attributes.values_list('mac', flat=True)))
        for mac in mac_list:
            bluetooth_device = {'mac': mac}
            bluetooth_attrs = dy_attributes.filter(mac=mac).order_by('-updated_at')
            if bluetooth_attrs.exists():
                innate_idf = ['type', 'seq', 'nick_name', 'online', 'bp', 'rssi']
                # 附加设备专属参数
                specify_attrs = bluetooth_attrs.exclude(identifier__in=innate_idf)
                # 通用参数
                innate_attrs = bluetooth_attrs.filter(identifier__in=innate_idf)

                for attr in innate_attrs:
                    bluetooth_device[attr.identifier] = attr.value

                attrs = []
                # 固定 attrs 的顺序
                for attr in specify_attrs.order_by('-identifier'):
                    attrs.append({
                        'identifier': attr.identifier,
                        'value': attr.value,
                        'unit': self.get_identifier_unit(attr.identifier),
                        'name': self.get_identifier_name(attr.identifier)})

                bluetooth_device['attrs'] = attrs
                # 更改昵称和排序不计入最后更新时间
                last_ba = bluetooth_attrs.exclude(identifier__in=['type', 'seq', 'nick_name']).order_by('updated_at').last()
                if last_ba:
                    bluetooth_device['last_update_at'] = last_ba.updated_at
                else:
                    bluetooth_device['last_update_at'] = bluetooth_attrs.last().updated_at

                if 'seq' not in bluetooth_device:
                    bluetooth_device['seq'] = bluetooth_attrs.count()

            attach_devices.append(bluetooth_device)

        # 附加设备排序
        attach_devices.sort(key=lambda x: int(x['seq']))
        return attach_devices

    def cus_create(self, device: Device, mac, identifier, value):
        attr = self.filter(device_id=device.id, mac=mac, identifier=identifier).last()
        # 布尔值转换
        if isinstance(value, bool):
            if identifier == 'body_status':
                # 人体感应转换
                value = '有人' if value else '无人'
            elif identifier == 'sw_status':
                # 开关状态转换
                value = '开' if value else '关'
            else:
                value = 1 if value else 0

        if attr is None:
            self.create(device_id=device.id, mac=mac, identifier=identifier, value=value)
        else:
            attr.value = value
            attr.save()

    @classmethod
    def get_identifier_name(cls, idf):
        idf_dict = {
            'sw_status': '开关状态',
            'light': '光照强度',
            'body_status': '人体感应',
            'temp': '温度',
            'humi': '湿度',
            'online': '在线状态',
            'bp': '电池电量',
            'rssi': '信号',
            'type': '附加设备类型',
            'nick_name': '昵称'
        }
        if idf in idf_dict:
            return idf_dict[idf]
        return ''

    @classmethod
    def get_identifier_unit(cls, idf):
        idf_dict = {
            'rssi': 'dBm',
            'bp': '%',
            'temp': '℃',
            'humi': '%',
            'light': 'lux'
        }
        if idf in idf_dict:
            return idf_dict[idf]
        return ''

    @classmethod
    def get_default_value(cls, idf):
        if idf == 'nick_name':
            return ''
        if idf == 'online' or idf == 'sw_status' or idf == 'body_status':
            return False
        if idf == 'rssi':
            return -114
        if idf == 'bp' or idf == 'temp' or idf == 'humi' or idf == 'light':
            return 0
        return None

    def create_by_info_list(self, device: Device, info_list: list):
        """第一次添加蓝牙设备成功时，初始化蓝牙设备的属性"""
        # 过滤掉已经关联的蓝牙设备信息
        present_mac = set(DyAttribute.objects.filter(device_id=device.id).values_list('mac', flat=True))
        new_info_list = list(filter(lambda x: x['mac'] not in present_mac, info_list))
        for info in new_info_list:
            mac = info['mac']
            device_type = info['type']
            nick_name = info['nick_name']
            seq = info['seq']
            datapoints = info['datapoint']

            DyAttribute.objects.cus_create(device, mac, 'type', device_type)
            DyAttribute.objects.cus_create(device, mac, 'nick_name', nick_name)
            DyAttribute.objects.cus_create(device, mac, 'seq', seq)

            for idf in datapoints:
                self.cus_create(device, mac, idf, self.get_default_value(idf))

    def update_by_devices_data(self, device: Device, devices_data: list):
        """AttachDevicesData 上报，更新蓝牙设备的属性值"""
        for data in devices_data:
            mac = data['mac']
            messages = data['msg']

            for key, value in messages.items():
                self.cus_create(device, mac, key, value)

    def update_by_info_list(self, device: Device, info_list: list):
        """AttachDevicesInfoList 上报，更新数据点信息"""
        # 删除附加设备处理
        all_mac = set(DyAttribute.objects.filter(device=device).values_list('mac', flat=True))
        current_mac_list = [info['mac'] for info in info_list]
        # -- 要删除的附加设备 mac
        delete_mac_list = all_mac - set(current_mac_list)
        self.filter(mac__in=delete_mac_list).delete()

        for info in info_list:
            mac = info.get('mac', None)
            if mac is None:
                continue
            nick_name = info.get('nick_name', None)
            seq = info.get('seq', None)
            attach_device_type = info.get('type', None)
            datapoints = info.get('datapoint', None)

            # 更新固定数据点：nick_name, seq, type
            if nick_name is not None:
                self.cus_create(device, mac, 'nick_name', nick_name)
            if seq is not None:
                self.cus_create(device, mac, 'seq', seq)
            if attach_device_type is not None:
                self.cus_create(device, mac, 'type', attach_device_type)

            if datapoints is not None:
                # 参数点发生了变化
                present_attrs = self.filter(device=device, mac=mac).exclude(identifier__in=['type', 'nick_name', 'seq'])
                present_dps = present_attrs.values_list('identifier', flat=True)

                # 应该删除的数据点
                delete_dps = list(set(present_dps) - set(datapoints))
                present_attrs.filter(identifier__in=delete_dps).delete()
                # 新增的数据点
                add_dps = list(set(datapoints) - set(present_dps))
                for idf in add_dps:
                    self.cus_create(device, mac, idf, self.get_default_value(idf))


class DyAttribute(models.Model):
    # 关联的设备
    device = models.ForeignKey(Device, on_delete=models.CASCADE)
    # 蓝牙设备Mac
    mac = models.CharField(max_length=255)
    # 属性
    identifier = models.CharField(max_length=255)
    # 值
    value = models.CharField(max_length=255, null=True)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'dy_attributes'

    objects = SyAttributeManager()


class SySimManager(models.Manager):
    from saian_api.utils.httpapi.onelink import OneLinkApi
    appid = os.environ.get('ONELINK_APPID')
    password = os.environ.get('ONELINK_PASSWORD')
    api = OneLinkApi(appid=appid, password=password)

    def update_sim_base(self, sy_sim):
        """ 更新 sim 卡基础信息 """
        iccid = sy_sim.iccid

        status = self.api.get_sim_status(iccid=iccid)
        if status is not None:
            status = self.convert_status(sy_sim, status)
            sy_sim.status = status

        imei = self.api.get_imei(iccid=iccid)
        if imei is not None:
            sy_sim.imei = imei

        stop_reason, desc = self.api.get_stop_reason(iccid=iccid)
        if stop_reason is not None:
            sy_sim.stop_reason = self.convert_stop_reason(sy_sim, stop_reason, desc)

        data_consumed = self.api.get_data_consumed(iccid=iccid)
        if data_consumed is not None:
            try:
                sy_sim.data_consumed = float(data_consumed)
            except Exception as e:
                logging.error(f'更新 sim 卡月用量出错，value: {data_consumed}, err: {e.__str__()}')

        sy_sim.sync_at = datetime.datetime.now()

        sy_sim.save()

        return sy_sim

    def update_sim_info(self, sy_sim):
        """ 查询更新 SySim 表的信息 """

        sy_sim = self.update_sim_base(sy_sim)
        iccid = sy_sim.iccid
        base_info = self.api.get_sim_basic_info(iccid=iccid)
        if base_info is not None:
            sy_sim.msisdn = base_info.get('msidsn', sy_sim.msisdn)
            sy_sim.activated_at = base_info.get('activeDate', sy_sim.activated_at)
            sy_sim.opened_at = base_info.get('openDate', sy_sim.opened_at)

        if sy_sim.platform is None:
            platform = self.api.get_platform(iccids=iccid)
            if platform is not None:
                sy_sim.platform = platform[0].get('platformType')

        sy_sim.save()
        return sy_sim

    @classmethod
    def convert_status(cls, sy_sim, status_code):
        if sy_sim.platform == 'OneLink-PB':
            if status_code == '00':
                return 10
            if status_code == '01':
                return 20
            if status_code == '02':
                return 30
            if status_code == '03':
                return 40
            if status_code == '05':
                return 50
            if status_code == '06':
                return 60
            if status_code == '07':
                return 70
            if status_code == '99':
                return 999
            logging.warning(f'状态不匹配: iccid-{sy_sim.iccid}, platform-{sy_sim.platform}, status_code-{status_code}')
            return None
        elif sy_sim.platform == 'OneLink-CT':
            if status_code == '1':
                return 70
            if status_code == '2':
                return 80
            if status_code == '4':
                return 30
            if status_code == '6':
                return 90
            if status_code == '7':
                return 100
            if status_code == '8':
                return 40
            logging.warning(f'状态不匹配: iccid-{sy_sim.iccid}, platform-{sy_sim.platform}, status_code-{status_code}')
            return None
        else:
            logging.error(f'未知 platform: {sy_sim.platform}')
            return None

    @classmethod
    def convert_stop_reason(cls, sy_sim, stop_reason, desc):
        if sy_sim.platform == 'OneLink-PB':
            if stop_reason == '01':
                return '欠费停机'
            if stop_reason == '02':
                return '用户挂失'
            if stop_reason == '03':
                return '用户主动停机'
            if stop_reason == '11':
                return f'管理型停机-{desc}'
            logging.warning(f'状态不匹配: iccid-{sy_sim.iccid}, platform-{sy_sim.platform}, stop_reason-{stop_reason}')
        elif sy_sim.platform == 'OneLink-CT':
            reason = ''
            for idx, s, in enumerate(stop_reason):
                if s == '2':
                    if idx == 0:
                        reason += '窄带网商品到期失效停机;'
                    elif idx == 1:
                        reason += '机卡分离停机;'
                    elif idx == 3:
                        reason += 'M2M 管理停机;'
                    elif idx == 4:
                        reason += '信控双向停机;'
                    elif idx == 8:
                        reason += '主动(申请)双向停机;'
                    elif idx == 10:
                        reason += '强制双向停机;'
                    else:
                        logging.warning(f'状态不匹配: iccid-{sy_sim.iccid}, platform-{sy_sim.platform}, stop_reason-{stop_reason}')

            if reason == '':
                reason = None
            return reason
        else:
            logging.error(f'未知 platform: {sy_sim.platform}')
            return None


class SySim(models.Model):
    """物联网卡"""
    # sim 卡的 iccid
    iccid = models.CharField(max_length=255, db_index=True)
    # sim 卡的 imei（即设备 mac）
    imei = models.CharField(max_length=255, null=True, db_index=True)
    # 网关 SN 码
    sn = models.CharField(max_length=255, null=True, blank=True, default=None)
    # sim 卡的 msisdn
    msisdn = models.CharField(max_length=255)
    # 开卡平台，OneLink-PB或OneLink-CT，默认：OneLink-CT
    platform = models.CharField(max_length=24)
    # 状态，10 - 正常，20 - 单向停机，30 - 停机，40 - 预销户，50 - 过户，60 - 休眠，
    #      70 - 待激活，80 - 已激活，90 - 可测试，100 - 库存，999 - 号码不存在
    status = models.CharField(max_length=24, null=True)
    # 停机原因
    stop_reason = models.CharField(max_length=255, null=True, blank=True)
    # 月流量，单位：KB
    data_consumed = models.IntegerField(default=0)

    # 备注
    remark = models.CharField(max_length=255, null=True, blank=True)
    # 激活时间
    activated_at = models.DateTimeField(null=True)
    # 开卡时间
    opened_at = models.DateTimeField(null=True)
    # 与 onelink 同步信息时间
    sync_at = models.DateTimeField(null=True)

    # 数据创建时间
    created_at = models.DateTimeField(auto_now_add=True)
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'sy_sims'
        ordering = ['-created_at']

    objects = SySimManager()


class DaSnapshotManager(models.Manager):
    @classmethod
    def query_object_by_names(cls, project_id, names: list):
        results = RedisHelper.get_multi_value(project_id, names, True)
        snapshots = {}
        for idx, name in enumerate(names):
            if results[idx]:
                snapshots[name.replace('snap:', '')] = results[idx]

        return snapshots

    @classmethod
    def get_latest_snapshots(cls, project_id, device_id, ap_ids, dt=None):
        """查询快照表中，设备的最新快照"""
        if dt is None:
            dt = datetime.datetime.now()
        subquery = DaSnapshot.objects.using(f'prj{project_id}db').filter(
            device_id=device_id,
            attribute_prototype_id=OuterRef('attribute_prototype_id'),
            snapped_at__lte=dt
        ).order_by('-snapped_at').values('snapped_at')[:1]

        latest_records = DaSnapshot.objects.filter(device_id=device_id, attribute_prototype_id__in=ap_ids, snapped_at=Subquery(subquery))

        # 从历史表查询
        record_ap_ids = list(latest_records.values_list('attribute_prototype_id', flat=True))
        his_ap_ids = set(ap_ids) - set(record_ap_ids)

        his_subquery = DaSnapshotHistory.objects.using(f'prj{project_id}db').filter(
            device_id=device_id,
            attribute_prototype_id=OuterRef('attribute_prototype_id'),
            snapped_at__lte=dt
        ).order_by('-snapped_at').values('snapped_at')[:1]
        his_records = DaSnapshotHistory.objects.filter(device_id=device_id, attribute_prototype_id__in=his_ap_ids, snapped_at=Subquery(his_subquery))

        return list(latest_records) + list(his_records)


class DaSnapshot(models.Model):
    """ 设备数据快照 """
    # 所属设备
    device = models.ForeignKey(Device, on_delete=models.CASCADE)
    # 属性类型id
    attribute_prototype_id = models.BigIntegerField()
    # 属性值
    value = models.CharField(max_length=255, null=True)

    # 数据创建时间
    snapped_at = models.DateTimeField()
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'da_snapshots'
        ordering = ['-snapped_at']
        indexes = [
            models.Index(fields=['device_id', 'attribute_prototype_id', 'snapped_at']),
        ]

    objects = DaSnapshotManager()


class DaSnapshotHistory(models.Model):
    """ 设备数据快照历史表 """
    # 所属设备
    device = models.ForeignKey(Device, on_delete=models.CASCADE)
    # 属性类型id
    attribute_prototype_id = models.BigIntegerField()
    # 属性值
    value = models.CharField(max_length=255, null=True)

    # 数据创建时间
    snapped_at = models.DateTimeField()
    # 数据最后更新时间
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'da_snapshot_histories'
        ordering = ['-snapped_at']
        indexes = [
            models.Index(fields=['device_id', 'attribute_prototype_id', 'snapped_at']),
        ]
