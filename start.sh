#!/bin/bash

# 如果环境变量"DRF_CELERY"等于"True", 则运行celery
if [ "$DRF_CELERY" = "True" ]; then
  nohup celery -A saian_api.celery:celery_app worker -l WARNING -P gevent -c 100 --broker-connection-retry --broker-connection-retry-on-startup > logs/celery_worker.log &
#  nohup celery -A saian_api.celery:celery_app beat -l WARNING > logs/celery_beat.log &
fi
gunicorn -c gunicorn_conf.py saian_api.wsgi --reload
