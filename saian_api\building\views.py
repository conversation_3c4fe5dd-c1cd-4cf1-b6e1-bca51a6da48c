import calendar
import datetime
import json
import logging
import os
from pathlib import Path

from django.contrib.contenttypes.models import ContentType
from django.db import transaction
from django.db.models import Q, Avg, F, Count
from django.shortcuts import get_object_or_404
from openpyxl.reader.excel import load_workbook
from rest_framework import exceptions
from rest_framework import status
from rest_framework import viewsets
from rest_framework.response import Response

from saian_api.building.serializers import (BuildingSerializer, RoomItemSerializer, RoomTemphumSerializer,
                                            FloorV5Serializer, FloorTerminalSerializer, SimpleRoomSerializer, FloorDeviceSerializer)
from saian_api.device.models import RoomDevice, DeviceEvent, Device
from saian_api.project.models import Project
from saian_api.report.models import ReportConfigurer, RoomHourlyStat, RoomDailyStat
from saian_api.utils.httpapi.image import ImageAPI
from saian_api.utils.sy_jsonrenderer import SyJSONRender
from saian_api.utils.tools import fill_element
from .models import Building, Floor, ActiveRoom
from .serializers import FloorSerializer, RoomDeviceSerializer, RoomSerializer
from .. import settings
from ..devdefine.models import DevicePrototype
from ..devdefine.serializers import SimpleDevicePrototypeSerializer
from ..terminal.models import Terminal
from ..terminal.serializers import TerminalSerializer
from ..utils.db.Convert import Convert
from ..utils.utils import ExcelUtils, AuthUtils


class BuildingViewSet(viewsets.ModelViewSet):
    renderer_classes = (SyJSONRender,)
    serializer_class = BuildingSerializer

    def get_queryset(self):
        project = Project.objects.get_by_request(self.request)
        return project.building_set.all().order_by(F('building_no').asc(nulls_last=True), 'id')

    # 创建前添加项目id
    def perform_create(self, serializer):
        serializer.save(project_id=self.request.user['project_id'])

    def list(self, request, *args, **kwargs):
        buildings = super(BuildingViewSet, self).list(
            request, *args, **kwargs).data

        res_data = {
            "count": buildings['count'],
            'buildings': buildings['results']
        }

        return Response(res_data)

    def create(self, request, *args, **kwargs):
        building = super(BuildingViewSet, self).create(
            request, *args, **kwargs).data
        res_data = {
            'building': building
        }

        return Response(res_data)

    def retrieve(self, request, *args, **kwargs):
        building = super(BuildingViewSet, self).retrieve(
            request, *args, **kwargs).data
        res_data = {
            'building': building
        }

        return Response(res_data)

    def partial_update(self, request, *args, **kwargs):
        building = super().partial_update(request, *args, **kwargs).data
        res_data = {
            'building': building
        }

        return Response(res_data)


class FloorViewSet(viewsets.ModelViewSet):
    serializer_class = FloorSerializer
    renderer_classes = (SyJSONRender,)

    """
      根据项目范围返回对应结果集
    """

    def get_queryset(self):
        project = Project.objects.get_by_request(self.request)
        queryset = Floor.objects.filter(
            building__in=project.building_set.all())
        building_id = self.request.query_params.get('building_id', None)
        search = self.request.query_params.get('search', None)
        path_building_id = self.kwargs.get('building_id', None)

        if building_id is not None:
            queryset = queryset.filter(building_id=building_id)

        if search is not None:
            queryset = queryset.filter(name__contains=search)

        if path_building_id is not None:
            queryset = queryset.filter(building_id=path_building_id)

        return queryset.order_by('floor_no')

    def list(self, request, *args, **kwargs):
        floors = super(FloorViewSet, self).list(request, *args, **kwargs).data

        res_data = {
            "count": floors['count'],
            'floors': floors['results']
        }

        return Response(res_data)

    def create(self, request, *args, **kwargs):
        building_id = kwargs.get('building_id')
        data = request.data
        data['building'] = building_id
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)

        res_data = {
            'floor': serializer.data
        }

        return Response(res_data)

    def retrieve(self, request, *args, **kwargs):
        floor_data = super(FloorViewSet, self).retrieve(request, *args, **kwargs).data
        res_data = {
            'floor': floor_data
        }

        return Response(res_data)

    def partial_update(self, request, *args, **kwargs):
        coords_arr = request.data.get('coords_arr', None)
        if coords_arr is not None:
            self.get_object().update_with_covers(request)

        floor = super().partial_update(request, *args, **kwargs).data
        res_data = {
            'floor': floor
        }

        return Response(res_data)

class SimpleRoomView(viewsets.ModelViewSet):
    serializer_class = SimpleRoomSerializer

    def get_queryset(self):
        queryset = ActiveRoom.objects.all()

        building_id = self.request.query_params.get('building', None)
        if building_id is not None:
            queryset = queryset.filter(building_id=building_id)

        floor_id = self.request.query_params.get('floor', None)
        if floor_id is not None:
            queryset = queryset.filter(floor_id=floor_id)

        search = self.request.query_params.get('search', None)
        if search is not None:
            queryset = queryset.filter(name__icontains=search)

        return queryset.order_by(F('active_room_no').asc(nulls_last=True), 'floor__floor_no', 'name')

    def list(self, request, *args, **kwargs):
        data = super().list(request, *args, **kwargs).data
        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'rooms': data['results'],
                'count': data['count']
            }
        })


class RoomViewSet(viewsets.ModelViewSet):
    """
      房间的CRUD
    """
    serializer_class = RoomSerializer
    renderer_classes = (SyJSONRender,)
    queryset = ActiveRoom.objects.all().order_by(F('active_room_no').asc(nulls_last=True), 'floor__floor_no', 'name')

    def get_serializer_class(self):
        if self.action == 'retrieve':
            return RoomItemSerializer
        return self.serializer_class

    def list(self, request, *args, **kwargs):
        building_id = self.request.query_params.get('building', None)
        floor_id = self.request.query_params.get('floor', None)
        search = self.request.query_params.get('search', None)

        queryset = ActiveRoom.objects.all().order_by(F('active_room_no').asc(nulls_last=True), 'floor__floor_no')
        if building_id is not None:
            queryset = queryset.filter(building_id=building_id)
        if floor_id is not None:
            queryset = queryset.filter(floor_id=floor_id)
        if search is not None:
            queryset = queryset.filter(name__contains=search)

        rooms = self.paginate_queryset(queryset)
        rooms = RoomSerializer(rooms, many=True).data

        now = datetime.datetime.now()
        building_cache = {}
        floor_cache = {}
        for room in rooms:
            building_id = room['building']
            building = building_cache.get(building_id, None)
            if building is None:
                building = Building.objects.get(pk=building_id)
                building_cache[building_id] = building

            floor_id = room['floor']
            floor = floor_cache.get(floor_id, None)
            if floor is None:
                floor = Floor.objects.get(pk=floor_id)
                floor_cache[floor_id] = floor

            room['building_id'] = building.id
            room['building'] = building.name
            room['floor_id'] = floor.id
            room['floor_name'] = floor.name

            avg_temp = RoomHourlyStat.objects.filter(created_at__gte=now - datetime.timedelta(hours=24),
                                                     active_room_id=room['id'], identifier__icontains='Temp').aggregate(avg=Avg('avg'))['avg']
            avg_hum = RoomHourlyStat.objects.filter(created_at__gte=now - datetime.timedelta(hours=24), active_room_id=room['id']).filter(
                Q(identifier__icontains='Hum') | Q(identifier__icontains='RH')).aggregate(avg=Avg('avg'))['avg']
            if avg_temp:
                room['temp'] = round(avg_temp, 2)
            if avg_hum:
                room['hum'] = round(avg_hum, 2)

        res_data = {
            "count": queryset.count(),
            'rooms': rooms
        }

        return Response(res_data)

    def create(self, request, *args, **kwargs):
        building = kwargs.get('building')
        floor = kwargs.get('floor')
        data = request.data
        data['building'] = building
        data['floor'] = floor
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)

        res_data = {
            'active_room': serializer.data
        }

        return Response(res_data)

    def retrieve(self, request, *args, **kwargs):
        room = super().retrieve(request, *args, **kwargs).data

        device_model = ContentType.objects.get_for_model(Device)
        terminal_model = ContentType.objects.get_for_model(Terminal)

        room_id = room.get('id')
        rd_devices = list(RoomDevice.objects.filter(content_type=device_model, active_room_id=room_id))
        device_ids = [rd.object_id for rd in rd_devices]
        rd_terminals = list(RoomDevice.objects.filter(content_type=terminal_model, active_room_id=room_id))
        terminal_ids = [rd.object_id for rd in rd_terminals]

        dt_cache = {}

        devices = []
        for device in Device.objects.filter(id__in=device_ids):
            device_type = dt_cache.get(device.device_type_id, None)
            if device_type is None:
                device_type = device.device_type
                dt_cache[device.device_type_id] = device_type
            room_device = list(filter(lambda x: x.object_id == device.id, rd_devices))[0]
            devices.append({
                'id': device.id,
                'nick_name': device.nick_name,
                'mac': device.mac,
                'device_type': device_type.name,
                'room_device_id': room_device.id
            })

        terminals = []
        for terminal in Terminal.objects.filter(id__in=terminal_ids):
            room_terminal = list(filter(lambda x: x.object_id == terminal.id, rd_terminals))[0]
            t_device = terminal.device
            device_type = dt_cache.get(t_device.device_type_id, None)
            if device_type is None:
                device_type = t_device.device_type
                dt_cache[t_device.device_type_id] = device_type

            terminals.append({
                'id': terminal.id,
                'nick_name': terminal.nick_name,
                'idx': terminal.idx,
                'prefix': terminal.prefix,
                'device_type': device_type.name,
                'room_device_id': room_terminal.id,
                'mac': t_device.mac
            })

        room['devices'] = devices
        room['terminals'] = terminals

        res_data = {
            'room': room
        }

        return Response(res_data)

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()
        data = request.data
        new_floor = request.data.get('new_floor_id', None)
        if new_floor is not None:
            data['floor'] = new_floor
        serializer = self.get_serializer(instance, data=data, partial=True)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        res_data = {
            'active_room': serializer.data
        }

        return Response(res_data)


class RoomTemphumsViewSet(viewsets.ModelViewSet):
    serializer_class = RoomTemphumSerializer
    renderer_classes = (SyJSONRender,)

    def get_queryset(self):
        queryset = ActiveRoom.objects.all()

        building_id = self.request.query_params.get('building_id', None)
        floor_id = self.request.query_params.get('floor_id', None)
        search = self.request.query_params.get('search', None)

        if building_id is not None:
            queryset = queryset.filter(building_id=building_id)
        if floor_id is not None:
            queryset = queryset.filter(floor_id=floor_id)
        if search is not None:
            queryset = queryset.filter(Q(building__name__contains=search) | Q(
                building__floor__name__contains=search) | Q(name__contains=search))
        return queryset

    def list(self, request, *args, **kwargs):
        rooms = super(RoomTemphumsViewSet, self).list(request, *args, **kwargs).data

        res_data = {
            'rooms': rooms['results'],
            'count': self.get_queryset().count()
        }

        return Response(res_data)


class RoomDeviceViewSet(viewsets.ModelViewSet):
    queryset = RoomDevice.objects.all()
    serializer_class = RoomDeviceSerializer
    renderer_classes = (SyJSONRender,)

    def create(self, request, *args, **kwargs):
        project_id = request.user['project_id']
        # 前端传的是列表字符串，先转换成列表
        room_id = request.data.get('room_id', None)
        device_ids = request.data.get('device_ids', None)
        terminal_ids = request.data.get('terminal_ids', None)

        if room_id is None:
            raise exceptions.ValidationError(detail={'detail': 'room_id is required!'})
        if device_ids is not None:
            device_ids = eval(device_ids)
            # data['device_ids'] = device_ids
        if terminal_ids is not None:
            terminal_ids = eval(terminal_ids)
            # data['terminal_ids'] = terminal_ids

        room_devices = []
        terminal_model = ContentType.objects.get_for_model(Terminal)
        device_model = ContentType.objects.get_for_model(Device)

        if device_ids is not None:
            with transaction.atomic(using=f'prj{project_id}db'):
                # 删除房间终端的关联
                RoomDevice.objects.filter(content_type=terminal_model, active_room_id=room_id).delete()

                current_devices = list(RoomDevice.objects.filter(content_type=device_model, active_room_id=room_id
                                                                 ).values_list('object_id', flat=True))

                create_ids = set(device_ids) - set(current_devices)
                delete_ids = set(current_devices) - set(device_ids)

                new_room_devices = []
                for did in create_ids:
                    new_room_devices.append(RoomDevice(
                        content_type=device_model,
                        object_id=did,
                        active_room_id=room_id
                    ))
                RoomDevice.objects.bulk_create(new_room_devices)

                RoomDevice.objects.filter(content_type=device_model, active_room_id=room_id, object_id__in=delete_ids).delete()

        elif terminal_ids is not None:
            with transaction.atomic(using=f'prj{project_id}db'):
                # 删除房间设备的关联
                RoomDevice.objects.filter(content_type=device_model, active_room_id=room_id).delete()

                current_terminals = list(RoomDevice.objects.filter(content_type=terminal_model, active_room_id=room_id
                                                                   ).values_list('object_id', flat=True))

                create_ids = set(terminal_ids) - set(current_terminals)
                delete_ids = set(current_terminals) - set(terminal_ids)

                new_room_terminals = []
                for tid in create_ids:
                    new_room_terminals.append(RoomDevice(
                        content_type=terminal_model,
                        object_id=tid,
                        active_room_id=room_id
                    ))
                RoomDevice.objects.bulk_create(new_room_terminals)

                RoomDevice.objects.filter(content_type=terminal_model, active_room_id=room_id, object_id__in=delete_ids).delete()

        return Response({
            'status': status.HTTP_200_OK,
            'data': None
        })

    def destroy(self, request, *args, **kwargs):
        super().destroy(request, *args, **kwargs)

        res_data = None

        return Response(res_data)


class FloorV2ViewSet(viewsets.GenericViewSet):
    renderer_classes = (SyJSONRender,)

    def list(self, request):
        buildings = Building.objects.filter(project_id=request.user['project_id'])

        queryset = Floor.objects.filter(building__in=buildings).order_by('floor_no')

        building_id = request.query_params.get('building_id', None)
        search = request.query_params.get('search', None)

        if building_id is not None:
            queryset = queryset.filter(building_id=building_id)

        if search is not None:
            queryset = queryset.filter(name__contains=search)

        total = queryset.count()
        queryset = self.paginate_queryset(queryset)

        floors_fields = []
        for floor in queryset:
            floor_fields = {
                'id': floor.id,
                'floor_no': floor.floor_no,
                'name': floor.name,
                'created_at': floor.created_at,
                'thumb': ImageAPI.get_url(request, image_id=floor.thumb),
                'building': {
                    'id': floor.building.id,
                    'name': floor.building.name
                }
            }

            floors_fields.append(floor_fields)

        res_data = {
            "count": total,
            'floors': floors_fields
        }

        return Response(res_data)

    def retrieve(self, request, pk=None):
        # floor = Floor.objects.get(pk=pk)
        floor = get_object_or_404(Floor, pk=pk)

        rooms_fields = []

        for ar in floor.activeroom_set.all():
            room_fields = {
                'id': ar.id,
                'building_id': ar.building_id,
                'name': ar.name
            }

            devices = []
            for room_device in RoomDevice.objects.filter(active_room_id=ar.id, content_type=ContentType.objects.get_for_model(Device)):
                device = room_device.content_object
                if device is not None:
                    device_type = device.device_type
                    device_fields = {
                        'id': device.id,
                        'nick_name': device.nick_name,
                        'status': device.status,
                        'in_alarm': device.in_alarm,
                        'mac': device.mac,
                        'sw_status': device.sw_on,
                        'device_type': device_type.name,
                        # 为保持与旧接口一致
                        'icon_coords': None,
                        'icon_wh': None,
                        'name_coords': None,
                        'cover_coords': None,
                        'value_arr': None
                    }

                    devices.append(device_fields)
            room_fields['devices'] = devices

            terminals = []
            for room_terminal in RoomDevice.objects.filter(active_room_id=ar.id, content_type=ContentType.objects.get_for_model(Terminal)):
                terminal = room_terminal.content_object
                if terminal is not None:
                    terminals.append({
                        'id': terminal.id,
                        'nick_name': terminal.nick_name,
                    })
            room_fields['terminals'] = terminals

            rooms_fields.append(room_fields)

        coords_arr = fill_element(floor.coords_arr)

        dp_dict = {}
        cover_types = {}
        if coords_arr:
            device_ids = [arr['device_id'] for arr in coords_arr if arr.get('device_id', None)]
            devices = Device.objects.filter(id__in=device_ids).values('id', 'device_prototype_id')
            device_dp_map = {device["id"]: device["device_prototype_id"] for device in devices}
            dp_stats = devices.annotate(device_count=Count('id')).order_by('device_prototype_id')
            dp_ids = [stat['device_prototype_id'] for stat in dp_stats]
            dp_objects = {dp.id: dp for dp in DevicePrototype.objects.filter(id__in=dp_ids)}
            for stat in dp_stats:
                dp = dp_objects.get(stat['device_prototype_id'])
                stat['name'] = dp.name
                stat['uni_name'] = dp.uni_name
                if dp_dict.get(dp.id, None) is None:
                    dp_dict[dp.id] = stat
                else:
                    dp_dict[dp.id]['device_count'] += 1

            for arr in filter(lambda arr: arr.get('device_id'), coords_arr):
                arr_type = arr.get('type', None)
                icon_prefix = arr.get('icon_prefix', None)
                icon_type = arr.get('icon_type', None)
                if arr_type and (icon_prefix or icon_type):
                    device_id = arr['device_id']
                    dp_id = device_dp_map.get(device_id)
                    key = (arr_type, icon_prefix, icon_type)
                    cover_types[json.dumps(key)] = dp_dict.get(dp_id)

        floor_data = {
            'id': floor.id,
            'floor_no': floor.floor_no,
            'name': floor.name,
            'coords_arr': coords_arr,
            'cover_types': cover_types,
            'image': ImageAPI.get_url(request, image_id=floor.image, size='originals'),
            'thumb': ImageAPI.get_url(request, image_id=floor.thumb, size='originals'),
            'rooms': rooms_fields
        }
        res_data = {
            'floor': floor_data
        }

        return Response(res_data)


class RoomStatViewSet(viewsets.GenericViewSet):
    # 根据条件查询设备的报表格式数据
    def list(self, request):
        room_id = request.query_params.get('arid', None)
        name = request.query_params.get('name', None)
        type = request.query_params.get('type', None)
        from_at = request.query_params.get('from', None)
        till_at = request.query_params.get('till', None)
        names = request.query_params.get('names', None)
        rc, active_room = None, None

        values, multiSeries, total = None, None, None

        if room_id is not None:
            active_room = ActiveRoom.objects.get(pk=room_id)
        else:
            raise exceptions.ValidationError(detail={'detail': 'Room id is required!'})

        if type is None:
            raise exceptions.ValidationError(detail={'detail': 'Type is required!'})

        if name is None and names is None:
            raise exceptions.ValidationError(detail={'detail': 'Name or names is required!'})

        # 格式化时间
        if from_at is None or till_at is None:
            if type == 'di':
                from_at = datetime.datetime.now().strftime('%Y-%m-%d 00:00:00')
                till_at = datetime.datetime.now().strftime('%Y-%m-%d 23:59:59')
            else:
                from_at = datetime.datetime.now().strftime('%Y-%m-%d 00:00:00')
                till_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        else:
            from_at = datetime.datetime.strptime(from_at, '%Y%m%d%H%M%S')
            till_at = datetime.datetime.strptime(till_at, '%Y%m%d%H%M%S')

        target_type = ContentType.objects.get_for_model(active_room)

        if name is not None:
            rc = ReportConfigurer.objects.filter(target_type=target_type, target_id=active_room.id, name=name).first()
            if rc is not None:
                values = self.single_series(active_room, rc, type, from_at, till_at)

        if names is not None:
            rcs = ReportConfigurer.objects.filter(target_type=target_type, target_id=active_room.id, name__in=names.split(','))
            if rcs.count() > 0:
                multiSeries, total = self.multiple_series(active_room, rcs, type, from_at, till_at)

        res_data = {
            'status': status.HTTP_200_OK,
            'data': {
                'room_name': active_room.name if active_room is not None else '',
                'name': rc.name if rc is not None else '',
                'unit': rc.unit if rc is not None else '',
                'values': values,
                'total': total,
                'multiSeries': multiSeries
            }
        }

        return Response(res_data)

    # 单个系列的数据
    def single_series(self, room, rc, type, from_at, till_at):
        values = []

        # if type == 'rt':
        #     for device in room.devices.all():
        #         # 实时数据
        #         device_values = DeviceEvent.objects.get_data_by_idf(device.id, rc.identifier, from_at, till_at)
        #         if len(device_values) > 0:
        #             values.append(device_values)

        # 小时统计数据
        if type == 'hr':
            values = RoomHourlyStat.objects.get_report_stat(room, rc.identifier, from_at, till_at)

        # 日统计数据
        if type == 'di':
            values = RoomDailyStat.objects.get_report_stat(room, rc.identifier, from_at, till_at)

        return values

    # 多个系列的数据
    def multiple_series(self, room, rcs, type, from_at, till_at):
        data = []
        total = 0

        for rc in rcs:
            item = {'name': rc.name, 'unit': rc.unit, 'refer_point': rc.refer_point,
                    'values': self.single_series(room, rc, type, from_at, till_at)}

            total += len(item['values'])

            data.append(item)

        return data, total


class FloorV5View(viewsets.ModelViewSet):
    serializer_class = FloorV5Serializer

    def get_queryset(self):
        queryset = Floor.objects.all()

        building = self.request.query_params.get('building_id', None)
        if building is not None:
            queryset = queryset.filter(building_id=building)

        search = self.request.query_params.get('search', None)
        if search is not None:
            queryset = queryset.filter(name__icontains=search)

        return queryset

    def list(self, request, *args, **kwargs):
        data = super().list(request, *args, **kwargs).data

        device_model = ContentType.objects.get_for_model(Device)
        terminal_model = ContentType.objects.get_for_model(Terminal)

        floors = data['results']
        for floor in floors:
            room_ids = ActiveRoom.objects.filter(floor_id=floor['id']).values_list('id', flat=True)
            floor['device_count'] = RoomDevice.objects.filter(content_type=device_model, active_room_id__in=room_ids).count()
            floor['terminal_count'] = RoomDevice.objects.filter(content_type=terminal_model, active_room_id__in=room_ids).count()

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'floors': floors,
            },
            'total': data['count']
        })

class FloorTerminalView(viewsets.ModelViewSet):

    def list(self, request, *args, **kwargs):
        floor_id = request.query_params.get('floor_id', None)
        building_id = request.query_params.get('building_id', None)
        search = request.query_params.get('search', None)
        dp_id = request.query_params.get('dp_id', None)

        terminal_model = ContentType.objects.get_for_model(Terminal)

        if floor_id is not None:
            room_ids = ActiveRoom.objects.filter(floor_id=floor_id).values_list('id', flat=True)
        elif building_id is not None:
            floor_ids = Floor.objects.filter(building_id=building_id).values_list('id', flat=True)
            room_ids = ActiveRoom.objects.filter(floor_id__in=floor_ids).values_list('id', flat=True)
        else:
            raise exceptions.ValidationError(detail={'detail': 'floor or building is required!'})

        terminal_ids = RoomDevice.objects.filter(content_type=terminal_model, active_room_id__in=room_ids).values_list('object_id', flat=True)
        terminal_queryset = Terminal.objects.filter(id__in=terminal_ids)

        if search is not None:
            terminal_queryset = terminal_queryset.filter(nick_name__icontains=search)

        if dp_id is not None:
            terminal_queryset = terminal_queryset.filter(device_prototype_id=dp_id)

        dp_ids = terminal_queryset.values_list('device_prototype_id', flat=True)

        terminal_queryset = terminal_queryset.order_by(Convert('nick_name', 'gbk'))

        dp_cache = {}
        count = terminal_queryset.count()

        if request.query_params.get('per_page', None) is not None:
            terminals = list(self.paginate_queryset(terminal_queryset))
        else:
            terminals = list(terminal_queryset)

        # dp_ids = [t.device_prototype_id for t in terminals]
        dps = list(DevicePrototype.objects.filter(id__in=dp_ids))
        for dp in dps:
            dp_cache[dp.id] = dp

        results = Terminal.objects.append_dashboard_attres(terminals, FloorTerminalSerializer, dps)

        for item in results:
            dp_id = item['device_prototype_id']
            dp = dp_cache.get(dp_id, None)
            if dp is None:
                dp = DevicePrototype.objects.get(pk=dp_id)
                dp_cache[dp_id] = dp

            item['device_prototype'] = SimpleDevicePrototypeSerializer(dp).data
            item['custz_detail'] = dp.web_content is not None

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'floor_terminals': results,
                'count': count,
                'dp_ids': dp_ids
            }
        })


class RoomHourlyTemp(viewsets.ModelViewSet):
    renderer_classes = (SyJSONRender,)

    def list(self, request, *args, **kwargs):
        building_id = request.query_params.get('building_id', None)
        if building_id is not None:
            building_rooms = list(ActiveRoom.objects.filter(building_id=building_id).values_list('id', flat=True))
        else:
            building_rooms = list(ActiveRoom.objects.all().values_list('id', flat=True))

        now = datetime.datetime.now()
        current_date = now.date()
        day_begin = datetime.datetime.combine(current_date, datetime.time(0, 0, 0))
        room_stats = list(RoomHourlyStat.objects.filter(created_at__gte=day_begin, identifier__icontains='Temp', active_room_id__in=building_rooms))
        active_rooms = list(ActiveRoom.objects.filter(id__in=[stat.active_room_id for stat in room_stats]))

        rooms = []
        for room in active_rooms:
            values = []
            for hour in range(now.hour):
                created_at = datetime.datetime.combine(current_date, datetime.time(hour, 59, 59))
                stat = next(filter(lambda x: x.active_room_id == room.id and x.created_at == created_at, room_stats), None)
                if stat is not None:
                    values.append(stat.avg)
                else:
                    value = 0
                    if hour != 0:
                        value = values[hour - 1]
                    values.append(value)

            rooms.append({
                'id': room.id,
                'name': room.name,
                'type': 'line',
                'smooth': True,
                'data': values
            })

        rooms = sorted(rooms, key=lambda obj: obj['data'][-1])

        return Response({
            'status': status.HTTP_200_OK,
            'rooms': rooms,
            'time': [hour for hour in range(now.hour)]
        })

class RoomStatsView(viewsets.ModelViewSet):
    def create(self, request, *args, **kwargs):
        project_id = request.user['project_id']
        arid = request.query_params.get('arid', None)
        if arid is None:
            raise exceptions.ValidationError(detail={'detail': 'arid is required!'})
        room = ActiveRoom.objects.get(pk=arid)
        month = request.query_params.get('month', None)
        if month is not None:
            year = int(month[:4])
            month = int(month[4:])
            monthrange = calendar.monthrange(year, month)[1]
        else:
            now = datetime.datetime.now()
            year = now.year
            month = now.month
            monthrange = now.day

        daily_stats = RoomDailyStat.objects.filter(active_room_id=arid, created_at__year=year, created_at__month=month)
        report_cfg = list(ReportConfigurer.objects.filter(Q(identifier__icontains='temp') | Q(identifier__icontains='rh') |
                                                          Q(identifier__icontains='hum'), target_id=arid, target_type__model='ActiveRoom'))

        created_time = datetime.time(23, 59, 59)

        template_path = f'saian_api/building/templates/room_daily_template_{project_id}.xltx'
        file_path = os.path.join(settings.BASE_DIR, template_path)
        file = Path(file_path)
        if not file.exists():
            template_path = f'saian_api/building/templates/room_daily_template.xltx'
            file_path = os.path.join(settings.BASE_DIR, template_path)
            file = Path(file_path)

        if file.exists() and file.is_file():
            wb = load_workbook(file_path)
            wb.template = False
            ws = wb.active

            ws['E1'] = f'{year}年{month:02}月'
            ws['A2'] = room.name

            for row_idx, row in enumerate(ws.iter_rows(min_row=4, min_col=1, max_row=35)):
                day = row_idx + 1
                if day > monthrange:
                    continue
                row_num = row_idx + 4
                created_at = datetime.datetime.combine(datetime.date(year, month, day), created_time)
                date = f'{year}/{month:02}/{day:02}'
                ws[f'A{row_num}'].value = date
                day_stats = {
                    'min_temp': '--',
                    'max_temp': '--',
                    'avg_temp': '--',
                    'min_hum': '--',
                    'max_hum': '--',
                    'avg_hum': '--'
                }
                for cfg in report_cfg:
                    record = next(filter(lambda x: x.identifier == cfg.identifier and x.created_at == created_at, daily_stats), None)
                    if record is not None:
                        if 'temp' in cfg.identifier.casefold():
                            day_stats['min_temp'] = float(record.min)
                            day_stats['max_temp'] = float(record.max)
                            day_stats['avg_temp'] = float(record.avg)
                        elif 'hum' in cfg.identifier.casefold() or cfg.identifier == 'LocalRH':
                            day_stats['min_hum'] = float(record.min)
                            day_stats['max_hum'] = float(record.max)
                            day_stats['avg_hum'] = float(record.avg)

                ws[f'B{row_num}'].value = day_stats['min_temp']
                ws[f'C{row_num}'].value = day_stats['max_temp']
                ws[f'D{row_num}'].value = day_stats['avg_temp']
                ws[f'E{row_num}'].value = day_stats['min_hum']
                ws[f'F{row_num}'].value = day_stats['max_hum']
                ws[f'G{row_num}'].value = day_stats['avg_hum']

            random_id = AuthUtils.generate_random_comm_char(9)
            filepath = f'drf-assets/exports/{room.name}_{year}{month:02}__{random_id}.xlsx'

            wb.save(filename=filepath)

        else:
            stats = []
            for d in range(1, monthrange):
                created_at = datetime.datetime.combine(datetime.date(year, month, d), created_time)
                stat = {
                    '日期': f'{year}/{month:02}/{d:02}',
                }
                for cfg in report_cfg:
                    record = next(filter(lambda x: x.identifier == cfg.identifier and x.created_at == created_at, daily_stats), None)
                    if record is not None:
                        stat[cfg.name] = record.avg
                    else:
                        stat[cfg.name] = '--'
                stats.append(stat)

            filepath, _ = ExcelUtils.generate_plain_sheet(stats, f'{room.name}_{year}{month:02}')

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'file': f'/{filepath}'
            }
        })

class FloorDeviceStatView(viewsets.ModelViewSet):

    def retrieve(self, request, *args, **kwargs):
        floor_id = request.query_params.get('floor_id', None)

        if not floor_id:
            raise exceptions.ValidationError(detail={'detail': 'floor_id is required!'})

        floor = Floor.objects.get(pk=floor_id)

        device_stats = {}

        if floor.coords_arr:
            try:
                coords_arr = json.loads(floor.coords_arr)
            except Exception as e:
                logging.error(f'解析楼层平面图控件出错. {floor.id}-{floor.name}, err: {e.__str__()}')
                raise exceptions.ValidationError(detail={'detail': 'floor coords_arr is invalid!'})

            # 房间温度功率控件
            room_mac = []
            room_ids = [item['room'] for item in coords_arr if item.get('type', None) == 'room-tew' and item.get('room', None)]
            if len(room_ids):
                terminal_type = ContentType.objects.get_for_model(Terminal)
                terminal_ids = list(RoomDevice.objects.filter(active_room_id__in=room_ids, content_type=terminal_type
                                                              ).values_list('object_id', flat=True))
                device_ids = list(Terminal.objects.filter(id__in=terminal_ids).values_list('device_id', flat=True))
                room_mac = list(Device.objects.filter(id__in=device_ids).values_list('mac', flat=True))

            mac_list = [item['mac'] for item in coords_arr if item.get('mac', None)] + room_mac

            devices = Device.objects.filter(mac__in=mac_list)
            
            device_stats['total'] = devices.count()
            device_stats['online'] = devices.filter(online=True).count()
            device_stats['offline'] = devices.filter(online=False).count()
            device_stats['total_alarm'] = devices.filter(online=True, in_alarm=True).count()
            device_stats['total_fault'] = devices.filter(online=True, in_fault=True).count()
            device_stats['total_run'] = devices.filter(online=True, sw_on=True).count()
            device_stats['total_stop'] = devices.filter(online=True, sw_on=False).count()

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'device_stats': device_stats
            }
        })

    def list(self, request, *args, **kwargs):

        floor_id = request.query_params.get('floor_id', None)

        if not floor_id:
            raise exceptions.ValidationError(detail={'detail': 'floor_id is required!'})
        results = []
        floor = Floor.objects.get(pk=floor_id)

        if floor.coords_arr:
            try:
                coords_arr = json.loads(floor.coords_arr)
            except Exception as e:
                logging.error(f'解析楼层平面图控件出错. {floor.id}-{floor.name}, err: {e.__str__()}')
                raise exceptions.ValidationError(detail={'detail': 'floor coords_arr is invalid!'})

            mac_list = [item['mac'] for item in coords_arr if item.get('mac', None)]
            devices = Device.objects.filter(mac__in=mac_list).order_by('nick_name')

            dp_cache = {}

            for device in devices:
                serializer = FloorDeviceSerializer(device)
                device_data = serializer.data
                dp = dp_cache.get(device.device_prototype_id, None)
                if dp is None:
                    dp = DevicePrototype.objects.get(pk=device.device_prototype_id)
                    dp_cache[dp.id] = dp
                device_data['device_prototype'] = {
                    'id': dp.id,
                    'name': dp.name,
                    'uni_name': dp.uni_name
                }

                device_data['dashboard_attres'] = device.dashboard_attres()

                results.append(device_data)

        return Response({
            'status': status.HTTP_200_OK,
            'data': {
                'devices': results
            },
            'count': len(results)
        })
