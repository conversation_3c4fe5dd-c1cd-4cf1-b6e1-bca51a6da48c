from rest_framework import status
from rest_framework import viewsets
from rest_framework.response import Response

from saian_api.utils.sy_jsonrenderer import <PERSON>yJ<PERSON><PERSON><PERSON>
from .models import DeviceMaintenance
from .serializers import DeviceMaintenanceSerializer

# Create your views here.
class DeviceMaintenanceViewSet(viewsets.ModelViewSet):
    serializer_class = DeviceMaintenanceSerializer
    renderer_classes = (SyJSONRender,)

    def perform_create(self, serializer):
        serializer.save(user_id=self.request.user['id'], project_id=self.request.user['project_id'])

    def get_queryset(self):
        return DeviceMaintenance.objects.filter(project_id=self.request.user['project_id'])

    def list(self, request, *args, **kwargs):
        return super(DeviceMaintenanceViewSet, self).list(request, *args, **kwargs)


class MaintenanceStatViewSet(viewsets.ViewSet):
    def list(self, request, prj=None):
        from saian_api.device.models import Device
        from saian_api.project.models import Project

        project = Project.objects.get(pk=request.user['project_id'])
        devices = Device.objects.filter(project=project).order_by('-needs_m')
        num_need = Device.objects.filter(project_id=request.user['project_id'], needs_m=True).count()

        devices_data = []

        for device in devices:
            tmp_data = {
                'id': device.id,
                'nick_name': device.nick_name,
                'run_time': device.cal_run_time(),
                'do_need': device.needs_m
            }

            devices_data.append(tmp_data)

        res_data = {
            'status': status.HTTP_200_OK,
            'total': len(devices),
            'data': {
                'total_num': len(devices),
                'num_need': num_need,
                'fan_time': project.fan_time,
                'devices': devices_data
            }
        }

        return Response(res_data)
