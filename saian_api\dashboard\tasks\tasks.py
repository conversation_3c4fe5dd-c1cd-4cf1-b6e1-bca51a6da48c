import datetime
import os
from statistics import mean

import django

from saian_api.celery import celery_app
from saian_api.utils.tools import most_frequent

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "../../settings.py")
django.setup()

from saian_api.dashboard.models import ProjectWeather
from saian_api.utils.utils import CeleryTaskUtils

@celery_app.task(name='saian_api.dashboard.tasks.project_hourly_weather')
def projectWeatherHourTask():
    projects = CeleryTaskUtils.get_all_project()

    for project in projects:
        ProjectWeather.objects.create_record(project)

    # ProjectWeather.objects.bulk_create(weathers)

    # print('test')

@celery_app.task(name='saian_api.dashboard.tasks.project_daily_weather')
def projectWeatherDayTask(date=None):
    # 今天
    projects = CeleryTaskUtils.get_all_project()
    today = datetime.date.today() if date is None else date + datetime.timedelta(days=1)
    oneday = datetime.timedelta(days=1)
    # 昨天开始的时间
    yesterday = datetime.datetime.combine(today - oneday, datetime.time.min)
    # 昨天结束的时间，精确到秒
    last_second_of_yesterday = datetime.datetime.combine(today, datetime.time.min) + datetime.timedelta(seconds=-1)
    # 统计的是从昨天 00:00:00 开始，到昨天 23:59:59 ，这段时间的记录
    for project in projects:
        # 找出对应项目昨天的每小时记录
        weathers = ProjectWeather.objects.using(project['db_name']).filter(
            created_at__range=[yesterday, last_second_of_yesterday],
            project=project['project'], type='hr')
        weather_day = computeWeatherData(weathers, project['project_id'], 'di', yesterday)

        # 是否当日数据已存在
        db_yesterday = ProjectWeather.objects.using(project['db_name']).filter(
            created_at__range=[yesterday, last_second_of_yesterday],
            project=project['project'],
            type='di')
        if db_yesterday.exists():
            weather_day.id = db_yesterday.last().id

        weather_day.save(using=project['db_name'])
        # 将创建时间改为昨天
        # weather_day.created_at = yesterday
        # weather_day.save()

@celery_app.task(name='saian_api.dashboard.tasks.project_monthly_weather')
def projectWeatherMonthTask(date=None):
    today = datetime.date.today() if date is None else date
    last_day_of_last_month = datetime.datetime.combine(datetime.date(today.year, today.month, 1),
                                                       datetime.time.min) + datetime.timedelta(seconds=-1)
    first_day_of_last_month = datetime.datetime.combine(
        datetime.date(last_day_of_last_month.year, last_day_of_last_month.month, 1), datetime.time.min)

    projects = CeleryTaskUtils.get_all_project()
    for project in projects:
        weathers = ProjectWeather.objects.using(project['db_name']).filter(
            created_at__range=[first_day_of_last_month, last_day_of_last_month],
            project=project['project'], type='di')
        weather_month = computeWeatherData(weathers, project['project_id'], 'mth', first_day_of_last_month)

        weather_month.save(using=project['db_name'])
        # 创建时间改为上个月
        # weather_month.created_at = first_day_of_last_month
        # weather_month.save()

@celery_app.task(name='saian_api.dashboard.tasks.project_yearly_weather')
def projectWeatherYearTask(date=None):
    today = datetime.date.today() if date is None else date
    last_day_of_last_year = datetime.datetime.combine(datetime.date(today.year, 1, 1),
                                                      datetime.time.min) + datetime.timedelta(seconds=-1)
    first_day_of_last_year = datetime.datetime.combine(datetime.date(last_day_of_last_year.year, 1, 1),
                                                       datetime.time.min)

    projects = CeleryTaskUtils.get_all_project()
    for project in projects:
        weathers = ProjectWeather.objects.using(project['db_name']).filter(
            created_at__range=[first_day_of_last_year, last_day_of_last_year],
            type='mth', project=project['project'])

        weather_year = computeWeatherData(weathers, project['project_id'], 'yr', first_day_of_last_year)

        weather_year.save(using=project['db_name'])
        # weather_year.created_at = first_day_of_last_year
        # weather_year.save()

# @celery_app.on_after_configure.connect()
# def settingTasks(sender, **kwargs):
#     sender.add_periodic_task(10, tenSecondTask.s(), name='Ten Second Task')

# 计算年月日的天气数据
def computeWeatherData(weathers: list[ProjectWeather], project, weather_type, created_at):
    min_temp_record = []
    max_temp_record = []
    min_humidity_record = []
    max_humidity_record = []
    temp_record = []
    humidity_record = []
    wetbulb_record = []
    pressure_record = []
    wea_record = []
    air_record = []
    uv_record = []

    for weather in weathers:
        min_temp_record.append(weather.min_temp)
        max_temp_record.append(weather.max_temp)
        min_humidity_record.append(weather.min_humidity)
        max_humidity_record.append(weather.max_humidity)
        temp_record.append(weather.temp)
        humidity_record.append(weather.humidity)
        if weather.wetbulb_temp:
            wetbulb_record.append(weather.wetbulb_temp)
        if weather.pressure:
            pressure_record.append(weather.pressure)
        if weather.air:
            air_record.append(weather.air)
        if weather.wea:
            wea_record.append(weather.wea)
        if weather.uv:
            uv_record.append(weather.uv)

    min_temp = min(min_temp_record) if len(min_temp_record) else 0
    max_temp = max(max_temp_record) if len(max_temp_record) else 0
    min_humidity = min(min_humidity_record) if len(min_humidity_record) else 0
    max_humidity = max(max_humidity_record) if len(max_humidity_record) else 0
    temp = round(mean(temp_record), 2) if len(temp_record) else 0
    humidity = round(mean(humidity_record), 2) if len(humidity_record) else 0
    wetbulb_temp = round(mean(wetbulb_record), 2) if len(wetbulb_record) else 0
    pressure = round(mean(pressure_record), 2) if len(pressure_record) else 0
    air = most_frequent(air_record)
    wea = most_frequent(wea_record)
    uv = most_frequent(uv_record)

    w = ProjectWeather(temp=temp,
                       humidity=humidity,
                       min_temp=min_temp,
                       max_temp=max_temp,
                       min_humidity=min_humidity,
                       max_humidity=max_humidity,
                       type=weather_type,
                       created_at=created_at,
                       wetbulb_temp=wetbulb_temp,
                       pressure=pressure,
                       air=air,
                       wea=wea,
                       uv=uv)
    w.project_id = project
    return w
