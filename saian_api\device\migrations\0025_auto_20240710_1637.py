# Generated by Django 3.2.19 on 2024-07-10 16:37

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('device', '0024_auto_20240625_1153'),
    ]

    operations = [
        migrations.CreateModel(
            name='DaSnapshotHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('attribute_prototype_id', models.BigIntegerField()),
                ('value', models.CharField(max_length=255, null=True)),
                ('snapped_at', models.DateTimeField()),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('device', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='device.device')),
            ],
            options={
                'db_table': 'da_snapshot_histories',
                'ordering': ['-snapped_at'],
            },
        ),
        migrations.AddIndex(
            model_name='dasnapshothistory',
            index=models.Index(fields=['device_id', 'attribute_prototype_id', 'snapped_at'], name='da_snapshot_device__d66833_idx'),
        ),
    ]
